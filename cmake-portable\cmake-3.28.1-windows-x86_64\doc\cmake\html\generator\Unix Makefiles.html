
<!DOCTYPE html>

<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="generator" content="Docutils 0.19: https://docutils.sourceforge.io/" />

  <title>Unix Makefiles &mdash; CMake 3.28.1 Documentation</title>

    <link rel="stylesheet" type="text/css" href="../_static/pygments.css" />
    <link rel="stylesheet" type="text/css" href="../_static/cmake.css" />
    
    <script data-url_root="../" id="documentation_options" src="../_static/documentation_options.js"></script>
    <script src="../_static/doctools.js"></script>
    <script src="../_static/sphinx_highlight.js"></script>
    
    <link rel="icon" href="../_static/cmake-favicon.ico"/>
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="next" title="Watcom WMake" href="Watcom%20WMake.html" />
    <link rel="prev" title="NMake Makefiles JOM" href="NMake%20Makefiles%20JOM.html" />
 

  </head><body>

    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             accesskey="I">index</a></li>
        <li class="right" >
          <a href="Watcom%20WMake.html" title="Watcom WMake"
             accesskey="N">next</a> |</li>
        <li class="right" >
          <a href="NMake%20Makefiles%20JOM.html" title="NMake Makefiles JOM"
             accesskey="P">previous</a> |</li>
  <li>
    <img src="../_static/cmake-logo-16.png" alt=""
         style="vertical-align: middle; margin-top: -2px" />
  </li>
  <li>
    <a href="https://cmake.org/">CMake</a> &#187;
  </li>
  <li>
    <a href="../index.html">3.28.1 Documentation</a> &#187;
  </li>

          <li class="nav-item nav-item-1"><a href="../manual/cmake-generators.7.html" accesskey="U">cmake-generators(7)</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href="">Unix Makefiles</a></li> 
      </ul>
    </div>  

    <div class="document">
      <div class="documentwrapper">
        <div class="bodywrapper">
          <div class="body" role="main">
            
  <section id="unix-makefiles">
<span id="generator:Unix Makefiles"></span><h1>Unix Makefiles<a class="headerlink" href="#unix-makefiles" title="Permalink to this heading">¶</a></h1>
<p>Generates standard UNIX makefiles.</p>
<p>A hierarchy of UNIX makefiles is generated into the build tree.  Use
any standard UNIX-style make program to build the project through
the <code class="docutils literal notranslate"><span class="pre">all</span></code> target and install the project through the <code class="docutils literal notranslate"><span class="pre">install</span></code>
(or <code class="docutils literal notranslate"><span class="pre">install/strip</span></code>) target.</p>
<p>For each subdirectory <code class="docutils literal notranslate"><span class="pre">sub/dir</span></code> of the project a UNIX makefile will
be created, containing the following targets:</p>
<dl>
<dt><code class="docutils literal notranslate"><span class="pre">all</span></code></dt><dd><p>Depends on all targets required by the subdirectory.</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">install</span></code></dt><dd><p>Runs the install step in the subdirectory, if any.</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">install/strip</span></code></dt><dd><p>Runs the install step in the subdirectory followed by a <code class="docutils literal notranslate"><span class="pre">CMAKE_STRIP</span></code> command,
if any.</p>
<p>The <code class="docutils literal notranslate"><span class="pre">CMAKE_STRIP</span></code> variable will contain the platform's <code class="docutils literal notranslate"><span class="pre">strip</span></code> utility, which
removes symbols information from generated binaries.</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">test</span></code></dt><dd><p>Runs the test step in the subdirectory, if any.</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">package</span></code></dt><dd><p>Runs the package step in the subdirectory, if any.</p>
</dd>
</dl>
</section>


            <div class="clearer"></div>
          </div>
        </div>
      </div>
      <div class="sphinxsidebar" role="navigation" aria-label="main navigation">
        <div class="sphinxsidebarwrapper">
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="NMake%20Makefiles%20JOM.html"
                          title="previous chapter">NMake Makefiles JOM</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="Watcom%20WMake.html"
                          title="next chapter">Watcom WMake</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../_sources/generator/Unix Makefiles.rst.txt"
            rel="nofollow">Show Source</a></li>
    </ul>
   </div>
<div id="searchbox" style="display: none" role="search">
  <h3 id="searchlabel">Quick search</h3>
    <div class="searchformwrapper">
    <form class="search" action="../search.html" method="get">
      <input type="text" name="q" aria-labelledby="searchlabel" autocomplete="off" autocorrect="off" autocapitalize="off" spellcheck="false"/>
      <input type="submit" value="Go" />
    </form>
    </div>
</div>
<script>document.getElementById('searchbox').style.display = "block"</script>
        </div>
      </div>
      <div class="clearer"></div>
    </div>
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             >index</a></li>
        <li class="right" >
          <a href="Watcom%20WMake.html" title="Watcom WMake"
             >next</a> |</li>
        <li class="right" >
          <a href="NMake%20Makefiles%20JOM.html" title="NMake Makefiles JOM"
             >previous</a> |</li>
  <li>
    <img src="../_static/cmake-logo-16.png" alt=""
         style="vertical-align: middle; margin-top: -2px" />
  </li>
  <li>
    <a href="https://cmake.org/">CMake</a> &#187;
  </li>
  <li>
    <a href="../index.html">3.28.1 Documentation</a> &#187;
  </li>

          <li class="nav-item nav-item-1"><a href="../manual/cmake-generators.7.html" >cmake-generators(7)</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href="">Unix Makefiles</a></li> 
      </ul>
    </div>

    <div class="footer" role="contentinfo">
        &#169; Copyright 2000-2023 Kitware, Inc. and Contributors.
      Created using <a href="https://www.sphinx-doc.org/">Sphinx</a> 6.2.1.
    </div>
  </body>
</html>