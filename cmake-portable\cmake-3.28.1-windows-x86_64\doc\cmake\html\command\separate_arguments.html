
<!DOCTYPE html>

<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="generator" content="Docutils 0.19: https://docutils.sourceforge.io/" />

  <title>separate_arguments &mdash; CMake 3.28.1 Documentation</title>

    <link rel="stylesheet" type="text/css" href="../_static/pygments.css" />
    <link rel="stylesheet" type="text/css" href="../_static/cmake.css" />
    
    <script data-url_root="../" id="documentation_options" src="../_static/documentation_options.js"></script>
    <script src="../_static/doctools.js"></script>
    <script src="../_static/sphinx_highlight.js"></script>
    
    <link rel="icon" href="../_static/cmake-favicon.ico"/>
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="next" title="set" href="set.html" />
    <link rel="prev" title="return" href="return.html" />
 

  </head><body>

    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             accesskey="I">index</a></li>
        <li class="right" >
          <a href="set.html" title="set"
             accesskey="N">next</a> |</li>
        <li class="right" >
          <a href="return.html" title="return"
             accesskey="P">previous</a> |</li>
  <li>
    <img src="../_static/cmake-logo-16.png" alt=""
         style="vertical-align: middle; margin-top: -2px" />
  </li>
  <li>
    <a href="https://cmake.org/">CMake</a> &#187;
  </li>
  <li>
    <a href="../index.html">3.28.1 Documentation</a> &#187;
  </li>

          <li class="nav-item nav-item-1"><a href="../manual/cmake-commands.7.html" accesskey="U">cmake-commands(7)</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href="">separate_arguments</a></li> 
      </ul>
    </div>  

    <div class="document">
      <div class="documentwrapper">
        <div class="bodywrapper">
          <div class="body" role="main">
            
  <section id="separate-arguments">
<span id="command:separate_arguments"></span><h1>separate_arguments<a class="headerlink" href="#separate-arguments" title="Permalink to this heading">¶</a></h1>
<p>Parse command-line arguments into a semicolon-separated list.</p>
<div class="highlight-cmake notranslate"><div class="highlight"><pre><span></span><span class="nf">separate_arguments(</span><span class="nv">&lt;variable&gt;</span><span class="w"> </span><span class="nv">&lt;mode&gt;</span><span class="w"> </span><span class="p">[</span><span class="no">PROGRAM</span><span class="w"> </span><span class="p">[</span><span class="no">SEPARATE_ARGS</span><span class="p">]]</span><span class="w"> </span><span class="nv">&lt;args&gt;</span><span class="nf">)</span>
</pre></div>
</div>
<p>Parses a space-separated string <code class="docutils literal notranslate"><span class="pre">&lt;args&gt;</span></code> into a list of items,
and stores this list in semicolon-separated standard form in <code class="docutils literal notranslate"><span class="pre">&lt;variable&gt;</span></code>.</p>
<p>This function is intended for parsing command-line arguments.
The entire command line must be passed as one string in the
argument <code class="docutils literal notranslate"><span class="pre">&lt;args&gt;</span></code>.</p>
<p>The exact parsing rules depend on the operating system.
They are specified by the <code class="docutils literal notranslate"><span class="pre">&lt;mode&gt;</span></code> argument which must
be one of the following keywords:</p>
<dl>
<dt><code class="docutils literal notranslate"><span class="pre">UNIX_COMMAND</span></code></dt><dd><p>Arguments are separated by unquoted whitespace.
Both single-quote and double-quote pairs are respected.
A backslash escapes the next literal character (<code class="docutils literal notranslate"><span class="pre">\&quot;</span></code> is <code class="docutils literal notranslate"><span class="pre">&quot;</span></code>);
there are no special escapes (<code class="docutils literal notranslate"><span class="pre">\n</span></code> is just <code class="docutils literal notranslate"><span class="pre">n</span></code>).</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">WINDOWS_COMMAND</span></code></dt><dd><p>A Windows command-line is parsed using the same
syntax the runtime library uses to construct argv at startup.  It
separates arguments by whitespace that is not double-quoted.
Backslashes are literal unless they precede double-quotes.  See the
MSDN article <a class="reference external" href="https://learn.microsoft.com/en-us/cpp/c-language/parsing-c-command-line-arguments">Parsing C Command-Line Arguments</a> for details.</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">NATIVE_COMMAND</span></code></dt><dd><div class="versionadded">
<p><span class="versionmodified added">New in version 3.9.</span></p>
</div>
<p>Proceeds as in <code class="docutils literal notranslate"><span class="pre">WINDOWS_COMMAND</span></code> mode if the host system is Windows.
Otherwise proceeds as in <code class="docutils literal notranslate"><span class="pre">UNIX_COMMAND</span></code> mode.</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">PROGRAM</span></code></dt><dd><div class="versionadded">
<p><span class="versionmodified added">New in version 3.19.</span></p>
</div>
<p>The first item in <code class="docutils literal notranslate"><span class="pre">&lt;args&gt;</span></code> is assumed to be an executable and will be
searched in the system search path or left as a full path. If not found,
<code class="docutils literal notranslate"><span class="pre">&lt;variable&gt;</span></code> will be empty. Otherwise, <code class="docutils literal notranslate"><span class="pre">&lt;variable&gt;</span></code> is a list of 2
elements:</p>
<blockquote>
<div><ol class="arabic simple" start="0">
<li><p>Absolute path of the program</p></li>
<li><p>Any command-line arguments present in <code class="docutils literal notranslate"><span class="pre">&lt;args&gt;</span></code> as a string</p></li>
</ol>
</div></blockquote>
<p>For example:</p>
<div class="highlight-cmake notranslate"><div class="highlight"><pre><span></span><span class="nf">separate_arguments</span> <span class="nf">(</span><span class="nb">out</span><span class="w"> </span><span class="no">UNIX_COMMAND</span><span class="w"> </span><span class="no">PROGRAM</span><span class="w"> </span><span class="s">&quot;cc -c main.c&quot;</span><span class="nf">)</span>
</pre></div>
</div>
<ul class="simple">
<li><p>First element of the list: <code class="docutils literal notranslate"><span class="pre">/path/to/cc</span></code></p></li>
<li><p>Second element of the list: <code class="docutils literal notranslate"><span class="pre">&quot;</span> <span class="pre">-c</span> <span class="pre">main.c&quot;</span></code></p></li>
</ul>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">SEPARATE_ARGS</span></code></dt><dd><p>When this sub-option of <code class="docutils literal notranslate"><span class="pre">PROGRAM</span></code> option is specified, command-line
arguments will be split as well and stored in <code class="docutils literal notranslate"><span class="pre">&lt;variable&gt;</span></code>.</p>
<p>For example:</p>
<div class="highlight-cmake notranslate"><div class="highlight"><pre><span></span><span class="nf">separate_arguments</span> <span class="nf">(</span><span class="nb">out</span><span class="w"> </span><span class="no">UNIX_COMMAND</span><span class="w"> </span><span class="no">PROGRAM</span><span class="w"> </span><span class="no">SEPARATE_ARGS</span><span class="w"> </span><span class="s">&quot;cc -c main.c&quot;</span><span class="nf">)</span>
</pre></div>
</div>
<p>The contents of <code class="docutils literal notranslate"><span class="pre">out</span></code> will be: <code class="docutils literal notranslate"><span class="pre">/path/to/cc;-c;main.c</span></code></p>
</dd>
</dl>
<div class="highlight-cmake notranslate"><div class="highlight"><pre><span></span><span class="nf">separate_arguments(</span><span class="nv">&lt;var&gt;</span><span class="nf">)</span>
</pre></div>
</div>
<p>Convert the value of <code class="docutils literal notranslate"><span class="pre">&lt;var&gt;</span></code> to a semi-colon separated list.  All
spaces are replaced with ';'.  This helps with generating command
lines.</p>
</section>


            <div class="clearer"></div>
          </div>
        </div>
      </div>
      <div class="sphinxsidebar" role="navigation" aria-label="main navigation">
        <div class="sphinxsidebarwrapper">
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="return.html"
                          title="previous chapter">return</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="set.html"
                          title="next chapter">set</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../_sources/command/separate_arguments.rst.txt"
            rel="nofollow">Show Source</a></li>
    </ul>
   </div>
<div id="searchbox" style="display: none" role="search">
  <h3 id="searchlabel">Quick search</h3>
    <div class="searchformwrapper">
    <form class="search" action="../search.html" method="get">
      <input type="text" name="q" aria-labelledby="searchlabel" autocomplete="off" autocorrect="off" autocapitalize="off" spellcheck="false"/>
      <input type="submit" value="Go" />
    </form>
    </div>
</div>
<script>document.getElementById('searchbox').style.display = "block"</script>
        </div>
      </div>
      <div class="clearer"></div>
    </div>
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             >index</a></li>
        <li class="right" >
          <a href="set.html" title="set"
             >next</a> |</li>
        <li class="right" >
          <a href="return.html" title="return"
             >previous</a> |</li>
  <li>
    <img src="../_static/cmake-logo-16.png" alt=""
         style="vertical-align: middle; margin-top: -2px" />
  </li>
  <li>
    <a href="https://cmake.org/">CMake</a> &#187;
  </li>
  <li>
    <a href="../index.html">3.28.1 Documentation</a> &#187;
  </li>

          <li class="nav-item nav-item-1"><a href="../manual/cmake-commands.7.html" >cmake-commands(7)</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href="">separate_arguments</a></li> 
      </ul>
    </div>

    <div class="footer" role="contentinfo">
        &#169; Copyright 2000-2023 Kitware, Inc. and Contributors.
      Created using <a href="https://www.sphinx-doc.org/">Sphinx</a> 6.2.1.
    </div>
  </body>
</html>