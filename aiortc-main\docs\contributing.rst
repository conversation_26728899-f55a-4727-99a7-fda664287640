Contributing
============

Thanks for taking the time to contribute to ``aiortc``!

Code of Conduct
---------------

This project and everyone participating in it is governed by the `Code of
Conduct`_. By participating, you are expected to uphold this code. Please
report inappropriate behavior to jere<PERSON> laine AT m4x DOT org.

.. _Code of Conduct: https://github.com/aiortc/aiortc/blob/main/CODE_OF_CONDUCT.md

Contributions
-------------

Bug reports, patches and suggestions are welcome!

Please open an issue_ or send a `pull request`_.

Feedback about the examples or documentation are especially valuable as they
make ``aiortc`` accessible to a wider audience.

Code contributions *must* come with full unit test coverage. WebRTC is a
complex protocol stack and ensuring correct behaviour now and in the future
requires a proper investment in automated testing.

.. _issue: https://github.com/aiortc/aiortc/issues/new
.. _pull request: https://github.com/aiortc/aiortc/compare/

Questions
---------

GitHub issues aren't a good medium for handling questions. There are better
places to ask questions, for example Stack Overflow.

If you want to ask a question anyway, please make sure that:

- it's a question about ``aiortc`` and not about :mod:`asyncio`;
- it isn't answered by the documentation;
- it wasn't asked already.

A good question can be written as a suggestion to improve the documentation.
