
<!DOCTYPE html>

<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="generator" content="Docutils 0.19: https://docutils.sourceforge.io/" />

  <title>InstallRequiredSystemLibraries &mdash; CMake 3.28.1 Documentation</title>

    <link rel="stylesheet" type="text/css" href="../_static/pygments.css" />
    <link rel="stylesheet" type="text/css" href="../_static/cmake.css" />
    
    <script data-url_root="../" id="documentation_options" src="../_static/documentation_options.js"></script>
    <script src="../_static/doctools.js"></script>
    <script src="../_static/sphinx_highlight.js"></script>
    
    <link rel="icon" href="../_static/cmake-favicon.ico"/>
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="next" title="ProcessorCount" href="ProcessorCount.html" />
    <link rel="prev" title="GoogleTest" href="GoogleTest.html" />
 

  </head><body>

    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             accesskey="I">index</a></li>
        <li class="right" >
          <a href="ProcessorCount.html" title="ProcessorCount"
             accesskey="N">next</a> |</li>
        <li class="right" >
          <a href="GoogleTest.html" title="GoogleTest"
             accesskey="P">previous</a> |</li>
  <li>
    <img src="../_static/cmake-logo-16.png" alt=""
         style="vertical-align: middle; margin-top: -2px" />
  </li>
  <li>
    <a href="https://cmake.org/">CMake</a> &#187;
  </li>
  <li>
    <a href="../index.html">3.28.1 Documentation</a> &#187;
  </li>

          <li class="nav-item nav-item-1"><a href="../manual/cmake-modules.7.html" accesskey="U">cmake-modules(7)</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href="">InstallRequiredSystemLibraries</a></li> 
      </ul>
    </div>  

    <div class="document">
      <div class="documentwrapper">
        <div class="bodywrapper">
          <div class="body" role="main">
            
  <section id="installrequiredsystemlibraries">
<span id="module:InstallRequiredSystemLibraries"></span><h1>InstallRequiredSystemLibraries<a class="headerlink" href="#installrequiredsystemlibraries" title="Permalink to this heading">¶</a></h1>
<p>Include this module to search for compiler-provided system runtime
libraries and add install rules for them.  Some optional variables
may be set prior to including the module to adjust behavior:</p>
<dl>
<dt><code class="docutils literal notranslate"><span class="pre">CMAKE_INSTALL_SYSTEM_RUNTIME_LIBS</span></code></dt><dd><p>Specify additional runtime libraries that may not be detected.
After inclusion any detected libraries will be appended to this.</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">CMAKE_INSTALL_SYSTEM_RUNTIME_LIBS_SKIP</span></code></dt><dd><p>Set to TRUE to skip calling the <span class="target" id="index-0-command:install"></span><a class="reference internal" href="../command/install.html#programs" title="install(programs)"><code class="xref cmake cmake-command docutils literal notranslate"><span class="pre">install(PROGRAMS)</span></code></a> command to
allow the includer to specify its own install rule, using the value of
<code class="docutils literal notranslate"><span class="pre">CMAKE_INSTALL_SYSTEM_RUNTIME_LIBS</span></code> to get the list of libraries.</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">CMAKE_INSTALL_DEBUG_LIBRARIES</span></code></dt><dd><p>Set to TRUE to install the debug runtime libraries when available
with MSVC tools.</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">CMAKE_INSTALL_DEBUG_LIBRARIES_ONLY</span></code></dt><dd><p>Set to TRUE to install only the debug runtime libraries with MSVC
tools even if the release runtime libraries are also available.</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">CMAKE_INSTALL_UCRT_LIBRARIES</span></code></dt><dd><div class="versionadded">
<p><span class="versionmodified added">New in version 3.6.</span></p>
</div>
<p>Set to TRUE to install the Windows Universal CRT libraries for
app-local deployment (e.g. to Windows XP).  This is meaningful
only with MSVC from Visual Studio 2015 or higher.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.9: </span>One may set a <code class="docutils literal notranslate"><span class="pre">CMAKE_WINDOWS_KITS_10_DIR</span></code> <em>environment variable</em>
to an absolute path to tell CMake to look for Windows 10 SDKs in
a custom location.  The specified directory is expected to contain
<code class="docutils literal notranslate"><span class="pre">Redist/ucrt/DLLs/*</span></code> directories.</p>
</div>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">CMAKE_INSTALL_MFC_LIBRARIES</span></code></dt><dd><p>Set to TRUE to install the MSVC MFC runtime libraries.</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">CMAKE_INSTALL_OPENMP_LIBRARIES</span></code></dt><dd><p>Set to TRUE to install the MSVC OpenMP runtime libraries</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">CMAKE_INSTALL_SYSTEM_RUNTIME_DESTINATION</span></code></dt><dd><p>Specify the <span class="target" id="index-1-command:install"></span><a class="reference internal" href="../command/install.html#programs" title="install(programs)"><code class="xref cmake cmake-command docutils literal notranslate"><span class="pre">install(PROGRAMS)</span></code></a> command <code class="docutils literal notranslate"><span class="pre">DESTINATION</span></code>
option.  If not specified, the default is <code class="docutils literal notranslate"><span class="pre">bin</span></code> on Windows
and <code class="docutils literal notranslate"><span class="pre">lib</span></code> elsewhere.</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">CMAKE_INSTALL_SYSTEM_RUNTIME_LIBS_NO_WARNINGS</span></code></dt><dd><p>Set to TRUE to disable warnings about required library files that
do not exist.  (For example, Visual Studio Express editions may
not provide the redistributable files.)</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">CMAKE_INSTALL_SYSTEM_RUNTIME_COMPONENT</span></code></dt><dd><div class="versionadded">
<p><span class="versionmodified added">New in version 3.3.</span></p>
</div>
<p>Specify the <span class="target" id="index-2-command:install"></span><a class="reference internal" href="../command/install.html#programs" title="install(programs)"><code class="xref cmake cmake-command docutils literal notranslate"><span class="pre">install(PROGRAMS)</span></code></a> command <code class="docutils literal notranslate"><span class="pre">COMPONENT</span></code>
option.  If not specified, no such option will be used.</p>
</dd>
</dl>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.10: </span>Support for installing Intel compiler runtimes.</p>
</div>
</section>


            <div class="clearer"></div>
          </div>
        </div>
      </div>
      <div class="sphinxsidebar" role="navigation" aria-label="main navigation">
        <div class="sphinxsidebarwrapper">
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="GoogleTest.html"
                          title="previous chapter">GoogleTest</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="ProcessorCount.html"
                          title="next chapter">ProcessorCount</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../_sources/module/InstallRequiredSystemLibraries.rst.txt"
            rel="nofollow">Show Source</a></li>
    </ul>
   </div>
<div id="searchbox" style="display: none" role="search">
  <h3 id="searchlabel">Quick search</h3>
    <div class="searchformwrapper">
    <form class="search" action="../search.html" method="get">
      <input type="text" name="q" aria-labelledby="searchlabel" autocomplete="off" autocorrect="off" autocapitalize="off" spellcheck="false"/>
      <input type="submit" value="Go" />
    </form>
    </div>
</div>
<script>document.getElementById('searchbox').style.display = "block"</script>
        </div>
      </div>
      <div class="clearer"></div>
    </div>
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             >index</a></li>
        <li class="right" >
          <a href="ProcessorCount.html" title="ProcessorCount"
             >next</a> |</li>
        <li class="right" >
          <a href="GoogleTest.html" title="GoogleTest"
             >previous</a> |</li>
  <li>
    <img src="../_static/cmake-logo-16.png" alt=""
         style="vertical-align: middle; margin-top: -2px" />
  </li>
  <li>
    <a href="https://cmake.org/">CMake</a> &#187;
  </li>
  <li>
    <a href="../index.html">3.28.1 Documentation</a> &#187;
  </li>

          <li class="nav-item nav-item-1"><a href="../manual/cmake-modules.7.html" >cmake-modules(7)</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href="">InstallRequiredSystemLibraries</a></li> 
      </ul>
    </div>

    <div class="footer" role="contentinfo">
        &#169; Copyright 2000-2023 Kitware, Inc. and Contributors.
      Created using <a href="https://www.sphinx-doc.org/">Sphinx</a> 6.2.1.
    </div>
  </body>
</html>