
<!DOCTYPE html>

<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="generator" content="Docutils 0.19: https://docutils.sourceforge.io/" />

  <title>create_test_sourcelist &mdash; CMake 3.28.1 Documentation</title>

    <link rel="stylesheet" type="text/css" href="../_static/pygments.css" />
    <link rel="stylesheet" type="text/css" href="../_static/cmake.css" />
    
    <script data-url_root="../" id="documentation_options" src="../_static/documentation_options.js"></script>
    <script src="../_static/doctools.js"></script>
    <script src="../_static/sphinx_highlight.js"></script>
    
    <link rel="icon" href="../_static/cmake-favicon.ico"/>
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="next" title="define_property" href="define_property.html" />
    <link rel="prev" title="cmake_file_api" href="cmake_file_api.html" />
 

  </head><body>

    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             accesskey="I">index</a></li>
        <li class="right" >
          <a href="define_property.html" title="define_property"
             accesskey="N">next</a> |</li>
        <li class="right" >
          <a href="cmake_file_api.html" title="cmake_file_api"
             accesskey="P">previous</a> |</li>
  <li>
    <img src="../_static/cmake-logo-16.png" alt=""
         style="vertical-align: middle; margin-top: -2px" />
  </li>
  <li>
    <a href="https://cmake.org/">CMake</a> &#187;
  </li>
  <li>
    <a href="../index.html">3.28.1 Documentation</a> &#187;
  </li>

          <li class="nav-item nav-item-1"><a href="../manual/cmake-commands.7.html" accesskey="U">cmake-commands(7)</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href="">create_test_sourcelist</a></li> 
      </ul>
    </div>  

    <div class="document">
      <div class="documentwrapper">
        <div class="bodywrapper">
          <div class="body" role="main">
            
  <section id="create-test-sourcelist">
<span id="command:create_test_sourcelist"></span><h1>create_test_sourcelist<a class="headerlink" href="#create-test-sourcelist" title="Permalink to this heading">¶</a></h1>
<p>Create a test driver and source list for building test programs.</p>
<div class="highlight-cmake notranslate"><div class="highlight"><pre><span></span><span class="nf">create_test_sourcelist(</span><span class="nv">&lt;sourceListName&gt;</span><span class="w"> </span><span class="nv">&lt;driverName&gt;</span>
<span class="w">                       </span><span class="nv">&lt;tests&gt;</span><span class="w"> </span><span class="p">...</span>
<span class="w">                       </span><span class="p">[</span><span class="no">EXTRA_INCLUDE</span><span class="w"> </span><span class="nv">&lt;include&gt;</span><span class="p">]</span>
<span class="w">                       </span><span class="p">[</span><span class="no">FUNCTION</span><span class="w"> </span><span class="nv">&lt;function&gt;</span><span class="p">]</span><span class="nf">)</span>
</pre></div>
</div>
<p>A test driver is a program that links together many small tests into a single
executable.  This is useful when building static executables with large
libraries to shrink the total required size.  The list of source files needed
to build the test driver will be in <code class="docutils literal notranslate"><span class="pre">sourceListName</span></code>.  <code class="docutils literal notranslate"><span class="pre">driverName</span></code> is the
name of the test driver program. The rest of the arguments consist of a list
of test source files and can be semicolon separated.  Each test source file
should have a function in it that is the same name as the file with no
extension (<code class="docutils literal notranslate"><span class="pre">foo.cxx</span></code> should have <code class="docutils literal notranslate"><span class="pre">int</span> <span class="pre">foo(int,</span> <span class="pre">char*[]);</span></code>). <code class="docutils literal notranslate"><span class="pre">driverName</span></code>
will be able to call each of the tests by name on the command line.  If
<code class="docutils literal notranslate"><span class="pre">EXTRA_INCLUDE</span></code> is specified, then the next argument is included into the
generated file. If <code class="docutils literal notranslate"><span class="pre">FUNCTION</span></code> is specified, then the next argument is taken
as a function name that is passed pointers to <code class="docutils literal notranslate"><span class="pre">argc</span></code> and <code class="docutils literal notranslate"><span class="pre">argv</span></code>.  This can
be used to add extra command line processing to each test.  The
<code class="docutils literal notranslate"><span class="pre">CMAKE_TESTDRIVER_BEFORE_TESTMAIN</span></code> cmake variable can be set to have code
that will be placed directly before calling the test <code class="docutils literal notranslate"><span class="pre">main</span></code> function.
<code class="docutils literal notranslate"><span class="pre">CMAKE_TESTDRIVER_AFTER_TESTMAIN</span></code> can be set to have code that will be
placed directly after the call to the test <code class="docutils literal notranslate"><span class="pre">main</span></code> function.</p>
</section>


            <div class="clearer"></div>
          </div>
        </div>
      </div>
      <div class="sphinxsidebar" role="navigation" aria-label="main navigation">
        <div class="sphinxsidebarwrapper">
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="cmake_file_api.html"
                          title="previous chapter">cmake_file_api</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="define_property.html"
                          title="next chapter">define_property</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../_sources/command/create_test_sourcelist.rst.txt"
            rel="nofollow">Show Source</a></li>
    </ul>
   </div>
<div id="searchbox" style="display: none" role="search">
  <h3 id="searchlabel">Quick search</h3>
    <div class="searchformwrapper">
    <form class="search" action="../search.html" method="get">
      <input type="text" name="q" aria-labelledby="searchlabel" autocomplete="off" autocorrect="off" autocapitalize="off" spellcheck="false"/>
      <input type="submit" value="Go" />
    </form>
    </div>
</div>
<script>document.getElementById('searchbox').style.display = "block"</script>
        </div>
      </div>
      <div class="clearer"></div>
    </div>
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             >index</a></li>
        <li class="right" >
          <a href="define_property.html" title="define_property"
             >next</a> |</li>
        <li class="right" >
          <a href="cmake_file_api.html" title="cmake_file_api"
             >previous</a> |</li>
  <li>
    <img src="../_static/cmake-logo-16.png" alt=""
         style="vertical-align: middle; margin-top: -2px" />
  </li>
  <li>
    <a href="https://cmake.org/">CMake</a> &#187;
  </li>
  <li>
    <a href="../index.html">3.28.1 Documentation</a> &#187;
  </li>

          <li class="nav-item nav-item-1"><a href="../manual/cmake-commands.7.html" >cmake-commands(7)</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href="">create_test_sourcelist</a></li> 
      </ul>
    </div>

    <div class="footer" role="contentinfo">
        &#169; Copyright 2000-2023 Kitware, Inc. and Contributors.
      Created using <a href="https://www.sphinx-doc.org/">Sphinx</a> 6.2.1.
    </div>
  </body>
</html>