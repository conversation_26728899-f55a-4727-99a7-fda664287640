
<!DOCTYPE html>

<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="generator" content="Docutils 0.19: https://docutils.sourceforge.io/" />

  <title>CPack NSIS Generator &mdash; CMake 3.28.1 Documentation</title>

    <link rel="stylesheet" type="text/css" href="../_static/pygments.css" />
    <link rel="stylesheet" type="text/css" href="../_static/cmake.css" />
    
    <script data-url_root="../" id="documentation_options" src="../_static/documentation_options.js"></script>
    <script src="../_static/doctools.js"></script>
    <script src="../_static/sphinx_highlight.js"></script>
    
    <link rel="icon" href="../_static/cmake-favicon.ico"/>
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="next" title="CPack NuGet Generator" href="nuget.html" />
    <link rel="prev" title="CPack IFW Generator" href="ifw.html" />
 

  </head><body>

    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             accesskey="I">index</a></li>
        <li class="right" >
          <a href="nuget.html" title="CPack NuGet Generator"
             accesskey="N">next</a> |</li>
        <li class="right" >
          <a href="ifw.html" title="CPack IFW Generator"
             accesskey="P">previous</a> |</li>
  <li>
    <img src="../_static/cmake-logo-16.png" alt=""
         style="vertical-align: middle; margin-top: -2px" />
  </li>
  <li>
    <a href="https://cmake.org/">CMake</a> &#187;
  </li>
  <li>
    <a href="../index.html">3.28.1 Documentation</a> &#187;
  </li>

          <li class="nav-item nav-item-1"><a href="../manual/cpack-generators.7.html" accesskey="U">cpack-generators(7)</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href="">CPack NSIS Generator</a></li> 
      </ul>
    </div>  

    <div class="document">
      <div class="documentwrapper">
        <div class="bodywrapper">
          <div class="body" role="main">
            
  <section id="cpack-nsis-generator">
<span id="cpack_gen:CPack NSIS Generator"></span><h1>CPack NSIS Generator<a class="headerlink" href="#cpack-nsis-generator" title="Permalink to this heading">¶</a></h1>
<p>CPack Nullsoft Scriptable Install System (NSIS) generator specific options.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.22: </span>The NSIS generator requires NSIS 3.03 or newer.</p>
</div>
<section id="variables-specific-to-cpack-nsis-generator">
<h2>Variables specific to CPack NSIS generator<a class="headerlink" href="#variables-specific-to-cpack-nsis-generator" title="Permalink to this heading">¶</a></h2>
<p>The following variables are specific to the graphical installers built
on Windows Nullsoft Scriptable Install System.</p>
<dl class="cmake variable">
<dt class="sig sig-object cmake" id="variable:CPACK_NSIS_INSTALL_ROOT">
<span class="sig-name descname"><span class="pre">CPACK_NSIS_INSTALL_ROOT</span></span><a class="headerlink" href="#variable:CPACK_NSIS_INSTALL_ROOT" title="Permalink to this definition">¶</a></dt>
<dd><p>The default installation directory presented to the end user by the NSIS
installer is under this root dir. The full directory presented to the end
user is: <code class="docutils literal notranslate"><span class="pre">${CPACK_NSIS_INSTALL_ROOT}/${CPACK_PACKAGE_INSTALL_DIRECTORY}</span></code></p>
</dd></dl>

<dl class="cmake variable">
<dt class="sig sig-object cmake" id="variable:CPACK_NSIS_MUI_ICON">
<span class="sig-name descname"><span class="pre">CPACK_NSIS_MUI_ICON</span></span><a class="headerlink" href="#variable:CPACK_NSIS_MUI_ICON" title="Permalink to this definition">¶</a></dt>
<dd><p>An icon filename.  The name of a <code class="docutils literal notranslate"><span class="pre">*.ico</span></code> file used as the main icon for the
generated install program.</p>
</dd></dl>

<dl class="cmake variable">
<dt class="sig sig-object cmake" id="variable:CPACK_NSIS_MUI_UNIICON">
<span class="sig-name descname"><span class="pre">CPACK_NSIS_MUI_UNIICON</span></span><a class="headerlink" href="#variable:CPACK_NSIS_MUI_UNIICON" title="Permalink to this definition">¶</a></dt>
<dd><p>An icon filename.  The name of a <code class="docutils literal notranslate"><span class="pre">*.ico</span></code> file used as the main icon for the
generated uninstall program.</p>
</dd></dl>

<dl class="cmake variable">
<dt class="sig sig-object cmake" id="variable:CPACK_NSIS_INSTALLER_MUI_ICON_CODE">
<span class="sig-name descname"><span class="pre">CPACK_NSIS_INSTALLER_MUI_ICON_CODE</span></span><a class="headerlink" href="#variable:CPACK_NSIS_INSTALLER_MUI_ICON_CODE" title="Permalink to this definition">¶</a></dt>
<dd><p>undocumented.</p>
</dd></dl>

<dl class="cmake variable">
<dt class="sig sig-object cmake" id="variable:CPACK_NSIS_MUI_WELCOMEFINISHPAGE_BITMAP">
<span class="sig-name descname"><span class="pre">CPACK_NSIS_MUI_WELCOMEFINISHPAGE_BITMAP</span></span><a class="headerlink" href="#variable:CPACK_NSIS_MUI_WELCOMEFINISHPAGE_BITMAP" title="Permalink to this definition">¶</a></dt>
<dd><div class="versionadded">
<p><span class="versionmodified added">New in version 3.5.</span></p>
</div>
<p>The filename of a bitmap to use as the NSIS <code class="docutils literal notranslate"><span class="pre">MUI_WELCOMEFINISHPAGE_BITMAP</span></code>.</p>
</dd></dl>

<dl class="cmake variable">
<dt class="sig sig-object cmake" id="variable:CPACK_NSIS_MUI_UNWELCOMEFINISHPAGE_BITMAP">
<span class="sig-name descname"><span class="pre">CPACK_NSIS_MUI_UNWELCOMEFINISHPAGE_BITMAP</span></span><a class="headerlink" href="#variable:CPACK_NSIS_MUI_UNWELCOMEFINISHPAGE_BITMAP" title="Permalink to this definition">¶</a></dt>
<dd><div class="versionadded">
<p><span class="versionmodified added">New in version 3.5.</span></p>
</div>
<p>The filename of a bitmap to use as the NSIS <code class="docutils literal notranslate"><span class="pre">MUI_UNWELCOMEFINISHPAGE_BITMAP</span></code>.</p>
</dd></dl>

<dl class="cmake variable">
<dt class="sig sig-object cmake" id="variable:CPACK_NSIS_EXTRA_PREINSTALL_COMMANDS">
<span class="sig-name descname"><span class="pre">CPACK_NSIS_EXTRA_PREINSTALL_COMMANDS</span></span><a class="headerlink" href="#variable:CPACK_NSIS_EXTRA_PREINSTALL_COMMANDS" title="Permalink to this definition">¶</a></dt>
<dd><p>Extra NSIS commands that will be added to the beginning of the install
Section, before your install tree is available on the target system.</p>
</dd></dl>

<dl class="cmake variable">
<dt class="sig sig-object cmake" id="variable:CPACK_NSIS_EXTRA_INSTALL_COMMANDS">
<span class="sig-name descname"><span class="pre">CPACK_NSIS_EXTRA_INSTALL_COMMANDS</span></span><a class="headerlink" href="#variable:CPACK_NSIS_EXTRA_INSTALL_COMMANDS" title="Permalink to this definition">¶</a></dt>
<dd><p>Extra NSIS commands that will be added to the end of the install Section,
after your install tree is available on the target system.</p>
</dd></dl>

<dl class="cmake variable">
<dt class="sig sig-object cmake" id="variable:CPACK_NSIS_EXTRA_UNINSTALL_COMMANDS">
<span class="sig-name descname"><span class="pre">CPACK_NSIS_EXTRA_UNINSTALL_COMMANDS</span></span><a class="headerlink" href="#variable:CPACK_NSIS_EXTRA_UNINSTALL_COMMANDS" title="Permalink to this definition">¶</a></dt>
<dd><p>Extra NSIS commands that will be added to the uninstall Section, before
your install tree is removed from the target system.</p>
</dd></dl>

<dl class="cmake variable">
<dt class="sig sig-object cmake" id="variable:CPACK_NSIS_COMPRESSOR">
<span class="sig-name descname"><span class="pre">CPACK_NSIS_COMPRESSOR</span></span><a class="headerlink" href="#variable:CPACK_NSIS_COMPRESSOR" title="Permalink to this definition">¶</a></dt>
<dd><p>The arguments that will be passed to the NSIS <cite>SetCompressor</cite> command.</p>
</dd></dl>

<dl class="cmake variable">
<dt class="sig sig-object cmake" id="variable:CPACK_NSIS_ENABLE_UNINSTALL_BEFORE_INSTALL">
<span class="sig-name descname"><span class="pre">CPACK_NSIS_ENABLE_UNINSTALL_BEFORE_INSTALL</span></span><a class="headerlink" href="#variable:CPACK_NSIS_ENABLE_UNINSTALL_BEFORE_INSTALL" title="Permalink to this definition">¶</a></dt>
<dd><p>Ask about uninstalling previous versions first.  If this is set to <code class="docutils literal notranslate"><span class="pre">ON</span></code>,
then an installer will look for previous installed versions and if one is
found, ask the user whether to uninstall it before proceeding with the
install.</p>
</dd></dl>

<dl class="cmake variable">
<dt class="sig sig-object cmake" id="variable:CPACK_NSIS_MODIFY_PATH">
<span class="sig-name descname"><span class="pre">CPACK_NSIS_MODIFY_PATH</span></span><a class="headerlink" href="#variable:CPACK_NSIS_MODIFY_PATH" title="Permalink to this definition">¶</a></dt>
<dd><p>Modify <code class="docutils literal notranslate"><span class="pre">PATH</span></code> toggle.  If this is set to <code class="docutils literal notranslate"><span class="pre">ON</span></code>, then an extra page will appear
in the installer that will allow the user to choose whether the program
directory should be added to the system <code class="docutils literal notranslate"><span class="pre">PATH</span></code> variable.</p>
</dd></dl>

<dl class="cmake variable">
<dt class="sig sig-object cmake" id="variable:CPACK_NSIS_DISPLAY_NAME">
<span class="sig-name descname"><span class="pre">CPACK_NSIS_DISPLAY_NAME</span></span><a class="headerlink" href="#variable:CPACK_NSIS_DISPLAY_NAME" title="Permalink to this definition">¶</a></dt>
<dd><p>The display name string that appears in the Windows <cite>Apps &amp; features</cite>
in <cite>Control Panel</cite></p>
</dd></dl>

<dl class="cmake variable">
<dt class="sig sig-object cmake" id="variable:CPACK_NSIS_PACKAGE_NAME">
<span class="sig-name descname"><span class="pre">CPACK_NSIS_PACKAGE_NAME</span></span><a class="headerlink" href="#variable:CPACK_NSIS_PACKAGE_NAME" title="Permalink to this definition">¶</a></dt>
<dd><p>The title displayed at the top of the installer.</p>
</dd></dl>

<dl class="cmake variable">
<dt class="sig sig-object cmake" id="variable:CPACK_NSIS_INSTALLED_ICON_NAME">
<span class="sig-name descname"><span class="pre">CPACK_NSIS_INSTALLED_ICON_NAME</span></span><a class="headerlink" href="#variable:CPACK_NSIS_INSTALLED_ICON_NAME" title="Permalink to this definition">¶</a></dt>
<dd><p>A path to the executable that contains the installer icon.</p>
</dd></dl>

<dl class="cmake variable">
<dt class="sig sig-object cmake" id="variable:CPACK_NSIS_HELP_LINK">
<span class="sig-name descname"><span class="pre">CPACK_NSIS_HELP_LINK</span></span><a class="headerlink" href="#variable:CPACK_NSIS_HELP_LINK" title="Permalink to this definition">¶</a></dt>
<dd><p>URL to a web site providing assistance in installing your application.</p>
</dd></dl>

<dl class="cmake variable">
<dt class="sig sig-object cmake" id="variable:CPACK_NSIS_URL_INFO_ABOUT">
<span class="sig-name descname"><span class="pre">CPACK_NSIS_URL_INFO_ABOUT</span></span><a class="headerlink" href="#variable:CPACK_NSIS_URL_INFO_ABOUT" title="Permalink to this definition">¶</a></dt>
<dd><p>URL to a web site providing more information about your application.</p>
</dd></dl>

<dl class="cmake variable">
<dt class="sig sig-object cmake" id="variable:CPACK_NSIS_CONTACT">
<span class="sig-name descname"><span class="pre">CPACK_NSIS_CONTACT</span></span><a class="headerlink" href="#variable:CPACK_NSIS_CONTACT" title="Permalink to this definition">¶</a></dt>
<dd><p>Contact information for questions and comments about the installation
process.</p>
</dd></dl>

<dl class="cmake variable">
<dt class="sig sig-object cmake" id="variable:CPACK_NSIS_&lt;compName&gt;_INSTALL_DIRECTORY">
<span class="sig-name descname"><span class="pre">CPACK_NSIS_&lt;compName&gt;_INSTALL_DIRECTORY</span></span><a class="headerlink" href="#variable:CPACK_NSIS_<compName>_INSTALL_DIRECTORY" title="Permalink to this definition">¶</a></dt>
<dd><div class="versionadded">
<p><span class="versionmodified added">New in version 3.7.</span></p>
</div>
<p>Custom install directory for the specified component <code class="docutils literal notranslate"><span class="pre">&lt;compName&gt;</span></code> instead
of <code class="docutils literal notranslate"><span class="pre">$INSTDIR</span></code>.</p>
</dd></dl>

<dl class="cmake variable">
<dt class="sig sig-object cmake" id="variable:CPACK_NSIS_CREATE_ICONS_EXTRA">
<span class="sig-name descname"><span class="pre">CPACK_NSIS_CREATE_ICONS_EXTRA</span></span><a class="headerlink" href="#variable:CPACK_NSIS_CREATE_ICONS_EXTRA" title="Permalink to this definition">¶</a></dt>
<dd><p>Additional NSIS commands for creating <cite>Start Menu</cite> shortcuts.</p>
</dd></dl>

<dl class="cmake variable">
<dt class="sig sig-object cmake" id="variable:CPACK_NSIS_DELETE_ICONS_EXTRA">
<span class="sig-name descname"><span class="pre">CPACK_NSIS_DELETE_ICONS_EXTRA</span></span><a class="headerlink" href="#variable:CPACK_NSIS_DELETE_ICONS_EXTRA" title="Permalink to this definition">¶</a></dt>
<dd><p>Additional NSIS commands to uninstall <cite>Start Menu</cite> shortcuts.</p>
</dd></dl>

<dl class="cmake variable">
<dt class="sig sig-object cmake" id="variable:CPACK_NSIS_EXECUTABLES_DIRECTORY">
<span class="sig-name descname"><span class="pre">CPACK_NSIS_EXECUTABLES_DIRECTORY</span></span><a class="headerlink" href="#variable:CPACK_NSIS_EXECUTABLES_DIRECTORY" title="Permalink to this definition">¶</a></dt>
<dd><p>Creating NSIS <cite>Start Menu</cite> links assumes that they are in <code class="docutils literal notranslate"><span class="pre">bin</span></code> unless this
variable is set.  For example, you would set this to <code class="docutils literal notranslate"><span class="pre">exec</span></code> if your
executables are in an exec directory.</p>
</dd></dl>

<dl class="cmake variable">
<dt class="sig sig-object cmake" id="variable:CPACK_NSIS_MUI_FINISHPAGE_RUN">
<span class="sig-name descname"><span class="pre">CPACK_NSIS_MUI_FINISHPAGE_RUN</span></span><a class="headerlink" href="#variable:CPACK_NSIS_MUI_FINISHPAGE_RUN" title="Permalink to this definition">¶</a></dt>
<dd><p>Specify an executable to add an option to run on the finish page of the
NSIS installer.</p>
</dd></dl>

<dl class="cmake variable">
<dt class="sig sig-object cmake" id="variable:CPACK_NSIS_MENU_LINKS">
<span class="sig-name descname"><span class="pre">CPACK_NSIS_MENU_LINKS</span></span><a class="headerlink" href="#variable:CPACK_NSIS_MENU_LINKS" title="Permalink to this definition">¶</a></dt>
<dd><p>Specify links in <code class="docutils literal notranslate"><span class="pre">[application]</span></code> menu.  This should contain a list of pair
<code class="docutils literal notranslate"><span class="pre">link</span></code> <code class="docutils literal notranslate"><span class="pre">link</span> <span class="pre">name</span></code>. The link may be a URL or a path relative to
installation prefix.  Like:</p>
<div class="highlight-none notranslate"><div class="highlight"><pre><span></span>set(CPACK_NSIS_MENU_LINKS
    &quot;doc/cmake-@CMake_VERSION_MAJOR@.@CMake_VERSION_MINOR@/cmake.html&quot;
    &quot;CMake Help&quot; &quot;https://cmake.org&quot; &quot;CMake Web Site&quot;)
</pre></div>
</div>
</dd></dl>

<dl class="cmake variable">
<dt class="sig sig-object cmake" id="variable:CPACK_NSIS_UNINSTALL_NAME">
<span class="sig-name descname"><span class="pre">CPACK_NSIS_UNINSTALL_NAME</span></span><a class="headerlink" href="#variable:CPACK_NSIS_UNINSTALL_NAME" title="Permalink to this definition">¶</a></dt>
<dd><div class="versionadded">
<p><span class="versionmodified added">New in version 3.17.</span></p>
</div>
<p>Specify the name of the program to uninstall the version.
Default is <code class="docutils literal notranslate"><span class="pre">Uninstall</span></code>.</p>
</dd></dl>

<dl class="cmake variable">
<dt class="sig sig-object cmake" id="variable:CPACK_NSIS_WELCOME_TITLE">
<span class="sig-name descname"><span class="pre">CPACK_NSIS_WELCOME_TITLE</span></span><a class="headerlink" href="#variable:CPACK_NSIS_WELCOME_TITLE" title="Permalink to this definition">¶</a></dt>
<dd><div class="versionadded">
<p><span class="versionmodified added">New in version 3.17.</span></p>
</div>
<p>The title to display on the top of the page for the welcome page.</p>
</dd></dl>

<dl class="cmake variable">
<dt class="sig sig-object cmake" id="variable:CPACK_NSIS_WELCOME_TITLE_3LINES">
<span class="sig-name descname"><span class="pre">CPACK_NSIS_WELCOME_TITLE_3LINES</span></span><a class="headerlink" href="#variable:CPACK_NSIS_WELCOME_TITLE_3LINES" title="Permalink to this definition">¶</a></dt>
<dd><div class="versionadded">
<p><span class="versionmodified added">New in version 3.17.</span></p>
</div>
<p>Display the title in the welcome page on 3 lines instead of 2.</p>
</dd></dl>

<dl class="cmake variable">
<dt class="sig sig-object cmake" id="variable:CPACK_NSIS_FINISH_TITLE">
<span class="sig-name descname"><span class="pre">CPACK_NSIS_FINISH_TITLE</span></span><a class="headerlink" href="#variable:CPACK_NSIS_FINISH_TITLE" title="Permalink to this definition">¶</a></dt>
<dd><div class="versionadded">
<p><span class="versionmodified added">New in version 3.17.</span></p>
</div>
<p>The title to display on the top of the page for the finish page.</p>
</dd></dl>

<dl class="cmake variable">
<dt class="sig sig-object cmake" id="variable:CPACK_NSIS_FINISH_TITLE_3LINES">
<span class="sig-name descname"><span class="pre">CPACK_NSIS_FINISH_TITLE_3LINES</span></span><a class="headerlink" href="#variable:CPACK_NSIS_FINISH_TITLE_3LINES" title="Permalink to this definition">¶</a></dt>
<dd><div class="versionadded">
<p><span class="versionmodified added">New in version 3.17.</span></p>
</div>
<p>Display the title in the finish page on 3 lines instead of 2.</p>
</dd></dl>

<dl class="cmake variable">
<dt class="sig sig-object cmake" id="variable:CPACK_NSIS_MUI_HEADERIMAGE">
<span class="sig-name descname"><span class="pre">CPACK_NSIS_MUI_HEADERIMAGE</span></span><a class="headerlink" href="#variable:CPACK_NSIS_MUI_HEADERIMAGE" title="Permalink to this definition">¶</a></dt>
<dd><div class="versionadded">
<p><span class="versionmodified added">New in version 3.17.</span></p>
</div>
<p>The image to display on the header of installers pages.</p>
</dd></dl>

<dl class="cmake variable">
<dt class="sig sig-object cmake" id="variable:CPACK_NSIS_MANIFEST_DPI_AWARE">
<span class="sig-name descname"><span class="pre">CPACK_NSIS_MANIFEST_DPI_AWARE</span></span><a class="headerlink" href="#variable:CPACK_NSIS_MANIFEST_DPI_AWARE" title="Permalink to this definition">¶</a></dt>
<dd><div class="versionadded">
<p><span class="versionmodified added">New in version 3.18.</span></p>
</div>
<p>If set, declares that the installer is DPI-aware.</p>
</dd></dl>

<dl class="cmake variable">
<dt class="sig sig-object cmake" id="variable:CPACK_NSIS_BRANDING_TEXT">
<span class="sig-name descname"><span class="pre">CPACK_NSIS_BRANDING_TEXT</span></span><a class="headerlink" href="#variable:CPACK_NSIS_BRANDING_TEXT" title="Permalink to this definition">¶</a></dt>
<dd><div class="versionadded">
<p><span class="versionmodified added">New in version 3.20.</span></p>
</div>
<p>If set, updates the text at the bottom of the install window.
To set the string to blank, use a space (&quot; &quot;).</p>
</dd></dl>

<dl class="cmake variable">
<dt class="sig sig-object cmake" id="variable:CPACK_NSIS_BRANDING_TEXT_TRIM_POSITION">
<span class="sig-name descname"><span class="pre">CPACK_NSIS_BRANDING_TEXT_TRIM_POSITION</span></span><a class="headerlink" href="#variable:CPACK_NSIS_BRANDING_TEXT_TRIM_POSITION" title="Permalink to this definition">¶</a></dt>
<dd><div class="versionadded">
<p><span class="versionmodified added">New in version 3.20.</span></p>
</div>
<p>If set, trim down the size of the control to the size of the branding text string.
Allowed values for this variable are <code class="docutils literal notranslate"><span class="pre">LEFT</span></code>, <code class="docutils literal notranslate"><span class="pre">CENTER</span></code> or <code class="docutils literal notranslate"><span class="pre">RIGHT</span></code>.
If not specified, the default behavior is <code class="docutils literal notranslate"><span class="pre">LEFT</span></code>.</p>
</dd></dl>

<dl class="cmake variable">
<dt class="sig sig-object cmake" id="variable:CPACK_NSIS_EXECUTABLE">
<span class="sig-name descname"><span class="pre">CPACK_NSIS_EXECUTABLE</span></span><a class="headerlink" href="#variable:CPACK_NSIS_EXECUTABLE" title="Permalink to this definition">¶</a></dt>
<dd><div class="versionadded">
<p><span class="versionmodified added">New in version 3.21.</span></p>
</div>
<p>If set, specify the name of the NSIS executable. Default is <code class="docutils literal notranslate"><span class="pre">makensis</span></code>.</p>
</dd></dl>

<dl class="cmake variable">
<dt class="sig sig-object cmake" id="variable:CPACK_NSIS_IGNORE_LICENSE_PAGE">
<span class="sig-name descname"><span class="pre">CPACK_NSIS_IGNORE_LICENSE_PAGE</span></span><a class="headerlink" href="#variable:CPACK_NSIS_IGNORE_LICENSE_PAGE" title="Permalink to this definition">¶</a></dt>
<dd><div class="versionadded">
<p><span class="versionmodified added">New in version 3.22.</span></p>
</div>
<p>If set, do not display the page containing the license during installation.</p>
</dd></dl>

<dl class="cmake variable">
<dt class="sig sig-object cmake" id="variable:CPACK_NSIS_EXECUTABLE_PRE_ARGUMENTS">
<span class="sig-name descname"><span class="pre">CPACK_NSIS_EXECUTABLE_PRE_ARGUMENTS</span></span><a class="headerlink" href="#variable:CPACK_NSIS_EXECUTABLE_PRE_ARGUMENTS" title="Permalink to this definition">¶</a></dt>
<dd><div class="versionadded">
<p><span class="versionmodified added">New in version 3.25.</span></p>
</div>
<p>This variable is a <a class="reference internal" href="../manual/cmake-language.7.html#cmake-language-lists"><span class="std std-ref">semicolon-separated list</span></a> of
arguments to prepend to the nsis script to run.
If the arguments do not start with a <code class="docutils literal notranslate"><span class="pre">/</span></code> or a <code class="docutils literal notranslate"><span class="pre">-</span></code>, it will add one
automatically to the corresponding arguments.
The command that will be run is:</p>
<div class="highlight-none notranslate"><div class="highlight"><pre><span></span>makensis.exe &lt;preArgs&gt;... &quot;nsisFileName.nsi&quot; &lt;postArgs&gt;...
</pre></div>
</div>
<p>where <code class="docutils literal notranslate"><span class="pre">&lt;preArgs&gt;...</span></code> is constructed from <code class="docutils literal notranslate"><span class="pre">CPACK_NSIS_EXECUTABLE_PRE_ARGUMENTS</span></code>
and <code class="docutils literal notranslate"><span class="pre">&lt;postArgs&gt;...</span></code>  is constructed from <code class="docutils literal notranslate"><span class="pre">CPACK_NSIS_EXECUTABLE_POST_ARGUMENTS</span></code>.</p>
</dd></dl>

<dl class="cmake variable">
<dt class="sig sig-object cmake" id="variable:CPACK_NSIS_EXECUTABLE_POST_ARGUMENTS">
<span class="sig-name descname"><span class="pre">CPACK_NSIS_EXECUTABLE_POST_ARGUMENTS</span></span><a class="headerlink" href="#variable:CPACK_NSIS_EXECUTABLE_POST_ARGUMENTS" title="Permalink to this definition">¶</a></dt>
<dd><div class="versionadded">
<p><span class="versionmodified added">New in version 3.25.</span></p>
</div>
<p>This variable is a <a class="reference internal" href="../manual/cmake-language.7.html#cmake-language-lists"><span class="std std-ref">semicolon-separated list</span></a> of
arguments to append to the nsis script to run.
If the arguments do not start with a <code class="docutils literal notranslate"><span class="pre">/</span></code> or a <code class="docutils literal notranslate"><span class="pre">-</span></code>, it will add one
automatically to the corresponding arguments.
The command that will be run is:</p>
<div class="highlight-none notranslate"><div class="highlight"><pre><span></span>makensis.exe &lt;preArgs&gt;... &quot;nsisFileName.nsi&quot; &lt;postArgs&gt;...
</pre></div>
</div>
<p>where <code class="docutils literal notranslate"><span class="pre">&lt;preArgs&gt;...</span></code> is constructed from <code class="docutils literal notranslate"><span class="pre">CPACK_NSIS_EXECUTABLE_PRE_ARGUMENTS</span></code>
and <code class="docutils literal notranslate"><span class="pre">&lt;postArgs&gt;...</span></code>  is constructed from <code class="docutils literal notranslate"><span class="pre">CPACK_NSIS_EXECUTABLE_POST_ARGUMENTS</span></code>.</p>
</dd></dl>

</section>
</section>


            <div class="clearer"></div>
          </div>
        </div>
      </div>
      <div class="sphinxsidebar" role="navigation" aria-label="main navigation">
        <div class="sphinxsidebarwrapper">
  <div>
    <h3><a href="../index.html">Table of Contents</a></h3>
    <ul>
<li><a class="reference internal" href="#">CPack NSIS Generator</a><ul>
<li><a class="reference internal" href="#variables-specific-to-cpack-nsis-generator">Variables specific to CPack NSIS generator</a></li>
</ul>
</li>
</ul>

  </div>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="ifw.html"
                          title="previous chapter">CPack IFW Generator</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="nuget.html"
                          title="next chapter">CPack NuGet Generator</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../_sources/cpack_gen/nsis.rst.txt"
            rel="nofollow">Show Source</a></li>
    </ul>
   </div>
<div id="searchbox" style="display: none" role="search">
  <h3 id="searchlabel">Quick search</h3>
    <div class="searchformwrapper">
    <form class="search" action="../search.html" method="get">
      <input type="text" name="q" aria-labelledby="searchlabel" autocomplete="off" autocorrect="off" autocapitalize="off" spellcheck="false"/>
      <input type="submit" value="Go" />
    </form>
    </div>
</div>
<script>document.getElementById('searchbox').style.display = "block"</script>
        </div>
      </div>
      <div class="clearer"></div>
    </div>
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             >index</a></li>
        <li class="right" >
          <a href="nuget.html" title="CPack NuGet Generator"
             >next</a> |</li>
        <li class="right" >
          <a href="ifw.html" title="CPack IFW Generator"
             >previous</a> |</li>
  <li>
    <img src="../_static/cmake-logo-16.png" alt=""
         style="vertical-align: middle; margin-top: -2px" />
  </li>
  <li>
    <a href="https://cmake.org/">CMake</a> &#187;
  </li>
  <li>
    <a href="../index.html">3.28.1 Documentation</a> &#187;
  </li>

          <li class="nav-item nav-item-1"><a href="../manual/cpack-generators.7.html" >cpack-generators(7)</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href="">CPack NSIS Generator</a></li> 
      </ul>
    </div>

    <div class="footer" role="contentinfo">
        &#169; Copyright 2000-2023 Kitware, Inc. and Contributors.
      Created using <a href="https://www.sphinx-doc.org/">Sphinx</a> 6.2.1.
    </div>
  </body>
</html>