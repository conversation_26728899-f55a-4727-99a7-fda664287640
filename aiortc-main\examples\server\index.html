<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8"/>
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>WebRTC demo</title>
    <style>
    button {
        padding: 8px 16px;
    }

    pre {
        overflow-x: hidden;
        overflow-y: auto;
    }

    video {
        width: 100%;
    }

    .option {
        margin-bottom: 8px;
    }

    #media {
        max-width: 1280px;
    }
    </style>
</head>
<body>

<h2>Options</h2>
<div class="option">
    <input id="use-datachannel" checked="checked" type="checkbox"/>
    <label for="use-datachannel">Use datachannel</label>
    <select id="datachannel-parameters">
        <option value='{"ordered": true}'>Ordered, reliable</option>
        <option value='{"ordered": false, "maxRetransmits": 0}'>Unordered, no retransmissions</option>
        <option value='{"ordered": false, "maxPacketLifetime": 500}'>Unordered, 500ms lifetime</option>
    </select>
</div>
<div class="option">
    <input id="use-audio" checked="checked" type="checkbox"/>
    <label for="use-audio">Use audio</label>
    <select id="audio-input">
        <option value="" selected>Default device</option>
    </select>
    <select id="audio-codec">
        <option value="default" selected>Default codecs</option>
        <option value="opus/48000/2">Opus</option>
        <option value="G722/8000">G722</option>
        <option value="PCMU/8000">PCMU</option>
        <option value="PCMA/8000">PCMA</option>
    </select>
</div>
<div class="option">
    <input id="use-video" type="checkbox"/>
    <label for="use-video">Use video</label>
    <select id="video-input">
        <option value="" selected>Default device</option>
    </select>
    <select id="video-resolution">
        <option value="" selected>Default resolution</option>
        <option value="320x240">320x240</option>
        <option value="640x480">640x480</option>
        <option value="960x540">960x540</option>
        <option value="1280x720">1280x720</option>
    </select>
    <select id="video-transform">
        <option value="none" selected>No transform</option>
        <option value="edges">Edge detection</option>
        <option value="cartoon">Cartoon effect</option>
        <option value="rotate">Rotate</option>
    </select>
    <select id="video-codec">
        <option value="default" selected>Default codecs</option>
        <option value="VP8/90000">VP8</option>
        <option value="H264/90000">H264</option>
    </select>
</div>
<div class="option">
    <input id="use-stun" type="checkbox"/>
    <label for="use-stun">Use STUN server</label>
</div>

<button id="start" onclick="start()">Start</button>
<button id="stop" style="display: none" onclick="stop()">Stop</button>

<h2>State</h2>
<p>
    ICE gathering state: <span id="ice-gathering-state"></span>
</p>
<p>
    ICE connection state: <span id="ice-connection-state"></span>
</p>
<p>
    Signaling state: <span id="signaling-state"></span>
</p>

<div id="media" style="display: none">
    <h2>Media</h2>

    <audio id="audio" autoplay="true"></audio>
    <video id="video" autoplay="true" playsinline="true"></video>
</div>

<h2>Data channel</h2>
<pre id="data-channel" style="height: 200px;"></pre>

<h2>SDP</h2>

<h3>Offer</h3>
<pre id="offer-sdp"></pre>

<h3>Answer</h3>
<pre id="answer-sdp"></pre>

<script src="client.js"></script>
</body>
</html>
