
<!DOCTYPE html>

<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="generator" content="Docutils 0.19: https://docutils.sourceforge.io/" />

  <title>include_guard &mdash; CMake 3.28.1 Documentation</title>

    <link rel="stylesheet" type="text/css" href="../_static/pygments.css" />
    <link rel="stylesheet" type="text/css" href="../_static/cmake.css" />
    
    <script data-url_root="../" id="documentation_options" src="../_static/documentation_options.js"></script>
    <script src="../_static/doctools.js"></script>
    <script src="../_static/sphinx_highlight.js"></script>
    
    <link rel="icon" href="../_static/cmake-favicon.ico"/>
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="next" title="list" href="list.html" />
    <link rel="prev" title="include" href="include.html" />
 

  </head><body>

    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             accesskey="I">index</a></li>
        <li class="right" >
          <a href="list.html" title="list"
             accesskey="N">next</a> |</li>
        <li class="right" >
          <a href="include.html" title="include"
             accesskey="P">previous</a> |</li>
  <li>
    <img src="../_static/cmake-logo-16.png" alt=""
         style="vertical-align: middle; margin-top: -2px" />
  </li>
  <li>
    <a href="https://cmake.org/">CMake</a> &#187;
  </li>
  <li>
    <a href="../index.html">3.28.1 Documentation</a> &#187;
  </li>

          <li class="nav-item nav-item-1"><a href="../manual/cmake-commands.7.html" accesskey="U">cmake-commands(7)</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href="">include_guard</a></li> 
      </ul>
    </div>  

    <div class="document">
      <div class="documentwrapper">
        <div class="bodywrapper">
          <div class="body" role="main">
            
  <section id="include-guard">
<span id="command:include_guard"></span><h1>include_guard<a class="headerlink" href="#include-guard" title="Permalink to this heading">¶</a></h1>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.10.</span></p>
</div>
<p>Provides an include guard for the file currently being processed by CMake.</p>
<div class="highlight-cmake notranslate"><div class="highlight"><pre><span></span><span class="nf">include_guard(</span><span class="p">[</span><span class="no">DIRECTORY</span><span class="p">|</span><span class="no">GLOBAL</span><span class="p">]</span><span class="nf">)</span>
</pre></div>
</div>
<p>Sets up an include guard for the current CMake file (see the
<span class="target" id="index-0-variable:CMAKE_CURRENT_LIST_FILE"></span><a class="reference internal" href="../variable/CMAKE_CURRENT_LIST_FILE.html#variable:CMAKE_CURRENT_LIST_FILE" title="CMAKE_CURRENT_LIST_FILE"><code class="xref cmake cmake-variable docutils literal notranslate"><span class="pre">CMAKE_CURRENT_LIST_FILE</span></code></a> variable documentation).</p>
<p>CMake will end its processing of the current file at the location of the
<code class="docutils literal notranslate"><span class="pre">include_guard</span></code> command if the current file has already been
processed for the applicable scope (see below). This provides functionality
similar to the include guards commonly used in source headers or to the
<code class="docutils literal notranslate"><span class="pre">#pragma</span> <span class="pre">once</span></code> directive. If the current file has been processed previously
for the applicable scope, the effect is as though <span class="target" id="index-0-command:return"></span><a class="reference internal" href="return.html#command:return" title="return"><code class="xref cmake cmake-command docutils literal notranslate"><span class="pre">return()</span></code></a> had been
called. Do not call this command from inside a function being defined within
the current file.</p>
<p>An optional argument specifying the scope of the guard may be provided.
Possible values for the option are:</p>
<dl class="simple">
<dt><code class="docutils literal notranslate"><span class="pre">DIRECTORY</span></code></dt><dd><p>The include guard applies within the current directory and below. The file
will only be included once within this directory scope, but may be included
again by other files outside of this directory (i.e. a parent directory or
another directory not pulled in by <span class="target" id="index-0-command:add_subdirectory"></span><a class="reference internal" href="add_subdirectory.html#command:add_subdirectory" title="add_subdirectory"><code class="xref cmake cmake-command docutils literal notranslate"><span class="pre">add_subdirectory()</span></code></a> or
<span class="target" id="index-0-command:include"></span><a class="reference internal" href="include.html#command:include" title="include"><code class="xref cmake cmake-command docutils literal notranslate"><span class="pre">include()</span></code></a> from the current file or its children).</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">GLOBAL</span></code></dt><dd><p>The include guard applies globally to the whole build. The current file
will only be included once regardless of the scope.</p>
</dd>
</dl>
<p>If no arguments given, <code class="docutils literal notranslate"><span class="pre">include_guard</span></code> has the same scope as a variable,
meaning that the include guard effect is isolated by the most recent
function scope or current directory if no inner function scopes exist.
In this case the command behavior is the same as:</p>
<div class="highlight-cmake notranslate"><div class="highlight"><pre><span></span><span class="nf">if(</span><span class="nb">__CURRENT_FILE_VAR__</span><span class="nf">)</span>
<span class="w">  </span><span class="nf">return()</span>
<span class="nf">endif()</span>
<span class="nf">set(</span><span class="nb">__CURRENT_FILE_VAR__</span><span class="w"> </span><span class="no">TRUE</span><span class="nf">)</span>
</pre></div>
</div>
</section>


            <div class="clearer"></div>
          </div>
        </div>
      </div>
      <div class="sphinxsidebar" role="navigation" aria-label="main navigation">
        <div class="sphinxsidebarwrapper">
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="include.html"
                          title="previous chapter">include</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="list.html"
                          title="next chapter">list</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../_sources/command/include_guard.rst.txt"
            rel="nofollow">Show Source</a></li>
    </ul>
   </div>
<div id="searchbox" style="display: none" role="search">
  <h3 id="searchlabel">Quick search</h3>
    <div class="searchformwrapper">
    <form class="search" action="../search.html" method="get">
      <input type="text" name="q" aria-labelledby="searchlabel" autocomplete="off" autocorrect="off" autocapitalize="off" spellcheck="false"/>
      <input type="submit" value="Go" />
    </form>
    </div>
</div>
<script>document.getElementById('searchbox').style.display = "block"</script>
        </div>
      </div>
      <div class="clearer"></div>
    </div>
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             >index</a></li>
        <li class="right" >
          <a href="list.html" title="list"
             >next</a> |</li>
        <li class="right" >
          <a href="include.html" title="include"
             >previous</a> |</li>
  <li>
    <img src="../_static/cmake-logo-16.png" alt=""
         style="vertical-align: middle; margin-top: -2px" />
  </li>
  <li>
    <a href="https://cmake.org/">CMake</a> &#187;
  </li>
  <li>
    <a href="../index.html">3.28.1 Documentation</a> &#187;
  </li>

          <li class="nav-item nav-item-1"><a href="../manual/cmake-commands.7.html" >cmake-commands(7)</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href="">include_guard</a></li> 
      </ul>
    </div>

    <div class="footer" role="contentinfo">
        &#169; Copyright 2000-2023 Kitware, Inc. and Contributors.
      Created using <a href="https://www.sphinx-doc.org/">Sphinx</a> 6.2.1.
    </div>
  </body>
</html>