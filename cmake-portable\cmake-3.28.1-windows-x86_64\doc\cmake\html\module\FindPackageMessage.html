
<!DOCTYPE html>

<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="generator" content="Docutils 0.19: https://docutils.sourceforge.io/" />

  <title>FindPackageMessage &mdash; CMake 3.28.1 Documentation</title>

    <link rel="stylesheet" type="text/css" href="../_static/pygments.css" />
    <link rel="stylesheet" type="text/css" href="../_static/cmake.css" />
    
    <script data-url_root="../" id="documentation_options" src="../_static/documentation_options.js"></script>
    <script src="../_static/doctools.js"></script>
    <script src="../_static/sphinx_highlight.js"></script>
    
    <link rel="icon" href="../_static/cmake-favicon.ico"/>
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="next" title="FortranCInterface" href="FortranCInterface.html" />
    <link rel="prev" title="FindPackageHandleStandardArgs" href="FindPackageHandleStandardArgs.html" />
 

  </head><body>

    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             accesskey="I">index</a></li>
        <li class="right" >
          <a href="FortranCInterface.html" title="FortranCInterface"
             accesskey="N">next</a> |</li>
        <li class="right" >
          <a href="FindPackageHandleStandardArgs.html" title="FindPackageHandleStandardArgs"
             accesskey="P">previous</a> |</li>
  <li>
    <img src="../_static/cmake-logo-16.png" alt=""
         style="vertical-align: middle; margin-top: -2px" />
  </li>
  <li>
    <a href="https://cmake.org/">CMake</a> &#187;
  </li>
  <li>
    <a href="../index.html">3.28.1 Documentation</a> &#187;
  </li>

          <li class="nav-item nav-item-1"><a href="../manual/cmake-modules.7.html" accesskey="U">cmake-modules(7)</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href="">FindPackageMessage</a></li> 
      </ul>
    </div>  

    <div class="document">
      <div class="documentwrapper">
        <div class="bodywrapper">
          <div class="body" role="main">
            
  <section id="findpackagemessage">
<span id="module:FindPackageMessage"></span><h1>FindPackageMessage<a class="headerlink" href="#findpackagemessage" title="Permalink to this heading">¶</a></h1>
<div class="highlight-cmake notranslate"><div class="highlight"><pre><span></span><span class="nf">find_package_message(</span><span class="nv">&lt;name&gt;</span><span class="w"> </span><span class="s">&quot;message for user&quot;</span><span class="w"> </span><span class="s">&quot;find result details&quot;</span><span class="nf">)</span>
</pre></div>
</div>
<p>This function is intended to be used in FindXXX.cmake modules files.
It will print a message once for each unique find result.  This is
useful for telling the user where a package was found.  The first
argument specifies the name (XXX) of the package.  The second argument
specifies the message to display.  The third argument lists details
about the find result so that if they change the message will be
displayed again.  The macro also obeys the QUIET argument to the
find_package command.</p>
<p>Example:</p>
<div class="highlight-cmake notranslate"><div class="highlight"><pre><span></span><span class="nf">if(</span><span class="no">X11_FOUND</span><span class="nf">)</span>
<span class="w">  </span><span class="nf">find_package_message(</span><span class="no">X11</span><span class="w"> </span><span class="s">&quot;Found X11: ${X11_X11_LIB}&quot;</span>
<span class="w">    </span><span class="s">&quot;[${X11_X11_LIB}][${X11_INCLUDE_DIR}]&quot;</span><span class="nf">)</span>
<span class="nf">else()</span>
<span class="w"> </span><span class="p">...</span>
<span class="nf">endif()</span>
</pre></div>
</div>
</section>


            <div class="clearer"></div>
          </div>
        </div>
      </div>
      <div class="sphinxsidebar" role="navigation" aria-label="main navigation">
        <div class="sphinxsidebarwrapper">
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="FindPackageHandleStandardArgs.html"
                          title="previous chapter">FindPackageHandleStandardArgs</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="FortranCInterface.html"
                          title="next chapter">FortranCInterface</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../_sources/module/FindPackageMessage.rst.txt"
            rel="nofollow">Show Source</a></li>
    </ul>
   </div>
<div id="searchbox" style="display: none" role="search">
  <h3 id="searchlabel">Quick search</h3>
    <div class="searchformwrapper">
    <form class="search" action="../search.html" method="get">
      <input type="text" name="q" aria-labelledby="searchlabel" autocomplete="off" autocorrect="off" autocapitalize="off" spellcheck="false"/>
      <input type="submit" value="Go" />
    </form>
    </div>
</div>
<script>document.getElementById('searchbox').style.display = "block"</script>
        </div>
      </div>
      <div class="clearer"></div>
    </div>
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             >index</a></li>
        <li class="right" >
          <a href="FortranCInterface.html" title="FortranCInterface"
             >next</a> |</li>
        <li class="right" >
          <a href="FindPackageHandleStandardArgs.html" title="FindPackageHandleStandardArgs"
             >previous</a> |</li>
  <li>
    <img src="../_static/cmake-logo-16.png" alt=""
         style="vertical-align: middle; margin-top: -2px" />
  </li>
  <li>
    <a href="https://cmake.org/">CMake</a> &#187;
  </li>
  <li>
    <a href="../index.html">3.28.1 Documentation</a> &#187;
  </li>

          <li class="nav-item nav-item-1"><a href="../manual/cmake-modules.7.html" >cmake-modules(7)</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href="">FindPackageMessage</a></li> 
      </ul>
    </div>

    <div class="footer" role="contentinfo">
        &#169; Copyright 2000-2023 Kitware, Inc. and Contributors.
      Created using <a href="https://www.sphinx-doc.org/">Sphinx</a> 6.2.1.
    </div>
  </body>
</html>