
<!DOCTYPE html>

<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="generator" content="Docutils 0.19: https://docutils.sourceforge.io/" />

  <title>Eclipse CDT4 &mdash; CMake 3.28.1 Documentation</title>

    <link rel="stylesheet" type="text/css" href="../_static/pygments.css" />
    <link rel="stylesheet" type="text/css" href="../_static/cmake.css" />
    
    <script data-url_root="../" id="documentation_options" src="../_static/documentation_options.js"></script>
    <script src="../_static/doctools.js"></script>
    <script src="../_static/sphinx_highlight.js"></script>
    
    <link rel="icon" href="../_static/cmake-favicon.ico"/>
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="next" title="Kate" href="Kate.html" />
    <link rel="prev" title="CodeLite" href="CodeLite.html" />
 

  </head><body>

    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             accesskey="I">index</a></li>
        <li class="right" >
          <a href="Kate.html" title="Kate"
             accesskey="N">next</a> |</li>
        <li class="right" >
          <a href="CodeLite.html" title="CodeLite"
             accesskey="P">previous</a> |</li>
  <li>
    <img src="../_static/cmake-logo-16.png" alt=""
         style="vertical-align: middle; margin-top: -2px" />
  </li>
  <li>
    <a href="https://cmake.org/">CMake</a> &#187;
  </li>
  <li>
    <a href="../index.html">3.28.1 Documentation</a> &#187;
  </li>

          <li class="nav-item nav-item-1"><a href="../manual/cmake-generators.7.html" accesskey="U">cmake-generators(7)</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href="">Eclipse CDT4</a></li> 
      </ul>
    </div>  

    <div class="document">
      <div class="documentwrapper">
        <div class="bodywrapper">
          <div class="body" role="main">
            
  <section id="eclipse-cdt4">
<span id="generator:Eclipse CDT4"></span><h1>Eclipse CDT4<a class="headerlink" href="#eclipse-cdt4" title="Permalink to this heading">¶</a></h1>
<div class="deprecated">
<p><span class="versionmodified deprecated">Deprecated since version 3.27: </span>Support for <a class="reference internal" href="../manual/cmake-generators.7.html#extra-generators"><span class="std std-ref">Extra Generators</span></a> is deprecated and will be removed from
a future version of CMake.  IDEs may use the <span class="target" id="index-0-manual:cmake-file-api(7)"></span><a class="reference internal" href="../manual/cmake-file-api.7.html#manual:cmake-file-api(7)" title="cmake-file-api(7)"><code class="xref cmake cmake-manual docutils literal notranslate"><span class="pre">cmake-file-api(7)</span></code></a>
to view CMake-generated project build trees.</p>
</div>
<p>Generates Eclipse CDT 4.0 project files.</p>
<p>Project files for Eclipse will be created in the top directory.  In
out of source builds, a linked resource to the top level source
directory will be created.  Additionally a hierarchy of makefiles is
generated into the build tree.  The appropriate make program can build
the project through the default <code class="docutils literal notranslate"><span class="pre">all</span></code> target.  An <code class="docutils literal notranslate"><span class="pre">install</span></code> target
is also provided.</p>
<p>This &quot;extra&quot; generator may be specified as:</p>
<dl class="simple">
<dt><code class="docutils literal notranslate"><span class="pre">Eclipse</span> <span class="pre">CDT4</span> <span class="pre">-</span> <span class="pre">MinGW</span> <span class="pre">Makefiles</span></code></dt><dd><p>Generate with <span class="target" id="index-0-generator:MinGW Makefiles"></span><a class="reference internal" href="MinGW%20Makefiles.html#generator:MinGW Makefiles" title="MinGW Makefiles"><code class="xref cmake cmake-generator docutils literal notranslate"><span class="pre">MinGW</span> <span class="pre">Makefiles</span></code></a>.</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">Eclipse</span> <span class="pre">CDT4</span> <span class="pre">-</span> <span class="pre">NMake</span> <span class="pre">Makefiles</span></code></dt><dd><p>Generate with <span class="target" id="index-0-generator:NMake Makefiles"></span><a class="reference internal" href="NMake%20Makefiles.html#generator:NMake Makefiles" title="NMake Makefiles"><code class="xref cmake cmake-generator docutils literal notranslate"><span class="pre">NMake</span> <span class="pre">Makefiles</span></code></a>.</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">Eclipse</span> <span class="pre">CDT4</span> <span class="pre">-</span> <span class="pre">Ninja</span></code></dt><dd><p>Generate with <span class="target" id="index-0-generator:Ninja"></span><a class="reference internal" href="Ninja.html#generator:Ninja" title="Ninja"><code class="xref cmake cmake-generator docutils literal notranslate"><span class="pre">Ninja</span></code></a>.</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">Eclipse</span> <span class="pre">CDT4</span> <span class="pre">-</span> <span class="pre">Unix</span> <span class="pre">Makefiles</span></code></dt><dd><p>Generate with <span class="target" id="index-0-generator:Unix Makefiles"></span><a class="reference internal" href="Unix%20Makefiles.html#generator:Unix Makefiles" title="Unix Makefiles"><code class="xref cmake cmake-generator docutils literal notranslate"><span class="pre">Unix</span> <span class="pre">Makefiles</span></code></a>.</p>
</dd>
</dl>
</section>


            <div class="clearer"></div>
          </div>
        </div>
      </div>
      <div class="sphinxsidebar" role="navigation" aria-label="main navigation">
        <div class="sphinxsidebarwrapper">
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="CodeLite.html"
                          title="previous chapter">CodeLite</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="Kate.html"
                          title="next chapter">Kate</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../_sources/generator/Eclipse CDT4.rst.txt"
            rel="nofollow">Show Source</a></li>
    </ul>
   </div>
<div id="searchbox" style="display: none" role="search">
  <h3 id="searchlabel">Quick search</h3>
    <div class="searchformwrapper">
    <form class="search" action="../search.html" method="get">
      <input type="text" name="q" aria-labelledby="searchlabel" autocomplete="off" autocorrect="off" autocapitalize="off" spellcheck="false"/>
      <input type="submit" value="Go" />
    </form>
    </div>
</div>
<script>document.getElementById('searchbox').style.display = "block"</script>
        </div>
      </div>
      <div class="clearer"></div>
    </div>
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             >index</a></li>
        <li class="right" >
          <a href="Kate.html" title="Kate"
             >next</a> |</li>
        <li class="right" >
          <a href="CodeLite.html" title="CodeLite"
             >previous</a> |</li>
  <li>
    <img src="../_static/cmake-logo-16.png" alt=""
         style="vertical-align: middle; margin-top: -2px" />
  </li>
  <li>
    <a href="https://cmake.org/">CMake</a> &#187;
  </li>
  <li>
    <a href="../index.html">3.28.1 Documentation</a> &#187;
  </li>

          <li class="nav-item nav-item-1"><a href="../manual/cmake-generators.7.html" >cmake-generators(7)</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href="">Eclipse CDT4</a></li> 
      </ul>
    </div>

    <div class="footer" role="contentinfo">
        &#169; Copyright 2000-2023 Kitware, Inc. and Contributors.
      Created using <a href="https://www.sphinx-doc.org/">Sphinx</a> 6.2.1.
    </div>
  </body>
</html>