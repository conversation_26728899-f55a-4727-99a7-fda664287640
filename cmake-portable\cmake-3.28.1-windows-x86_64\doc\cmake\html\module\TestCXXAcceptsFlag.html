
<!DOCTYPE html>

<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="generator" content="Docutils 0.19: https://docutils.sourceforge.io/" />

  <title>TestCXXAcceptsFlag &mdash; CMake 3.28.1 Documentation</title>

    <link rel="stylesheet" type="text/css" href="../_static/pygments.css" />
    <link rel="stylesheet" type="text/css" href="../_static/cmake.css" />
    
    <script data-url_root="../" id="documentation_options" src="../_static/documentation_options.js"></script>
    <script src="../_static/doctools.js"></script>
    <script src="../_static/sphinx_highlight.js"></script>
    
    <link rel="icon" href="../_static/cmake-favicon.ico"/>
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="next" title="UseJavaClassFilelist" href="UseJavaClassFilelist.html" />
    <link rel="prev" title="TestBigEndian" href="TestBigEndian.html" />
 

  </head><body>

    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             accesskey="I">index</a></li>
        <li class="right" >
          <a href="UseJavaClassFilelist.html" title="UseJavaClassFilelist"
             accesskey="N">next</a> |</li>
        <li class="right" >
          <a href="TestBigEndian.html" title="TestBigEndian"
             accesskey="P">previous</a> |</li>
  <li>
    <img src="../_static/cmake-logo-16.png" alt=""
         style="vertical-align: middle; margin-top: -2px" />
  </li>
  <li>
    <a href="https://cmake.org/">CMake</a> &#187;
  </li>
  <li>
    <a href="../index.html">3.28.1 Documentation</a> &#187;
  </li>

          <li class="nav-item nav-item-1"><a href="../manual/cmake-modules.7.html" accesskey="U">cmake-modules(7)</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href="">TestCXXAcceptsFlag</a></li> 
      </ul>
    </div>  

    <div class="document">
      <div class="documentwrapper">
        <div class="bodywrapper">
          <div class="body" role="main">
            
  <section id="testcxxacceptsflag">
<span id="module:TestCXXAcceptsFlag"></span><h1>TestCXXAcceptsFlag<a class="headerlink" href="#testcxxacceptsflag" title="Permalink to this heading">¶</a></h1>
<div class="deprecated">
<p><span class="versionmodified deprecated">Deprecated since version 3.0: </span>See <span class="target" id="index-0-module:CheckCXXCompilerFlag"></span><a class="reference internal" href="CheckCXXCompilerFlag.html#module:CheckCXXCompilerFlag" title="CheckCXXCompilerFlag"><code class="xref cmake cmake-module docutils literal notranslate"><span class="pre">CheckCXXCompilerFlag</span></code></a>.</p>
</div>
<p>Check if the CXX compiler accepts a flag.</p>
<div class="highlight-cmake notranslate"><div class="highlight"><pre><span></span><span class="nf">CHECK_CXX_ACCEPTS_FLAG(</span><span class="nv">&lt;flags&gt;</span><span class="w"> </span><span class="nv">&lt;variable&gt;</span><span class="nf">)</span>
</pre></div>
</div>
<dl class="simple">
<dt><code class="docutils literal notranslate"><span class="pre">&lt;flags&gt;</span></code></dt><dd><p>the flags to try</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">&lt;variable&gt;</span></code></dt><dd><p>variable to store the result</p>
</dd>
</dl>
</section>


            <div class="clearer"></div>
          </div>
        </div>
      </div>
      <div class="sphinxsidebar" role="navigation" aria-label="main navigation">
        <div class="sphinxsidebarwrapper">
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="TestBigEndian.html"
                          title="previous chapter">TestBigEndian</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="UseJavaClassFilelist.html"
                          title="next chapter">UseJavaClassFilelist</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../_sources/module/TestCXXAcceptsFlag.rst.txt"
            rel="nofollow">Show Source</a></li>
    </ul>
   </div>
<div id="searchbox" style="display: none" role="search">
  <h3 id="searchlabel">Quick search</h3>
    <div class="searchformwrapper">
    <form class="search" action="../search.html" method="get">
      <input type="text" name="q" aria-labelledby="searchlabel" autocomplete="off" autocorrect="off" autocapitalize="off" spellcheck="false"/>
      <input type="submit" value="Go" />
    </form>
    </div>
</div>
<script>document.getElementById('searchbox').style.display = "block"</script>
        </div>
      </div>
      <div class="clearer"></div>
    </div>
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             >index</a></li>
        <li class="right" >
          <a href="UseJavaClassFilelist.html" title="UseJavaClassFilelist"
             >next</a> |</li>
        <li class="right" >
          <a href="TestBigEndian.html" title="TestBigEndian"
             >previous</a> |</li>
  <li>
    <img src="../_static/cmake-logo-16.png" alt=""
         style="vertical-align: middle; margin-top: -2px" />
  </li>
  <li>
    <a href="https://cmake.org/">CMake</a> &#187;
  </li>
  <li>
    <a href="../index.html">3.28.1 Documentation</a> &#187;
  </li>

          <li class="nav-item nav-item-1"><a href="../manual/cmake-modules.7.html" >cmake-modules(7)</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href="">TestCXXAcceptsFlag</a></li> 
      </ul>
    </div>

    <div class="footer" role="contentinfo">
        &#169; Copyright 2000-2023 Kitware, Inc. and Contributors.
      Created using <a href="https://www.sphinx-doc.org/">Sphinx</a> 6.2.1.
    </div>
  </body>
</html>