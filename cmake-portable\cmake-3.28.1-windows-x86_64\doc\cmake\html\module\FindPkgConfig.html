
<!DOCTYPE html>

<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="generator" content="Docutils 0.19: https://docutils.sourceforge.io/" />

  <title>FindPkgConfig &mdash; CMake 3.28.1 Documentation</title>

    <link rel="stylesheet" type="text/css" href="../_static/pygments.css" />
    <link rel="stylesheet" type="text/css" href="../_static/cmake.css" />
    
    <script data-url_root="../" id="documentation_options" src="../_static/documentation_options.js"></script>
    <script src="../_static/doctools.js"></script>
    <script src="../_static/sphinx_highlight.js"></script>
    
    <link rel="icon" href="../_static/cmake-favicon.ico"/>
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="next" title="FindPNG" href="FindPNG.html" />
    <link rel="prev" title="FindPike" href="FindPike.html" />
 

  </head><body>

    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             accesskey="I">index</a></li>
        <li class="right" >
          <a href="FindPNG.html" title="FindPNG"
             accesskey="N">next</a> |</li>
        <li class="right" >
          <a href="FindPike.html" title="FindPike"
             accesskey="P">previous</a> |</li>
  <li>
    <img src="../_static/cmake-logo-16.png" alt=""
         style="vertical-align: middle; margin-top: -2px" />
  </li>
  <li>
    <a href="https://cmake.org/">CMake</a> &#187;
  </li>
  <li>
    <a href="../index.html">3.28.1 Documentation</a> &#187;
  </li>

          <li class="nav-item nav-item-1"><a href="../manual/cmake-modules.7.html" accesskey="U">cmake-modules(7)</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href="">FindPkgConfig</a></li> 
      </ul>
    </div>  

    <div class="document">
      <div class="documentwrapper">
        <div class="bodywrapper">
          <div class="body" role="main">
            
  <section id="findpkgconfig">
<span id="module:FindPkgConfig"></span><h1>FindPkgConfig<a class="headerlink" href="#findpkgconfig" title="Permalink to this heading">¶</a></h1>
<p>A <code class="docutils literal notranslate"><span class="pre">pkg-config</span></code> module for CMake.</p>
<p>Finds the <code class="docutils literal notranslate"><span class="pre">pkg-config</span></code> executable and adds the <span class="target" id="index-0-command:pkg_get_variable"></span><a class="reference internal" href="#command:pkg_get_variable" title="pkg_get_variable"><code class="xref cmake cmake-command docutils literal notranslate"><span class="pre">pkg_get_variable()</span></code></a>,
<span class="target" id="index-0-command:pkg_check_modules"></span><a class="reference internal" href="#command:pkg_check_modules" title="pkg_check_modules"><code class="xref cmake cmake-command docutils literal notranslate"><span class="pre">pkg_check_modules()</span></code></a> and <span class="target" id="index-0-command:pkg_search_module"></span><a class="reference internal" href="#command:pkg_search_module" title="pkg_search_module"><code class="xref cmake cmake-command docutils literal notranslate"><span class="pre">pkg_search_module()</span></code></a> commands. The
following variables will also be set:</p>
<dl>
<dt><code class="docutils literal notranslate"><span class="pre">PKG_CONFIG_FOUND</span></code></dt><dd><p>True if a pkg-config executable was found.</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">PKG_CONFIG_VERSION_STRING</span></code></dt><dd><div class="versionadded">
<p><span class="versionmodified added">New in version 2.8.8.</span></p>
</div>
<p>The version of pkg-config that was found.</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">PKG_CONFIG_EXECUTABLE</span></code></dt><dd><p>The pathname of the pkg-config program.</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">PKG_CONFIG_ARGN</span></code></dt><dd><div class="versionadded">
<p><span class="versionmodified added">New in version 3.22.</span></p>
</div>
<p>A list of arguments to pass to pkg-config.</p>
</dd>
</dl>
<p>Both <code class="docutils literal notranslate"><span class="pre">PKG_CONFIG_EXECUTABLE</span></code> and <code class="docutils literal notranslate"><span class="pre">PKG_CONFIG_ARGN</span></code> are initialized by the
module, but may be overridden by the user.  See <a class="reference internal" href="#variables-affecting-behavior">Variables Affecting Behavior</a>
for how these variables are initialized.</p>
<dl class="cmake command">
<dt class="sig sig-object cmake" id="command:pkg_check_modules">
<span class="sig-name descname"><span class="pre">pkg_check_modules</span></span><a class="headerlink" href="#command:pkg_check_modules" title="Permalink to this definition">¶</a></dt>
<dd><p>Checks for all the given modules, setting a variety of result variables in
the calling scope.</p>
<div class="highlight-cmake notranslate"><div class="highlight"><pre><span></span><span class="nf">pkg_check_modules(</span><span class="nv">&lt;prefix&gt;</span>
<span class="w">                  </span><span class="p">[</span><span class="no">REQUIRED</span><span class="p">]</span><span class="w"> </span><span class="p">[</span><span class="no">QUIET</span><span class="p">]</span>
<span class="w">                  </span><span class="p">[</span><span class="no">NO_CMAKE_PATH</span><span class="p">]</span>
<span class="w">                  </span><span class="p">[</span><span class="no">NO_CMAKE_ENVIRONMENT_PATH</span><span class="p">]</span>
<span class="w">                  </span><span class="p">[</span><span class="no">IMPORTED_TARGET</span><span class="w"> </span><span class="p">[</span><span class="no">GLOBAL</span><span class="p">]]</span>
<span class="w">                  </span><span class="nv">&lt;moduleSpec&gt;</span><span class="w"> </span><span class="p">[</span><span class="nv">&lt;moduleSpec&gt;...</span><span class="p">]</span><span class="nf">)</span>
</pre></div>
</div>
<p>When the <code class="docutils literal notranslate"><span class="pre">REQUIRED</span></code> argument is given, the command will fail with an error
if module(s) could not be found.</p>
<p>When the <code class="docutils literal notranslate"><span class="pre">QUIET</span></code> argument is given, no status messages will be printed.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.1: </span>The <span class="target" id="index-0-variable:CMAKE_PREFIX_PATH"></span><a class="reference internal" href="../variable/CMAKE_PREFIX_PATH.html#variable:CMAKE_PREFIX_PATH" title="CMAKE_PREFIX_PATH"><code class="xref cmake cmake-variable docutils literal notranslate"><span class="pre">CMAKE_PREFIX_PATH</span></code></a>,
<span class="target" id="index-0-variable:CMAKE_FRAMEWORK_PATH"></span><a class="reference internal" href="../variable/CMAKE_FRAMEWORK_PATH.html#variable:CMAKE_FRAMEWORK_PATH" title="CMAKE_FRAMEWORK_PATH"><code class="xref cmake cmake-variable docutils literal notranslate"><span class="pre">CMAKE_FRAMEWORK_PATH</span></code></a>, and <span class="target" id="index-0-variable:CMAKE_APPBUNDLE_PATH"></span><a class="reference internal" href="../variable/CMAKE_APPBUNDLE_PATH.html#variable:CMAKE_APPBUNDLE_PATH" title="CMAKE_APPBUNDLE_PATH"><code class="xref cmake cmake-variable docutils literal notranslate"><span class="pre">CMAKE_APPBUNDLE_PATH</span></code></a> cache
and environment variables will be added to the <code class="docutils literal notranslate"><span class="pre">pkg-config</span></code> search path.
The <code class="docutils literal notranslate"><span class="pre">NO_CMAKE_PATH</span></code> and <code class="docutils literal notranslate"><span class="pre">NO_CMAKE_ENVIRONMENT_PATH</span></code> arguments
disable this behavior for the cache variables and environment variables
respectively.
The <span class="target" id="index-0-variable:PKG_CONFIG_USE_CMAKE_PREFIX_PATH"></span><a class="reference internal" href="#variable:PKG_CONFIG_USE_CMAKE_PREFIX_PATH" title="PKG_CONFIG_USE_CMAKE_PREFIX_PATH"><code class="xref cmake cmake-variable docutils literal notranslate"><span class="pre">PKG_CONFIG_USE_CMAKE_PREFIX_PATH</span></code></a> variable set to <code class="docutils literal notranslate"><span class="pre">FALSE</span></code>
disables this behavior globally.</p>
</div>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.6: </span>The <code class="docutils literal notranslate"><span class="pre">IMPORTED_TARGET</span></code> argument will create an imported target named
<code class="docutils literal notranslate"><span class="pre">PkgConfig::&lt;prefix&gt;</span></code> that can be passed directly as an argument to
<span class="target" id="index-0-command:target_link_libraries"></span><a class="reference internal" href="../command/target_link_libraries.html#command:target_link_libraries" title="target_link_libraries"><code class="xref cmake cmake-command docutils literal notranslate"><span class="pre">target_link_libraries()</span></code></a>.</p>
</div>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.13: </span>The <code class="docutils literal notranslate"><span class="pre">GLOBAL</span></code> argument will make the
imported target available in global scope.</p>
</div>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.15: </span>Non-library linker options reported by <code class="docutils literal notranslate"><span class="pre">pkg-config</span></code> are stored in the
<span class="target" id="index-0-prop_tgt:INTERFACE_LINK_OPTIONS"></span><a class="reference internal" href="../prop_tgt/INTERFACE_LINK_OPTIONS.html#prop_tgt:INTERFACE_LINK_OPTIONS" title="INTERFACE_LINK_OPTIONS"><code class="xref cmake cmake-prop_tgt docutils literal notranslate"><span class="pre">INTERFACE_LINK_OPTIONS</span></code></a> target property.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.18: </span>Include directories specified with <code class="docutils literal notranslate"><span class="pre">-isystem</span></code> are stored in the
<span class="target" id="index-0-prop_tgt:INTERFACE_INCLUDE_DIRECTORIES"></span><a class="reference internal" href="../prop_tgt/INTERFACE_INCLUDE_DIRECTORIES.html#prop_tgt:INTERFACE_INCLUDE_DIRECTORIES" title="INTERFACE_INCLUDE_DIRECTORIES"><code class="xref cmake cmake-prop_tgt docutils literal notranslate"><span class="pre">INTERFACE_INCLUDE_DIRECTORIES</span></code></a> target property.  Previous
versions of CMake left them in the <span class="target" id="index-0-prop_tgt:INTERFACE_COMPILE_OPTIONS"></span><a class="reference internal" href="../prop_tgt/INTERFACE_COMPILE_OPTIONS.html#prop_tgt:INTERFACE_COMPILE_OPTIONS" title="INTERFACE_COMPILE_OPTIONS"><code class="xref cmake cmake-prop_tgt docutils literal notranslate"><span class="pre">INTERFACE_COMPILE_OPTIONS</span></code></a>
property.</p>
</div>
<p>Each <code class="docutils literal notranslate"><span class="pre">&lt;moduleSpec&gt;</span></code> can be either a bare module name or it can be a
module name with a version constraint (operators <code class="docutils literal notranslate"><span class="pre">=</span></code>, <code class="docutils literal notranslate"><span class="pre">&lt;</span></code>, <code class="docutils literal notranslate"><span class="pre">&gt;</span></code>,
<code class="docutils literal notranslate"><span class="pre">&lt;=</span></code> and <code class="docutils literal notranslate"><span class="pre">&gt;=</span></code> are supported).  The following are examples for a module
named <code class="docutils literal notranslate"><span class="pre">foo</span></code> with various constraints:</p>
<ul class="simple">
<li><p><code class="docutils literal notranslate"><span class="pre">foo</span></code> matches any version.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">foo&lt;2</span></code> only matches versions before 2.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">foo&gt;=3.1</span></code> matches any version from 3.1 or later.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">foo=1.2.3</span></code> requires that foo must be exactly version 1.2.3.</p></li>
</ul>
<p>The following variables may be set upon return.  Two sets of values exist:
One for the common case (<code class="docutils literal notranslate"><span class="pre">&lt;XXX&gt;</span> <span class="pre">=</span> <span class="pre">&lt;prefix&gt;</span></code>) and another for the
information <code class="docutils literal notranslate"><span class="pre">pkg-config</span></code> provides when called with the <code class="docutils literal notranslate"><span class="pre">--static</span></code>
option (<code class="docutils literal notranslate"><span class="pre">&lt;XXX&gt;</span> <span class="pre">=</span> <span class="pre">&lt;prefix&gt;_STATIC</span></code>).</p>
<dl class="simple">
<dt><code class="docutils literal notranslate"><span class="pre">&lt;XXX&gt;_FOUND</span></code></dt><dd><p>set to 1 if module(s) exist</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">&lt;XXX&gt;_LIBRARIES</span></code></dt><dd><p>only the libraries (without the '-l')</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">&lt;XXX&gt;_LINK_LIBRARIES</span></code></dt><dd><p>the libraries and their absolute paths</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">&lt;XXX&gt;_LIBRARY_DIRS</span></code></dt><dd><p>the paths of the libraries (without the '-L')</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">&lt;XXX&gt;_LDFLAGS</span></code></dt><dd><p>all required linker flags</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">&lt;XXX&gt;_LDFLAGS_OTHER</span></code></dt><dd><p>all other linker flags</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">&lt;XXX&gt;_INCLUDE_DIRS</span></code></dt><dd><p>the '-I' preprocessor flags (without the '-I')</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">&lt;XXX&gt;_CFLAGS</span></code></dt><dd><p>all required cflags</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">&lt;XXX&gt;_CFLAGS_OTHER</span></code></dt><dd><p>the other compiler flags</p>
</dd>
</dl>
<p>All but <code class="docutils literal notranslate"><span class="pre">&lt;XXX&gt;_FOUND</span></code> may be a <a class="reference internal" href="../manual/cmake-language.7.html#cmake-language-lists"><span class="std std-ref">;-list</span></a> if the
associated variable returned from <code class="docutils literal notranslate"><span class="pre">pkg-config</span></code> has multiple values.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.18: </span>Include directories specified with <code class="docutils literal notranslate"><span class="pre">-isystem</span></code> are stored in the
<code class="docutils literal notranslate"><span class="pre">&lt;XXX&gt;_INCLUDE_DIRS</span></code> variable.  Previous versions of CMake left them
in <code class="docutils literal notranslate"><span class="pre">&lt;XXX&gt;_CFLAGS_OTHER</span></code>.</p>
</div>
<p>There are some special variables whose prefix depends on the number of
<code class="docutils literal notranslate"><span class="pre">&lt;moduleSpec&gt;</span></code> given.  When there is only one <code class="docutils literal notranslate"><span class="pre">&lt;moduleSpec&gt;</span></code>,
<code class="docutils literal notranslate"><span class="pre">&lt;YYY&gt;</span></code> will simply be <code class="docutils literal notranslate"><span class="pre">&lt;prefix&gt;</span></code>, but if two or more <code class="docutils literal notranslate"><span class="pre">&lt;moduleSpec&gt;</span></code>
items are given, <code class="docutils literal notranslate"><span class="pre">&lt;YYY&gt;</span></code> will be <code class="docutils literal notranslate"><span class="pre">&lt;prefix&gt;_&lt;moduleName&gt;</span></code>.</p>
<dl class="simple">
<dt><code class="docutils literal notranslate"><span class="pre">&lt;YYY&gt;_VERSION</span></code></dt><dd><p>version of the module</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">&lt;YYY&gt;_PREFIX</span></code></dt><dd><p>prefix directory of the module</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">&lt;YYY&gt;_INCLUDEDIR</span></code></dt><dd><p>include directory of the module</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">&lt;YYY&gt;_LIBDIR</span></code></dt><dd><p>lib directory of the module</p>
</dd>
</dl>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.8: </span>For any given <code class="docutils literal notranslate"><span class="pre">&lt;prefix&gt;</span></code>, <code class="docutils literal notranslate"><span class="pre">pkg_check_modules()</span></code> can be called multiple
times with different parameters.  Previous versions of CMake cached and
returned the first successful result.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.16: </span>If a full path to the found library can't be determined, but it's still
visible to the linker, pass it through as <code class="docutils literal notranslate"><span class="pre">-l&lt;name&gt;</span></code>.  Previous versions
of CMake failed in this case.</p>
</div>
<p>Examples:</p>
<div class="highlight-cmake notranslate"><div class="highlight"><pre><span></span><span class="nf">pkg_check_modules</span> <span class="nf">(</span><span class="no">GLIB2</span><span class="w"> </span><span class="nb">glib-2.0</span><span class="nf">)</span>
</pre></div>
</div>
<p>Looks for any version of glib2.  If found, the output variable
<code class="docutils literal notranslate"><span class="pre">GLIB2_VERSION</span></code> will hold the actual version found.</p>
<div class="highlight-cmake notranslate"><div class="highlight"><pre><span></span><span class="nf">pkg_check_modules</span> <span class="nf">(</span><span class="no">GLIB2</span><span class="w"> </span><span class="nb">glib-2.0</span><span class="p">&gt;=</span><span class="m">2.10</span><span class="nf">)</span>
</pre></div>
</div>
<p>Looks for at least version 2.10 of glib2.  If found, the output variable
<code class="docutils literal notranslate"><span class="pre">GLIB2_VERSION</span></code> will hold the actual version found.</p>
<div class="highlight-cmake notranslate"><div class="highlight"><pre><span></span><span class="nf">pkg_check_modules</span> <span class="nf">(</span><span class="no">FOO</span><span class="w"> </span><span class="nb">glib-2.0</span><span class="p">&gt;=</span><span class="m">2.10</span><span class="w"> </span><span class="nb">gtk+-2.0</span><span class="nf">)</span>
</pre></div>
</div>
<p>Looks for both glib2-2.0 (at least version 2.10) and any version of
gtk2+-2.0.  Only if both are found will <code class="docutils literal notranslate"><span class="pre">FOO</span></code> be considered found.
The <code class="docutils literal notranslate"><span class="pre">FOO_glib-2.0_VERSION</span></code> and <code class="docutils literal notranslate"><span class="pre">FOO_gtk+-2.0_VERSION</span></code> variables will be
set to their respective found module versions.</p>
<div class="highlight-cmake notranslate"><div class="highlight"><pre><span></span><span class="nf">pkg_check_modules</span> <span class="nf">(</span><span class="no">XRENDER</span><span class="w"> </span><span class="no">REQUIRED</span><span class="w"> </span><span class="nb">xrender</span><span class="nf">)</span>
</pre></div>
</div>
<p>Requires any version of <code class="docutils literal notranslate"><span class="pre">xrender</span></code>.  Example output variables set by a
successful call:</p>
<div class="highlight-none notranslate"><div class="highlight"><pre><span></span>XRENDER_LIBRARIES=Xrender;X11
XRENDER_STATIC_LIBRARIES=Xrender;X11;pthread;Xau;Xdmcp
</pre></div>
</div>
</dd></dl>

<dl class="cmake command">
<dt class="sig sig-object cmake" id="command:pkg_search_module">
<span class="sig-name descname"><span class="pre">pkg_search_module</span></span><a class="headerlink" href="#command:pkg_search_module" title="Permalink to this definition">¶</a></dt>
<dd><p>The behavior of this command is the same as <span class="target" id="index-1-command:pkg_check_modules"></span><a class="reference internal" href="#command:pkg_check_modules" title="pkg_check_modules"><code class="xref cmake cmake-command docutils literal notranslate"><span class="pre">pkg_check_modules()</span></code></a>,
except that rather than checking for all the specified modules, it searches
for just the first successful match.</p>
<div class="highlight-cmake notranslate"><div class="highlight"><pre><span></span><span class="nf">pkg_search_module(</span><span class="nv">&lt;prefix&gt;</span>
<span class="w">                  </span><span class="p">[</span><span class="no">REQUIRED</span><span class="p">]</span><span class="w"> </span><span class="p">[</span><span class="no">QUIET</span><span class="p">]</span>
<span class="w">                  </span><span class="p">[</span><span class="no">NO_CMAKE_PATH</span><span class="p">]</span>
<span class="w">                  </span><span class="p">[</span><span class="no">NO_CMAKE_ENVIRONMENT_PATH</span><span class="p">]</span>
<span class="w">                  </span><span class="p">[</span><span class="no">IMPORTED_TARGET</span><span class="w"> </span><span class="p">[</span><span class="no">GLOBAL</span><span class="p">]]</span>
<span class="w">                  </span><span class="nv">&lt;moduleSpec&gt;</span><span class="w"> </span><span class="p">[</span><span class="nv">&lt;moduleSpec&gt;...</span><span class="p">]</span><span class="nf">)</span>
</pre></div>
</div>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.16: </span>If a module is found, the <code class="docutils literal notranslate"><span class="pre">&lt;prefix&gt;_MODULE_NAME</span></code> variable will contain the
name of the matching module. This variable can be used if you need to run
<span class="target" id="index-1-command:pkg_get_variable"></span><a class="reference internal" href="#command:pkg_get_variable" title="pkg_get_variable"><code class="xref cmake cmake-command docutils literal notranslate"><span class="pre">pkg_get_variable()</span></code></a>.</p>
</div>
<p>Example:</p>
<div class="highlight-cmake notranslate"><div class="highlight"><pre><span></span><span class="nf">pkg_search_module</span> <span class="nf">(</span><span class="no">BAR</span><span class="w"> </span><span class="nb">libxml-2.0</span><span class="w"> </span><span class="nb">libxml2</span><span class="w"> </span><span class="nb">libxml</span><span class="p">&gt;=</span><span class="m">2</span><span class="nf">)</span>
</pre></div>
</div>
</dd></dl>

<dl class="cmake command">
<dt class="sig sig-object cmake" id="command:pkg_get_variable">
<span class="sig-name descname"><span class="pre">pkg_get_variable</span></span><a class="headerlink" href="#command:pkg_get_variable" title="Permalink to this definition">¶</a></dt>
<dd><div class="versionadded">
<p><span class="versionmodified added">New in version 3.4.</span></p>
</div>
<p>Retrieves the value of a pkg-config variable <code class="docutils literal notranslate"><span class="pre">varName</span></code> and stores it in the
result variable <code class="docutils literal notranslate"><span class="pre">resultVar</span></code> in the calling scope.</p>
<div class="highlight-cmake notranslate"><div class="highlight"><pre><span></span><span class="nf">pkg_get_variable(</span><span class="nv">&lt;resultVar&gt;</span><span class="w"> </span><span class="nv">&lt;moduleName&gt;</span><span class="w"> </span><span class="nv">&lt;varName&gt;</span>
<span class="w">                 </span><span class="p">[</span><span class="no">DEFINE_VARIABLES</span><span class="w"> </span><span class="nv">&lt;key&gt;</span><span class="p">=</span><span class="nv">&lt;value&gt;...</span><span class="p">]</span><span class="nf">)</span>
</pre></div>
</div>
<p>If <code class="docutils literal notranslate"><span class="pre">pkg-config</span></code> returns multiple values for the specified variable,
<code class="docutils literal notranslate"><span class="pre">resultVar</span></code> will contain a <a class="reference internal" href="../manual/cmake-language.7.html#cmake-language-lists"><span class="std std-ref">;-list</span></a>.</p>
<p>Options:</p>
<dl>
<dt><code class="docutils literal notranslate"><span class="pre">DEFINE_VARIABLES</span> <span class="pre">&lt;key&gt;=&lt;value&gt;...</span></code></dt><dd><div class="versionadded">
<p><span class="versionmodified added">New in version 3.28.</span></p>
</div>
<p>Specify key-value pairs to redefine variables affecting the variable
retrieved with <code class="docutils literal notranslate"><span class="pre">pkg-config</span></code>.</p>
</dd>
</dl>
<p>For example:</p>
<div class="highlight-cmake notranslate"><div class="highlight"><pre><span></span><span class="nf">pkg_get_variable(</span><span class="no">GI_GIRDIR</span><span class="w"> </span><span class="nb">gobject-introspection-1.0</span><span class="w"> </span><span class="nb">girdir</span><span class="nf">)</span>
</pre></div>
</div>
</dd></dl>

<section id="variables-affecting-behavior">
<h2>Variables Affecting Behavior<a class="headerlink" href="#variables-affecting-behavior" title="Permalink to this heading">¶</a></h2>
<dl class="cmake variable">
<dt class="sig sig-object cmake" id="variable:PKG_CONFIG_EXECUTABLE">
<span class="sig-name descname"><span class="pre">PKG_CONFIG_EXECUTABLE</span></span><a class="headerlink" href="#variable:PKG_CONFIG_EXECUTABLE" title="Permalink to this definition">¶</a></dt>
<dd><p>This cache variable can be set to the path of the pkg-config executable.
<span class="target" id="index-0-command:find_program"></span><a class="reference internal" href="../command/find_program.html#command:find_program" title="find_program"><code class="xref cmake cmake-command docutils literal notranslate"><span class="pre">find_program()</span></code></a> is called internally by the module with this
variable.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.1: </span>The <code class="docutils literal notranslate"><span class="pre">PKG_CONFIG</span></code> environment variable can be used as a hint if
<code class="docutils literal notranslate"><span class="pre">PKG_CONFIG_EXECUTABLE</span></code> has not yet been set.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.22: </span>If the <code class="docutils literal notranslate"><span class="pre">PKG_CONFIG</span></code> environment variable is set, only the first
argument is taken from it when using it as a hint.</p>
</div>
</dd></dl>

<dl class="cmake variable">
<dt class="sig sig-object cmake" id="variable:PKG_CONFIG_ARGN">
<span class="sig-name descname"><span class="pre">PKG_CONFIG_ARGN</span></span><a class="headerlink" href="#variable:PKG_CONFIG_ARGN" title="Permalink to this definition">¶</a></dt>
<dd><div class="versionadded">
<p><span class="versionmodified added">New in version 3.22.</span></p>
</div>
<p>This cache variable can be set to a list of arguments to additionally pass
to pkg-config if needed. If not provided, it will be initialized from the
<code class="docutils literal notranslate"><span class="pre">PKG_CONFIG</span></code> environment variable, if set. The first argument in that
environment variable is assumed to be the pkg-config program, while all
remaining arguments after that are used to initialize <code class="docutils literal notranslate"><span class="pre">PKG_CONFIG_ARGN</span></code>.
If no such environment variable is defined, <code class="docutils literal notranslate"><span class="pre">PKG_CONFIG_ARGN</span></code> is
initialized to an empty string. The module does not update the variable once
it has been set in the cache.</p>
</dd></dl>

<dl class="cmake variable">
<dt class="sig sig-object cmake" id="variable:PKG_CONFIG_USE_CMAKE_PREFIX_PATH">
<span class="sig-name descname"><span class="pre">PKG_CONFIG_USE_CMAKE_PREFIX_PATH</span></span><a class="headerlink" href="#variable:PKG_CONFIG_USE_CMAKE_PREFIX_PATH" title="Permalink to this definition">¶</a></dt>
<dd><div class="versionadded">
<p><span class="versionmodified added">New in version 3.1.</span></p>
</div>
<p>Specifies whether <span class="target" id="index-2-command:pkg_check_modules"></span><a class="reference internal" href="#command:pkg_check_modules" title="pkg_check_modules"><code class="xref cmake cmake-command docutils literal notranslate"><span class="pre">pkg_check_modules()</span></code></a> and
<span class="target" id="index-1-command:pkg_search_module"></span><a class="reference internal" href="#command:pkg_search_module" title="pkg_search_module"><code class="xref cmake cmake-command docutils literal notranslate"><span class="pre">pkg_search_module()</span></code></a> should add the paths in the
<span class="target" id="index-1-variable:CMAKE_PREFIX_PATH"></span><a class="reference internal" href="../variable/CMAKE_PREFIX_PATH.html#variable:CMAKE_PREFIX_PATH" title="CMAKE_PREFIX_PATH"><code class="xref cmake cmake-variable docutils literal notranslate"><span class="pre">CMAKE_PREFIX_PATH</span></code></a>, <span class="target" id="index-1-variable:CMAKE_FRAMEWORK_PATH"></span><a class="reference internal" href="../variable/CMAKE_FRAMEWORK_PATH.html#variable:CMAKE_FRAMEWORK_PATH" title="CMAKE_FRAMEWORK_PATH"><code class="xref cmake cmake-variable docutils literal notranslate"><span class="pre">CMAKE_FRAMEWORK_PATH</span></code></a> and
<span class="target" id="index-1-variable:CMAKE_APPBUNDLE_PATH"></span><a class="reference internal" href="../variable/CMAKE_APPBUNDLE_PATH.html#variable:CMAKE_APPBUNDLE_PATH" title="CMAKE_APPBUNDLE_PATH"><code class="xref cmake cmake-variable docutils literal notranslate"><span class="pre">CMAKE_APPBUNDLE_PATH</span></code></a> cache and environment variables to the
<code class="docutils literal notranslate"><span class="pre">pkg-config</span></code> search path.</p>
<p>If this variable is not set, this behavior is enabled by default if
<span class="target" id="index-0-variable:CMAKE_MINIMUM_REQUIRED_VERSION"></span><a class="reference internal" href="../variable/CMAKE_MINIMUM_REQUIRED_VERSION.html#variable:CMAKE_MINIMUM_REQUIRED_VERSION" title="CMAKE_MINIMUM_REQUIRED_VERSION"><code class="xref cmake cmake-variable docutils literal notranslate"><span class="pre">CMAKE_MINIMUM_REQUIRED_VERSION</span></code></a> is 3.1 or later, disabled
otherwise.</p>
</dd></dl>

</section>
</section>


            <div class="clearer"></div>
          </div>
        </div>
      </div>
      <div class="sphinxsidebar" role="navigation" aria-label="main navigation">
        <div class="sphinxsidebarwrapper">
  <div>
    <h3><a href="../index.html">Table of Contents</a></h3>
    <ul>
<li><a class="reference internal" href="#">FindPkgConfig</a><ul>
<li><a class="reference internal" href="#variables-affecting-behavior">Variables Affecting Behavior</a></li>
</ul>
</li>
</ul>

  </div>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="FindPike.html"
                          title="previous chapter">FindPike</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="FindPNG.html"
                          title="next chapter">FindPNG</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../_sources/module/FindPkgConfig.rst.txt"
            rel="nofollow">Show Source</a></li>
    </ul>
   </div>
<div id="searchbox" style="display: none" role="search">
  <h3 id="searchlabel">Quick search</h3>
    <div class="searchformwrapper">
    <form class="search" action="../search.html" method="get">
      <input type="text" name="q" aria-labelledby="searchlabel" autocomplete="off" autocorrect="off" autocapitalize="off" spellcheck="false"/>
      <input type="submit" value="Go" />
    </form>
    </div>
</div>
<script>document.getElementById('searchbox').style.display = "block"</script>
        </div>
      </div>
      <div class="clearer"></div>
    </div>
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             >index</a></li>
        <li class="right" >
          <a href="FindPNG.html" title="FindPNG"
             >next</a> |</li>
        <li class="right" >
          <a href="FindPike.html" title="FindPike"
             >previous</a> |</li>
  <li>
    <img src="../_static/cmake-logo-16.png" alt=""
         style="vertical-align: middle; margin-top: -2px" />
  </li>
  <li>
    <a href="https://cmake.org/">CMake</a> &#187;
  </li>
  <li>
    <a href="../index.html">3.28.1 Documentation</a> &#187;
  </li>

          <li class="nav-item nav-item-1"><a href="../manual/cmake-modules.7.html" >cmake-modules(7)</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href="">FindPkgConfig</a></li> 
      </ul>
    </div>

    <div class="footer" role="contentinfo">
        &#169; Copyright 2000-2023 Kitware, Inc. and Contributors.
      Created using <a href="https://www.sphinx-doc.org/">Sphinx</a> 6.2.1.
    </div>
  </body>
</html>