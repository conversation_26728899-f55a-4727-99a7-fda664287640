
<!DOCTYPE html>

<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="generator" content="Docutils 0.19: https://docutils.sourceforge.io/" />

  <title>UseEcos &mdash; CMake 3.28.1 Documentation</title>

    <link rel="stylesheet" type="text/css" href="../_static/pygments.css" />
    <link rel="stylesheet" type="text/css" href="../_static/cmake.css" />
    
    <script data-url_root="../" id="documentation_options" src="../_static/documentation_options.js"></script>
    <script src="../_static/doctools.js"></script>
    <script src="../_static/sphinx_highlight.js"></script>
    
    <link rel="icon" href="../_static/cmake-favicon.ico"/>
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="next" title="UseJava" href="UseJava.html" />
    <link rel="prev" title="TestForSTDNamespace" href="TestForSTDNamespace.html" />
 

  </head><body>

    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             accesskey="I">index</a></li>
        <li class="right" >
          <a href="UseJava.html" title="UseJava"
             accesskey="N">next</a> |</li>
        <li class="right" >
          <a href="TestForSTDNamespace.html" title="TestForSTDNamespace"
             accesskey="P">previous</a> |</li>
  <li>
    <img src="../_static/cmake-logo-16.png" alt=""
         style="vertical-align: middle; margin-top: -2px" />
  </li>
  <li>
    <a href="https://cmake.org/">CMake</a> &#187;
  </li>
  <li>
    <a href="../index.html">3.28.1 Documentation</a> &#187;
  </li>

          <li class="nav-item nav-item-1"><a href="../manual/cmake-modules.7.html" accesskey="U">cmake-modules(7)</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href="">UseEcos</a></li> 
      </ul>
    </div>  

    <div class="document">
      <div class="documentwrapper">
        <div class="bodywrapper">
          <div class="body" role="main">
            
  <section id="useecos">
<span id="module:UseEcos"></span><h1>UseEcos<a class="headerlink" href="#useecos" title="Permalink to this heading">¶</a></h1>
<p>This module defines variables and macros required to build eCos application.</p>
<p>This file contains the following macros:
ECOS_ADD_INCLUDE_DIRECTORIES() - add the eCos include dirs
ECOS_ADD_EXECUTABLE(name source1 ...  sourceN ) - create an eCos
executable ECOS_ADJUST_DIRECTORY(VAR source1 ...  sourceN ) - adjusts
the path of the source files and puts the result into VAR</p>
<p>Macros for selecting the toolchain: ECOS_USE_ARM_ELF_TOOLS() - enable
the ARM ELF toolchain for the directory where it is called
ECOS_USE_I386_ELF_TOOLS() - enable the i386 ELF toolchain for the
directory where it is called ECOS_USE_PPC_EABI_TOOLS() - enable the
PowerPC toolchain for the directory where it is called</p>
<p>It contains the following variables: ECOS_DEFINITIONS
ECOSCONFIG_EXECUTABLE ECOS_CONFIG_FILE - defaults to ecos.ecc, if your
eCos configuration file has a different name, adjust this variable for
internal use only:</p>
<div class="highlight-none notranslate"><div class="highlight"><pre><span></span>ECOS_ADD_TARGET_LIB
</pre></div>
</div>
</section>


            <div class="clearer"></div>
          </div>
        </div>
      </div>
      <div class="sphinxsidebar" role="navigation" aria-label="main navigation">
        <div class="sphinxsidebarwrapper">
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="TestForSTDNamespace.html"
                          title="previous chapter">TestForSTDNamespace</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="UseJava.html"
                          title="next chapter">UseJava</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../_sources/module/UseEcos.rst.txt"
            rel="nofollow">Show Source</a></li>
    </ul>
   </div>
<div id="searchbox" style="display: none" role="search">
  <h3 id="searchlabel">Quick search</h3>
    <div class="searchformwrapper">
    <form class="search" action="../search.html" method="get">
      <input type="text" name="q" aria-labelledby="searchlabel" autocomplete="off" autocorrect="off" autocapitalize="off" spellcheck="false"/>
      <input type="submit" value="Go" />
    </form>
    </div>
</div>
<script>document.getElementById('searchbox').style.display = "block"</script>
        </div>
      </div>
      <div class="clearer"></div>
    </div>
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             >index</a></li>
        <li class="right" >
          <a href="UseJava.html" title="UseJava"
             >next</a> |</li>
        <li class="right" >
          <a href="TestForSTDNamespace.html" title="TestForSTDNamespace"
             >previous</a> |</li>
  <li>
    <img src="../_static/cmake-logo-16.png" alt=""
         style="vertical-align: middle; margin-top: -2px" />
  </li>
  <li>
    <a href="https://cmake.org/">CMake</a> &#187;
  </li>
  <li>
    <a href="../index.html">3.28.1 Documentation</a> &#187;
  </li>

          <li class="nav-item nav-item-1"><a href="../manual/cmake-modules.7.html" >cmake-modules(7)</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href="">UseEcos</a></li> 
      </ul>
    </div>

    <div class="footer" role="contentinfo">
        &#169; Copyright 2000-2023 Kitware, Inc. and Contributors.
      Created using <a href="https://www.sphinx-doc.org/">Sphinx</a> 6.2.1.
    </div>
  </body>
</html>