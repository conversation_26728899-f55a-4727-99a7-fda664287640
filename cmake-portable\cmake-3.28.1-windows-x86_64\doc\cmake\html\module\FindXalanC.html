
<!DOCTYPE html>

<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="generator" content="Docutils 0.19: https://docutils.sourceforge.io/" />

  <title>FindXalanC &mdash; CMake 3.28.1 Documentation</title>

    <link rel="stylesheet" type="text/css" href="../_static/pygments.css" />
    <link rel="stylesheet" type="text/css" href="../_static/cmake.css" />
    
    <script data-url_root="../" id="documentation_options" src="../_static/documentation_options.js"></script>
    <script src="../_static/doctools.js"></script>
    <script src="../_static/sphinx_highlight.js"></script>
    
    <link rel="icon" href="../_static/cmake-favicon.ico"/>
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="next" title="FindXCTest" href="FindXCTest.html" />
    <link rel="prev" title="FindX11" href="FindX11.html" />
 

  </head><body>

    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             accesskey="I">index</a></li>
        <li class="right" >
          <a href="FindXCTest.html" title="FindXCTest"
             accesskey="N">next</a> |</li>
        <li class="right" >
          <a href="FindX11.html" title="FindX11"
             accesskey="P">previous</a> |</li>
  <li>
    <img src="../_static/cmake-logo-16.png" alt=""
         style="vertical-align: middle; margin-top: -2px" />
  </li>
  <li>
    <a href="https://cmake.org/">CMake</a> &#187;
  </li>
  <li>
    <a href="../index.html">3.28.1 Documentation</a> &#187;
  </li>

          <li class="nav-item nav-item-1"><a href="../manual/cmake-modules.7.html" accesskey="U">cmake-modules(7)</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href="">FindXalanC</a></li> 
      </ul>
    </div>  

    <div class="document">
      <div class="documentwrapper">
        <div class="bodywrapper">
          <div class="body" role="main">
            
  <section id="findxalanc">
<span id="module:FindXalanC"></span><h1>FindXalanC<a class="headerlink" href="#findxalanc" title="Permalink to this heading">¶</a></h1>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.5.</span></p>
</div>
<p>Find the Apache Xalan-C++ XSL transform processor headers and libraries.</p>
<section id="imported-targets">
<h2>Imported targets<a class="headerlink" href="#imported-targets" title="Permalink to this heading">¶</a></h2>
<p>This module defines the following <span class="target" id="index-0-prop_tgt:IMPORTED"></span><a class="reference internal" href="../prop_tgt/IMPORTED.html#prop_tgt:IMPORTED" title="IMPORTED"><code class="xref cmake cmake-prop_tgt docutils literal notranslate"><span class="pre">IMPORTED</span></code></a> targets:</p>
<dl class="simple">
<dt><code class="docutils literal notranslate"><span class="pre">XalanC::XalanC</span></code></dt><dd><p>The Xalan-C++ <code class="docutils literal notranslate"><span class="pre">xalan-c</span></code> library, if found.</p>
</dd>
</dl>
</section>
<section id="result-variables">
<h2>Result variables<a class="headerlink" href="#result-variables" title="Permalink to this heading">¶</a></h2>
<p>This module will set the following variables in your project:</p>
<dl class="simple">
<dt><code class="docutils literal notranslate"><span class="pre">XalanC_FOUND</span></code></dt><dd><p>true if the Xalan headers and libraries were found</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">XalanC_VERSION</span></code></dt><dd><p>Xalan release version</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">XalanC_INCLUDE_DIRS</span></code></dt><dd><p>the directory containing the Xalan headers; note
<code class="docutils literal notranslate"><span class="pre">XercesC_INCLUDE_DIRS</span></code> is also required</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">XalanC_LIBRARIES</span></code></dt><dd><p>Xalan libraries to be linked; note <code class="docutils literal notranslate"><span class="pre">XercesC_LIBRARIES</span></code> is also
required</p>
</dd>
</dl>
</section>
<section id="cache-variables">
<h2>Cache variables<a class="headerlink" href="#cache-variables" title="Permalink to this heading">¶</a></h2>
<p>The following cache variables may also be set:</p>
<dl class="simple">
<dt><code class="docutils literal notranslate"><span class="pre">XalanC_INCLUDE_DIR</span></code></dt><dd><p>the directory containing the Xalan headers</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">XalanC_LIBRARY</span></code></dt><dd><p>the Xalan library</p>
</dd>
</dl>
</section>
</section>


            <div class="clearer"></div>
          </div>
        </div>
      </div>
      <div class="sphinxsidebar" role="navigation" aria-label="main navigation">
        <div class="sphinxsidebarwrapper">
  <div>
    <h3><a href="../index.html">Table of Contents</a></h3>
    <ul>
<li><a class="reference internal" href="#">FindXalanC</a><ul>
<li><a class="reference internal" href="#imported-targets">Imported targets</a></li>
<li><a class="reference internal" href="#result-variables">Result variables</a></li>
<li><a class="reference internal" href="#cache-variables">Cache variables</a></li>
</ul>
</li>
</ul>

  </div>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="FindX11.html"
                          title="previous chapter">FindX11</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="FindXCTest.html"
                          title="next chapter">FindXCTest</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../_sources/module/FindXalanC.rst.txt"
            rel="nofollow">Show Source</a></li>
    </ul>
   </div>
<div id="searchbox" style="display: none" role="search">
  <h3 id="searchlabel">Quick search</h3>
    <div class="searchformwrapper">
    <form class="search" action="../search.html" method="get">
      <input type="text" name="q" aria-labelledby="searchlabel" autocomplete="off" autocorrect="off" autocapitalize="off" spellcheck="false"/>
      <input type="submit" value="Go" />
    </form>
    </div>
</div>
<script>document.getElementById('searchbox').style.display = "block"</script>
        </div>
      </div>
      <div class="clearer"></div>
    </div>
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             >index</a></li>
        <li class="right" >
          <a href="FindXCTest.html" title="FindXCTest"
             >next</a> |</li>
        <li class="right" >
          <a href="FindX11.html" title="FindX11"
             >previous</a> |</li>
  <li>
    <img src="../_static/cmake-logo-16.png" alt=""
         style="vertical-align: middle; margin-top: -2px" />
  </li>
  <li>
    <a href="https://cmake.org/">CMake</a> &#187;
  </li>
  <li>
    <a href="../index.html">3.28.1 Documentation</a> &#187;
  </li>

          <li class="nav-item nav-item-1"><a href="../manual/cmake-modules.7.html" >cmake-modules(7)</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href="">FindXalanC</a></li> 
      </ul>
    </div>

    <div class="footer" role="contentinfo">
        &#169; Copyright 2000-2023 Kitware, Inc. and Contributors.
      Created using <a href="https://www.sphinx-doc.org/">Sphinx</a> 6.2.1.
    </div>
  </body>
</html>