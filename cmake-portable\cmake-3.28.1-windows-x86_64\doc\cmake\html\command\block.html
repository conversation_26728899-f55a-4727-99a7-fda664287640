
<!DOCTYPE html>

<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="generator" content="Docutils 0.19: https://docutils.sourceforge.io/" />

  <title>block &mdash; CMake 3.28.1 Documentation</title>

    <link rel="stylesheet" type="text/css" href="../_static/pygments.css" />
    <link rel="stylesheet" type="text/css" href="../_static/cmake.css" />
    
    <script data-url_root="../" id="documentation_options" src="../_static/documentation_options.js"></script>
    <script src="../_static/doctools.js"></script>
    <script src="../_static/sphinx_highlight.js"></script>
    
    <link rel="icon" href="../_static/cmake-favicon.ico"/>
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="next" title="break" href="break.html" />
    <link rel="prev" title="cmake-commands(7)" href="../manual/cmake-commands.7.html" />
 

  </head><body>

    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             accesskey="I">index</a></li>
        <li class="right" >
          <a href="break.html" title="break"
             accesskey="N">next</a> |</li>
        <li class="right" >
          <a href="../manual/cmake-commands.7.html" title="cmake-commands(7)"
             accesskey="P">previous</a> |</li>
  <li>
    <img src="../_static/cmake-logo-16.png" alt=""
         style="vertical-align: middle; margin-top: -2px" />
  </li>
  <li>
    <a href="https://cmake.org/">CMake</a> &#187;
  </li>
  <li>
    <a href="../index.html">3.28.1 Documentation</a> &#187;
  </li>

          <li class="nav-item nav-item-1"><a href="../manual/cmake-commands.7.html" accesskey="U">cmake-commands(7)</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href="">block</a></li> 
      </ul>
    </div>  

    <div class="document">
      <div class="documentwrapper">
        <div class="bodywrapper">
          <div class="body" role="main">
            
  <section id="block">
<span id="command:block"></span><h1>block<a class="headerlink" href="#block" title="Permalink to this heading">¶</a></h1>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.25.</span></p>
</div>
<p>Evaluate a group of commands with a dedicated variable and/or policy scope.</p>
<div class="highlight-cmake notranslate"><div class="highlight"><pre><span></span><span class="nf">block(</span><span class="p">[</span><span class="no">SCOPE_FOR</span><span class="w"> </span><span class="p">[</span><span class="no">POLICIES</span><span class="p">]</span><span class="w"> </span><span class="p">[</span><span class="no">VARIABLES</span><span class="p">]</span><span class="w"> </span><span class="p">]</span><span class="w"> </span><span class="p">[</span><span class="no">PROPAGATE</span><span class="w"> </span><span class="nv">&lt;var-name&gt;...</span><span class="p">]</span><span class="nf">)</span>
<span class="w">  </span><span class="nv">&lt;commands&gt;</span>
<span class="nf">endblock()</span>
</pre></div>
</div>
<p>All commands between <code class="docutils literal notranslate"><span class="pre">block()</span></code> and the matching <span class="target" id="index-0-command:endblock"></span><a class="reference internal" href="endblock.html#command:endblock" title="endblock"><code class="xref cmake cmake-command docutils literal notranslate"><span class="pre">endblock()</span></code></a> are
recorded without being invoked.  Once the <span class="target" id="index-1-command:endblock"></span><a class="reference internal" href="endblock.html#command:endblock" title="endblock"><code class="xref cmake cmake-command docutils literal notranslate"><span class="pre">endblock()</span></code></a> is evaluated, the
recorded list of commands is invoked inside the requested scopes, then the
scopes created by the <code class="docutils literal notranslate"><span class="pre">block()</span></code> command are removed.</p>
<dl>
<dt><code class="docutils literal notranslate"><span class="pre">SCOPE_FOR</span></code></dt><dd><p>Specify which scopes must be created.</p>
<dl class="simple">
<dt><code class="docutils literal notranslate"><span class="pre">POLICIES</span></code></dt><dd><p>Create a new policy scope. This is equivalent to
<span class="target" id="index-0-command:cmake_policy"></span><a class="reference internal" href="cmake_policy.html#push" title="cmake_policy(push)"><code class="xref cmake cmake-command docutils literal notranslate"><span class="pre">cmake_policy(PUSH)</span></code></a> with an automatic
<span class="target" id="index-1-command:cmake_policy"></span><a class="reference internal" href="cmake_policy.html#pop" title="cmake_policy(pop)"><code class="xref cmake cmake-command docutils literal notranslate"><span class="pre">cmake_policy(POP)</span></code></a> when leaving the block scope.</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">VARIABLES</span></code></dt><dd><p>Create a new variable scope.</p>
</dd>
</dl>
<p>If <code class="docutils literal notranslate"><span class="pre">SCOPE_FOR</span></code> is not specified, this is equivalent to:</p>
<div class="highlight-cmake notranslate"><div class="highlight"><pre><span></span><span class="nf">block(</span><span class="no">SCOPE_FOR</span><span class="w"> </span><span class="no">VARIABLES</span><span class="w"> </span><span class="no">POLICIES</span><span class="nf">)</span>
</pre></div>
</div>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">PROPAGATE</span></code></dt><dd><p>When a variable scope is created by the <span class="target" id="index-0-command:block"></span><a class="reference internal" href="#command:block" title="block"><code class="xref cmake cmake-command docutils literal notranslate"><span class="pre">block()</span></code></a> command, this
option sets or unsets the specified variables in the parent scope. This is
equivalent to <span class="target" id="index-0-command:set"></span><a class="reference internal" href="set.html#command:set" title="set(parent_scope)"><code class="xref cmake cmake-command docutils literal notranslate"><span class="pre">set(PARENT_SCOPE)</span></code></a> or <span class="target" id="index-0-command:unset"></span><a class="reference internal" href="unset.html#command:unset" title="unset(parent_scope)"><code class="xref cmake cmake-command docutils literal notranslate"><span class="pre">unset(PARENT_SCOPE)</span></code></a>
commands.</p>
<div class="highlight-cmake notranslate"><div class="highlight"><pre><span></span><span class="nf">set(</span><span class="nb">var1</span><span class="w"> </span><span class="s">&quot;INIT1&quot;</span><span class="nf">)</span>
<span class="nf">set(</span><span class="nb">var2</span><span class="w"> </span><span class="s">&quot;INIT2&quot;</span><span class="nf">)</span>

<span class="nf">block(</span><span class="no">PROPAGATE</span><span class="w"> </span><span class="nb">var1</span><span class="w"> </span><span class="nb">var2</span><span class="nf">)</span>
<span class="w">  </span><span class="nf">set(</span><span class="nb">var1</span><span class="w"> </span><span class="s">&quot;VALUE1&quot;</span><span class="nf">)</span>
<span class="w">  </span><span class="nf">unset(</span><span class="nb">var2</span><span class="nf">)</span>
<span class="nf">endblock()</span>

<span class="c"># Now var1 holds VALUE1, and var2 is unset</span>
</pre></div>
</div>
<p>This option is only allowed when a variable scope is created. An error will
be raised in the other cases.</p>
</dd>
</dl>
<p>When the <code class="docutils literal notranslate"><span class="pre">block()</span></code> is inside a <span class="target" id="index-0-command:foreach"></span><a class="reference internal" href="foreach.html#command:foreach" title="foreach"><code class="xref cmake cmake-command docutils literal notranslate"><span class="pre">foreach()</span></code></a> or <span class="target" id="index-0-command:while"></span><a class="reference internal" href="while.html#command:while" title="while"><code class="xref cmake cmake-command docutils literal notranslate"><span class="pre">while()</span></code></a>
command, the <span class="target" id="index-0-command:break"></span><a class="reference internal" href="break.html#command:break" title="break"><code class="xref cmake cmake-command docutils literal notranslate"><span class="pre">break()</span></code></a> and <span class="target" id="index-0-command:continue"></span><a class="reference internal" href="continue.html#command:continue" title="continue"><code class="xref cmake cmake-command docutils literal notranslate"><span class="pre">continue()</span></code></a> commands can be used
inside the block.</p>
<div class="highlight-cmake notranslate"><div class="highlight"><pre><span></span><span class="nf">while(</span><span class="no">TRUE</span><span class="nf">)</span>
<span class="w">  </span><span class="nf">block()</span>
<span class="w">     </span><span class="p">...</span>
<span class="w">     </span><span class="c"># the break() command will terminate the while() command</span>
<span class="w">     </span><span class="nf">break()</span>
<span class="w">  </span><span class="nf">endblock()</span>
<span class="nf">endwhile()</span>
</pre></div>
</div>
<section id="see-also">
<h2>See Also<a class="headerlink" href="#see-also" title="Permalink to this heading">¶</a></h2>
<ul class="simple">
<li><p><span class="target" id="index-2-command:endblock"></span><a class="reference internal" href="endblock.html#command:endblock" title="endblock"><code class="xref cmake cmake-command docutils literal notranslate"><span class="pre">endblock()</span></code></a></p></li>
<li><p><span class="target" id="index-0-command:return"></span><a class="reference internal" href="return.html#command:return" title="return"><code class="xref cmake cmake-command docutils literal notranslate"><span class="pre">return()</span></code></a></p></li>
<li><p><span class="target" id="index-2-command:cmake_policy"></span><a class="reference internal" href="cmake_policy.html#command:cmake_policy" title="cmake_policy"><code class="xref cmake cmake-command docutils literal notranslate"><span class="pre">cmake_policy()</span></code></a></p></li>
</ul>
</section>
</section>


            <div class="clearer"></div>
          </div>
        </div>
      </div>
      <div class="sphinxsidebar" role="navigation" aria-label="main navigation">
        <div class="sphinxsidebarwrapper">
  <div>
    <h3><a href="../index.html">Table of Contents</a></h3>
    <ul>
<li><a class="reference internal" href="#">block</a><ul>
<li><a class="reference internal" href="#see-also">See Also</a></li>
</ul>
</li>
</ul>

  </div>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="../manual/cmake-commands.7.html"
                          title="previous chapter">cmake-commands(7)</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="break.html"
                          title="next chapter">break</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../_sources/command/block.rst.txt"
            rel="nofollow">Show Source</a></li>
    </ul>
   </div>
<div id="searchbox" style="display: none" role="search">
  <h3 id="searchlabel">Quick search</h3>
    <div class="searchformwrapper">
    <form class="search" action="../search.html" method="get">
      <input type="text" name="q" aria-labelledby="searchlabel" autocomplete="off" autocorrect="off" autocapitalize="off" spellcheck="false"/>
      <input type="submit" value="Go" />
    </form>
    </div>
</div>
<script>document.getElementById('searchbox').style.display = "block"</script>
        </div>
      </div>
      <div class="clearer"></div>
    </div>
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             >index</a></li>
        <li class="right" >
          <a href="break.html" title="break"
             >next</a> |</li>
        <li class="right" >
          <a href="../manual/cmake-commands.7.html" title="cmake-commands(7)"
             >previous</a> |</li>
  <li>
    <img src="../_static/cmake-logo-16.png" alt=""
         style="vertical-align: middle; margin-top: -2px" />
  </li>
  <li>
    <a href="https://cmake.org/">CMake</a> &#187;
  </li>
  <li>
    <a href="../index.html">3.28.1 Documentation</a> &#187;
  </li>

          <li class="nav-item nav-item-1"><a href="../manual/cmake-commands.7.html" >cmake-commands(7)</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href="">block</a></li> 
      </ul>
    </div>

    <div class="footer" role="contentinfo">
        &#169; Copyright 2000-2023 Kitware, Inc. and Contributors.
      Created using <a href="https://www.sphinx-doc.org/">Sphinx</a> 6.2.1.
    </div>
  </body>
</html>