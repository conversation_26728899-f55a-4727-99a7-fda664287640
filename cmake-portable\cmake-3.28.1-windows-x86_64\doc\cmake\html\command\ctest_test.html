
<!DOCTYPE html>

<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="generator" content="Docutils 0.19: https://docutils.sourceforge.io/" />

  <title>ctest_test &mdash; CMake 3.28.1 Documentation</title>

    <link rel="stylesheet" type="text/css" href="../_static/pygments.css" />
    <link rel="stylesheet" type="text/css" href="../_static/cmake.css" />
    
    <script data-url_root="../" id="documentation_options" src="../_static/documentation_options.js"></script>
    <script src="../_static/doctools.js"></script>
    <script src="../_static/sphinx_highlight.js"></script>
    
    <link rel="icon" href="../_static/cmake-favicon.ico"/>
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="next" title="ctest_update" href="ctest_update.html" />
    <link rel="prev" title="ctest_submit" href="ctest_submit.html" />
 

  </head><body>

    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             accesskey="I">index</a></li>
        <li class="right" >
          <a href="ctest_update.html" title="ctest_update"
             accesskey="N">next</a> |</li>
        <li class="right" >
          <a href="ctest_submit.html" title="ctest_submit"
             accesskey="P">previous</a> |</li>
  <li>
    <img src="../_static/cmake-logo-16.png" alt=""
         style="vertical-align: middle; margin-top: -2px" />
  </li>
  <li>
    <a href="https://cmake.org/">CMake</a> &#187;
  </li>
  <li>
    <a href="../index.html">3.28.1 Documentation</a> &#187;
  </li>

          <li class="nav-item nav-item-1"><a href="../manual/cmake-commands.7.html" accesskey="U">cmake-commands(7)</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href="">ctest_test</a></li> 
      </ul>
    </div>  

    <div class="document">
      <div class="documentwrapper">
        <div class="bodywrapper">
          <div class="body" role="main">
            
  <section id="ctest-test">
<span id="command:ctest_test"></span><h1>ctest_test<a class="headerlink" href="#ctest-test" title="Permalink to this heading">¶</a></h1>
<p>Perform the <a class="reference internal" href="../manual/ctest.1.html#ctest-test-step"><span class="std std-ref">CTest Test Step</span></a> as a <a class="reference internal" href="../manual/ctest.1.html#dashboard-client"><span class="std std-ref">Dashboard Client</span></a>.</p>
<div class="highlight-cmake notranslate"><div class="highlight"><pre><span></span><span class="nf">ctest_test(</span><span class="p">[</span><span class="no">BUILD</span><span class="w"> </span><span class="nv">&lt;build-dir&gt;</span><span class="p">]</span><span class="w"> </span><span class="p">[</span><span class="no">APPEND</span><span class="p">]</span>
<span class="w">           </span><span class="p">[</span><span class="no">START</span><span class="w"> </span><span class="nv">&lt;start-number&gt;</span><span class="p">]</span>
<span class="w">           </span><span class="p">[</span><span class="no">END</span><span class="w"> </span><span class="nv">&lt;end-number&gt;</span><span class="p">]</span>
<span class="w">           </span><span class="p">[</span><span class="no">STRIDE</span><span class="w"> </span><span class="nv">&lt;stride-number&gt;</span><span class="p">]</span>
<span class="w">           </span><span class="p">[</span><span class="no">EXCLUDE</span><span class="w"> </span><span class="nv">&lt;exclude-regex&gt;</span><span class="p">]</span>
<span class="w">           </span><span class="p">[</span><span class="no">INCLUDE</span><span class="w"> </span><span class="nv">&lt;include-regex&gt;</span><span class="p">]</span>
<span class="w">           </span><span class="p">[</span><span class="no">EXCLUDE_LABEL</span><span class="w"> </span><span class="nv">&lt;label-exclude-regex&gt;</span><span class="p">]</span>
<span class="w">           </span><span class="p">[</span><span class="no">INCLUDE_LABEL</span><span class="w"> </span><span class="nv">&lt;label-include-regex&gt;</span><span class="p">]</span>
<span class="w">           </span><span class="p">[</span><span class="no">EXCLUDE_FIXTURE</span><span class="w"> </span><span class="nv">&lt;regex&gt;</span><span class="p">]</span>
<span class="w">           </span><span class="p">[</span><span class="no">EXCLUDE_FIXTURE_SETUP</span><span class="w"> </span><span class="nv">&lt;regex&gt;</span><span class="p">]</span>
<span class="w">           </span><span class="p">[</span><span class="no">EXCLUDE_FIXTURE_CLEANUP</span><span class="w"> </span><span class="nv">&lt;regex&gt;</span><span class="p">]</span>
<span class="w">           </span><span class="p">[</span><span class="no">PARALLEL_LEVEL</span><span class="w"> </span><span class="nv">&lt;level&gt;</span><span class="p">]</span>
<span class="w">           </span><span class="p">[</span><span class="no">RESOURCE_SPEC_FILE</span><span class="w"> </span><span class="nv">&lt;file&gt;</span><span class="p">]</span>
<span class="w">           </span><span class="p">[</span><span class="no">TEST_LOAD</span><span class="w"> </span><span class="nv">&lt;threshold&gt;</span><span class="p">]</span>
<span class="w">           </span><span class="p">[</span><span class="no">SCHEDULE_RANDOM</span><span class="w"> </span><span class="o">&lt;</span><span class="no">ON</span><span class="p">|</span><span class="no">OFF</span><span class="o">&gt;</span><span class="p">]</span>
<span class="w">           </span><span class="p">[</span><span class="no">STOP_ON_FAILURE</span><span class="p">]</span>
<span class="w">           </span><span class="p">[</span><span class="no">STOP_TIME</span><span class="w"> </span><span class="nv">&lt;time-of-day&gt;</span><span class="p">]</span>
<span class="w">           </span><span class="p">[</span><span class="no">RETURN_VALUE</span><span class="w"> </span><span class="nv">&lt;result-var&gt;</span><span class="p">]</span>
<span class="w">           </span><span class="p">[</span><span class="no">CAPTURE_CMAKE_ERROR</span><span class="w"> </span><span class="nv">&lt;result-var&gt;</span><span class="p">]</span>
<span class="w">           </span><span class="p">[</span><span class="no">REPEAT</span><span class="w"> </span><span class="nv">&lt;mode&gt;</span><span class="o">:</span><span class="nv">&lt;n&gt;</span><span class="p">]</span>
<span class="w">           </span><span class="p">[</span><span class="no">OUTPUT_JUNIT</span><span class="w"> </span><span class="nv">&lt;file&gt;</span><span class="p">]</span>
<span class="w">           </span><span class="p">[</span><span class="no">QUIET</span><span class="p">]</span>
<span class="w">           </span><span class="nf">)</span>
</pre></div>
</div>
<p>Run tests in the project build tree and store results in
<code class="docutils literal notranslate"><span class="pre">Test.xml</span></code> for submission with the <span class="target" id="index-0-command:ctest_submit"></span><a class="reference internal" href="ctest_submit.html#command:ctest_submit" title="ctest_submit"><code class="xref cmake cmake-command docutils literal notranslate"><span class="pre">ctest_submit()</span></code></a> command.</p>
<p>The options are:</p>
<dl>
<dt><code class="docutils literal notranslate"><span class="pre">BUILD</span> <span class="pre">&lt;build-dir&gt;</span></code></dt><dd><p>Specify the top-level build directory.  If not given, the
<span class="target" id="index-0-variable:CTEST_BINARY_DIRECTORY"></span><a class="reference internal" href="../variable/CTEST_BINARY_DIRECTORY.html#variable:CTEST_BINARY_DIRECTORY" title="CTEST_BINARY_DIRECTORY"><code class="xref cmake cmake-variable docutils literal notranslate"><span class="pre">CTEST_BINARY_DIRECTORY</span></code></a> variable is used.</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">APPEND</span></code></dt><dd><p>Mark <code class="docutils literal notranslate"><span class="pre">Test.xml</span></code> for append to results previously submitted to a
dashboard server since the last <span class="target" id="index-0-command:ctest_start"></span><a class="reference internal" href="ctest_start.html#command:ctest_start" title="ctest_start"><code class="xref cmake cmake-command docutils literal notranslate"><span class="pre">ctest_start()</span></code></a> call.
Append semantics are defined by the dashboard server in use.
This does <em>not</em> cause results to be appended to a <code class="docutils literal notranslate"><span class="pre">.xml</span></code> file
produced by a previous call to this command.</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">START</span> <span class="pre">&lt;start-number&gt;</span></code></dt><dd><p>Specify the beginning of a range of test numbers.</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">END</span> <span class="pre">&lt;end-number&gt;</span></code></dt><dd><p>Specify the end of a range of test numbers.</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">STRIDE</span> <span class="pre">&lt;stride-number&gt;</span></code></dt><dd><p>Specify the stride by which to step across a range of test numbers.</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">EXCLUDE</span> <span class="pre">&lt;exclude-regex&gt;</span></code></dt><dd><p>Specify a regular expression matching test names to exclude.</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">INCLUDE</span> <span class="pre">&lt;include-regex&gt;</span></code></dt><dd><p>Specify a regular expression matching test names to include.
Tests not matching this expression are excluded.</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">EXCLUDE_LABEL</span> <span class="pre">&lt;label-exclude-regex&gt;</span></code></dt><dd><p>Specify a regular expression matching test labels to exclude.</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">INCLUDE_LABEL</span> <span class="pre">&lt;label-include-regex&gt;</span></code></dt><dd><p>Specify a regular expression matching test labels to include.
Tests not matching this expression are excluded.</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">EXCLUDE_FIXTURE</span> <span class="pre">&lt;regex&gt;</span></code></dt><dd><div class="versionadded">
<p><span class="versionmodified added">New in version 3.7.</span></p>
</div>
<p>If a test in the set of tests to be executed requires a particular fixture,
that fixture's setup and cleanup tests would normally be added to the test
set automatically. This option prevents adding setup or cleanup tests for
fixtures matching the <code class="docutils literal notranslate"><span class="pre">&lt;regex&gt;</span></code>. Note that all other fixture behavior is
retained, including test dependencies and skipping tests that have fixture
setup tests that fail.</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">EXCLUDE_FIXTURE_SETUP</span> <span class="pre">&lt;regex&gt;</span></code></dt><dd><div class="versionadded">
<p><span class="versionmodified added">New in version 3.7.</span></p>
</div>
<p>Same as <code class="docutils literal notranslate"><span class="pre">EXCLUDE_FIXTURE</span></code> except only matching setup tests are excluded.</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">EXCLUDE_FIXTURE_CLEANUP</span> <span class="pre">&lt;regex&gt;</span></code></dt><dd><div class="versionadded">
<p><span class="versionmodified added">New in version 3.7.</span></p>
</div>
<p>Same as <code class="docutils literal notranslate"><span class="pre">EXCLUDE_FIXTURE</span></code> except only matching cleanup tests are excluded.</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">PARALLEL_LEVEL</span> <span class="pre">&lt;level&gt;</span></code></dt><dd><p>Specify a positive number representing the number of tests to
be run in parallel.</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">RESOURCE_SPEC_FILE</span> <span class="pre">&lt;file&gt;</span></code></dt><dd><div class="versionadded">
<p><span class="versionmodified added">New in version 3.16.</span></p>
</div>
<p>Specify a
<a class="reference internal" href="../manual/ctest.1.html#ctest-resource-specification-file"><span class="std std-ref">resource specification file</span></a>. See
<a class="reference internal" href="../manual/ctest.1.html#ctest-resource-allocation"><span class="std std-ref">Resource Allocation</span></a> for more information.</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">TEST_LOAD</span> <span class="pre">&lt;threshold&gt;</span></code></dt><dd><div class="versionadded">
<p><span class="versionmodified added">New in version 3.4.</span></p>
</div>
<p>While running tests in parallel, try not to start tests when they
may cause the CPU load to pass above a given threshold.  If not
specified the <span class="target" id="index-0-variable:CTEST_TEST_LOAD"></span><a class="reference internal" href="../variable/CTEST_TEST_LOAD.html#variable:CTEST_TEST_LOAD" title="CTEST_TEST_LOAD"><code class="xref cmake cmake-variable docutils literal notranslate"><span class="pre">CTEST_TEST_LOAD</span></code></a> variable will be checked,
and then the <a class="reference internal" href="../manual/ctest.1.html#cmdoption-ctest-test-load"><code class="xref std std-option docutils literal notranslate"><span class="pre">--test-load</span></code></a> command-line
argument to <span class="target" id="index-0-manual:ctest(1)"></span><a class="reference internal" href="../manual/ctest.1.html#manual:ctest(1)" title="ctest(1)"><code class="xref cmake cmake-manual docutils literal notranslate"><span class="pre">ctest(1)</span></code></a>. See also the <code class="docutils literal notranslate"><span class="pre">TestLoad</span></code> setting
in the <a class="reference internal" href="../manual/ctest.1.html#ctest-test-step"><span class="std std-ref">CTest Test Step</span></a>.</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">REPEAT</span> <span class="pre">&lt;mode&gt;:&lt;n&gt;</span></code></dt><dd><div class="versionadded">
<p><span class="versionmodified added">New in version 3.17.</span></p>
</div>
<p>Run tests repeatedly based on the given <code class="docutils literal notranslate"><span class="pre">&lt;mode&gt;</span></code> up to <code class="docutils literal notranslate"><span class="pre">&lt;n&gt;</span></code> times.
The modes are:</p>
<dl class="simple">
<dt><code class="docutils literal notranslate"><span class="pre">UNTIL_FAIL</span></code></dt><dd><p>Require each test to run <code class="docutils literal notranslate"><span class="pre">&lt;n&gt;</span></code> times without failing in order to pass.
This is useful in finding sporadic failures in test cases.</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">UNTIL_PASS</span></code></dt><dd><p>Allow each test to run up to <code class="docutils literal notranslate"><span class="pre">&lt;n&gt;</span></code> times in order to pass.
Repeats tests if they fail for any reason.
This is useful in tolerating sporadic failures in test cases.</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">AFTER_TIMEOUT</span></code></dt><dd><p>Allow each test to run up to <code class="docutils literal notranslate"><span class="pre">&lt;n&gt;</span></code> times in order to pass.
Repeats tests only if they timeout.
This is useful in tolerating sporadic timeouts in test cases
on busy machines.</p>
</dd>
</dl>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">SCHEDULE_RANDOM</span> <span class="pre">&lt;ON|OFF&gt;</span></code></dt><dd><p>Launch tests in a random order.  This may be useful for detecting
implicit test dependencies.</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">STOP_ON_FAILURE</span></code></dt><dd><div class="versionadded">
<p><span class="versionmodified added">New in version 3.18.</span></p>
</div>
<p>Stop the execution of the tests once one has failed.</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">STOP_TIME</span> <span class="pre">&lt;time-of-day&gt;</span></code></dt><dd><p>Specify a time of day at which the tests should all stop running.</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">RETURN_VALUE</span> <span class="pre">&lt;result-var&gt;</span></code></dt><dd><p>Store in the <code class="docutils literal notranslate"><span class="pre">&lt;result-var&gt;</span></code> variable <code class="docutils literal notranslate"><span class="pre">0</span></code> if all tests passed.
Store non-zero if anything went wrong.</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">CAPTURE_CMAKE_ERROR</span> <span class="pre">&lt;result-var&gt;</span></code></dt><dd><div class="versionadded">
<p><span class="versionmodified added">New in version 3.7.</span></p>
</div>
<p>Store in the <code class="docutils literal notranslate"><span class="pre">&lt;result-var&gt;</span></code> variable -1 if there are any errors running
the command and prevent ctest from returning non-zero if an error occurs.</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">OUTPUT_JUNIT</span> <span class="pre">&lt;file&gt;</span></code></dt><dd><div class="versionadded">
<p><span class="versionmodified added">New in version 3.21.</span></p>
</div>
<p>Write test results to <code class="docutils literal notranslate"><span class="pre">&lt;file&gt;</span></code> in JUnit XML format. If <code class="docutils literal notranslate"><span class="pre">&lt;file&gt;</span></code> is a
relative path, it will be placed in the build directory. If <code class="docutils literal notranslate"><span class="pre">&lt;file&gt;</span></code>
already exists, it will be overwritten. Note that the resulting JUnit XML
file is <strong>not</strong> uploaded to CDash because it would be redundant with
CTest's <code class="docutils literal notranslate"><span class="pre">Test.xml</span></code> file.</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">QUIET</span></code></dt><dd><div class="versionadded">
<p><span class="versionmodified added">New in version 3.3.</span></p>
</div>
<p>Suppress any CTest-specific non-error messages that would have otherwise
been printed to the console.  Output from the underlying test command is not
affected.  Summary info detailing the percentage of passing tests is also
unaffected by the <code class="docutils literal notranslate"><span class="pre">QUIET</span></code> option.</p>
</dd>
</dl>
<p>See also the <span class="target" id="index-0-variable:CTEST_CUSTOM_MAXIMUM_PASSED_TEST_OUTPUT_SIZE"></span><a class="reference internal" href="../variable/CTEST_CUSTOM_MAXIMUM_PASSED_TEST_OUTPUT_SIZE.html#variable:CTEST_CUSTOM_MAXIMUM_PASSED_TEST_OUTPUT_SIZE" title="CTEST_CUSTOM_MAXIMUM_PASSED_TEST_OUTPUT_SIZE"><code class="xref cmake cmake-variable docutils literal notranslate"><span class="pre">CTEST_CUSTOM_MAXIMUM_PASSED_TEST_OUTPUT_SIZE</span></code></a>,
<span class="target" id="index-0-variable:CTEST_CUSTOM_MAXIMUM_FAILED_TEST_OUTPUT_SIZE"></span><a class="reference internal" href="../variable/CTEST_CUSTOM_MAXIMUM_FAILED_TEST_OUTPUT_SIZE.html#variable:CTEST_CUSTOM_MAXIMUM_FAILED_TEST_OUTPUT_SIZE" title="CTEST_CUSTOM_MAXIMUM_FAILED_TEST_OUTPUT_SIZE"><code class="xref cmake cmake-variable docutils literal notranslate"><span class="pre">CTEST_CUSTOM_MAXIMUM_FAILED_TEST_OUTPUT_SIZE</span></code></a> and
<span class="target" id="index-0-variable:CTEST_CUSTOM_TEST_OUTPUT_TRUNCATION"></span><a class="reference internal" href="../variable/CTEST_CUSTOM_TEST_OUTPUT_TRUNCATION.html#variable:CTEST_CUSTOM_TEST_OUTPUT_TRUNCATION" title="CTEST_CUSTOM_TEST_OUTPUT_TRUNCATION"><code class="xref cmake cmake-variable docutils literal notranslate"><span class="pre">CTEST_CUSTOM_TEST_OUTPUT_TRUNCATION</span></code></a> variables, along with their
corresponding <span class="target" id="index-1-manual:ctest(1)"></span><a class="reference internal" href="../manual/ctest.1.html#manual:ctest(1)" title="ctest(1)"><code class="xref cmake cmake-manual docutils literal notranslate"><span class="pre">ctest(1)</span></code></a> command line options
<a class="reference internal" href="../manual/ctest.1.html#cmdoption-ctest-test-output-size-passed"><code class="xref std std-option docutils literal notranslate"><span class="pre">--test-output-size-passed</span></code></a>,
<a class="reference internal" href="../manual/ctest.1.html#cmdoption-ctest-test-output-size-failed"><code class="xref std std-option docutils literal notranslate"><span class="pre">--test-output-size-failed</span></code></a>, and
<a class="reference internal" href="../manual/ctest.1.html#cmdoption-ctest-test-output-truncation"><code class="xref std std-option docutils literal notranslate"><span class="pre">--test-output-truncation</span></code></a>.</p>
<section id="additional-test-measurements">
<span id="id1"></span><h2>Additional Test Measurements<a class="headerlink" href="#additional-test-measurements" title="Permalink to this heading">¶</a></h2>
<p>CTest can parse the output of your tests for extra measurements to report
to CDash.</p>
<p>When run as a <a class="reference internal" href="../manual/ctest.1.html#dashboard-client"><span class="std std-ref">Dashboard Client</span></a>, CTest will include these custom
measurements in the <code class="docutils literal notranslate"><span class="pre">Test.xml</span></code> file that gets uploaded to CDash.</p>
<p>Check the <a class="reference external" href="https://github.com/Kitware/CDash/blob/master/docs/test_measurements.md">CDash test measurement documentation</a>
for more information on the types of test measurements that CDash recognizes.</p>
<p>The following example demonstrates how to output a variety of custom test
measurements.</p>
<div class="highlight-c++ notranslate"><div class="highlight"><pre><span></span><span class="n">std</span><span class="o">::</span><span class="n">cout</span><span class="w"> </span><span class="o">&lt;&lt;</span>
<span class="w">  </span><span class="s">&quot;&lt;CTestMeasurement type=</span><span class="se">\&quot;</span><span class="s">numeric/double</span><span class="se">\&quot;</span><span class="s"> name=</span><span class="se">\&quot;</span><span class="s">score</span><span class="se">\&quot;</span><span class="s">&gt;28.3&lt;/CTestMeasurement&gt;&quot;</span>
<span class="w">  </span><span class="o">&lt;&lt;</span><span class="w"> </span><span class="n">std</span><span class="o">::</span><span class="n">endl</span><span class="p">;</span>

<span class="n">std</span><span class="o">::</span><span class="n">cout</span><span class="w"> </span><span class="o">&lt;&lt;</span>
<span class="w">  </span><span class="s">&quot;&lt;CTestMeasurement type=</span><span class="se">\&quot;</span><span class="s">text/string</span><span class="se">\&quot;</span><span class="s"> name=</span><span class="se">\&quot;</span><span class="s">color</span><span class="se">\&quot;</span><span class="s">&gt;red&lt;/CTestMeasurement&gt;&quot;</span>
<span class="w">  </span><span class="o">&lt;&lt;</span><span class="w"> </span><span class="n">std</span><span class="o">::</span><span class="n">endl</span><span class="p">;</span>

<span class="n">std</span><span class="o">::</span><span class="n">cout</span><span class="w"> </span><span class="o">&lt;&lt;</span>
<span class="w">  </span><span class="s">&quot;&lt;CTestMeasurement type=</span><span class="se">\&quot;</span><span class="s">text/link</span><span class="se">\&quot;</span><span class="s"> name=</span><span class="se">\&quot;</span><span class="s">CMake URL</span><span class="se">\&quot;</span><span class="s">&gt;https://cmake.org&lt;/CTestMeasurement&gt;&quot;</span>
<span class="w">  </span><span class="o">&lt;&lt;</span><span class="w"> </span><span class="n">std</span><span class="o">::</span><span class="n">endl</span><span class="p">;</span>

<span class="n">std</span><span class="o">::</span><span class="n">cout</span><span class="w"> </span><span class="o">&lt;&lt;</span>
<span class="w">  </span><span class="s">&quot;&lt;CTestMeasurement type=</span><span class="se">\&quot;</span><span class="s">text/preformatted</span><span class="se">\&quot;</span><span class="s"> name=</span><span class="se">\&quot;</span><span class="s">Console Output</span><span class="se">\&quot;</span><span class="s">&gt;&quot;</span><span class="w"> </span><span class="o">&lt;&lt;</span>
<span class="w">  </span><span class="s">&quot;line 1.</span><span class="se">\n</span><span class="s">&quot;</span><span class="w"> </span><span class="o">&lt;&lt;</span>
<span class="w">  </span><span class="s">&quot;  </span><span class="se">\033</span><span class="s">[31;1m line 2. Bold red, and indented!</span><span class="se">\033</span><span class="s">[0;0ml</span><span class="se">\n</span><span class="s">&quot;</span><span class="w"> </span><span class="o">&lt;&lt;</span>
<span class="w">  </span><span class="s">&quot;line 3. Not bold or indented...</span><span class="se">\n</span><span class="s">&quot;</span><span class="w"> </span><span class="o">&lt;&lt;</span>
<span class="w">  </span><span class="s">&quot;&lt;/CTestMeasurement&gt;&quot;</span><span class="w"> </span><span class="o">&lt;&lt;</span><span class="w"> </span><span class="n">std</span><span class="o">::</span><span class="n">endl</span><span class="p">;</span>
</pre></div>
</div>
<section id="image-measurements">
<h3>Image Measurements<a class="headerlink" href="#image-measurements" title="Permalink to this heading">¶</a></h3>
<p>The following example demonstrates how to upload test images to CDash.</p>
<div class="highlight-c++ notranslate"><div class="highlight"><pre><span></span><span class="n">std</span><span class="o">::</span><span class="n">cout</span><span class="w"> </span><span class="o">&lt;&lt;</span>
<span class="w">  </span><span class="s">&quot;&lt;CTestMeasurementFile type=</span><span class="se">\&quot;</span><span class="s">image/jpg</span><span class="se">\&quot;</span><span class="s"> name=</span><span class="se">\&quot;</span><span class="s">TestImage</span><span class="se">\&quot;</span><span class="s">&gt;&quot;</span><span class="w"> </span><span class="o">&lt;&lt;</span>
<span class="w">  </span><span class="s">&quot;/dir/to/test_img.jpg&lt;/CTestMeasurementFile&gt;&quot;</span><span class="w"> </span><span class="o">&lt;&lt;</span><span class="w"> </span><span class="n">std</span><span class="o">::</span><span class="n">endl</span><span class="p">;</span>

<span class="n">std</span><span class="o">::</span><span class="n">cout</span><span class="w"> </span><span class="o">&lt;&lt;</span>
<span class="w">  </span><span class="s">&quot;&lt;CTestMeasurementFile type=</span><span class="se">\&quot;</span><span class="s">image/gif</span><span class="se">\&quot;</span><span class="s"> name=</span><span class="se">\&quot;</span><span class="s">ValidImage</span><span class="se">\&quot;</span><span class="s">&gt;&quot;</span><span class="w"> </span><span class="o">&lt;&lt;</span>
<span class="w">  </span><span class="s">&quot;/dir/to/valid_img.gif&lt;/CTestMeasurementFile&gt;&quot;</span><span class="w"> </span><span class="o">&lt;&lt;</span><span class="w"> </span><span class="n">std</span><span class="o">::</span><span class="n">endl</span><span class="p">;</span>

<span class="n">std</span><span class="o">::</span><span class="n">cout</span><span class="w"> </span><span class="o">&lt;&lt;</span>
<span class="w">  </span><span class="s">&quot;&lt;CTestMeasurementFile type=</span><span class="se">\&quot;</span><span class="s">image/png</span><span class="se">\&quot;</span><span class="s"> name=</span><span class="se">\&quot;</span><span class="s">AlgoResult</span><span class="se">\&quot;</span><span class="s">&gt;&quot;</span><span class="w"> </span><span class="o">&lt;&lt;</span>
<span class="w">  </span><span class="s">&quot;/dir/to/img.png&lt;/CTestMeasurementFile&gt;&quot;</span>
<span class="w">  </span><span class="o">&lt;&lt;</span><span class="w"> </span><span class="n">std</span><span class="o">::</span><span class="n">endl</span><span class="p">;</span>
</pre></div>
</div>
<p>Images will be displayed together in an interactive comparison mode on CDash
if they are provided with two or more of the following names.</p>
<ul class="simple">
<li><p><code class="docutils literal notranslate"><span class="pre">TestImage</span></code></p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">ValidImage</span></code></p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">BaselineImage</span></code></p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">DifferenceImage2</span></code></p></li>
</ul>
<p>By convention, <code class="docutils literal notranslate"><span class="pre">TestImage</span></code> is the image generated by your test, and
<code class="docutils literal notranslate"><span class="pre">ValidImage</span></code> (or <code class="docutils literal notranslate"><span class="pre">BaselineImage</span></code>) is basis of comparison used to determine
if the test passed or failed.</p>
<p>If another image name is used it will be displayed by CDash as a static image
separate from the interactive comparison UI.</p>
</section>
<section id="attached-files">
<h3>Attached Files<a class="headerlink" href="#attached-files" title="Permalink to this heading">¶</a></h3>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.21.</span></p>
</div>
<p>The following example demonstrates how to upload non-image files to CDash.</p>
<div class="highlight-c++ notranslate"><div class="highlight"><pre><span></span><span class="n">std</span><span class="o">::</span><span class="n">cout</span><span class="w"> </span><span class="o">&lt;&lt;</span>
<span class="w">  </span><span class="s">&quot;&lt;CTestMeasurementFile type=</span><span class="se">\&quot;</span><span class="s">file</span><span class="se">\&quot;</span><span class="s"> name=</span><span class="se">\&quot;</span><span class="s">TestInputData1</span><span class="se">\&quot;</span><span class="s">&gt;&quot;</span><span class="w"> </span><span class="o">&lt;&lt;</span>
<span class="w">  </span><span class="s">&quot;/dir/to/data1.csv&lt;/CTestMeasurementFile&gt;</span><span class="se">\n</span><span class="s">&quot;</span><span class="w">                   </span><span class="o">&lt;&lt;</span>
<span class="w">  </span><span class="s">&quot;&lt;CTestMeasurementFile type=</span><span class="se">\&quot;</span><span class="s">file</span><span class="se">\&quot;</span><span class="s"> name=</span><span class="se">\&quot;</span><span class="s">TestInputData2</span><span class="se">\&quot;</span><span class="s">&gt;&quot;</span><span class="w"> </span><span class="o">&lt;&lt;</span>
<span class="w">  </span><span class="s">&quot;/dir/to/data2.csv&lt;/CTestMeasurementFile&gt;&quot;</span><span class="w">                     </span><span class="o">&lt;&lt;</span><span class="w"> </span><span class="n">std</span><span class="o">::</span><span class="n">endl</span><span class="p">;</span>
</pre></div>
</div>
<p>If the name of the file to upload is known at configure time, you can use the
<span class="target" id="index-0-prop_test:ATTACHED_FILES"></span><a class="reference internal" href="../prop_test/ATTACHED_FILES.html#prop_test:ATTACHED_FILES" title="ATTACHED_FILES"><code class="xref cmake cmake-prop_test docutils literal notranslate"><span class="pre">ATTACHED_FILES</span></code></a> or <span class="target" id="index-0-prop_test:ATTACHED_FILES_ON_FAIL"></span><a class="reference internal" href="../prop_test/ATTACHED_FILES_ON_FAIL.html#prop_test:ATTACHED_FILES_ON_FAIL" title="ATTACHED_FILES_ON_FAIL"><code class="xref cmake cmake-prop_test docutils literal notranslate"><span class="pre">ATTACHED_FILES_ON_FAIL</span></code></a> test
properties instead.</p>
</section>
<section id="custom-details">
<h3>Custom Details<a class="headerlink" href="#custom-details" title="Permalink to this heading">¶</a></h3>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.21.</span></p>
</div>
<p>The following example demonstrates how to specify a custom value for the
<code class="docutils literal notranslate"><span class="pre">Test</span> <span class="pre">Details</span></code> field displayed on CDash.</p>
<div class="highlight-c++ notranslate"><div class="highlight"><pre><span></span><span class="n">std</span><span class="o">::</span><span class="n">cout</span><span class="w"> </span><span class="o">&lt;&lt;</span>
<span class="w">  </span><span class="s">&quot;&lt;CTestDetails&gt;My Custom Details Value&lt;/CTestDetails&gt;&quot;</span><span class="w"> </span><span class="o">&lt;&lt;</span><span class="w"> </span><span class="n">std</span><span class="o">::</span><span class="n">endl</span><span class="p">;</span>
</pre></div>
</div>
</section>
<section id="additional-labels">
<span id="id2"></span><h3>Additional Labels<a class="headerlink" href="#additional-labels" title="Permalink to this heading">¶</a></h3>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.22.</span></p>
</div>
<p>The following example demonstrates how to add additional labels to a test
at runtime.</p>
<div class="highlight-c++ notranslate"><div class="highlight"><pre><span></span><span class="n">std</span><span class="o">::</span><span class="n">cout</span><span class="w"> </span><span class="o">&lt;&lt;</span>
<span class="w">  </span><span class="s">&quot;&lt;CTestLabel&gt;Custom Label 1&lt;/CTestLabel&gt;</span><span class="se">\n</span><span class="s">&quot;</span><span class="w"> </span><span class="o">&lt;&lt;</span>
<span class="w">  </span><span class="s">&quot;&lt;CTestLabel&gt;Custom Label 2&lt;/CTestLabel&gt;&quot;</span><span class="w">   </span><span class="o">&lt;&lt;</span><span class="w"> </span><span class="n">std</span><span class="o">::</span><span class="n">endl</span><span class="p">;</span>
</pre></div>
</div>
<p>Use the <span class="target" id="index-0-prop_test:LABELS"></span><a class="reference internal" href="../prop_test/LABELS.html#prop_test:LABELS" title="LABELS"><code class="xref cmake cmake-prop_test docutils literal notranslate"><span class="pre">LABELS</span></code></a> test property instead for labels that can be
determined at configure time.</p>
</section>
</section>
</section>


            <div class="clearer"></div>
          </div>
        </div>
      </div>
      <div class="sphinxsidebar" role="navigation" aria-label="main navigation">
        <div class="sphinxsidebarwrapper">
  <div>
    <h3><a href="../index.html">Table of Contents</a></h3>
    <ul>
<li><a class="reference internal" href="#">ctest_test</a><ul>
<li><a class="reference internal" href="#additional-test-measurements">Additional Test Measurements</a><ul>
<li><a class="reference internal" href="#image-measurements">Image Measurements</a></li>
<li><a class="reference internal" href="#attached-files">Attached Files</a></li>
<li><a class="reference internal" href="#custom-details">Custom Details</a></li>
<li><a class="reference internal" href="#additional-labels">Additional Labels</a></li>
</ul>
</li>
</ul>
</li>
</ul>

  </div>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="ctest_submit.html"
                          title="previous chapter">ctest_submit</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="ctest_update.html"
                          title="next chapter">ctest_update</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../_sources/command/ctest_test.rst.txt"
            rel="nofollow">Show Source</a></li>
    </ul>
   </div>
<div id="searchbox" style="display: none" role="search">
  <h3 id="searchlabel">Quick search</h3>
    <div class="searchformwrapper">
    <form class="search" action="../search.html" method="get">
      <input type="text" name="q" aria-labelledby="searchlabel" autocomplete="off" autocorrect="off" autocapitalize="off" spellcheck="false"/>
      <input type="submit" value="Go" />
    </form>
    </div>
</div>
<script>document.getElementById('searchbox').style.display = "block"</script>
        </div>
      </div>
      <div class="clearer"></div>
    </div>
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             >index</a></li>
        <li class="right" >
          <a href="ctest_update.html" title="ctest_update"
             >next</a> |</li>
        <li class="right" >
          <a href="ctest_submit.html" title="ctest_submit"
             >previous</a> |</li>
  <li>
    <img src="../_static/cmake-logo-16.png" alt=""
         style="vertical-align: middle; margin-top: -2px" />
  </li>
  <li>
    <a href="https://cmake.org/">CMake</a> &#187;
  </li>
  <li>
    <a href="../index.html">3.28.1 Documentation</a> &#187;
  </li>

          <li class="nav-item nav-item-1"><a href="../manual/cmake-commands.7.html" >cmake-commands(7)</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href="">ctest_test</a></li> 
      </ul>
    </div>

    <div class="footer" role="contentinfo">
        &#169; Copyright 2000-2023 Kitware, Inc. and Contributors.
      Created using <a href="https://www.sphinx-doc.org/">Sphinx</a> 6.2.1.
    </div>
  </body>
</html>