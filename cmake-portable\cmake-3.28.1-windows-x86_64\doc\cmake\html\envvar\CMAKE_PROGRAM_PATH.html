
<!DOCTYPE html>

<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="generator" content="Docutils 0.19: https://docutils.sourceforge.io/" />

  <title>CMAKE_PROGRAM_PATH &mdash; CMake 3.28.1 Documentation</title>

    <link rel="stylesheet" type="text/css" href="../_static/pygments.css" />
    <link rel="stylesheet" type="text/css" href="../_static/cmake.css" />
    
    <script data-url_root="../" id="documentation_options" src="../_static/documentation_options.js"></script>
    <script src="../_static/doctools.js"></script>
    <script src="../_static/sphinx_highlight.js"></script>
    
    <link rel="icon" href="../_static/cmake-favicon.ico"/>
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="next" title="SSL_CERT_DIR" href="SSL_CERT_DIR.html" />
    <link rel="prev" title="CMAKE_PREFIX_PATH" href="CMAKE_PREFIX_PATH.html" />
 

  </head><body>

    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             accesskey="I">index</a></li>
        <li class="right" >
          <a href="SSL_CERT_DIR.html" title="SSL_CERT_DIR"
             accesskey="N">next</a> |</li>
        <li class="right" >
          <a href="CMAKE_PREFIX_PATH.html" title="CMAKE_PREFIX_PATH"
             accesskey="P">previous</a> |</li>
  <li>
    <img src="../_static/cmake-logo-16.png" alt=""
         style="vertical-align: middle; margin-top: -2px" />
  </li>
  <li>
    <a href="https://cmake.org/">CMake</a> &#187;
  </li>
  <li>
    <a href="../index.html">3.28.1 Documentation</a> &#187;
  </li>

          <li class="nav-item nav-item-1"><a href="../manual/cmake-env-variables.7.html" accesskey="U">cmake-env-variables(7)</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href="">CMAKE_PROGRAM_PATH</a></li> 
      </ul>
    </div>  

    <div class="document">
      <div class="documentwrapper">
        <div class="bodywrapper">
          <div class="body" role="main">
            
  <section id="cmake-program-path">
<span id="envvar:CMAKE_PROGRAM_PATH"></span><h1>CMAKE_PROGRAM_PATH<a class="headerlink" href="#cmake-program-path" title="Permalink to this heading">¶</a></h1>
<p>This is a CMake <a class="reference internal" href="../manual/cmake-language.7.html#cmake-language-environment-variables"><span class="std std-ref">Environment Variable</span></a>. Its initial value is taken from
the calling process environment.</p>
<p>The <code class="docutils literal notranslate"><span class="pre">CMAKE_PROGRAM_PATH</span></code> environment variable may be set to a list of
directories to be searched by the <span class="target" id="index-0-command:find_program"></span><a class="reference internal" href="../command/find_program.html#command:find_program" title="find_program"><code class="xref cmake cmake-command docutils literal notranslate"><span class="pre">find_program()</span></code></a> command.</p>
<p>This variable may hold a single directory or a list of directories separated
by <code class="docutils literal notranslate"><span class="pre">:</span></code> on UNIX or <code class="docutils literal notranslate"><span class="pre">;</span></code> on Windows (the same as the <code class="docutils literal notranslate"><span class="pre">PATH</span></code> environment
variable convention on those platforms).</p>
<p>See also the <span class="target" id="index-0-variable:CMAKE_PROGRAM_PATH"></span><a class="reference internal" href="../variable/CMAKE_PROGRAM_PATH.html#variable:CMAKE_PROGRAM_PATH" title="CMAKE_PROGRAM_PATH"><code class="xref cmake cmake-variable docutils literal notranslate"><span class="pre">CMAKE_PROGRAM_PATH</span></code></a> CMake variable.</p>
</section>


            <div class="clearer"></div>
          </div>
        </div>
      </div>
      <div class="sphinxsidebar" role="navigation" aria-label="main navigation">
        <div class="sphinxsidebarwrapper">
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="CMAKE_PREFIX_PATH.html"
                          title="previous chapter">CMAKE_PREFIX_PATH</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="SSL_CERT_DIR.html"
                          title="next chapter">SSL_CERT_DIR</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../_sources/envvar/CMAKE_PROGRAM_PATH.rst.txt"
            rel="nofollow">Show Source</a></li>
    </ul>
   </div>
<div id="searchbox" style="display: none" role="search">
  <h3 id="searchlabel">Quick search</h3>
    <div class="searchformwrapper">
    <form class="search" action="../search.html" method="get">
      <input type="text" name="q" aria-labelledby="searchlabel" autocomplete="off" autocorrect="off" autocapitalize="off" spellcheck="false"/>
      <input type="submit" value="Go" />
    </form>
    </div>
</div>
<script>document.getElementById('searchbox').style.display = "block"</script>
        </div>
      </div>
      <div class="clearer"></div>
    </div>
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             >index</a></li>
        <li class="right" >
          <a href="SSL_CERT_DIR.html" title="SSL_CERT_DIR"
             >next</a> |</li>
        <li class="right" >
          <a href="CMAKE_PREFIX_PATH.html" title="CMAKE_PREFIX_PATH"
             >previous</a> |</li>
  <li>
    <img src="../_static/cmake-logo-16.png" alt=""
         style="vertical-align: middle; margin-top: -2px" />
  </li>
  <li>
    <a href="https://cmake.org/">CMake</a> &#187;
  </li>
  <li>
    <a href="../index.html">3.28.1 Documentation</a> &#187;
  </li>

          <li class="nav-item nav-item-1"><a href="../manual/cmake-env-variables.7.html" >cmake-env-variables(7)</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href="">CMAKE_PROGRAM_PATH</a></li> 
      </ul>
    </div>

    <div class="footer" role="contentinfo">
        &#169; Copyright 2000-2023 Kitware, Inc. and Contributors.
      Created using <a href="https://www.sphinx-doc.org/">Sphinx</a> 6.2.1.
    </div>
  </body>
</html>