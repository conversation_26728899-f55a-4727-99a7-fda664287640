{"permissions": {"allow": ["Bash(find:*)", "<PERSON><PERSON>(python3:*)", "<PERSON><PERSON>(source:*)", "Bash(ls:*)", "Bash(./venv_new/Scripts/python.exe whip_server_with_ai.py --host 0.0.0.0 --port 8089 --verbose)", "<PERSON><PERSON>(pkill:*)", "Bash(rm:*)", "<PERSON><PERSON>(apt:*)", "Bash(apt install:*)", "<PERSON><PERSON>(sudo apt:*)", "Bash(sudo apt install:*)", "<PERSON><PERSON>(python:*)", "Bash(./venv_new/Scripts/python:*)", "<PERSON><PERSON>(taskkill:*)", "Bash(./venv_new/Scripts/pip.exe list)", "Bash(cmd.exe:*)", "Bash(grep:*)", "<PERSON><PERSON>(timeout:*)"], "deny": []}}