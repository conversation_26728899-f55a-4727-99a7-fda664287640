
<!DOCTYPE html>

<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="generator" content="Docutils 0.19: https://docutils.sourceforge.io/" />

  <title>CTEST_PROGRESS_OUTPUT &mdash; CMake 3.28.1 Documentation</title>

    <link rel="stylesheet" type="text/css" href="../_static/pygments.css" />
    <link rel="stylesheet" type="text/css" href="../_static/cmake.css" />
    
    <script data-url_root="../" id="documentation_options" src="../_static/documentation_options.js"></script>
    <script src="../_static/doctools.js"></script>
    <script src="../_static/sphinx_highlight.js"></script>
    
    <link rel="icon" href="../_static/cmake-favicon.ico"/>
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="next" title="CTEST_USE_LAUNCHERS_DEFAULT" href="CTEST_USE_LAUNCHERS_DEFAULT.html" />
    <link rel="prev" title="CTEST_PARALLEL_LEVEL" href="CTEST_PARALLEL_LEVEL.html" />
 

  </head><body>

    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             accesskey="I">index</a></li>
        <li class="right" >
          <a href="CTEST_USE_LAUNCHERS_DEFAULT.html" title="CTEST_USE_LAUNCHERS_DEFAULT"
             accesskey="N">next</a> |</li>
        <li class="right" >
          <a href="CTEST_PARALLEL_LEVEL.html" title="CTEST_PARALLEL_LEVEL"
             accesskey="P">previous</a> |</li>
  <li>
    <img src="../_static/cmake-logo-16.png" alt=""
         style="vertical-align: middle; margin-top: -2px" />
  </li>
  <li>
    <a href="https://cmake.org/">CMake</a> &#187;
  </li>
  <li>
    <a href="../index.html">3.28.1 Documentation</a> &#187;
  </li>

          <li class="nav-item nav-item-1"><a href="../manual/cmake-env-variables.7.html" accesskey="U">cmake-env-variables(7)</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href="">CTEST_PROGRESS_OUTPUT</a></li> 
      </ul>
    </div>  

    <div class="document">
      <div class="documentwrapper">
        <div class="bodywrapper">
          <div class="body" role="main">
            
  <section id="ctest-progress-output">
<span id="envvar:CTEST_PROGRESS_OUTPUT"></span><h1>CTEST_PROGRESS_OUTPUT<a class="headerlink" href="#ctest-progress-output" title="Permalink to this heading">¶</a></h1>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.13.</span></p>
</div>
<p>This is a CMake <a class="reference internal" href="../manual/cmake-language.7.html#cmake-language-environment-variables"><span class="std std-ref">Environment Variable</span></a>. Its initial value is taken from
the calling process environment.</p>
<p>Boolean environment variable that affects how <span class="target" id="index-0-manual:ctest(1)"></span><a class="reference internal" href="../manual/ctest.1.html#manual:ctest(1)" title="ctest(1)"><code class="xref cmake cmake-manual docutils literal notranslate"><span class="pre">ctest</span></code></a>
command output reports overall progress.  When set to <code class="docutils literal notranslate"><span class="pre">1</span></code>, <code class="docutils literal notranslate"><span class="pre">TRUE</span></code>, <code class="docutils literal notranslate"><span class="pre">ON</span></code> or anything
else that evaluates to boolean true, progress is reported by repeatedly
updating the same line.  This greatly reduces the overall verbosity, but is
only supported when output is sent directly to a terminal.  If the environment
variable is not set or has a value that evaluates to false, output is reported
normally with each test having its own start and end lines logged to the
output.</p>
<p>The <a class="reference internal" href="../manual/ctest.1.html#cmdoption-ctest-progress"><code class="xref std std-option docutils literal notranslate"><span class="pre">--progress</span></code></a> option to <span class="target" id="index-1-manual:ctest(1)"></span><a class="reference internal" href="../manual/ctest.1.html#manual:ctest(1)" title="ctest(1)"><code class="xref cmake cmake-manual docutils literal notranslate"><span class="pre">ctest</span></code></a>
overrides this environment variable if both are given.</p>
</section>


            <div class="clearer"></div>
          </div>
        </div>
      </div>
      <div class="sphinxsidebar" role="navigation" aria-label="main navigation">
        <div class="sphinxsidebarwrapper">
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="CTEST_PARALLEL_LEVEL.html"
                          title="previous chapter">CTEST_PARALLEL_LEVEL</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="CTEST_USE_LAUNCHERS_DEFAULT.html"
                          title="next chapter">CTEST_USE_LAUNCHERS_DEFAULT</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../_sources/envvar/CTEST_PROGRESS_OUTPUT.rst.txt"
            rel="nofollow">Show Source</a></li>
    </ul>
   </div>
<div id="searchbox" style="display: none" role="search">
  <h3 id="searchlabel">Quick search</h3>
    <div class="searchformwrapper">
    <form class="search" action="../search.html" method="get">
      <input type="text" name="q" aria-labelledby="searchlabel" autocomplete="off" autocorrect="off" autocapitalize="off" spellcheck="false"/>
      <input type="submit" value="Go" />
    </form>
    </div>
</div>
<script>document.getElementById('searchbox').style.display = "block"</script>
        </div>
      </div>
      <div class="clearer"></div>
    </div>
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             >index</a></li>
        <li class="right" >
          <a href="CTEST_USE_LAUNCHERS_DEFAULT.html" title="CTEST_USE_LAUNCHERS_DEFAULT"
             >next</a> |</li>
        <li class="right" >
          <a href="CTEST_PARALLEL_LEVEL.html" title="CTEST_PARALLEL_LEVEL"
             >previous</a> |</li>
  <li>
    <img src="../_static/cmake-logo-16.png" alt=""
         style="vertical-align: middle; margin-top: -2px" />
  </li>
  <li>
    <a href="https://cmake.org/">CMake</a> &#187;
  </li>
  <li>
    <a href="../index.html">3.28.1 Documentation</a> &#187;
  </li>

          <li class="nav-item nav-item-1"><a href="../manual/cmake-env-variables.7.html" >cmake-env-variables(7)</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href="">CTEST_PROGRESS_OUTPUT</a></li> 
      </ul>
    </div>

    <div class="footer" role="contentinfo">
        &#169; Copyright 2000-2023 Kitware, Inc. and Contributors.
      Created using <a href="https://www.sphinx-doc.org/">Sphinx</a> 6.2.1.
    </div>
  </body>
</html>