
<!DOCTYPE html>

<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="generator" content="Docutils 0.19: https://docutils.sourceforge.io/" />

  <title>FindUnixCommands &mdash; CMake 3.28.1 Documentation</title>

    <link rel="stylesheet" type="text/css" href="../_static/pygments.css" />
    <link rel="stylesheet" type="text/css" href="../_static/cmake.css" />
    
    <script data-url_root="../" id="documentation_options" src="../_static/documentation_options.js"></script>
    <script src="../_static/doctools.js"></script>
    <script src="../_static/sphinx_highlight.js"></script>
    
    <link rel="icon" href="../_static/cmake-favicon.ico"/>
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="next" title="FindVTK" href="FindVTK.html" />
    <link rel="prev" title="FindQt" href="FindQt.html" />
 

  </head><body>

    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             accesskey="I">index</a></li>
        <li class="right" >
          <a href="FindVTK.html" title="FindVTK"
             accesskey="N">next</a> |</li>
        <li class="right" >
          <a href="FindQt.html" title="FindQt"
             accesskey="P">previous</a> |</li>
  <li>
    <img src="../_static/cmake-logo-16.png" alt=""
         style="vertical-align: middle; margin-top: -2px" />
  </li>
  <li>
    <a href="https://cmake.org/">CMake</a> &#187;
  </li>
  <li>
    <a href="../index.html">3.28.1 Documentation</a> &#187;
  </li>

          <li class="nav-item nav-item-1"><a href="../manual/cmake-modules.7.html" accesskey="U">cmake-modules(7)</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href="">FindUnixCommands</a></li> 
      </ul>
    </div>  

    <div class="document">
      <div class="documentwrapper">
        <div class="bodywrapper">
          <div class="body" role="main">
            
  <section id="findunixcommands">
<span id="module:FindUnixCommands"></span><h1>FindUnixCommands<a class="headerlink" href="#findunixcommands" title="Permalink to this heading">¶</a></h1>
<div class="deprecated">
<p><span class="versionmodified deprecated">Deprecated since version 3.26: </span>Use <a class="reference internal" href="../manual/cmake.1.html#cmdoption-cmake-E"><code class="xref std std-option docutils literal notranslate"><span class="pre">${CMAKE_COMMAND}</span> <span class="pre">-E</span></code></a> subcommands instead.</p>
</div>
<p>Find Unix commands, including the ones from Cygwin</p>
<p>This module looks for the Unix commands <code class="docutils literal notranslate"><span class="pre">bash</span></code>, <code class="docutils literal notranslate"><span class="pre">cp</span></code>, <code class="docutils literal notranslate"><span class="pre">gzip</span></code>,
<code class="docutils literal notranslate"><span class="pre">mv</span></code>, <code class="docutils literal notranslate"><span class="pre">rm</span></code>, and <code class="docutils literal notranslate"><span class="pre">tar</span></code> and stores the result in the variables
<code class="docutils literal notranslate"><span class="pre">BASH</span></code>, <code class="docutils literal notranslate"><span class="pre">CP</span></code>, <code class="docutils literal notranslate"><span class="pre">GZIP</span></code>, <code class="docutils literal notranslate"><span class="pre">MV</span></code>, <code class="docutils literal notranslate"><span class="pre">RM</span></code>, and <code class="docutils literal notranslate"><span class="pre">TAR</span></code>.</p>
</section>


            <div class="clearer"></div>
          </div>
        </div>
      </div>
      <div class="sphinxsidebar" role="navigation" aria-label="main navigation">
        <div class="sphinxsidebarwrapper">
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="FindQt.html"
                          title="previous chapter">FindQt</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="FindVTK.html"
                          title="next chapter">FindVTK</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../_sources/module/FindUnixCommands.rst.txt"
            rel="nofollow">Show Source</a></li>
    </ul>
   </div>
<div id="searchbox" style="display: none" role="search">
  <h3 id="searchlabel">Quick search</h3>
    <div class="searchformwrapper">
    <form class="search" action="../search.html" method="get">
      <input type="text" name="q" aria-labelledby="searchlabel" autocomplete="off" autocorrect="off" autocapitalize="off" spellcheck="false"/>
      <input type="submit" value="Go" />
    </form>
    </div>
</div>
<script>document.getElementById('searchbox').style.display = "block"</script>
        </div>
      </div>
      <div class="clearer"></div>
    </div>
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             >index</a></li>
        <li class="right" >
          <a href="FindVTK.html" title="FindVTK"
             >next</a> |</li>
        <li class="right" >
          <a href="FindQt.html" title="FindQt"
             >previous</a> |</li>
  <li>
    <img src="../_static/cmake-logo-16.png" alt=""
         style="vertical-align: middle; margin-top: -2px" />
  </li>
  <li>
    <a href="https://cmake.org/">CMake</a> &#187;
  </li>
  <li>
    <a href="../index.html">3.28.1 Documentation</a> &#187;
  </li>

          <li class="nav-item nav-item-1"><a href="../manual/cmake-modules.7.html" >cmake-modules(7)</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href="">FindUnixCommands</a></li> 
      </ul>
    </div>

    <div class="footer" role="contentinfo">
        &#169; Copyright 2000-2023 Kitware, Inc. and Contributors.
      Created using <a href="https://www.sphinx-doc.org/">Sphinx</a> 6.2.1.
    </div>
  </body>
</html>