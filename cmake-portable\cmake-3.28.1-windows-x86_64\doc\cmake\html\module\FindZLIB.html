
<!DOCTYPE html>

<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="generator" content="Docutils 0.19: https://docutils.sourceforge.io/" />

  <title>FindZLIB &mdash; CMake 3.28.1 Documentation</title>

    <link rel="stylesheet" type="text/css" href="../_static/pygments.css" />
    <link rel="stylesheet" type="text/css" href="../_static/cmake.css" />
    
    <script data-url_root="../" id="documentation_options" src="../_static/documentation_options.js"></script>
    <script src="../_static/doctools.js"></script>
    <script src="../_static/sphinx_highlight.js"></script>
    
    <link rel="icon" href="../_static/cmake-favicon.ico"/>
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="next" title="AddFileDependencies" href="AddFileDependencies.html" />
    <link rel="prev" title="FindXMLRPC" href="FindXMLRPC.html" />
 

  </head><body>

    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             accesskey="I">index</a></li>
        <li class="right" >
          <a href="AddFileDependencies.html" title="AddFileDependencies"
             accesskey="N">next</a> |</li>
        <li class="right" >
          <a href="FindXMLRPC.html" title="FindXMLRPC"
             accesskey="P">previous</a> |</li>
  <li>
    <img src="../_static/cmake-logo-16.png" alt=""
         style="vertical-align: middle; margin-top: -2px" />
  </li>
  <li>
    <a href="https://cmake.org/">CMake</a> &#187;
  </li>
  <li>
    <a href="../index.html">3.28.1 Documentation</a> &#187;
  </li>

          <li class="nav-item nav-item-1"><a href="../manual/cmake-modules.7.html" accesskey="U">cmake-modules(7)</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href="">FindZLIB</a></li> 
      </ul>
    </div>  

    <div class="document">
      <div class="documentwrapper">
        <div class="bodywrapper">
          <div class="body" role="main">
            
  <section id="findzlib">
<span id="module:FindZLIB"></span><h1>FindZLIB<a class="headerlink" href="#findzlib" title="Permalink to this heading">¶</a></h1>
<p>Find the native ZLIB includes and library.</p>
<section id="imported-targets">
<h2>IMPORTED Targets<a class="headerlink" href="#imported-targets" title="Permalink to this heading">¶</a></h2>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.1.</span></p>
</div>
<p>This module defines <span class="target" id="index-0-prop_tgt:IMPORTED"></span><a class="reference internal" href="../prop_tgt/IMPORTED.html#prop_tgt:IMPORTED" title="IMPORTED"><code class="xref cmake cmake-prop_tgt docutils literal notranslate"><span class="pre">IMPORTED</span></code></a> target <code class="docutils literal notranslate"><span class="pre">ZLIB::ZLIB</span></code>, if
ZLIB has been found.</p>
</section>
<section id="result-variables">
<h2>Result Variables<a class="headerlink" href="#result-variables" title="Permalink to this heading">¶</a></h2>
<p>This module defines the following variables:</p>
<dl>
<dt><code class="docutils literal notranslate"><span class="pre">ZLIB_INCLUDE_DIRS</span></code></dt><dd><p>where to find zlib.h, etc.</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">ZLIB_LIBRARIES</span></code></dt><dd><p>List of libraries when using zlib.</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">ZLIB_FOUND</span></code></dt><dd><p>True if zlib found.</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">ZLIB_VERSION</span></code></dt><dd><div class="versionadded">
<p><span class="versionmodified added">New in version 3.26: </span>the version of Zlib found.</p>
</div>
<p>See also legacy variable <code class="docutils literal notranslate"><span class="pre">ZLIB_VERSION_STRING</span></code>.</p>
</dd>
</dl>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.4: </span>Debug and Release variants are found separately.</p>
</div>
</section>
<section id="legacy-variables">
<h2>Legacy Variables<a class="headerlink" href="#legacy-variables" title="Permalink to this heading">¶</a></h2>
<p>The following variables are provided for backward compatibility:</p>
<dl>
<dt><code class="docutils literal notranslate"><span class="pre">ZLIB_VERSION_MAJOR</span></code></dt><dd><p>The major version of zlib.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.26: </span>Superseded by <code class="docutils literal notranslate"><span class="pre">ZLIB_VERSION</span></code>.</p>
</div>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">ZLIB_VERSION_MINOR</span></code></dt><dd><p>The minor version of zlib.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.26: </span>Superseded by <code class="docutils literal notranslate"><span class="pre">ZLIB_VERSION</span></code>.</p>
</div>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">ZLIB_VERSION_PATCH</span></code></dt><dd><p>The patch version of zlib.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.26: </span>Superseded by <code class="docutils literal notranslate"><span class="pre">ZLIB_VERSION</span></code>.</p>
</div>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">ZLIB_VERSION_TWEAK</span></code></dt><dd><p>The tweak version of zlib.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.26: </span>Superseded by <code class="docutils literal notranslate"><span class="pre">ZLIB_VERSION</span></code>.</p>
</div>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">ZLIB_VERSION_STRING</span></code></dt><dd><p>The version of zlib found (x.y.z)</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.26: </span>Superseded by <code class="docutils literal notranslate"><span class="pre">ZLIB_VERSION</span></code>.</p>
</div>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">ZLIB_MAJOR_VERSION</span></code></dt><dd><p>The major version of zlib.  Superseded by <code class="docutils literal notranslate"><span class="pre">ZLIB_VERSION_MAJOR</span></code>.</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">ZLIB_MINOR_VERSION</span></code></dt><dd><p>The minor version of zlib.  Superseded by <code class="docutils literal notranslate"><span class="pre">ZLIB_VERSION_MINOR</span></code>.</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">ZLIB_PATCH_VERSION</span></code></dt><dd><p>The patch version of zlib.  Superseded by <code class="docutils literal notranslate"><span class="pre">ZLIB_VERSION_PATCH</span></code>.</p>
</dd>
</dl>
</section>
<section id="hints">
<h2>Hints<a class="headerlink" href="#hints" title="Permalink to this heading">¶</a></h2>
<p>A user may set <code class="docutils literal notranslate"><span class="pre">ZLIB_ROOT</span></code> to a zlib installation root to tell this
module where to look.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.24: </span>Set <code class="docutils literal notranslate"><span class="pre">ZLIB_USE_STATIC_LIBS</span></code> to <code class="docutils literal notranslate"><span class="pre">ON</span></code> to look for static libraries.
Default is <code class="docutils literal notranslate"><span class="pre">OFF</span></code>.</p>
</div>
</section>
</section>


            <div class="clearer"></div>
          </div>
        </div>
      </div>
      <div class="sphinxsidebar" role="navigation" aria-label="main navigation">
        <div class="sphinxsidebarwrapper">
  <div>
    <h3><a href="../index.html">Table of Contents</a></h3>
    <ul>
<li><a class="reference internal" href="#">FindZLIB</a><ul>
<li><a class="reference internal" href="#imported-targets">IMPORTED Targets</a></li>
<li><a class="reference internal" href="#result-variables">Result Variables</a></li>
<li><a class="reference internal" href="#legacy-variables">Legacy Variables</a></li>
<li><a class="reference internal" href="#hints">Hints</a></li>
</ul>
</li>
</ul>

  </div>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="FindXMLRPC.html"
                          title="previous chapter">FindXMLRPC</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="AddFileDependencies.html"
                          title="next chapter">AddFileDependencies</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../_sources/module/FindZLIB.rst.txt"
            rel="nofollow">Show Source</a></li>
    </ul>
   </div>
<div id="searchbox" style="display: none" role="search">
  <h3 id="searchlabel">Quick search</h3>
    <div class="searchformwrapper">
    <form class="search" action="../search.html" method="get">
      <input type="text" name="q" aria-labelledby="searchlabel" autocomplete="off" autocorrect="off" autocapitalize="off" spellcheck="false"/>
      <input type="submit" value="Go" />
    </form>
    </div>
</div>
<script>document.getElementById('searchbox').style.display = "block"</script>
        </div>
      </div>
      <div class="clearer"></div>
    </div>
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             >index</a></li>
        <li class="right" >
          <a href="AddFileDependencies.html" title="AddFileDependencies"
             >next</a> |</li>
        <li class="right" >
          <a href="FindXMLRPC.html" title="FindXMLRPC"
             >previous</a> |</li>
  <li>
    <img src="../_static/cmake-logo-16.png" alt=""
         style="vertical-align: middle; margin-top: -2px" />
  </li>
  <li>
    <a href="https://cmake.org/">CMake</a> &#187;
  </li>
  <li>
    <a href="../index.html">3.28.1 Documentation</a> &#187;
  </li>

          <li class="nav-item nav-item-1"><a href="../manual/cmake-modules.7.html" >cmake-modules(7)</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href="">FindZLIB</a></li> 
      </ul>
    </div>

    <div class="footer" role="contentinfo">
        &#169; Copyright 2000-2023 Kitware, Inc. and Contributors.
      Created using <a href="https://www.sphinx-doc.org/">Sphinx</a> 6.2.1.
    </div>
  </body>
</html>