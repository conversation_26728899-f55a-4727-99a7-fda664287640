
<!DOCTYPE html>

<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="generator" content="Docutils 0.19: https://docutils.sourceforge.io/" />

  <title>CMAKE_INSTALL_MODE &mdash; CMake 3.28.1 Documentation</title>

    <link rel="stylesheet" type="text/css" href="../_static/pygments.css" />
    <link rel="stylesheet" type="text/css" href="../_static/cmake.css" />
    
    <script data-url_root="../" id="documentation_options" src="../_static/documentation_options.js"></script>
    <script src="../_static/doctools.js"></script>
    <script src="../_static/sphinx_highlight.js"></script>
    
    <link rel="icon" href="../_static/cmake-favicon.ico"/>
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="next" title="CMAKE_&lt;LANG&gt;_COMPILER_LAUNCHER" href="CMAKE_LANG_COMPILER_LAUNCHER.html" />
    <link rel="prev" title="CMAKE_GENERATOR_TOOLSET" href="CMAKE_GENERATOR_TOOLSET.html" />
 

  </head><body>

    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             accesskey="I">index</a></li>
        <li class="right" >
          <a href="CMAKE_LANG_COMPILER_LAUNCHER.html" title="CMAKE_&lt;LANG&gt;_COMPILER_LAUNCHER"
             accesskey="N">next</a> |</li>
        <li class="right" >
          <a href="CMAKE_GENERATOR_TOOLSET.html" title="CMAKE_GENERATOR_TOOLSET"
             accesskey="P">previous</a> |</li>
  <li>
    <img src="../_static/cmake-logo-16.png" alt=""
         style="vertical-align: middle; margin-top: -2px" />
  </li>
  <li>
    <a href="https://cmake.org/">CMake</a> &#187;
  </li>
  <li>
    <a href="../index.html">3.28.1 Documentation</a> &#187;
  </li>

          <li class="nav-item nav-item-1"><a href="../manual/cmake-env-variables.7.html" accesskey="U">cmake-env-variables(7)</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href="">CMAKE_INSTALL_MODE</a></li> 
      </ul>
    </div>  

    <div class="document">
      <div class="documentwrapper">
        <div class="bodywrapper">
          <div class="body" role="main">
            
  <section id="cmake-install-mode">
<span id="envvar:CMAKE_INSTALL_MODE"></span><h1>CMAKE_INSTALL_MODE<a class="headerlink" href="#cmake-install-mode" title="Permalink to this heading">¶</a></h1>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.22.</span></p>
</div>
<p>This is a CMake <a class="reference internal" href="../manual/cmake-language.7.html#cmake-language-environment-variables"><span class="std std-ref">Environment Variable</span></a>. Its initial value is taken from
the calling process environment.</p>
<p>The <code class="docutils literal notranslate"><span class="pre">CMAKE_INSTALL_MODE</span></code> environment variable allows users to operate
CMake in an alternate mode of <span class="target" id="index-0-command:file"></span><a class="reference internal" href="../command/file.html#install" title="file(install)"><code class="xref cmake cmake-command docutils literal notranslate"><span class="pre">file(INSTALL)</span></code></a> and <span class="target" id="index-0-command:install"></span><a class="reference internal" href="../command/install.html#command:install" title="install"><code class="xref cmake cmake-command docutils literal notranslate"><span class="pre">install()</span></code></a>.</p>
<p>The default behavior for an installation is to copy a source file from a
source directory into a destination directory. This environment variable
however allows the user to override this behavior, causing CMake to create
symbolic links instead.</p>
<section id="usage-scenarios">
<h2>Usage Scenarios<a class="headerlink" href="#usage-scenarios" title="Permalink to this heading">¶</a></h2>
<p>Installing symbolic links rather than copying files can help in the following
ways:</p>
<ul class="simple">
<li><p>Conserving storage space because files do not have to be duplicated on disk.</p></li>
<li><p>Changes to the source of the symbolic link are seen at the install
destination without having to re-run the install step.</p></li>
<li><p>Editing through the link at the install destination will modify the source
of the link. This may be useful when dealing with CMake project hierarchies,
i.e. using <span class="target" id="index-0-module:ExternalProject"></span><a class="reference internal" href="../module/ExternalProject.html#module:ExternalProject" title="ExternalProject"><code class="xref cmake cmake-module docutils literal notranslate"><span class="pre">ExternalProject</span></code></a> and consistent source navigation and
refactoring is desired across projects.</p></li>
</ul>
</section>
<section id="allowed-values">
<h2>Allowed Values<a class="headerlink" href="#allowed-values" title="Permalink to this heading">¶</a></h2>
<p>The following values are allowed for <code class="docutils literal notranslate"><span class="pre">CMAKE_INSTALL_MODE</span></code>:</p>
<dl class="simple">
<dt><code class="docutils literal notranslate"><span class="pre">COPY</span></code>, empty or unset</dt><dd><p>Duplicate the file at its destination.  This is the default behavior.</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">ABS_SYMLINK</span></code></dt><dd><p>Create an <em>absolute</em> symbolic link to the source file at the destination.
Halt with an error if the link cannot be created.</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">ABS_SYMLINK_OR_COPY</span></code></dt><dd><p>Like <code class="docutils literal notranslate"><span class="pre">ABS_SYMLINK</span></code> but fall back to silently copying if the symlink
couldn't be created.</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">REL_SYMLINK</span></code></dt><dd><p>Create a <em>relative</em> symbolic link to the source file at the destination.
Halt with an error if the link cannot be created.</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">REL_SYMLINK_OR_COPY</span></code></dt><dd><p>Like <code class="docutils literal notranslate"><span class="pre">REL_SYMLINK</span></code> but fall back to silently copying if the symlink
couldn't be created.</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">SYMLINK</span></code></dt><dd><p>Try as if through <code class="docutils literal notranslate"><span class="pre">REL_SYMLINK</span></code> and fall back to <code class="docutils literal notranslate"><span class="pre">ABS_SYMLINK</span></code> if the
referenced file cannot be expressed using a relative path.
Halt with an error if the link cannot be created.</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">SYMLINK_OR_COPY</span></code></dt><dd><p>Like <code class="docutils literal notranslate"><span class="pre">SYMLINK</span></code> but fall back to silently copying if the symlink couldn't
be created.</p>
</dd>
</dl>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>A symbolic link consists of a reference file path rather than contents of its
own, hence there are two ways to express the relation, either by a <em>relative</em>
or an <em>absolute</em> path.</p>
</div>
</section>
<section id="when-to-set-the-environment-variable">
<h2>When To Set The Environment Variable<a class="headerlink" href="#when-to-set-the-environment-variable" title="Permalink to this heading">¶</a></h2>
<p>For the environment variable to take effect, it must be set during the correct
build phase(s).</p>
<ul class="simple">
<li><p>If the project calls <span class="target" id="index-1-command:file"></span><a class="reference internal" href="../command/file.html#install" title="file(install)"><code class="xref cmake cmake-command docutils literal notranslate"><span class="pre">file(INSTALL)</span></code></a> directly, the environment
variable must be set during the configuration phase.</p></li>
<li><p>In order to apply to <span class="target" id="index-1-command:install"></span><a class="reference internal" href="../command/install.html#command:install" title="install"><code class="xref cmake cmake-command docutils literal notranslate"><span class="pre">install()</span></code></a>, the environment variable must be
set during installation.  This could be during a build if using the
<code class="docutils literal notranslate"><span class="pre">install</span></code> or <code class="docutils literal notranslate"><span class="pre">package</span></code> build targets, or separate from the build when
invoking an install or running <span class="target" id="index-0-manual:cpack(1)"></span><a class="reference internal" href="../manual/cpack.1.html#manual:cpack(1)" title="cpack(1)"><code class="xref cmake cmake-manual docutils literal notranslate"><span class="pre">cpack</span></code></a> from the command
line.</p></li>
<li><p>When using <span class="target" id="index-1-module:ExternalProject"></span><a class="reference internal" href="../module/ExternalProject.html#module:ExternalProject" title="ExternalProject"><code class="xref cmake cmake-module docutils literal notranslate"><span class="pre">ExternalProject</span></code></a>, it might be required during the build
phase, since the external project's own configure, build and install steps
will execute during the main project's build phase.</p></li>
</ul>
<p>Given the above, it is recommended to set the environment variable consistently
across all phases (configure, build and install).</p>
</section>
<section id="caveats">
<h2>Caveats<a class="headerlink" href="#caveats" title="Permalink to this heading">¶</a></h2>
<p>Use this environment variable with caution. The following highlights some
points to be considered:</p>
<ul class="simple">
<li><p><code class="docutils literal notranslate"><span class="pre">CMAKE_INSTALL_MODE</span></code> only affects files, not directories.</p></li>
<li><p>Symbolic links are not available on all platforms.</p></li>
<li><p>The way this environment variable interacts with the install step of
<span class="target" id="index-2-module:ExternalProject"></span><a class="reference internal" href="../module/ExternalProject.html#module:ExternalProject" title="ExternalProject"><code class="xref cmake cmake-module docutils literal notranslate"><span class="pre">ExternalProject</span></code></a> is more complex. For further details, see that
module's documentation.</p></li>
<li><p>A symbolic link ties the destination to the source in a persistent way.
Writing to either of the two affects both file system objects.
This is in contrast to normal install behavior which only copies files as
they were at the time the install was performed, with no enduring
relationship between the source and destination of the install.</p></li>
<li><p>Combining <code class="docutils literal notranslate"><span class="pre">CMAKE_INSTALL_MODE</span></code> with <span class="target" id="index-0-prop_tgt:IOS_INSTALL_COMBINED"></span><a class="reference internal" href="../prop_tgt/IOS_INSTALL_COMBINED.html#prop_tgt:IOS_INSTALL_COMBINED" title="IOS_INSTALL_COMBINED"><code class="xref cmake cmake-prop_tgt docutils literal notranslate"><span class="pre">IOS_INSTALL_COMBINED</span></code></a> is
not supported.</p></li>
<li><p>Changing <code class="docutils literal notranslate"><span class="pre">CMAKE_INSTALL_MODE</span></code> from what it was on a previous run can lead
to unexpected results.  Moving from a non-symlinking mode to a symlinking
mode will discard any previous file at the destination, but the reverse is
not true.  Once a symlink exists at the destination, even if you switch to a
non-symlink mode, the symlink will continue to exist at the destination and
will not be replaced by an actual file.</p></li>
</ul>
</section>
</section>


            <div class="clearer"></div>
          </div>
        </div>
      </div>
      <div class="sphinxsidebar" role="navigation" aria-label="main navigation">
        <div class="sphinxsidebarwrapper">
  <div>
    <h3><a href="../index.html">Table of Contents</a></h3>
    <ul>
<li><a class="reference internal" href="#">CMAKE_INSTALL_MODE</a><ul>
<li><a class="reference internal" href="#usage-scenarios">Usage Scenarios</a></li>
<li><a class="reference internal" href="#allowed-values">Allowed Values</a></li>
<li><a class="reference internal" href="#when-to-set-the-environment-variable">When To Set The Environment Variable</a></li>
<li><a class="reference internal" href="#caveats">Caveats</a></li>
</ul>
</li>
</ul>

  </div>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="CMAKE_GENERATOR_TOOLSET.html"
                          title="previous chapter">CMAKE_GENERATOR_TOOLSET</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="CMAKE_LANG_COMPILER_LAUNCHER.html"
                          title="next chapter">CMAKE_&lt;LANG&gt;_COMPILER_LAUNCHER</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../_sources/envvar/CMAKE_INSTALL_MODE.rst.txt"
            rel="nofollow">Show Source</a></li>
    </ul>
   </div>
<div id="searchbox" style="display: none" role="search">
  <h3 id="searchlabel">Quick search</h3>
    <div class="searchformwrapper">
    <form class="search" action="../search.html" method="get">
      <input type="text" name="q" aria-labelledby="searchlabel" autocomplete="off" autocorrect="off" autocapitalize="off" spellcheck="false"/>
      <input type="submit" value="Go" />
    </form>
    </div>
</div>
<script>document.getElementById('searchbox').style.display = "block"</script>
        </div>
      </div>
      <div class="clearer"></div>
    </div>
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             >index</a></li>
        <li class="right" >
          <a href="CMAKE_LANG_COMPILER_LAUNCHER.html" title="CMAKE_&lt;LANG&gt;_COMPILER_LAUNCHER"
             >next</a> |</li>
        <li class="right" >
          <a href="CMAKE_GENERATOR_TOOLSET.html" title="CMAKE_GENERATOR_TOOLSET"
             >previous</a> |</li>
  <li>
    <img src="../_static/cmake-logo-16.png" alt=""
         style="vertical-align: middle; margin-top: -2px" />
  </li>
  <li>
    <a href="https://cmake.org/">CMake</a> &#187;
  </li>
  <li>
    <a href="../index.html">3.28.1 Documentation</a> &#187;
  </li>

          <li class="nav-item nav-item-1"><a href="../manual/cmake-env-variables.7.html" >cmake-env-variables(7)</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href="">CMAKE_INSTALL_MODE</a></li> 
      </ul>
    </div>

    <div class="footer" role="contentinfo">
        &#169; Copyright 2000-2023 Kitware, Inc. and Contributors.
      Created using <a href="https://www.sphinx-doc.org/">Sphinx</a> 6.2.1.
    </div>
  </body>
</html>