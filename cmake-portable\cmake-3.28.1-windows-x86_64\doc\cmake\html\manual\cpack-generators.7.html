
<!DOCTYPE html>

<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="generator" content="Docutils 0.19: https://docutils.sourceforge.io/" />

  <title>cpack-generators(7) &mdash; CMake 3.28.1 Documentation</title>

    <link rel="stylesheet" type="text/css" href="../_static/pygments.css" />
    <link rel="stylesheet" type="text/css" href="../_static/cmake.css" />
    
    <script data-url_root="../" id="documentation_options" src="../_static/documentation_options.js"></script>
    <script src="../_static/doctools.js"></script>
    <script src="../_static/sphinx_highlight.js"></script>
    
    <link rel="icon" href="../_static/cmake-favicon.ico"/>
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="next" title="CPack Archive Generator" href="../cpack_gen/archive.html" />
    <link rel="prev" title="CMAKE_VS_INTEL_Fortran_PROJECT_VERSION" href="../variable/CMAKE_VS_INTEL_Fortran_PROJECT_VERSION.html" />
 

  </head><body>

    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             accesskey="I">index</a></li>
        <li class="right" >
          <a href="../cpack_gen/archive.html" title="CPack Archive Generator"
             accesskey="N">next</a> |</li>
        <li class="right" >
          <a href="../variable/CMAKE_VS_INTEL_Fortran_PROJECT_VERSION.html" title="CMAKE_VS_INTEL_Fortran_PROJECT_VERSION"
             accesskey="P">previous</a> |</li>
  <li>
    <img src="../_static/cmake-logo-16.png" alt=""
         style="vertical-align: middle; margin-top: -2px" />
  </li>
  <li>
    <a href="https://cmake.org/">CMake</a> &#187;
  </li>
  <li>
    <a href="../index.html">3.28.1 Documentation</a> &#187;
  </li>

        <li class="nav-item nav-item-this"><a href="">cpack-generators(7)</a></li> 
      </ul>
    </div>  

    <div class="document">
      <div class="documentwrapper">
        <div class="bodywrapper">
          <div class="body" role="main">
            
  <span class="target" id="manual:cpack-generators(7)"></span><section id="cpack-generators-7">
<h1><a class="toc-backref" href="#id1" role="doc-backlink">cpack-generators(7)</a><a class="headerlink" href="#cpack-generators-7" title="Permalink to this heading">¶</a></h1>
<nav class="contents" id="contents">
<p class="topic-title">Contents</p>
<ul class="simple">
<li><p><a class="reference internal" href="#cpack-generators-7" id="id1">cpack-generators(7)</a></p>
<ul>
<li><p><a class="reference internal" href="#generators" id="id2">Generators</a></p></li>
</ul>
</li>
</ul>
</nav>
<section id="generators">
<h2><a class="toc-backref" href="#id2" role="doc-backlink">Generators</a><a class="headerlink" href="#generators" title="Permalink to this heading">¶</a></h2>
<div class="toctree-wrapper compound">
<ul>
<li class="toctree-l1"><a class="reference internal" href="../cpack_gen/archive.html">CPack Archive Generator</a></li>
<li class="toctree-l1"><a class="reference internal" href="../cpack_gen/bundle.html">CPack Bundle Generator</a></li>
<li class="toctree-l1"><a class="reference internal" href="../cpack_gen/cygwin.html">CPack Cygwin Generator</a></li>
<li class="toctree-l1"><a class="reference internal" href="../cpack_gen/deb.html">CPack DEB Generator</a></li>
<li class="toctree-l1"><a class="reference internal" href="../cpack_gen/dmg.html">CPack DragNDrop Generator</a></li>
<li class="toctree-l1"><a class="reference internal" href="../cpack_gen/external.html">CPack External Generator</a></li>
<li class="toctree-l1"><a class="reference internal" href="../cpack_gen/freebsd.html">CPack FreeBSD Generator</a></li>
<li class="toctree-l1"><a class="reference internal" href="../cpack_gen/innosetup.html">CPack Inno Setup Generator</a></li>
<li class="toctree-l1"><a class="reference internal" href="../cpack_gen/ifw.html">CPack IFW Generator</a></li>
<li class="toctree-l1"><a class="reference internal" href="../cpack_gen/nsis.html">CPack NSIS Generator</a></li>
<li class="toctree-l1"><a class="reference internal" href="../cpack_gen/nuget.html">CPack NuGet Generator</a></li>
<li class="toctree-l1"><a class="reference internal" href="../cpack_gen/packagemaker.html">CPack PackageMaker Generator</a></li>
<li class="toctree-l1"><a class="reference internal" href="../cpack_gen/productbuild.html">CPack productbuild Generator</a></li>
<li class="toctree-l1"><a class="reference internal" href="../cpack_gen/rpm.html">CPack RPM Generator</a></li>
<li class="toctree-l1"><a class="reference internal" href="../cpack_gen/wix.html">CPack WIX Generator</a></li>
</ul>
</div>
</section>
</section>


            <div class="clearer"></div>
          </div>
        </div>
      </div>
      <div class="sphinxsidebar" role="navigation" aria-label="main navigation">
        <div class="sphinxsidebarwrapper">
  <div>
    <h3><a href="../index.html">Table of Contents</a></h3>
    <ul>
<li><a class="reference internal" href="#">cpack-generators(7)</a><ul>
<li><a class="reference internal" href="#generators">Generators</a></li>
</ul>
</li>
</ul>

  </div>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="../variable/CMAKE_VS_INTEL_Fortran_PROJECT_VERSION.html"
                          title="previous chapter">CMAKE_VS_INTEL_Fortran_PROJECT_VERSION</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="../cpack_gen/archive.html"
                          title="next chapter">CPack Archive Generator</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../_sources/manual/cpack-generators.7.rst.txt"
            rel="nofollow">Show Source</a></li>
    </ul>
   </div>
<div id="searchbox" style="display: none" role="search">
  <h3 id="searchlabel">Quick search</h3>
    <div class="searchformwrapper">
    <form class="search" action="../search.html" method="get">
      <input type="text" name="q" aria-labelledby="searchlabel" autocomplete="off" autocorrect="off" autocapitalize="off" spellcheck="false"/>
      <input type="submit" value="Go" />
    </form>
    </div>
</div>
<script>document.getElementById('searchbox').style.display = "block"</script>
        </div>
      </div>
      <div class="clearer"></div>
    </div>
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             >index</a></li>
        <li class="right" >
          <a href="../cpack_gen/archive.html" title="CPack Archive Generator"
             >next</a> |</li>
        <li class="right" >
          <a href="../variable/CMAKE_VS_INTEL_Fortran_PROJECT_VERSION.html" title="CMAKE_VS_INTEL_Fortran_PROJECT_VERSION"
             >previous</a> |</li>
  <li>
    <img src="../_static/cmake-logo-16.png" alt=""
         style="vertical-align: middle; margin-top: -2px" />
  </li>
  <li>
    <a href="https://cmake.org/">CMake</a> &#187;
  </li>
  <li>
    <a href="../index.html">3.28.1 Documentation</a> &#187;
  </li>

        <li class="nav-item nav-item-this"><a href="">cpack-generators(7)</a></li> 
      </ul>
    </div>

    <div class="footer" role="contentinfo">
        &#169; Copyright 2000-2023 Kitware, Inc. and Contributors.
      Created using <a href="https://www.sphinx-doc.org/">Sphinx</a> 6.2.1.
    </div>
  </body>
</html>