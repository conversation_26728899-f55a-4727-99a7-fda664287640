
<!DOCTYPE html>

<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="generator" content="Docutils 0.19: https://docutils.sourceforge.io/" />

  <title>FindThreads &mdash; CMake 3.28.1 Documentation</title>

    <link rel="stylesheet" type="text/css" href="../_static/pygments.css" />
    <link rel="stylesheet" type="text/css" href="../_static/cmake.css" />
    
    <script data-url_root="../" id="documentation_options" src="../_static/documentation_options.js"></script>
    <script src="../_static/doctools.js"></script>
    <script src="../_static/sphinx_highlight.js"></script>
    
    <link rel="icon" href="../_static/cmake-favicon.ico"/>
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="next" title="FindTIFF" href="FindTIFF.html" />
    <link rel="prev" title="FindTclStub" href="FindTclStub.html" />
 

  </head><body>

    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             accesskey="I">index</a></li>
        <li class="right" >
          <a href="FindTIFF.html" title="FindTIFF"
             accesskey="N">next</a> |</li>
        <li class="right" >
          <a href="FindTclStub.html" title="FindTclStub"
             accesskey="P">previous</a> |</li>
  <li>
    <img src="../_static/cmake-logo-16.png" alt=""
         style="vertical-align: middle; margin-top: -2px" />
  </li>
  <li>
    <a href="https://cmake.org/">CMake</a> &#187;
  </li>
  <li>
    <a href="../index.html">3.28.1 Documentation</a> &#187;
  </li>

          <li class="nav-item nav-item-1"><a href="../manual/cmake-modules.7.html" accesskey="U">cmake-modules(7)</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href="">FindThreads</a></li> 
      </ul>
    </div>  

    <div class="document">
      <div class="documentwrapper">
        <div class="bodywrapper">
          <div class="body" role="main">
            
  <section id="findthreads">
<span id="module:FindThreads"></span><h1>FindThreads<a class="headerlink" href="#findthreads" title="Permalink to this heading">¶</a></h1>
<p>This module determines the thread library of the system.</p>
<section id="imported-targets">
<h2>Imported Targets<a class="headerlink" href="#imported-targets" title="Permalink to this heading">¶</a></h2>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.1.</span></p>
</div>
<p>This module defines the following <span class="target" id="index-0-prop_tgt:IMPORTED"></span><a class="reference internal" href="../prop_tgt/IMPORTED.html#prop_tgt:IMPORTED" title="IMPORTED"><code class="xref cmake cmake-prop_tgt docutils literal notranslate"><span class="pre">IMPORTED</span></code></a> target:</p>
<dl class="simple">
<dt><code class="docutils literal notranslate"><span class="pre">Threads::Threads</span></code></dt><dd><p>The thread library, if found.</p>
</dd>
</dl>
</section>
<section id="result-variables">
<h2>Result Variables<a class="headerlink" href="#result-variables" title="Permalink to this heading">¶</a></h2>
<p>The following variables are set:</p>
<dl class="simple">
<dt><code class="docutils literal notranslate"><span class="pre">Threads_FOUND</span></code></dt><dd><p>If a supported thread library was found.</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">CMAKE_THREAD_LIBS_INIT</span></code></dt><dd><p>The thread library to use. This may be empty if the thread functions
are provided by the system libraries and no special flags are needed
to use them.</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">CMAKE_USE_WIN32_THREADS_INIT</span></code></dt><dd><p>If the found thread library is the win32 one.</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">CMAKE_USE_PTHREADS_INIT</span></code></dt><dd><p>If the found thread library is pthread compatible.</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">CMAKE_HP_PTHREADS_INIT</span></code></dt><dd><p>If the found thread library is the HP thread library.</p>
</dd>
</dl>
</section>
<section id="variables-affecting-behavior">
<h2>Variables Affecting Behavior<a class="headerlink" href="#variables-affecting-behavior" title="Permalink to this heading">¶</a></h2>
<dl class="cmake variable">
<dt class="sig sig-object cmake" id="variable:THREADS_PREFER_PTHREAD_FLAG">
<span class="sig-name descname"><span class="pre">THREADS_PREFER_PTHREAD_FLAG</span></span><a class="headerlink" href="#variable:THREADS_PREFER_PTHREAD_FLAG" title="Permalink to this definition">¶</a></dt>
<dd><div class="versionadded">
<p><span class="versionmodified added">New in version 3.1.</span></p>
</div>
<p>If the use of the -pthread compiler and linker flag is preferred then
the caller can set this variable to TRUE. The compiler flag can only be
used with the imported target. Use of both the imported target as well
as this switch is highly recommended for new code.</p>
<p>This variable has no effect if the system libraries provide the
thread functions, i.e. when <code class="docutils literal notranslate"><span class="pre">CMAKE_THREAD_LIBS_INIT</span></code> will be empty.</p>
</dd></dl>

</section>
</section>


            <div class="clearer"></div>
          </div>
        </div>
      </div>
      <div class="sphinxsidebar" role="navigation" aria-label="main navigation">
        <div class="sphinxsidebarwrapper">
  <div>
    <h3><a href="../index.html">Table of Contents</a></h3>
    <ul>
<li><a class="reference internal" href="#">FindThreads</a><ul>
<li><a class="reference internal" href="#imported-targets">Imported Targets</a></li>
<li><a class="reference internal" href="#result-variables">Result Variables</a></li>
<li><a class="reference internal" href="#variables-affecting-behavior">Variables Affecting Behavior</a></li>
</ul>
</li>
</ul>

  </div>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="FindTclStub.html"
                          title="previous chapter">FindTclStub</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="FindTIFF.html"
                          title="next chapter">FindTIFF</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../_sources/module/FindThreads.rst.txt"
            rel="nofollow">Show Source</a></li>
    </ul>
   </div>
<div id="searchbox" style="display: none" role="search">
  <h3 id="searchlabel">Quick search</h3>
    <div class="searchformwrapper">
    <form class="search" action="../search.html" method="get">
      <input type="text" name="q" aria-labelledby="searchlabel" autocomplete="off" autocorrect="off" autocapitalize="off" spellcheck="false"/>
      <input type="submit" value="Go" />
    </form>
    </div>
</div>
<script>document.getElementById('searchbox').style.display = "block"</script>
        </div>
      </div>
      <div class="clearer"></div>
    </div>
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             >index</a></li>
        <li class="right" >
          <a href="FindTIFF.html" title="FindTIFF"
             >next</a> |</li>
        <li class="right" >
          <a href="FindTclStub.html" title="FindTclStub"
             >previous</a> |</li>
  <li>
    <img src="../_static/cmake-logo-16.png" alt=""
         style="vertical-align: middle; margin-top: -2px" />
  </li>
  <li>
    <a href="https://cmake.org/">CMake</a> &#187;
  </li>
  <li>
    <a href="../index.html">3.28.1 Documentation</a> &#187;
  </li>

          <li class="nav-item nav-item-1"><a href="../manual/cmake-modules.7.html" >cmake-modules(7)</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href="">FindThreads</a></li> 
      </ul>
    </div>

    <div class="footer" role="contentinfo">
        &#169; Copyright 2000-2023 Kitware, Inc. and Contributors.
      Created using <a href="https://www.sphinx-doc.org/">Sphinx</a> 6.2.1.
    </div>
  </body>
</html>