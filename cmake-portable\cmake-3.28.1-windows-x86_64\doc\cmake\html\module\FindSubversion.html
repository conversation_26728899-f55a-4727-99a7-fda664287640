
<!DOCTYPE html>

<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="generator" content="Docutils 0.19: https://docutils.sourceforge.io/" />

  <title>FindSubversion &mdash; CMake 3.28.1 Documentation</title>

    <link rel="stylesheet" type="text/css" href="../_static/pygments.css" />
    <link rel="stylesheet" type="text/css" href="../_static/cmake.css" />
    
    <script data-url_root="../" id="documentation_options" src="../_static/documentation_options.js"></script>
    <script src="../_static/doctools.js"></script>
    <script src="../_static/sphinx_highlight.js"></script>
    
    <link rel="icon" href="../_static/cmake-favicon.ico"/>
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="next" title="FindSWIG" href="FindSWIG.html" />
    <link rel="prev" title="FindSQLite3" href="FindSQLite3.html" />
 

  </head><body>

    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             accesskey="I">index</a></li>
        <li class="right" >
          <a href="FindSWIG.html" title="FindSWIG"
             accesskey="N">next</a> |</li>
        <li class="right" >
          <a href="FindSQLite3.html" title="FindSQLite3"
             accesskey="P">previous</a> |</li>
  <li>
    <img src="../_static/cmake-logo-16.png" alt=""
         style="vertical-align: middle; margin-top: -2px" />
  </li>
  <li>
    <a href="https://cmake.org/">CMake</a> &#187;
  </li>
  <li>
    <a href="../index.html">3.28.1 Documentation</a> &#187;
  </li>

          <li class="nav-item nav-item-1"><a href="../manual/cmake-modules.7.html" accesskey="U">cmake-modules(7)</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href="">FindSubversion</a></li> 
      </ul>
    </div>  

    <div class="document">
      <div class="documentwrapper">
        <div class="bodywrapper">
          <div class="body" role="main">
            
  <section id="findsubversion">
<span id="module:FindSubversion"></span><h1>FindSubversion<a class="headerlink" href="#findsubversion" title="Permalink to this heading">¶</a></h1>
<p>Extract information from a subversion working copy</p>
<p>The module defines the following variables:</p>
<div class="highlight-none notranslate"><div class="highlight"><pre><span></span>Subversion_SVN_EXECUTABLE - path to svn command line client
Subversion_VERSION_SVN - version of svn command line client
Subversion_FOUND - true if the command line client was found
SUBVERSION_FOUND - same as Subversion_FOUND, set for compatibility reasons
</pre></div>
</div>
<p>The minimum required version of Subversion can be specified using the
standard syntax, e.g. <code class="docutils literal notranslate"><span class="pre">find_package(Subversion</span> <span class="pre">1.4)</span></code>.</p>
<p>If the command line client executable is found two macros are defined:</p>
<div class="highlight-none notranslate"><div class="highlight"><pre><span></span>Subversion_WC_INFO(&lt;dir&gt; &lt;var-prefix&gt; [IGNORE_SVN_FAILURE])
Subversion_WC_LOG(&lt;dir&gt; &lt;var-prefix&gt;)
</pre></div>
</div>
<p><code class="docutils literal notranslate"><span class="pre">Subversion_WC_INFO</span></code> extracts information of a subversion working copy at a
given location.  This macro defines the following variables if running
Subversion's <code class="docutils literal notranslate"><span class="pre">info</span></code> command on <code class="docutils literal notranslate"><span class="pre">&lt;dir&gt;</span></code> succeeds; otherwise a
<code class="docutils literal notranslate"><span class="pre">SEND_ERROR</span></code> message is generated.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.13: </span>The error can be ignored by providing the
<code class="docutils literal notranslate"><span class="pre">IGNORE_SVN_FAILURE</span></code> option, which causes these variables to remain
undefined.</p>
</div>
<div class="highlight-none notranslate"><div class="highlight"><pre><span></span>&lt;var-prefix&gt;_WC_URL - url of the repository (at &lt;dir&gt;)
&lt;var-prefix&gt;_WC_ROOT - root url of the repository
&lt;var-prefix&gt;_WC_REVISION - current revision
&lt;var-prefix&gt;_WC_LAST_CHANGED_AUTHOR - author of last commit
&lt;var-prefix&gt;_WC_LAST_CHANGED_DATE - date of last commit
&lt;var-prefix&gt;_WC_LAST_CHANGED_REV - revision of last commit
&lt;var-prefix&gt;_WC_INFO - output of command `svn info &lt;dir&gt;&#39;
</pre></div>
</div>
<p><code class="docutils literal notranslate"><span class="pre">Subversion_WC_LOG</span></code> retrieves the log message of the base revision of a
subversion working copy at a given location.  This macro defines the variable:</p>
<div class="highlight-none notranslate"><div class="highlight"><pre><span></span>&lt;var-prefix&gt;_LAST_CHANGED_LOG - last log of base revision
</pre></div>
</div>
<p>Example usage:</p>
<div class="highlight-none notranslate"><div class="highlight"><pre><span></span>find_package(Subversion)
if(SUBVERSION_FOUND)
  Subversion_WC_INFO(${PROJECT_SOURCE_DIR} Project)
  message(&quot;Current revision is ${Project_WC_REVISION}&quot;)
  Subversion_WC_LOG(${PROJECT_SOURCE_DIR} Project)
  message(&quot;Last changed log is ${Project_LAST_CHANGED_LOG}&quot;)
endif()
</pre></div>
</div>
</section>


            <div class="clearer"></div>
          </div>
        </div>
      </div>
      <div class="sphinxsidebar" role="navigation" aria-label="main navigation">
        <div class="sphinxsidebarwrapper">
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="FindSQLite3.html"
                          title="previous chapter">FindSQLite3</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="FindSWIG.html"
                          title="next chapter">FindSWIG</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../_sources/module/FindSubversion.rst.txt"
            rel="nofollow">Show Source</a></li>
    </ul>
   </div>
<div id="searchbox" style="display: none" role="search">
  <h3 id="searchlabel">Quick search</h3>
    <div class="searchformwrapper">
    <form class="search" action="../search.html" method="get">
      <input type="text" name="q" aria-labelledby="searchlabel" autocomplete="off" autocorrect="off" autocapitalize="off" spellcheck="false"/>
      <input type="submit" value="Go" />
    </form>
    </div>
</div>
<script>document.getElementById('searchbox').style.display = "block"</script>
        </div>
      </div>
      <div class="clearer"></div>
    </div>
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             >index</a></li>
        <li class="right" >
          <a href="FindSWIG.html" title="FindSWIG"
             >next</a> |</li>
        <li class="right" >
          <a href="FindSQLite3.html" title="FindSQLite3"
             >previous</a> |</li>
  <li>
    <img src="../_static/cmake-logo-16.png" alt=""
         style="vertical-align: middle; margin-top: -2px" />
  </li>
  <li>
    <a href="https://cmake.org/">CMake</a> &#187;
  </li>
  <li>
    <a href="../index.html">3.28.1 Documentation</a> &#187;
  </li>

          <li class="nav-item nav-item-1"><a href="../manual/cmake-modules.7.html" >cmake-modules(7)</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href="">FindSubversion</a></li> 
      </ul>
    </div>

    <div class="footer" role="contentinfo">
        &#169; Copyright 2000-2023 Kitware, Inc. and Contributors.
      Created using <a href="https://www.sphinx-doc.org/">Sphinx</a> 6.2.1.
    </div>
  </body>
</html>