
<!DOCTYPE html>

<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="generator" content="Docutils 0.19: https://docutils.sourceforge.io/" />

  <title>Step 10: Selecting Static or Shared Libraries &mdash; CMake 3.28.1 Documentation</title>

    <link rel="stylesheet" type="text/css" href="../../_static/pygments.css" />
    <link rel="stylesheet" type="text/css" href="../../_static/cmake.css" />
    
    <script data-url_root="../../" id="documentation_options" src="../../_static/documentation_options.js"></script>
    <script src="../../_static/doctools.js"></script>
    <script src="../../_static/sphinx_highlight.js"></script>
    
    <link rel="icon" href="../../_static/cmake-favicon.ico"/>
    <link rel="index" title="Index" href="../../genindex.html" />
    <link rel="search" title="Search" href="../../search.html" />
    <link rel="next" title="Step 11: Adding Export Configuration" href="Adding%20Export%20Configuration.html" />
    <link rel="prev" title="Step 9: Packaging an Installer" href="Packaging%20an%20Installer.html" />
 

  </head><body>

    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../../genindex.html" title="General Index"
             accesskey="I">index</a></li>
        <li class="right" >
          <a href="Adding%20Export%20Configuration.html" title="Step 11: Adding Export Configuration"
             accesskey="N">next</a> |</li>
        <li class="right" >
          <a href="Packaging%20an%20Installer.html" title="Step 9: Packaging an Installer"
             accesskey="P">previous</a> |</li>
  <li>
    <img src="../../_static/cmake-logo-16.png" alt=""
         style="vertical-align: middle; margin-top: -2px" />
  </li>
  <li>
    <a href="https://cmake.org/">CMake</a> &#187;
  </li>
  <li>
    <a href="../../index.html">3.28.1 Documentation</a> &#187;
  </li>

          <li class="nav-item nav-item-1"><a href="index.html" accesskey="U">CMake Tutorial</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href="">Step 10: Selecting Static or Shared Libraries</a></li> 
      </ul>
    </div>  

    <div class="document">
      <div class="documentwrapper">
        <div class="bodywrapper">
          <div class="body" role="main">
            
  <section id="step-10-selecting-static-or-shared-libraries">
<span id="guide:tutorial/Selecting Static or Shared Libraries"></span><h1>Step 10: Selecting Static or Shared Libraries<a class="headerlink" href="#step-10-selecting-static-or-shared-libraries" title="Permalink to this heading">¶</a></h1>
<p>In this section we will show how the <span class="target" id="index-0-variable:BUILD_SHARED_LIBS"></span><a class="reference internal" href="../../variable/BUILD_SHARED_LIBS.html#variable:BUILD_SHARED_LIBS" title="BUILD_SHARED_LIBS"><code class="xref cmake cmake-variable docutils literal notranslate"><span class="pre">BUILD_SHARED_LIBS</span></code></a> variable can
be used to control the default behavior of <span class="target" id="index-0-command:add_library"></span><a class="reference internal" href="../../command/add_library.html#command:add_library" title="add_library"><code class="xref cmake cmake-command docutils literal notranslate"><span class="pre">add_library()</span></code></a>,
and allow control over how libraries without an explicit type (<code class="docutils literal notranslate"><span class="pre">STATIC</span></code>,
<code class="docutils literal notranslate"><span class="pre">SHARED</span></code>, <code class="docutils literal notranslate"><span class="pre">MODULE</span></code> or <code class="docutils literal notranslate"><span class="pre">OBJECT</span></code>) are built.</p>
<p>To accomplish this we need to add <span class="target" id="index-1-variable:BUILD_SHARED_LIBS"></span><a class="reference internal" href="../../variable/BUILD_SHARED_LIBS.html#variable:BUILD_SHARED_LIBS" title="BUILD_SHARED_LIBS"><code class="xref cmake cmake-variable docutils literal notranslate"><span class="pre">BUILD_SHARED_LIBS</span></code></a> to the
top-level <code class="docutils literal notranslate"><span class="pre">CMakeLists.txt</span></code>. We use the <span class="target" id="index-0-command:option"></span><a class="reference internal" href="../../command/option.html#command:option" title="option"><code class="xref cmake cmake-command docutils literal notranslate"><span class="pre">option()</span></code></a> command as it allows
users to optionally select if the value should be <code class="docutils literal notranslate"><span class="pre">ON</span></code> or <code class="docutils literal notranslate"><span class="pre">OFF</span></code>.</p>
<div class="literal-block-wrapper docutils container" id="cmakelists-txt-option-build-shared-libs">
<div class="code-block-caption"><span class="caption-text">CMakeLists.txt</span><a class="headerlink" href="#cmakelists-txt-option-build-shared-libs" title="Permalink to this code">¶</a></div>
<div class="highlight-cmake notranslate"><div class="highlight"><pre><span></span><span class="nf">option(</span><span class="no">BUILD_SHARED_LIBS</span><span class="w"> </span><span class="s">&quot;Build using shared libraries&quot;</span><span class="w"> </span><span class="no">ON</span><span class="nf">)</span>
</pre></div>
</div>
</div>
<p>Next, we need to specify output directories for our static and shared
libraries.</p>
<div class="literal-block-wrapper docutils container" id="cmakelists-txt-cmake-output-directories">
<div class="code-block-caption"><span class="caption-text">CMakeLists.txt</span><a class="headerlink" href="#cmakelists-txt-cmake-output-directories" title="Permalink to this code">¶</a></div>
<div class="highlight-cmake notranslate"><div class="highlight"><pre><span></span><span class="nf">set(</span><span class="no">CMAKE_ARCHIVE_OUTPUT_DIRECTORY</span><span class="w"> </span><span class="s">&quot;${PROJECT_BINARY_DIR}&quot;</span><span class="nf">)</span>
<span class="nf">set(</span><span class="no">CMAKE_LIBRARY_OUTPUT_DIRECTORY</span><span class="w"> </span><span class="s">&quot;${PROJECT_BINARY_DIR}&quot;</span><span class="nf">)</span>
<span class="nf">set(</span><span class="no">CMAKE_RUNTIME_OUTPUT_DIRECTORY</span><span class="w"> </span><span class="s">&quot;${PROJECT_BINARY_DIR}&quot;</span><span class="nf">)</span>

<span class="nf">option(</span><span class="no">BUILD_SHARED_LIBS</span><span class="w"> </span><span class="s">&quot;Build using shared libraries&quot;</span><span class="w"> </span><span class="no">ON</span><span class="nf">)</span>
</pre></div>
</div>
</div>
<p>Finally, update <code class="docutils literal notranslate"><span class="pre">MathFunctions/MathFunctions.h</span></code> to use dll export defines:</p>
<div class="literal-block-wrapper docutils container" id="mathfunctions-mathfunctions-h">
<div class="code-block-caption"><span class="caption-text">MathFunctions/MathFunctions.h</span><a class="headerlink" href="#mathfunctions-mathfunctions-h" title="Permalink to this code">¶</a></div>
<div class="highlight-c++ notranslate"><div class="highlight"><pre><span></span><span class="cp">#if defined(_WIN32)</span>
<span class="cp">#  if defined(EXPORTING_MYMATH)</span>
<span class="cp">#    define DECLSPEC __declspec(dllexport)</span>
<span class="cp">#  else</span>
<span class="cp">#    define DECLSPEC __declspec(dllimport)</span>
<span class="cp">#  endif</span>
<span class="cp">#else </span><span class="c1">// non windows</span>
<span class="cp">#  define DECLSPEC</span>
<span class="cp">#endif</span>

<span class="k">namespace</span><span class="w"> </span><span class="nn">mathfunctions</span><span class="w"> </span><span class="p">{</span>
<span class="kt">double</span><span class="w"> </span><span class="n">DECLSPEC</span><span class="w"> </span><span class="nf">sqrt</span><span class="p">(</span><span class="kt">double</span><span class="w"> </span><span class="n">x</span><span class="p">);</span>
<span class="p">}</span>
</pre></div>
</div>
</div>
<p>At this point, if you build everything, you may notice that linking fails
as we are combining a static library without position independent code with a
library that has position independent code. The solution to this is to
explicitly set the <span class="target" id="index-0-prop_tgt:POSITION_INDEPENDENT_CODE"></span><a class="reference internal" href="../../prop_tgt/POSITION_INDEPENDENT_CODE.html#prop_tgt:POSITION_INDEPENDENT_CODE" title="POSITION_INDEPENDENT_CODE"><code class="xref cmake cmake-prop_tgt docutils literal notranslate"><span class="pre">POSITION_INDEPENDENT_CODE</span></code></a> target property of
SqrtLibrary to be <code class="docutils literal notranslate"><span class="pre">True</span></code> when building shared libraries.</p>
<div class="literal-block-wrapper docutils container" id="mathfunctions-cmakelists-txt-position-independent-code">
<div class="code-block-caption"><span class="caption-text">MathFunctions/CMakeLists.txt</span><a class="headerlink" href="#mathfunctions-cmakelists-txt-position-independent-code" title="Permalink to this code">¶</a></div>
<div class="highlight-cmake notranslate"><div class="highlight"><pre><span></span><span class="w">  </span><span class="c"># state that SqrtLibrary need PIC when the default is shared libraries</span>
<span class="w">  </span><span class="nf">set_target_properties(</span><span class="nb">SqrtLibrary</span><span class="w"> </span><span class="no">PROPERTIES</span>
<span class="w">                        </span><span class="no">POSITION_INDEPENDENT_CODE</span><span class="w"> </span><span class="o">${</span><span class="nt">BUILD_SHARED_LIBS</span><span class="o">}</span>
<span class="w">                        </span><span class="nf">)</span>
</pre></div>
</div>
</div>
<p>Define <code class="docutils literal notranslate"><span class="pre">EXPORTING_MYMATH</span></code> stating we are using <code class="docutils literal notranslate"><span class="pre">declspec(dllexport)</span></code> when
building on Windows.</p>
<div class="literal-block-wrapper docutils container" id="mathfunctions-cmakelists-txt-dll-export">
<div class="code-block-caption"><span class="caption-text">MathFunctions/CMakeLists.txt</span><a class="headerlink" href="#mathfunctions-cmakelists-txt-dll-export" title="Permalink to this code">¶</a></div>
<div class="highlight-cmake notranslate"><div class="highlight"><pre><span></span><span class="c"># define the symbol stating we are using the declspec(dllexport) when</span>
<span class="c"># building on windows</span>
<span class="nf">target_compile_definitions(</span><span class="nb">MathFunctions</span><span class="w"> </span><span class="no">PRIVATE</span><span class="w"> </span><span class="s">&quot;EXPORTING_MYMATH&quot;</span><span class="nf">)</span>
</pre></div>
</div>
</div>
<p><strong>Exercise</strong>: We modified <code class="docutils literal notranslate"><span class="pre">MathFunctions.h</span></code> to use dll export defines.
Using CMake documentation can you find a helper module to simplify this?</p>
</section>


            <div class="clearer"></div>
          </div>
        </div>
      </div>
      <div class="sphinxsidebar" role="navigation" aria-label="main navigation">
        <div class="sphinxsidebarwrapper">
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="Packaging%20an%20Installer.html"
                          title="previous chapter">Step 9: Packaging an Installer</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="Adding%20Export%20Configuration.html"
                          title="next chapter">Step 11: Adding Export Configuration</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../../_sources/guide/tutorial/Selecting Static or Shared Libraries.rst.txt"
            rel="nofollow">Show Source</a></li>
    </ul>
   </div>
<div id="searchbox" style="display: none" role="search">
  <h3 id="searchlabel">Quick search</h3>
    <div class="searchformwrapper">
    <form class="search" action="../../search.html" method="get">
      <input type="text" name="q" aria-labelledby="searchlabel" autocomplete="off" autocorrect="off" autocapitalize="off" spellcheck="false"/>
      <input type="submit" value="Go" />
    </form>
    </div>
</div>
<script>document.getElementById('searchbox').style.display = "block"</script>
        </div>
      </div>
      <div class="clearer"></div>
    </div>
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../../genindex.html" title="General Index"
             >index</a></li>
        <li class="right" >
          <a href="Adding%20Export%20Configuration.html" title="Step 11: Adding Export Configuration"
             >next</a> |</li>
        <li class="right" >
          <a href="Packaging%20an%20Installer.html" title="Step 9: Packaging an Installer"
             >previous</a> |</li>
  <li>
    <img src="../../_static/cmake-logo-16.png" alt=""
         style="vertical-align: middle; margin-top: -2px" />
  </li>
  <li>
    <a href="https://cmake.org/">CMake</a> &#187;
  </li>
  <li>
    <a href="../../index.html">3.28.1 Documentation</a> &#187;
  </li>

          <li class="nav-item nav-item-1"><a href="index.html" >CMake Tutorial</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href="">Step 10: Selecting Static or Shared Libraries</a></li> 
      </ul>
    </div>

    <div class="footer" role="contentinfo">
        &#169; Copyright 2000-2023 Kitware, Inc. and Contributors.
      Created using <a href="https://www.sphinx-doc.org/">Sphinx</a> 6.2.1.
    </div>
  </body>
</html>