
<!DOCTYPE html>

<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="generator" content="Docutils 0.19: https://docutils.sourceforge.io/" />

  <title>CPack WIX Generator &mdash; CMake 3.28.1 Documentation</title>

    <link rel="stylesheet" type="text/css" href="../_static/pygments.css" />
    <link rel="stylesheet" type="text/css" href="../_static/cmake.css" />
    
    <script data-url_root="../" id="documentation_options" src="../_static/documentation_options.js"></script>
    <script src="../_static/doctools.js"></script>
    <script src="../_static/sphinx_highlight.js"></script>
    
    <link rel="icon" href="../_static/cmake-favicon.ico"/>
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="next" title="CMake Tutorial" href="../guide/tutorial/index.html" />
    <link rel="prev" title="CPack RPM Generator" href="rpm.html" />
 

  </head><body>

    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             accesskey="I">index</a></li>
        <li class="right" >
          <a href="../guide/tutorial/index.html" title="CMake Tutorial"
             accesskey="N">next</a> |</li>
        <li class="right" >
          <a href="rpm.html" title="CPack RPM Generator"
             accesskey="P">previous</a> |</li>
  <li>
    <img src="../_static/cmake-logo-16.png" alt=""
         style="vertical-align: middle; margin-top: -2px" />
  </li>
  <li>
    <a href="https://cmake.org/">CMake</a> &#187;
  </li>
  <li>
    <a href="../index.html">3.28.1 Documentation</a> &#187;
  </li>

          <li class="nav-item nav-item-1"><a href="../manual/cpack-generators.7.html" accesskey="U">cpack-generators(7)</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href="">CPack WIX Generator</a></li> 
      </ul>
    </div>  

    <div class="document">
      <div class="documentwrapper">
        <div class="bodywrapper">
          <div class="body" role="main">
            
  <section id="cpack-wix-generator">
<span id="cpack_gen:CPack WIX Generator"></span><h1>CPack WIX Generator<a class="headerlink" href="#cpack-wix-generator" title="Permalink to this heading">¶</a></h1>
<p>CPack WIX generator specific options</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.7: </span>Support <span class="target" id="index-0-variable:CPACK_COMPONENT_&lt;compName&gt;_DISABLED"></span><a class="reference internal" href="../module/CPackComponent.html#variable:CPACK_COMPONENT_&lt;compName&gt;_DISABLED" title="CPACK_COMPONENT_&lt;compName&gt;_DISABLED"><code class="xref cmake cmake-variable docutils literal notranslate"><span class="pre">CPACK_COMPONENT_&lt;compName&gt;_DISABLED</span></code></a> variable.</p>
</div>
<section id="variables-specific-to-cpack-wix-generator">
<h2>Variables specific to CPack WIX generator<a class="headerlink" href="#variables-specific-to-cpack-wix-generator" title="Permalink to this heading">¶</a></h2>
<p>The following variables are specific to the installers built on
Windows using WiX.</p>
<dl class="cmake variable">
<dt class="sig sig-object cmake" id="variable:CPACK_WIX_UPGRADE_GUID">
<span class="sig-name descname"><span class="pre">CPACK_WIX_UPGRADE_GUID</span></span><a class="headerlink" href="#variable:CPACK_WIX_UPGRADE_GUID" title="Permalink to this definition">¶</a></dt>
<dd><p>Upgrade GUID (<code class="docutils literal notranslate"><span class="pre">Product/&#64;UpgradeCode</span></code>)</p>
<p>Will be automatically generated unless explicitly provided.</p>
<p>It should be explicitly set to a constant generated globally unique
identifier (GUID) to allow your installers to replace existing
installations that use the same GUID.</p>
<p>You may for example explicitly set this variable in your
CMakeLists.txt to the value that has been generated per default.  You
should not use GUIDs that you did not generate yourself or which may
belong to other projects.</p>
<p>A GUID shall have the following fixed length syntax:</p>
<div class="highlight-none notranslate"><div class="highlight"><pre><span></span>XXXXXXXX-XXXX-XXXX-XXXX-XXXXXXXXXXXX
</pre></div>
</div>
<p>(each X represents an uppercase hexadecimal digit)</p>
</dd></dl>

<dl class="cmake variable">
<dt class="sig sig-object cmake" id="variable:CPACK_WIX_PRODUCT_GUID">
<span class="sig-name descname"><span class="pre">CPACK_WIX_PRODUCT_GUID</span></span><a class="headerlink" href="#variable:CPACK_WIX_PRODUCT_GUID" title="Permalink to this definition">¶</a></dt>
<dd><p>Product GUID (<code class="docutils literal notranslate"><span class="pre">Product/&#64;Id</span></code>)</p>
<p>Will be automatically generated unless explicitly provided.</p>
<p>If explicitly provided this will set the Product Id of your installer.</p>
<p>The installer will abort if it detects a pre-existing installation that
uses the same GUID.</p>
<p>The GUID shall use the syntax described for CPACK_WIX_UPGRADE_GUID.</p>
</dd></dl>

<dl class="cmake variable">
<dt class="sig sig-object cmake" id="variable:CPACK_WIX_LICENSE_RTF">
<span class="sig-name descname"><span class="pre">CPACK_WIX_LICENSE_RTF</span></span><a class="headerlink" href="#variable:CPACK_WIX_LICENSE_RTF" title="Permalink to this definition">¶</a></dt>
<dd><p>RTF License File</p>
<p>If CPACK_RESOURCE_FILE_LICENSE has an .rtf extension it is used as-is.</p>
<p>If CPACK_RESOURCE_FILE_LICENSE has an .txt extension it is implicitly
converted to RTF by the WIX Generator.
The expected encoding of the .txt file is UTF-8.</p>
<p>With CPACK_WIX_LICENSE_RTF you can override the license file used by the
WIX Generator in case CPACK_RESOURCE_FILE_LICENSE is in an unsupported
format or the .txt -&gt; .rtf conversion does not work as expected.</p>
</dd></dl>

<dl class="cmake variable">
<dt class="sig sig-object cmake" id="variable:CPACK_WIX_PRODUCT_ICON">
<span class="sig-name descname"><span class="pre">CPACK_WIX_PRODUCT_ICON</span></span><a class="headerlink" href="#variable:CPACK_WIX_PRODUCT_ICON" title="Permalink to this definition">¶</a></dt>
<dd><p>The Icon shown next to the program name in Add/Remove programs.</p>
<p>If set, this icon is used in place of the default icon.</p>
</dd></dl>

<dl class="cmake variable">
<dt class="sig sig-object cmake" id="variable:CPACK_WIX_UI_REF">
<span class="sig-name descname"><span class="pre">CPACK_WIX_UI_REF</span></span><a class="headerlink" href="#variable:CPACK_WIX_UI_REF" title="Permalink to this definition">¶</a></dt>
<dd><p>This variable allows you to override the Id of the <code class="docutils literal notranslate"><span class="pre">&lt;UIRef&gt;</span></code> element
in the WiX template.</p>
<p>The default is <code class="docutils literal notranslate"><span class="pre">WixUI_InstallDir</span></code> in case no CPack components have
been defined and <code class="docutils literal notranslate"><span class="pre">WixUI_FeatureTree</span></code> otherwise.</p>
</dd></dl>

<dl class="cmake variable">
<dt class="sig sig-object cmake" id="variable:CPACK_WIX_UI_BANNER">
<span class="sig-name descname"><span class="pre">CPACK_WIX_UI_BANNER</span></span><a class="headerlink" href="#variable:CPACK_WIX_UI_BANNER" title="Permalink to this definition">¶</a></dt>
<dd><p>The bitmap will appear at the top of all installer pages other than the
welcome and completion dialogs.</p>
<p>If set, this image will replace the default banner image.</p>
<p>This image must be 493 by 58 pixels.</p>
</dd></dl>

<dl class="cmake variable">
<dt class="sig sig-object cmake" id="variable:CPACK_WIX_UI_DIALOG">
<span class="sig-name descname"><span class="pre">CPACK_WIX_UI_DIALOG</span></span><a class="headerlink" href="#variable:CPACK_WIX_UI_DIALOG" title="Permalink to this definition">¶</a></dt>
<dd><p>Background bitmap used on the welcome and completion dialogs.</p>
<p>If this variable is set, the installer will replace the default dialog
image.</p>
<p>This image must be 493 by 312 pixels.</p>
</dd></dl>

<dl class="cmake variable">
<dt class="sig sig-object cmake" id="variable:CPACK_WIX_PROGRAM_MENU_FOLDER">
<span class="sig-name descname"><span class="pre">CPACK_WIX_PROGRAM_MENU_FOLDER</span></span><a class="headerlink" href="#variable:CPACK_WIX_PROGRAM_MENU_FOLDER" title="Permalink to this definition">¶</a></dt>
<dd><p>Start menu folder name for launcher.</p>
<p>If this variable is not set, it will be initialized with CPACK_PACKAGE_NAME</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.16: </span>If this variable is set to <code class="docutils literal notranslate"><span class="pre">.</span></code>, then application shortcuts will be
created directly in the start menu and the uninstaller shortcut will be
omitted.</p>
</div>
</dd></dl>

<dl class="cmake variable">
<dt class="sig sig-object cmake" id="variable:CPACK_WIX_CULTURES">
<span class="sig-name descname"><span class="pre">CPACK_WIX_CULTURES</span></span><a class="headerlink" href="#variable:CPACK_WIX_CULTURES" title="Permalink to this definition">¶</a></dt>
<dd><p>Language(s) of the installer</p>
<p>Languages are compiled into the WixUI extension library.  To use them,
simply provide the name of the culture.  If you specify more than one
culture identifier in a comma or semicolon delimited list, the first one
that is found will be used.  You can find a list of supported languages at:
<a class="reference external" href="https://wixtoolset.org/docs/v3/wixui/wixui_localization/">https://wixtoolset.org/docs/v3/wixui/wixui_localization/</a></p>
</dd></dl>

<dl class="cmake variable">
<dt class="sig sig-object cmake" id="variable:CPACK_WIX_TEMPLATE">
<span class="sig-name descname"><span class="pre">CPACK_WIX_TEMPLATE</span></span><a class="headerlink" href="#variable:CPACK_WIX_TEMPLATE" title="Permalink to this definition">¶</a></dt>
<dd><p>Template file for WiX generation</p>
<p>If this variable is set, the specified template will be used to generate
the WiX wxs file.  This should be used if further customization of the
output is required.</p>
<p>If this variable is not set, the default MSI template included with CMake
will be used.</p>
</dd></dl>

<dl class="cmake variable">
<dt class="sig sig-object cmake" id="variable:CPACK_WIX_PATCH_FILE">
<span class="sig-name descname"><span class="pre">CPACK_WIX_PATCH_FILE</span></span><a class="headerlink" href="#variable:CPACK_WIX_PATCH_FILE" title="Permalink to this definition">¶</a></dt>
<dd><p>Optional list of XML files with fragments to be inserted into
generated WiX sources.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.5: </span>Support listing multiple patch files.</p>
</div>
<p>This optional variable can be used to specify an XML file that the
WIX generator will use to inject fragments into its generated
source files.</p>
<p>Patch files understood by the CPack WIX generator
roughly follow this RELAX NG compact schema:</p>
<div class="highlight-none notranslate"><div class="highlight"><pre><span></span>start = CPackWiXPatch

CPackWiXPatch = element CPackWiXPatch { CPackWiXFragment* }

CPackWiXFragment = element CPackWiXFragment
{
    attribute Id { string },
    fragmentContent*
}

fragmentContent = element * - CPackWiXFragment
{
    (attribute * { text } | text | fragmentContent)*
}
</pre></div>
</div>
<p>Currently fragments can be injected into most
Component, File, Directory and Feature elements.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.3: </span>The following additional special Ids can be used:</p>
<ul class="simple">
<li><p><code class="docutils literal notranslate"><span class="pre">#PRODUCT</span></code> for the <code class="docutils literal notranslate"><span class="pre">&lt;Product&gt;</span></code> element.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">#PRODUCTFEATURE</span></code> for the root <code class="docutils literal notranslate"><span class="pre">&lt;Feature&gt;</span></code> element.</p></li>
</ul>
</div>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.7: </span>Support patching arbitrary <code class="docutils literal notranslate"><span class="pre">&lt;Feature&gt;</span></code> elements.</p>
</div>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.9: </span>Allow setting additional attributes.</p>
</div>
<p>The following example illustrates how this works.</p>
<p>Given that the WIX generator creates the following XML element:</p>
<div class="highlight-xml notranslate"><div class="highlight"><pre><span></span><span class="nt">&lt;Component</span><span class="w"> </span><span class="na">Id=</span><span class="s">&quot;CM_CP_applications.bin.my_libapp.exe&quot;</span><span class="w"> </span><span class="na">Guid=</span><span class="s">&quot;*&quot;</span><span class="nt">/&gt;</span>
</pre></div>
</div>
<p>The following XML patch file may be used to inject an Environment element
into it:</p>
<div class="highlight-xml notranslate"><div class="highlight"><pre><span></span><span class="nt">&lt;CPackWiXPatch&gt;</span>
<span class="w">  </span><span class="nt">&lt;CPackWiXFragment</span><span class="w"> </span><span class="na">Id=</span><span class="s">&quot;CM_CP_applications.bin.my_libapp.exe&quot;</span><span class="nt">&gt;</span>
<span class="w">    </span><span class="nt">&lt;Environment</span><span class="w"> </span><span class="na">Id=</span><span class="s">&quot;MyEnvironment&quot;</span><span class="w"> </span><span class="na">Action=</span><span class="s">&quot;set&quot;</span>
<span class="w">      </span><span class="na">Name=</span><span class="s">&quot;MyVariableName&quot;</span><span class="w"> </span><span class="na">Value=</span><span class="s">&quot;MyVariableValue&quot;</span><span class="nt">/&gt;</span>
<span class="w">  </span><span class="nt">&lt;/CPackWiXFragment&gt;</span>
<span class="nt">&lt;/CPackWiXPatch&gt;</span>
</pre></div>
</div>
</dd></dl>

<dl class="cmake variable">
<dt class="sig sig-object cmake" id="variable:CPACK_WIX_EXTRA_SOURCES">
<span class="sig-name descname"><span class="pre">CPACK_WIX_EXTRA_SOURCES</span></span><a class="headerlink" href="#variable:CPACK_WIX_EXTRA_SOURCES" title="Permalink to this definition">¶</a></dt>
<dd><p>Extra WiX source files</p>
<p>This variable provides an optional list of extra WiX source files (.wxs)
that should be compiled and linked.  The full path to source files is
required.</p>
</dd></dl>

<dl class="cmake variable">
<dt class="sig sig-object cmake" id="variable:CPACK_WIX_EXTRA_OBJECTS">
<span class="sig-name descname"><span class="pre">CPACK_WIX_EXTRA_OBJECTS</span></span><a class="headerlink" href="#variable:CPACK_WIX_EXTRA_OBJECTS" title="Permalink to this definition">¶</a></dt>
<dd><p>Extra WiX object files or libraries</p>
<p>This variable provides an optional list of extra WiX object (.wixobj)
and/or WiX library (.wixlib) files.  The full path to objects and libraries
is required.</p>
</dd></dl>

<dl class="cmake variable">
<dt class="sig sig-object cmake" id="variable:CPACK_WIX_EXTENSIONS">
<span class="sig-name descname"><span class="pre">CPACK_WIX_EXTENSIONS</span></span><a class="headerlink" href="#variable:CPACK_WIX_EXTENSIONS" title="Permalink to this definition">¶</a></dt>
<dd><p>This variable provides a list of additional extensions for the WiX
tools light and candle.</p>
</dd></dl>

<dl class="cmake variable">
<dt class="sig sig-object cmake" id="variable:CPACK_WIX_&lt;TOOL&gt;_EXTENSIONS">
<span class="sig-name descname"><span class="pre">CPACK_WIX_&lt;TOOL&gt;_EXTENSIONS</span></span><a class="headerlink" href="#variable:CPACK_WIX_<TOOL>_EXTENSIONS" title="Permalink to this definition">¶</a></dt>
<dd><p>This is the tool specific version of CPACK_WIX_EXTENSIONS.
<code class="docutils literal notranslate"><span class="pre">&lt;TOOL&gt;</span></code> can be either LIGHT or CANDLE.</p>
</dd></dl>

<dl class="cmake variable">
<dt class="sig sig-object cmake" id="variable:CPACK_WIX_&lt;TOOL&gt;_EXTRA_FLAGS">
<span class="sig-name descname"><span class="pre">CPACK_WIX_&lt;TOOL&gt;_EXTRA_FLAGS</span></span><a class="headerlink" href="#variable:CPACK_WIX_<TOOL>_EXTRA_FLAGS" title="Permalink to this definition">¶</a></dt>
<dd><p>This list variable allows you to pass additional
flags to the WiX tool <code class="docutils literal notranslate"><span class="pre">&lt;TOOL&gt;</span></code>.</p>
<p>Use it at your own risk.
Future versions of CPack may generate flags which may be in conflict
with your own flags.</p>
<p><code class="docutils literal notranslate"><span class="pre">&lt;TOOL&gt;</span></code> can be either LIGHT or CANDLE.</p>
</dd></dl>

<dl class="cmake variable">
<dt class="sig sig-object cmake" id="variable:CPACK_WIX_CMAKE_PACKAGE_REGISTRY">
<span class="sig-name descname"><span class="pre">CPACK_WIX_CMAKE_PACKAGE_REGISTRY</span></span><a class="headerlink" href="#variable:CPACK_WIX_CMAKE_PACKAGE_REGISTRY" title="Permalink to this definition">¶</a></dt>
<dd><p>If this variable is set the generated installer will create
an entry in the windows registry key
<code class="docutils literal notranslate"><span class="pre">HKEY_LOCAL_MACHINE\Software\Kitware\CMake\Packages\&lt;PackageName&gt;</span></code>
The value for <code class="docutils literal notranslate"><span class="pre">&lt;PackageName&gt;</span></code> is provided by this variable.</p>
<p>Assuming you also install a CMake configuration file this will
allow other CMake projects to find your package with
the <span class="target" id="index-0-command:find_package"></span><a class="reference internal" href="../command/find_package.html#command:find_package" title="find_package"><code class="xref cmake cmake-command docutils literal notranslate"><span class="pre">find_package()</span></code></a> command.</p>
</dd></dl>

<dl class="cmake variable">
<dt class="sig sig-object cmake" id="variable:CPACK_WIX_PROPERTY_&lt;PROPERTY&gt;">
<span class="sig-name descname"><span class="pre">CPACK_WIX_PROPERTY_&lt;PROPERTY&gt;</span></span><a class="headerlink" href="#variable:CPACK_WIX_PROPERTY_<PROPERTY>" title="Permalink to this definition">¶</a></dt>
<dd><div class="versionadded">
<p><span class="versionmodified added">New in version 3.1.</span></p>
</div>
<p>This variable can be used to provide a value for
the Windows Installer property <code class="docutils literal notranslate"><span class="pre">&lt;PROPERTY&gt;</span></code></p>
<p>The following list contains some example properties that can be used to
customize information under
&quot;Programs and Features&quot; (also known as &quot;Add or Remove Programs&quot;)</p>
<ul class="simple">
<li><p>ARPCOMMENTS - Comments</p></li>
<li><p>ARPHELPLINK - Help and support information URL</p></li>
<li><p>ARPURLINFOABOUT - General information URL</p></li>
<li><p>ARPURLUPDATEINFO - Update information URL</p></li>
<li><p>ARPHELPTELEPHONE - Help and support telephone number</p></li>
<li><p>ARPSIZE - Size (in kilobytes) of the application</p></li>
</ul>
</dd></dl>

<dl class="cmake variable">
<dt class="sig sig-object cmake" id="variable:CPACK_WIX_ROOT_FEATURE_TITLE">
<span class="sig-name descname"><span class="pre">CPACK_WIX_ROOT_FEATURE_TITLE</span></span><a class="headerlink" href="#variable:CPACK_WIX_ROOT_FEATURE_TITLE" title="Permalink to this definition">¶</a></dt>
<dd><div class="versionadded">
<p><span class="versionmodified added">New in version 3.7.</span></p>
</div>
<p>Sets the name of the root install feature in the WIX installer. Same as
CPACK_COMPONENT_&lt;compName&gt;_DISPLAY_NAME for components.</p>
</dd></dl>

<dl class="cmake variable">
<dt class="sig sig-object cmake" id="variable:CPACK_WIX_ROOT_FEATURE_DESCRIPTION">
<span class="sig-name descname"><span class="pre">CPACK_WIX_ROOT_FEATURE_DESCRIPTION</span></span><a class="headerlink" href="#variable:CPACK_WIX_ROOT_FEATURE_DESCRIPTION" title="Permalink to this definition">¶</a></dt>
<dd><div class="versionadded">
<p><span class="versionmodified added">New in version 3.7.</span></p>
</div>
<p>Sets the description of the root install feature in the WIX installer. Same as
CPACK_COMPONENT_&lt;compName&gt;_DESCRIPTION for components.</p>
</dd></dl>

<dl class="cmake variable">
<dt class="sig sig-object cmake" id="variable:CPACK_WIX_SKIP_PROGRAM_FOLDER">
<span class="sig-name descname"><span class="pre">CPACK_WIX_SKIP_PROGRAM_FOLDER</span></span><a class="headerlink" href="#variable:CPACK_WIX_SKIP_PROGRAM_FOLDER" title="Permalink to this definition">¶</a></dt>
<dd><div class="versionadded">
<p><span class="versionmodified added">New in version 3.7.</span></p>
</div>
<p>If this variable is set to true, the default install location
of the generated package will be CPACK_PACKAGE_INSTALL_DIRECTORY directly.
The install location will not be located relatively below
ProgramFiles or ProgramFiles64.</p>
<blockquote>
<div><div class="admonition note">
<p class="admonition-title">Note</p>
<p>Installers created with this feature do not take differences
between the system on which the installer is created
and the system on which the installer might be used into account.</p>
<p>It is therefore possible that the installer e.g. might try to install
onto a drive that is unavailable or unintended or a path that does not
follow the localization or convention of the system on which the
installation is performed.</p>
</div>
</div></blockquote>
</dd></dl>

<dl class="cmake variable">
<dt class="sig sig-object cmake" id="variable:CPACK_WIX_ROOT_FOLDER_ID">
<span class="sig-name descname"><span class="pre">CPACK_WIX_ROOT_FOLDER_ID</span></span><a class="headerlink" href="#variable:CPACK_WIX_ROOT_FOLDER_ID" title="Permalink to this definition">¶</a></dt>
<dd><div class="versionadded">
<p><span class="versionmodified added">New in version 3.9.</span></p>
</div>
<p>This variable allows specification of a custom root folder ID.
The generator specific <code class="docutils literal notranslate"><span class="pre">&lt;64&gt;</span></code> token can be used for
folder IDs that come in 32-bit and 64-bit variants.
In 32-bit builds the token will expand empty while in 64-bit builds
it will expand to <code class="docutils literal notranslate"><span class="pre">64</span></code>.</p>
<p>When unset generated installers will default installing to
<code class="docutils literal notranslate"><span class="pre">ProgramFiles&lt;64&gt;Folder</span></code>.</p>
</dd></dl>

<dl class="cmake variable">
<dt class="sig sig-object cmake" id="variable:CPACK_WIX_ROOT">
<span class="sig-name descname"><span class="pre">CPACK_WIX_ROOT</span></span><a class="headerlink" href="#variable:CPACK_WIX_ROOT" title="Permalink to this definition">¶</a></dt>
<dd><p>This variable can optionally be set to the root directory
of a custom WiX Toolset installation.</p>
<p>When unspecified CPack will try to locate a WiX Toolset
installation via the <code class="docutils literal notranslate"><span class="pre">WIX</span></code> environment variable instead.</p>
</dd></dl>

<dl class="cmake variable">
<dt class="sig sig-object cmake" id="variable:CPACK_WIX_CUSTOM_XMLNS">
<span class="sig-name descname"><span class="pre">CPACK_WIX_CUSTOM_XMLNS</span></span><a class="headerlink" href="#variable:CPACK_WIX_CUSTOM_XMLNS" title="Permalink to this definition">¶</a></dt>
<dd><div class="versionadded">
<p><span class="versionmodified added">New in version 3.19.</span></p>
</div>
<p>This variable provides a list of custom namespace declarations that are necessary
for using WiX extensions. Each declaration should be in the form name=url, where
name is the plain namespace without the usual xmlns: prefix and url is an unquoted
namespace url. A list of commonly known WiX schemata can be found here:
<a class="reference external" href="https://wixtoolset.org/docs/v3/xsd/">https://wixtoolset.org/docs/v3/xsd/</a></p>
</dd></dl>

<dl class="cmake variable">
<dt class="sig sig-object cmake" id="variable:CPACK_WIX_SKIP_WIX_UI_EXTENSION">
<span class="sig-name descname"><span class="pre">CPACK_WIX_SKIP_WIX_UI_EXTENSION</span></span><a class="headerlink" href="#variable:CPACK_WIX_SKIP_WIX_UI_EXTENSION" title="Permalink to this definition">¶</a></dt>
<dd><div class="versionadded">
<p><span class="versionmodified added">New in version 3.23.</span></p>
</div>
<p>If this variable is set then the inclusion of WixUIExtensions is skipped,
i.e. the <code class="docutils literal notranslate"><span class="pre">-ext</span> <span class="pre">&quot;WixUIExtension&quot;</span></code> command line is not included during
the execution of the WiX light tool.</p>
</dd></dl>

<dl class="cmake variable">
<dt class="sig sig-object cmake" id="variable:CPACK_WIX_ARCHITECTURE">
<span class="sig-name descname"><span class="pre">CPACK_WIX_ARCHITECTURE</span></span><a class="headerlink" href="#variable:CPACK_WIX_ARCHITECTURE" title="Permalink to this definition">¶</a></dt>
<dd><div class="versionadded">
<p><span class="versionmodified added">New in version 3.24.</span></p>
</div>
<p>This variable can be optionally set to specify the target architecture
of the installer. May for example be set to <code class="docutils literal notranslate"><span class="pre">x64</span></code> or <code class="docutils literal notranslate"><span class="pre">arm64</span></code>.</p>
<p>When unspecified, CPack will default to <code class="docutils literal notranslate"><span class="pre">x64</span></code> or <code class="docutils literal notranslate"><span class="pre">x86</span></code>.</p>
</dd></dl>

</section>
</section>


            <div class="clearer"></div>
          </div>
        </div>
      </div>
      <div class="sphinxsidebar" role="navigation" aria-label="main navigation">
        <div class="sphinxsidebarwrapper">
  <div>
    <h3><a href="../index.html">Table of Contents</a></h3>
    <ul>
<li><a class="reference internal" href="#">CPack WIX Generator</a><ul>
<li><a class="reference internal" href="#variables-specific-to-cpack-wix-generator">Variables specific to CPack WIX generator</a></li>
</ul>
</li>
</ul>

  </div>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="rpm.html"
                          title="previous chapter">CPack RPM Generator</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="../guide/tutorial/index.html"
                          title="next chapter">CMake Tutorial</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../_sources/cpack_gen/wix.rst.txt"
            rel="nofollow">Show Source</a></li>
    </ul>
   </div>
<div id="searchbox" style="display: none" role="search">
  <h3 id="searchlabel">Quick search</h3>
    <div class="searchformwrapper">
    <form class="search" action="../search.html" method="get">
      <input type="text" name="q" aria-labelledby="searchlabel" autocomplete="off" autocorrect="off" autocapitalize="off" spellcheck="false"/>
      <input type="submit" value="Go" />
    </form>
    </div>
</div>
<script>document.getElementById('searchbox').style.display = "block"</script>
        </div>
      </div>
      <div class="clearer"></div>
    </div>
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             >index</a></li>
        <li class="right" >
          <a href="../guide/tutorial/index.html" title="CMake Tutorial"
             >next</a> |</li>
        <li class="right" >
          <a href="rpm.html" title="CPack RPM Generator"
             >previous</a> |</li>
  <li>
    <img src="../_static/cmake-logo-16.png" alt=""
         style="vertical-align: middle; margin-top: -2px" />
  </li>
  <li>
    <a href="https://cmake.org/">CMake</a> &#187;
  </li>
  <li>
    <a href="../index.html">3.28.1 Documentation</a> &#187;
  </li>

          <li class="nav-item nav-item-1"><a href="../manual/cpack-generators.7.html" >cpack-generators(7)</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href="">CPack WIX Generator</a></li> 
      </ul>
    </div>

    <div class="footer" role="contentinfo">
        &#169; Copyright 2000-2023 Kitware, Inc. and Contributors.
      Created using <a href="https://www.sphinx-doc.org/">Sphinx</a> 6.2.1.
    </div>
  </body>
</html>