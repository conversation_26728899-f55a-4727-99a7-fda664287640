
<!DOCTYPE html>

<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="generator" content="Docutils 0.19: https://docutils.sourceforge.io/" />

  <title>FindPython &mdash; CMake 3.28.1 Documentation</title>

    <link rel="stylesheet" type="text/css" href="../_static/pygments.css" />
    <link rel="stylesheet" type="text/css" href="../_static/cmake.css" />
    
    <script data-url_root="../" id="documentation_options" src="../_static/documentation_options.js"></script>
    <script src="../_static/doctools.js"></script>
    <script src="../_static/sphinx_highlight.js"></script>
    
    <link rel="icon" href="../_static/cmake-favicon.ico"/>
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="next" title="FindPython2" href="FindPython2.html" />
    <link rel="prev" title="FindProtobuf" href="FindProtobuf.html" />
 

  </head><body>

    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             accesskey="I">index</a></li>
        <li class="right" >
          <a href="FindPython2.html" title="FindPython2"
             accesskey="N">next</a> |</li>
        <li class="right" >
          <a href="FindProtobuf.html" title="FindProtobuf"
             accesskey="P">previous</a> |</li>
  <li>
    <img src="../_static/cmake-logo-16.png" alt=""
         style="vertical-align: middle; margin-top: -2px" />
  </li>
  <li>
    <a href="https://cmake.org/">CMake</a> &#187;
  </li>
  <li>
    <a href="../index.html">3.28.1 Documentation</a> &#187;
  </li>

          <li class="nav-item nav-item-1"><a href="../manual/cmake-modules.7.html" accesskey="U">cmake-modules(7)</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href="">FindPython</a></li> 
      </ul>
    </div>  

    <div class="document">
      <div class="documentwrapper">
        <div class="bodywrapper">
          <div class="body" role="main">
            
  <section id="findpython">
<span id="module:FindPython"></span><h1>FindPython<a class="headerlink" href="#findpython" title="Permalink to this heading">¶</a></h1>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.12.</span></p>
</div>
<p>Find Python interpreter, compiler and development environment (include
directories and libraries).</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.19: </span>When a version is requested, it can be specified as a simple value or as a
range. For a detailed description of version range usage and capabilities,
refer to the <span class="target" id="index-0-command:find_package"></span><a class="reference internal" href="../command/find_package.html#command:find_package" title="find_package"><code class="xref cmake cmake-command docutils literal notranslate"><span class="pre">find_package()</span></code></a> command.</p>
</div>
<p>The following components are supported:</p>
<ul>
<li><p><code class="docutils literal notranslate"><span class="pre">Interpreter</span></code>: search for Python interpreter.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">Compiler</span></code>: search for Python compiler. Only offered by IronPython.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">Development</span></code>: search for development artifacts (include directories and
libraries).</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.18: </span>This component includes two sub-components which can be specified
independently:</p>
<ul class="simple">
<li><p><code class="docutils literal notranslate"><span class="pre">Development.Module</span></code>: search for artifacts for Python module
developments.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">Development.Embed</span></code>: search for artifacts for Python embedding
developments.</p></li>
</ul>
</div>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.26: </span></p>
<ul class="simple">
<li><p><code class="docutils literal notranslate"><span class="pre">Development.SABIModule</span></code>: search for artifacts for Python module
developments using the
<a class="reference external" href="https://docs.python.org/3/c-api/stable.html">Stable Application Binary Interface</a>.
This component is available only for version <code class="docutils literal notranslate"><span class="pre">3.2</span></code> and upper.</p></li>
</ul>
</div>
</li>
<li><p><code class="docutils literal notranslate"><span class="pre">NumPy</span></code>: search for NumPy include directories.</p></li>
</ul>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.14: </span>Added the <code class="docutils literal notranslate"><span class="pre">NumPy</span></code> component.</p>
</div>
<p>If no <code class="docutils literal notranslate"><span class="pre">COMPONENTS</span></code> are specified, <code class="docutils literal notranslate"><span class="pre">Interpreter</span></code> is assumed.</p>
<p>If component <code class="docutils literal notranslate"><span class="pre">Development</span></code> is specified, it implies sub-components
<code class="docutils literal notranslate"><span class="pre">Development.Module</span></code> and <code class="docutils literal notranslate"><span class="pre">Development.Embed</span></code>.</p>
<p>To ensure consistent versions between components <code class="docutils literal notranslate"><span class="pre">Interpreter</span></code>, <code class="docutils literal notranslate"><span class="pre">Compiler</span></code>,
<code class="docutils literal notranslate"><span class="pre">Development</span></code> (or one of its sub-components) and <code class="docutils literal notranslate"><span class="pre">NumPy</span></code>, specify all
components at the same time:</p>
<div class="highlight-none notranslate"><div class="highlight"><pre><span></span>find_package (Python COMPONENTS Interpreter Development)
</pre></div>
</div>
<p>This module looks preferably for version 3 of Python. If not found, version 2
is searched.
To manage concurrent versions 3 and 2 of Python, use <span class="target" id="index-0-module:FindPython3"></span><a class="reference internal" href="FindPython3.html#module:FindPython3" title="FindPython3"><code class="xref cmake cmake-module docutils literal notranslate"><span class="pre">FindPython3</span></code></a> and
<span class="target" id="index-0-module:FindPython2"></span><a class="reference internal" href="FindPython2.html#module:FindPython2" title="FindPython2"><code class="xref cmake cmake-module docutils literal notranslate"><span class="pre">FindPython2</span></code></a> modules rather than this one.</p>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>If components <code class="docutils literal notranslate"><span class="pre">Interpreter</span></code> and <code class="docutils literal notranslate"><span class="pre">Development</span></code> (or one of its
sub-components) are both specified, this module search only for interpreter
with same platform architecture as the one defined by CMake
configuration. This constraint does not apply if only <code class="docutils literal notranslate"><span class="pre">Interpreter</span></code>
component is specified.</p>
</div>
<section id="imported-targets">
<h2>Imported Targets<a class="headerlink" href="#imported-targets" title="Permalink to this heading">¶</a></h2>
<p>This module defines the following <a class="reference internal" href="../manual/cmake-buildsystem.7.html#imported-targets"><span class="std std-ref">Imported Targets</span></a>:</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.14: </span><a class="reference internal" href="../manual/cmake-buildsystem.7.html#imported-targets"><span class="std std-ref">Imported Targets</span></a> are only created when
<span class="target" id="index-0-prop_gbl:CMAKE_ROLE"></span><a class="reference internal" href="../prop_gbl/CMAKE_ROLE.html#prop_gbl:CMAKE_ROLE" title="CMAKE_ROLE"><code class="xref cmake cmake-prop_gbl docutils literal notranslate"><span class="pre">CMAKE_ROLE</span></code></a> is <code class="docutils literal notranslate"><span class="pre">PROJECT</span></code>.</p>
</div>
<dl>
<dt><code class="docutils literal notranslate"><span class="pre">Python::Interpreter</span></code></dt><dd><p>Python interpreter. Target defined if component <code class="docutils literal notranslate"><span class="pre">Interpreter</span></code> is found.</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">Python::Compiler</span></code></dt><dd><p>Python compiler. Target defined if component <code class="docutils literal notranslate"><span class="pre">Compiler</span></code> is found.</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">Python::Module</span></code></dt><dd><div class="versionadded">
<p><span class="versionmodified added">New in version 3.15.</span></p>
</div>
<p>Python library for Python module. Target defined if component
<code class="docutils literal notranslate"><span class="pre">Development.Module</span></code> is found.</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">Python::SABIModule</span></code></dt><dd><div class="versionadded">
<p><span class="versionmodified added">New in version 3.26.</span></p>
</div>
<p>Python library for Python module using the Stable Application Binary
Interface. Target defined if component <code class="docutils literal notranslate"><span class="pre">Development.SABIModule</span></code> is found.</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">Python::Python</span></code></dt><dd><p>Python library for Python embedding. Target defined if component
<code class="docutils literal notranslate"><span class="pre">Development.Embed</span></code> is found.</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">Python::NumPy</span></code></dt><dd><div class="versionadded">
<p><span class="versionmodified added">New in version 3.14.</span></p>
</div>
<p>NumPy Python library. Target defined if component <code class="docutils literal notranslate"><span class="pre">NumPy</span></code> is found.</p>
</dd>
</dl>
</section>
<section id="result-variables">
<h2>Result Variables<a class="headerlink" href="#result-variables" title="Permalink to this heading">¶</a></h2>
<p>This module will set the following variables in your project
(see <a class="reference internal" href="../manual/cmake-developer.7.html#cmake-developer-standard-variable-names"><span class="std std-ref">Standard Variable Names</span></a>):</p>
<dl>
<dt><code class="docutils literal notranslate"><span class="pre">Python_FOUND</span></code></dt><dd><p>System has the Python requested components.</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">Python_Interpreter_FOUND</span></code></dt><dd><p>System has the Python interpreter.</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">Python_EXECUTABLE</span></code></dt><dd><p>Path to the Python interpreter.</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">Python_INTERPRETER_ID</span></code></dt><dd><dl class="simple">
<dt>A short string unique to the interpreter. Possible values include:</dt><dd><ul class="simple">
<li><p>Python</p></li>
<li><p>ActivePython</p></li>
<li><p>Anaconda</p></li>
<li><p>Canopy</p></li>
<li><p>IronPython</p></li>
<li><p>PyPy</p></li>
</ul>
</dd>
</dl>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">Python_STDLIB</span></code></dt><dd><p>Standard platform independent installation directory.</p>
<p>Information returned by <code class="docutils literal notranslate"><span class="pre">sysconfig.get_path('stdlib')</span></code>.</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">Python_STDARCH</span></code></dt><dd><p>Standard platform dependent installation directory.</p>
<p>Information returned by <code class="docutils literal notranslate"><span class="pre">sysconfig.get_path('platstdlib')</span></code>.</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">Python_SITELIB</span></code></dt><dd><p>Third-party platform independent installation directory.</p>
<p>Information returned by <code class="docutils literal notranslate"><span class="pre">sysconfig.get_path('purelib')</span></code>.</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">Python_SITEARCH</span></code></dt><dd><p>Third-party platform dependent installation directory.</p>
<p>Information returned by <code class="docutils literal notranslate"><span class="pre">sysconfig.get_path('platlib')</span></code>.</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">Python_SOABI</span></code></dt><dd><div class="versionadded">
<p><span class="versionmodified added">New in version 3.17.</span></p>
</div>
<p>Extension suffix for modules.</p>
<p>Information computed from <code class="docutils literal notranslate"><span class="pre">sysconfig.get_config_var('EXT_SUFFIX')</span></code> or
<code class="docutils literal notranslate"><span class="pre">sysconfig.get_config_var('SOABI')</span></code> or
<code class="docutils literal notranslate"><span class="pre">python3-config</span> <span class="pre">--extension-suffix</span></code>.</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">Python_SOSABI</span></code></dt><dd><div class="versionadded">
<p><span class="versionmodified added">New in version 3.26.</span></p>
</div>
<p>Extension suffix for modules using the Stable Application Binary Interface.</p>
<p>Information computed from <code class="docutils literal notranslate"><span class="pre">importlib.machinery.EXTENSION_SUFFIXES</span></code> if the
COMPONENT <code class="docutils literal notranslate"><span class="pre">Interpreter</span></code> was specified. Otherwise, the extension is <code class="docutils literal notranslate"><span class="pre">abi3</span></code>
except for <code class="docutils literal notranslate"><span class="pre">Windows</span></code>, <code class="docutils literal notranslate"><span class="pre">MSYS</span></code> and <code class="docutils literal notranslate"><span class="pre">CYGWIN</span></code> for which this is an empty
string.</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">Python_Compiler_FOUND</span></code></dt><dd><p>System has the Python compiler.</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">Python_COMPILER</span></code></dt><dd><p>Path to the Python compiler. Only offered by IronPython.</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">Python_COMPILER_ID</span></code></dt><dd><dl class="simple">
<dt>A short string unique to the compiler. Possible values include:</dt><dd><ul class="simple">
<li><p>IronPython</p></li>
</ul>
</dd>
</dl>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">Python_DOTNET_LAUNCHER</span></code></dt><dd><div class="versionadded">
<p><span class="versionmodified added">New in version 3.18.</span></p>
</div>
<p>The <code class="docutils literal notranslate"><span class="pre">.Net</span></code> interpreter. Only used by <code class="docutils literal notranslate"><span class="pre">IronPython</span></code> implementation.</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">Python_Development_FOUND</span></code></dt><dd><p>System has the Python development artifacts.</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">Python_Development.Module_FOUND</span></code></dt><dd><div class="versionadded">
<p><span class="versionmodified added">New in version 3.18.</span></p>
</div>
<p>System has the Python development artifacts for Python module.</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">Python_Development.SABIModule_FOUND</span></code></dt><dd><div class="versionadded">
<p><span class="versionmodified added">New in version 3.26.</span></p>
</div>
<p>System has the Python development artifacts for Python module using the
Stable Application Binary Interface.</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">Python_Development.Embed_FOUND</span></code></dt><dd><div class="versionadded">
<p><span class="versionmodified added">New in version 3.18.</span></p>
</div>
<p>System has the Python development artifacts for Python embedding.</p>
</dd>
</dl>
<p><code class="docutils literal notranslate"><span class="pre">Python_INCLUDE_DIRS</span></code></p>
<blockquote>
<div><p>The Python include directories.</p>
</div></blockquote>
<dl>
<dt><code class="docutils literal notranslate"><span class="pre">Python_LINK_OPTIONS</span></code></dt><dd><div class="versionadded">
<p><span class="versionmodified added">New in version 3.19.</span></p>
</div>
<p>The Python link options. Some configurations require specific link options
for a correct build and execution.</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">Python_LIBRARIES</span></code></dt><dd><p>The Python libraries.</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">Python_LIBRARY_DIRS</span></code></dt><dd><p>The Python library directories.</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">Python_RUNTIME_LIBRARY_DIRS</span></code></dt><dd><p>The Python runtime library directories.</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">Python_SABI_LIBRARIES</span></code></dt><dd><div class="versionadded">
<p><span class="versionmodified added">New in version 3.26.</span></p>
</div>
<p>The Python libraries for the Stable Application Binary Interface.</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">Python_SABI_LIBRARY_DIRS</span></code></dt><dd><div class="versionadded">
<p><span class="versionmodified added">New in version 3.26.</span></p>
</div>
<p>The Python <code class="docutils literal notranslate"><span class="pre">SABI</span></code> library directories.</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">Python_RUNTIME_SABI_LIBRARY_DIRS</span></code></dt><dd><div class="versionadded">
<p><span class="versionmodified added">New in version 3.26.</span></p>
</div>
<p>The Python runtime <code class="docutils literal notranslate"><span class="pre">SABI</span></code> library directories.</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">Python_VERSION</span></code></dt><dd><p>Python version.</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">Python_VERSION_MAJOR</span></code></dt><dd><p>Python major version.</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">Python_VERSION_MINOR</span></code></dt><dd><p>Python minor version.</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">Python_VERSION_PATCH</span></code></dt><dd><p>Python patch version.</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">Python_PyPy_VERSION</span></code></dt><dd><div class="versionadded">
<p><span class="versionmodified added">New in version 3.18.</span></p>
</div>
<p>Python PyPy version.</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">Python_NumPy_FOUND</span></code></dt><dd><div class="versionadded">
<p><span class="versionmodified added">New in version 3.14.</span></p>
</div>
<p>System has the NumPy.</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">Python_NumPy_INCLUDE_DIRS</span></code></dt><dd><div class="versionadded">
<p><span class="versionmodified added">New in version 3.14.</span></p>
</div>
<p>The NumPy include directories.</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">Python_NumPy_VERSION</span></code></dt><dd><div class="versionadded">
<p><span class="versionmodified added">New in version 3.14.</span></p>
</div>
<p>The NumPy version.</p>
</dd>
</dl>
</section>
<section id="hints">
<h2>Hints<a class="headerlink" href="#hints" title="Permalink to this heading">¶</a></h2>
<dl>
<dt><code class="docutils literal notranslate"><span class="pre">Python_ROOT_DIR</span></code></dt><dd><p>Define the root directory of a Python installation.</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">Python_USE_STATIC_LIBS</span></code></dt><dd><ul class="simple">
<li><p>If not defined, search for shared libraries and static libraries in that
order.</p></li>
<li><p>If set to TRUE, search <strong>only</strong> for static libraries.</p></li>
<li><p>If set to FALSE, search <strong>only</strong> for shared libraries.</p></li>
</ul>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>This hint will be ignored on <code class="docutils literal notranslate"><span class="pre">Windows</span></code> because static libraries are not
available on this platform.</p>
</div>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">Python_FIND_ABI</span></code></dt><dd><div class="versionadded">
<p><span class="versionmodified added">New in version 3.16.</span></p>
</div>
<p>This variable defines which ABIs, as defined in <span class="target" id="index-0"></span><a class="pep reference external" href="https://peps.python.org/pep-3149/"><strong>PEP 3149</strong></a>, should be
searched.</p>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>This hint will be honored only when searched for <code class="docutils literal notranslate"><span class="pre">Python</span></code> version 3.</p>
</div>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>If <code class="docutils literal notranslate"><span class="pre">Python_FIND_ABI</span></code> is not defined, any ABI will be searched.</p>
</div>
<p>The <code class="docutils literal notranslate"><span class="pre">Python_FIND_ABI</span></code> variable is a 3-tuple specifying, in that order,
<code class="docutils literal notranslate"><span class="pre">pydebug</span></code> (<code class="docutils literal notranslate"><span class="pre">d</span></code>), <code class="docutils literal notranslate"><span class="pre">pymalloc</span></code> (<code class="docutils literal notranslate"><span class="pre">m</span></code>) and <code class="docutils literal notranslate"><span class="pre">unicode</span></code> (<code class="docutils literal notranslate"><span class="pre">u</span></code>) flags.
Each element can be set to one of the following:</p>
<ul class="simple">
<li><p><code class="docutils literal notranslate"><span class="pre">ON</span></code>: Corresponding flag is selected.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">OFF</span></code>: Corresponding flag is not selected.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">ANY</span></code>: The two possibilities (<code class="docutils literal notranslate"><span class="pre">ON</span></code> and <code class="docutils literal notranslate"><span class="pre">OFF</span></code>) will be searched.</p></li>
</ul>
<p>From this 3-tuple, various ABIs will be searched starting from the most
specialized to the most general. Moreover, <code class="docutils literal notranslate"><span class="pre">debug</span></code> versions will be
searched <strong>after</strong> <code class="docutils literal notranslate"><span class="pre">non-debug</span></code> ones.</p>
<p>For example, if we have:</p>
<div class="highlight-none notranslate"><div class="highlight"><pre><span></span>set (Python_FIND_ABI &quot;ON&quot; &quot;ANY&quot; &quot;ANY&quot;)
</pre></div>
</div>
<p>The following flags combinations will be appended, in that order, to the
artifact names: <code class="docutils literal notranslate"><span class="pre">dmu</span></code>, <code class="docutils literal notranslate"><span class="pre">dm</span></code>, <code class="docutils literal notranslate"><span class="pre">du</span></code>, and <code class="docutils literal notranslate"><span class="pre">d</span></code>.</p>
<p>And to search any possible ABIs:</p>
<div class="highlight-none notranslate"><div class="highlight"><pre><span></span>set (Python_FIND_ABI &quot;ANY&quot; &quot;ANY&quot; &quot;ANY&quot;)
</pre></div>
</div>
<p>The following combinations, in that order, will be used: <code class="docutils literal notranslate"><span class="pre">mu</span></code>, <code class="docutils literal notranslate"><span class="pre">m</span></code>,
<code class="docutils literal notranslate"><span class="pre">u</span></code>, <code class="docutils literal notranslate"><span class="pre">&lt;empty&gt;</span></code>, <code class="docutils literal notranslate"><span class="pre">dmu</span></code>, <code class="docutils literal notranslate"><span class="pre">dm</span></code>, <code class="docutils literal notranslate"><span class="pre">du</span></code> and <code class="docutils literal notranslate"><span class="pre">d</span></code>.</p>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>This hint is useful only on <code class="docutils literal notranslate"><span class="pre">POSIX</span></code> systems. So, on <code class="docutils literal notranslate"><span class="pre">Windows</span></code> systems,
when <code class="docutils literal notranslate"><span class="pre">Python_FIND_ABI</span></code> is defined, <code class="docutils literal notranslate"><span class="pre">Python</span></code> distributions from
<a class="reference external" href="https://www.python.org/">python.org</a> will be found only if value for
each flag is <code class="docutils literal notranslate"><span class="pre">OFF</span></code> or <code class="docutils literal notranslate"><span class="pre">ANY</span></code>.</p>
</div>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">Python_FIND_STRATEGY</span></code></dt><dd><div class="versionadded">
<p><span class="versionmodified added">New in version 3.15.</span></p>
</div>
<p>This variable defines how lookup will be done.
The <code class="docutils literal notranslate"><span class="pre">Python_FIND_STRATEGY</span></code> variable can be set to one of the following:</p>
<ul class="simple">
<li><p><code class="docutils literal notranslate"><span class="pre">VERSION</span></code>: Try to find the most recent version in all specified
locations.
This is the default if policy <span class="target" id="index-0-policy:CMP0094"></span><a class="reference internal" href="../policy/CMP0094.html#policy:CMP0094" title="CMP0094"><code class="xref cmake cmake-policy docutils literal notranslate"><span class="pre">CMP0094</span></code></a> is undefined or set to
<code class="docutils literal notranslate"><span class="pre">OLD</span></code>.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">LOCATION</span></code>: Stops lookup as soon as a version satisfying version
constraints is founded.
This is the default if policy <span class="target" id="index-1-policy:CMP0094"></span><a class="reference internal" href="../policy/CMP0094.html#policy:CMP0094" title="CMP0094"><code class="xref cmake cmake-policy docutils literal notranslate"><span class="pre">CMP0094</span></code></a> is set to <code class="docutils literal notranslate"><span class="pre">NEW</span></code>.</p></li>
</ul>
<p>See also <code class="docutils literal notranslate"><span class="pre">Python_FIND_UNVERSIONED_NAMES</span></code>.</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">Python_FIND_REGISTRY</span></code></dt><dd><div class="versionadded">
<p><span class="versionmodified added">New in version 3.13.</span></p>
</div>
<p>On Windows the <code class="docutils literal notranslate"><span class="pre">Python_FIND_REGISTRY</span></code> variable determine the order
of preference between registry and environment variables.
the <code class="docutils literal notranslate"><span class="pre">Python_FIND_REGISTRY</span></code> variable can be set to one of the following:</p>
<ul class="simple">
<li><p><code class="docutils literal notranslate"><span class="pre">FIRST</span></code>: Try to use registry before environment variables.
This is the default.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">LAST</span></code>: Try to use registry after environment variables.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">NEVER</span></code>: Never try to use registry.</p></li>
</ul>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">Python_FIND_FRAMEWORK</span></code></dt><dd><div class="versionadded">
<p><span class="versionmodified added">New in version 3.15.</span></p>
</div>
<p>On macOS the <code class="docutils literal notranslate"><span class="pre">Python_FIND_FRAMEWORK</span></code> variable determine the order of
preference between Apple-style and unix-style package components.
This variable can take same values as <span class="target" id="index-0-variable:CMAKE_FIND_FRAMEWORK"></span><a class="reference internal" href="../variable/CMAKE_FIND_FRAMEWORK.html#variable:CMAKE_FIND_FRAMEWORK" title="CMAKE_FIND_FRAMEWORK"><code class="xref cmake cmake-variable docutils literal notranslate"><span class="pre">CMAKE_FIND_FRAMEWORK</span></code></a>
variable.</p>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>Value <code class="docutils literal notranslate"><span class="pre">ONLY</span></code> is not supported so <code class="docutils literal notranslate"><span class="pre">FIRST</span></code> will be used instead.</p>
</div>
<p>If <code class="docutils literal notranslate"><span class="pre">Python_FIND_FRAMEWORK</span></code> is not defined, <span class="target" id="index-1-variable:CMAKE_FIND_FRAMEWORK"></span><a class="reference internal" href="../variable/CMAKE_FIND_FRAMEWORK.html#variable:CMAKE_FIND_FRAMEWORK" title="CMAKE_FIND_FRAMEWORK"><code class="xref cmake cmake-variable docutils literal notranslate"><span class="pre">CMAKE_FIND_FRAMEWORK</span></code></a>
variable will be used, if any.</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">Python_FIND_VIRTUALENV</span></code></dt><dd><div class="versionadded">
<p><span class="versionmodified added">New in version 3.15.</span></p>
</div>
<p>This variable defines the handling of virtual environments managed by
<code class="docutils literal notranslate"><span class="pre">virtualenv</span></code> or <code class="docutils literal notranslate"><span class="pre">conda</span></code>. It is meaningful only when a virtual environment
is active (i.e. the <code class="docutils literal notranslate"><span class="pre">activate</span></code> script has been evaluated). In this case, it
takes precedence over <code class="docutils literal notranslate"><span class="pre">Python_FIND_REGISTRY</span></code> and <code class="docutils literal notranslate"><span class="pre">CMAKE_FIND_FRAMEWORK</span></code>
variables.  The <code class="docutils literal notranslate"><span class="pre">Python_FIND_VIRTUALENV</span></code> variable can be set to one of the
following:</p>
<ul class="simple">
<li><p><code class="docutils literal notranslate"><span class="pre">FIRST</span></code>: The virtual environment is used before any other standard
paths to look-up for the interpreter. This is the default.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">ONLY</span></code>: Only the virtual environment is used to look-up for the
interpreter.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">STANDARD</span></code>: The virtual environment is not used to look-up for the
interpreter but environment variable <code class="docutils literal notranslate"><span class="pre">PATH</span></code> is always considered.
In this case, variable <code class="docutils literal notranslate"><span class="pre">Python_FIND_REGISTRY</span></code> (Windows) or
<code class="docutils literal notranslate"><span class="pre">CMAKE_FIND_FRAMEWORK</span></code> (macOS) can be set with value <code class="docutils literal notranslate"><span class="pre">LAST</span></code> or
<code class="docutils literal notranslate"><span class="pre">NEVER</span></code> to select preferably the interpreter from the virtual
environment.</p></li>
</ul>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.17: </span>Added support for <code class="docutils literal notranslate"><span class="pre">conda</span></code> environments.</p>
</div>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>If the component <code class="docutils literal notranslate"><span class="pre">Development</span></code> is requested, it is <strong>strongly</strong>
recommended to also include the component <code class="docutils literal notranslate"><span class="pre">Interpreter</span></code> to get expected
result.</p>
</div>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">Python_FIND_IMPLEMENTATIONS</span></code></dt><dd><div class="versionadded">
<p><span class="versionmodified added">New in version 3.18.</span></p>
</div>
<p>This variable defines, in an ordered list, the different implementations
which will be searched. The <code class="docutils literal notranslate"><span class="pre">Python_FIND_IMPLEMENTATIONS</span></code> variable can
hold the following values:</p>
<ul class="simple">
<li><p><code class="docutils literal notranslate"><span class="pre">CPython</span></code>: this is the standard implementation. Various products, like
<code class="docutils literal notranslate"><span class="pre">Anaconda</span></code> or <code class="docutils literal notranslate"><span class="pre">ActivePython</span></code>, rely on this implementation.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">IronPython</span></code>: This implementation use the <code class="docutils literal notranslate"><span class="pre">CSharp</span></code> language for
<code class="docutils literal notranslate"><span class="pre">.NET</span> <span class="pre">Framework</span></code> on top of the <cite>Dynamic Language Runtime</cite> (<code class="docutils literal notranslate"><span class="pre">DLR</span></code>).
See <a class="reference external" href="https://ironpython.net">IronPython</a>.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">PyPy</span></code>: This implementation use <code class="docutils literal notranslate"><span class="pre">RPython</span></code> language and
<code class="docutils literal notranslate"><span class="pre">RPython</span> <span class="pre">translation</span> <span class="pre">toolchain</span></code> to produce the python interpreter.
See <a class="reference external" href="https://www.pypy.org">PyPy</a>.</p></li>
</ul>
<p>The default value is:</p>
<ul class="simple">
<li><p>Windows platform: <code class="docutils literal notranslate"><span class="pre">CPython</span></code>, <code class="docutils literal notranslate"><span class="pre">IronPython</span></code></p></li>
<li><p>Other platforms: <code class="docutils literal notranslate"><span class="pre">CPython</span></code></p></li>
</ul>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>This hint has the lowest priority of all hints, so even if, for example,
you specify <code class="docutils literal notranslate"><span class="pre">IronPython</span></code> first and <code class="docutils literal notranslate"><span class="pre">CPython</span></code> in second, a python
product based on <code class="docutils literal notranslate"><span class="pre">CPython</span></code> can be selected because, for example with
<code class="docutils literal notranslate"><span class="pre">Python_FIND_STRATEGY=LOCATION</span></code>, each location will be search first for
<code class="docutils literal notranslate"><span class="pre">IronPython</span></code> and second for <code class="docutils literal notranslate"><span class="pre">CPython</span></code>.</p>
</div>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>When <code class="docutils literal notranslate"><span class="pre">IronPython</span></code> is specified, on platforms other than <code class="docutils literal notranslate"><span class="pre">Windows</span></code>, the
<code class="docutils literal notranslate"><span class="pre">.Net</span></code> interpreter (i.e. <code class="docutils literal notranslate"><span class="pre">mono</span></code> command) is expected to be available
through the <code class="docutils literal notranslate"><span class="pre">PATH</span></code> variable.</p>
</div>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">Python_FIND_UNVERSIONED_NAMES</span></code></dt><dd><div class="versionadded">
<p><span class="versionmodified added">New in version 3.20.</span></p>
</div>
<p>This variable defines how the generic names will be searched. Currently, it
only applies to the generic names of the interpreter, namely, <code class="docutils literal notranslate"><span class="pre">python3</span></code> or
<code class="docutils literal notranslate"><span class="pre">python2</span></code> and <code class="docutils literal notranslate"><span class="pre">python</span></code>.
The <code class="docutils literal notranslate"><span class="pre">Python_FIND_UNVERSIONED_NAMES</span></code> variable can be set to one of the
following values:</p>
<ul class="simple">
<li><p><code class="docutils literal notranslate"><span class="pre">FIRST</span></code>: The generic names are searched before the more specialized ones
(such as <code class="docutils literal notranslate"><span class="pre">python2.5</span></code> for example).</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">LAST</span></code>: The generic names are searched after the more specialized ones.
This is the default.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">NEVER</span></code>: The generic name are not searched at all.</p></li>
</ul>
<p>See also <code class="docutils literal notranslate"><span class="pre">Python_FIND_STRATEGY</span></code>.</p>
</dd>
</dl>
</section>
<section id="artifacts-specification">
<h2>Artifacts Specification<a class="headerlink" href="#artifacts-specification" title="Permalink to this heading">¶</a></h2>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.16.</span></p>
</div>
<p>To solve special cases, it is possible to specify directly the artifacts by
setting the following variables:</p>
<dl>
<dt><code class="docutils literal notranslate"><span class="pre">Python_EXECUTABLE</span></code></dt><dd><p>The path to the interpreter.</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">Python_COMPILER</span></code></dt><dd><p>The path to the compiler.</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">Python_DOTNET_LAUNCHER</span></code></dt><dd><div class="versionadded">
<p><span class="versionmodified added">New in version 3.18.</span></p>
</div>
<p>The <code class="docutils literal notranslate"><span class="pre">.Net</span></code> interpreter. Only used by <code class="docutils literal notranslate"><span class="pre">IronPython</span></code> implementation.</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">Python_LIBRARY</span></code></dt><dd><p>The path to the library. It will be used to compute the
variables <code class="docutils literal notranslate"><span class="pre">Python_LIBRARIES</span></code>, <code class="docutils literal notranslate"><span class="pre">Python_LIBRARY_DIRS</span></code> and
<code class="docutils literal notranslate"><span class="pre">Python_RUNTIME_LIBRARY_DIRS</span></code>.</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">Python_SABI_LIBRARY</span></code></dt><dd><div class="versionadded">
<p><span class="versionmodified added">New in version 3.26.</span></p>
</div>
<p>The path to the library for Stable Application Binary Interface. It will be
used to compute the variables <code class="docutils literal notranslate"><span class="pre">Python_SABI_LIBRARIES</span></code>,
<code class="docutils literal notranslate"><span class="pre">Python_SABI_LIBRARY_DIRS</span></code> and <code class="docutils literal notranslate"><span class="pre">Python_RUNTIME_SABI_LIBRARY_DIRS</span></code>.</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">Python_INCLUDE_DIR</span></code></dt><dd><p>The path to the directory of the <code class="docutils literal notranslate"><span class="pre">Python</span></code> headers. It will be used to
compute the variable <code class="docutils literal notranslate"><span class="pre">Python_INCLUDE_DIRS</span></code>.</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">Python_NumPy_INCLUDE_DIR</span></code></dt><dd><p>The path to the directory of the <code class="docutils literal notranslate"><span class="pre">NumPy</span></code> headers. It will be used to
compute the variable <code class="docutils literal notranslate"><span class="pre">Python_NumPy_INCLUDE_DIRS</span></code>.</p>
</dd>
</dl>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>All paths must be absolute. Any artifact specified with a relative path
will be ignored.</p>
</div>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>When an artifact is specified, all <code class="docutils literal notranslate"><span class="pre">HINTS</span></code> will be ignored and no search
will be performed for this artifact.</p>
<p>If more than one artifact is specified, it is the user's responsibility to
ensure the consistency of the various artifacts.</p>
</div>
<p>By default, this module supports multiple calls in different directories of a
project with different version/component requirements while providing correct
and consistent results for each call. To support this behavior, CMake cache
is not used in the traditional way which can be problematic for interactive
specification. So, to enable also interactive specification, module behavior
can be controlled with the following variable:</p>
<dl>
<dt><code class="docutils literal notranslate"><span class="pre">Python_ARTIFACTS_INTERACTIVE</span></code></dt><dd><div class="versionadded">
<p><span class="versionmodified added">New in version 3.18.</span></p>
</div>
<p>Selects the behavior of the module. This is a boolean variable:</p>
<ul class="simple">
<li><p>If set to <code class="docutils literal notranslate"><span class="pre">TRUE</span></code>: Create CMake cache entries for the above artifact
specification variables so that users can edit them interactively.
This disables support for multiple version/component requirements.</p></li>
<li><p>If set to <code class="docutils literal notranslate"><span class="pre">FALSE</span></code> or undefined: Enable multiple version/component
requirements.</p></li>
</ul>
</dd>
</dl>
</section>
<section id="commands">
<h2>Commands<a class="headerlink" href="#commands" title="Permalink to this heading">¶</a></h2>
<p>This module defines the command <code class="docutils literal notranslate"><span class="pre">Python_add_library</span></code> (when
<span class="target" id="index-1-prop_gbl:CMAKE_ROLE"></span><a class="reference internal" href="../prop_gbl/CMAKE_ROLE.html#prop_gbl:CMAKE_ROLE" title="CMAKE_ROLE"><code class="xref cmake cmake-prop_gbl docutils literal notranslate"><span class="pre">CMAKE_ROLE</span></code></a> is <code class="docutils literal notranslate"><span class="pre">PROJECT</span></code>), which has the same semantics as
<span class="target" id="index-0-command:add_library"></span><a class="reference internal" href="../command/add_library.html#command:add_library" title="add_library"><code class="xref cmake cmake-command docutils literal notranslate"><span class="pre">add_library()</span></code></a> and adds a dependency to target <code class="docutils literal notranslate"><span class="pre">Python::Python</span></code> or,
when library type is <code class="docutils literal notranslate"><span class="pre">MODULE</span></code>, to target <code class="docutils literal notranslate"><span class="pre">Python::Module</span></code> or
<code class="docutils literal notranslate"><span class="pre">Python::SABIModule</span></code> (when <code class="docutils literal notranslate"><span class="pre">USE_SABI</span></code> option is specified) and takes care
of Python module naming rules:</p>
<div class="highlight-none notranslate"><div class="highlight"><pre><span></span>Python_add_library (&lt;name&gt; [STATIC | SHARED | MODULE [USE_SABI &lt;version&gt;] [WITH_SOABI]]
                    &lt;source1&gt; [&lt;source2&gt; ...])
</pre></div>
</div>
<p>If the library type is not specified, <code class="docutils literal notranslate"><span class="pre">MODULE</span></code> is assumed.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.17: </span>For <code class="docutils literal notranslate"><span class="pre">MODULE</span></code> library type, if option <code class="docutils literal notranslate"><span class="pre">WITH_SOABI</span></code> is specified, the
module suffix will include the <code class="docutils literal notranslate"><span class="pre">Python_SOABI</span></code> value, if any.</p>
</div>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.26: </span>For <code class="docutils literal notranslate"><span class="pre">MODULE</span></code> type, if the option <code class="docutils literal notranslate"><span class="pre">USE_SABI</span></code> is specified, the
preprocessor definition <code class="docutils literal notranslate"><span class="pre">Py_LIMITED_API</span></code> will be specified, as <code class="docutils literal notranslate"><span class="pre">PRIVATE</span></code>,
for the target <code class="docutils literal notranslate"><span class="pre">&lt;name&gt;</span></code> with the value computed from <code class="docutils literal notranslate"><span class="pre">&lt;version&gt;</span></code> argument.
The expected format for <code class="docutils literal notranslate"><span class="pre">&lt;version&gt;</span></code> is <code class="docutils literal notranslate"><span class="pre">major[.minor]</span></code>, where each
component is a numeric value. If <code class="docutils literal notranslate"><span class="pre">minor</span></code> component is specified, the
version should be, at least, <code class="docutils literal notranslate"><span class="pre">3.2</span></code> which is the version where the
<a class="reference external" href="https://docs.python.org/3/c-api/stable.html">Stable Application Binary Interface</a>
was introduced. Specifying only major version <code class="docutils literal notranslate"><span class="pre">3</span></code> is equivalent to <code class="docutils literal notranslate"><span class="pre">3.2</span></code>.</p>
<p>When option <code class="docutils literal notranslate"><span class="pre">WITH_SOABI</span></code> is also specified,  the module suffix will include
the <code class="docutils literal notranslate"><span class="pre">Python_SOSABI</span></code> value, if any.</p>
</div>
</section>
</section>


            <div class="clearer"></div>
          </div>
        </div>
      </div>
      <div class="sphinxsidebar" role="navigation" aria-label="main navigation">
        <div class="sphinxsidebarwrapper">
  <div>
    <h3><a href="../index.html">Table of Contents</a></h3>
    <ul>
<li><a class="reference internal" href="#">FindPython</a><ul>
<li><a class="reference internal" href="#imported-targets">Imported Targets</a></li>
<li><a class="reference internal" href="#result-variables">Result Variables</a></li>
<li><a class="reference internal" href="#hints">Hints</a></li>
<li><a class="reference internal" href="#artifacts-specification">Artifacts Specification</a></li>
<li><a class="reference internal" href="#commands">Commands</a></li>
</ul>
</li>
</ul>

  </div>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="FindProtobuf.html"
                          title="previous chapter">FindProtobuf</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="FindPython2.html"
                          title="next chapter">FindPython2</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../_sources/module/FindPython.rst.txt"
            rel="nofollow">Show Source</a></li>
    </ul>
   </div>
<div id="searchbox" style="display: none" role="search">
  <h3 id="searchlabel">Quick search</h3>
    <div class="searchformwrapper">
    <form class="search" action="../search.html" method="get">
      <input type="text" name="q" aria-labelledby="searchlabel" autocomplete="off" autocorrect="off" autocapitalize="off" spellcheck="false"/>
      <input type="submit" value="Go" />
    </form>
    </div>
</div>
<script>document.getElementById('searchbox').style.display = "block"</script>
        </div>
      </div>
      <div class="clearer"></div>
    </div>
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             >index</a></li>
        <li class="right" >
          <a href="FindPython2.html" title="FindPython2"
             >next</a> |</li>
        <li class="right" >
          <a href="FindProtobuf.html" title="FindProtobuf"
             >previous</a> |</li>
  <li>
    <img src="../_static/cmake-logo-16.png" alt=""
         style="vertical-align: middle; margin-top: -2px" />
  </li>
  <li>
    <a href="https://cmake.org/">CMake</a> &#187;
  </li>
  <li>
    <a href="../index.html">3.28.1 Documentation</a> &#187;
  </li>

          <li class="nav-item nav-item-1"><a href="../manual/cmake-modules.7.html" >cmake-modules(7)</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href="">FindPython</a></li> 
      </ul>
    </div>

    <div class="footer" role="contentinfo">
        &#169; Copyright 2000-2023 Kitware, Inc. and Contributors.
      Created using <a href="https://www.sphinx-doc.org/">Sphinx</a> 6.2.1.
    </div>
  </body>
</html>