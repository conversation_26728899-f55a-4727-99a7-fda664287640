
<!DOCTYPE html>

<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="generator" content="Docutils 0.19: https://docutils.sourceforge.io/" />

  <title>add_custom_target &mdash; CMake 3.28.1 Documentation</title>

    <link rel="stylesheet" type="text/css" href="../_static/pygments.css" />
    <link rel="stylesheet" type="text/css" href="../_static/cmake.css" />
    
    <script data-url_root="../" id="documentation_options" src="../_static/documentation_options.js"></script>
    <script src="../_static/doctools.js"></script>
    <script src="../_static/sphinx_highlight.js"></script>
    
    <link rel="icon" href="../_static/cmake-favicon.ico"/>
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="next" title="add_definitions" href="add_definitions.html" />
    <link rel="prev" title="add_custom_command" href="add_custom_command.html" />
 

  </head><body>

    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             accesskey="I">index</a></li>
        <li class="right" >
          <a href="add_definitions.html" title="add_definitions"
             accesskey="N">next</a> |</li>
        <li class="right" >
          <a href="add_custom_command.html" title="add_custom_command"
             accesskey="P">previous</a> |</li>
  <li>
    <img src="../_static/cmake-logo-16.png" alt=""
         style="vertical-align: middle; margin-top: -2px" />
  </li>
  <li>
    <a href="https://cmake.org/">CMake</a> &#187;
  </li>
  <li>
    <a href="../index.html">3.28.1 Documentation</a> &#187;
  </li>

          <li class="nav-item nav-item-1"><a href="../manual/cmake-commands.7.html" accesskey="U">cmake-commands(7)</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href="">add_custom_target</a></li> 
      </ul>
    </div>  

    <div class="document">
      <div class="documentwrapper">
        <div class="bodywrapper">
          <div class="body" role="main">
            
  <section id="add-custom-target">
<span id="command:add_custom_target"></span><h1>add_custom_target<a class="headerlink" href="#add-custom-target" title="Permalink to this heading">¶</a></h1>
<p>Add a target with no output so it will always be built.</p>
<div class="highlight-cmake notranslate"><div class="highlight"><pre><span></span><span class="nf">add_custom_target(</span><span class="nb">Name</span><span class="w"> </span><span class="p">[</span><span class="no">ALL</span><span class="p">]</span><span class="w"> </span><span class="p">[</span><span class="nb">command1</span><span class="w"> </span><span class="p">[</span><span class="nb">args1...</span><span class="p">]]</span>
<span class="w">                  </span><span class="p">[</span><span class="no">COMMAND</span><span class="w"> </span><span class="nb">command2</span><span class="w"> </span><span class="p">[</span><span class="nb">args2...</span><span class="p">]</span><span class="w"> </span><span class="p">...]</span>
<span class="w">                  </span><span class="p">[</span><span class="no">DEPENDS</span><span class="w"> </span><span class="nb">depend</span><span class="w"> </span><span class="nb">depend</span><span class="w"> </span><span class="nb">depend</span><span class="w"> </span><span class="p">...</span><span class="w"> </span><span class="p">]</span>
<span class="w">                  </span><span class="p">[</span><span class="no">BYPRODUCTS</span><span class="w"> </span><span class="p">[</span><span class="nb">files...</span><span class="p">]]</span>
<span class="w">                  </span><span class="p">[</span><span class="no">WORKING_DIRECTORY</span><span class="w"> </span><span class="nb">dir</span><span class="p">]</span>
<span class="w">                  </span><span class="p">[</span><span class="no">COMMENT</span><span class="w"> </span><span class="nb">comment</span><span class="p">]</span>
<span class="w">                  </span><span class="p">[</span><span class="no">JOB_POOL</span><span class="w"> </span><span class="nb">job_pool</span><span class="p">]</span>
<span class="w">                  </span><span class="p">[</span><span class="no">JOB_SERVER_AWARE</span><span class="w"> </span><span class="nv">&lt;bool&gt;</span><span class="p">]</span>
<span class="w">                  </span><span class="p">[</span><span class="no">VERBATIM</span><span class="p">]</span><span class="w"> </span><span class="p">[</span><span class="no">USES_TERMINAL</span><span class="p">]</span>
<span class="w">                  </span><span class="p">[</span><span class="no">COMMAND_EXPAND_LISTS</span><span class="p">]</span>
<span class="w">                  </span><span class="p">[</span><span class="no">SOURCES</span><span class="w"> </span><span class="nb">src1</span><span class="w"> </span><span class="p">[</span><span class="nb">src2...</span><span class="p">]]</span><span class="nf">)</span>
</pre></div>
</div>
<p>Adds a target with the given name that executes the given commands.
The target has no output file and is <em>always considered out of date</em>
even if the commands try to create a file with the name of the target.
Use the <span class="target" id="index-0-command:add_custom_command"></span><a class="reference internal" href="add_custom_command.html#command:add_custom_command" title="add_custom_command"><code class="xref cmake cmake-command docutils literal notranslate"><span class="pre">add_custom_command()</span></code></a> command to generate a file with
dependencies.  By default nothing depends on the custom target.  Use
the <span class="target" id="index-0-command:add_dependencies"></span><a class="reference internal" href="add_dependencies.html#command:add_dependencies" title="add_dependencies"><code class="xref cmake cmake-command docutils literal notranslate"><span class="pre">add_dependencies()</span></code></a> command to add dependencies to or
from other targets.</p>
<p>The options are:</p>
<dl>
<dt><code class="docutils literal notranslate"><span class="pre">ALL</span></code></dt><dd><p>Indicate that this target should be added to the default build
target so that it will be run every time (the command cannot be
called <code class="docutils literal notranslate"><span class="pre">ALL</span></code>).</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">BYPRODUCTS</span></code></dt><dd><div class="versionadded">
<p><span class="versionmodified added">New in version 3.2.</span></p>
</div>
<p>Specify the files the command is expected to produce but whose
modification time may or may not be updated on subsequent builds.
If a byproduct name is a relative path it will be interpreted
relative to the build tree directory corresponding to the
current source directory.
Each byproduct file will be marked with the <span class="target" id="index-0-prop_sf:GENERATED"></span><a class="reference internal" href="../prop_sf/GENERATED.html#prop_sf:GENERATED" title="GENERATED"><code class="xref cmake cmake-prop_sf docutils literal notranslate"><span class="pre">GENERATED</span></code></a>
source file property automatically.</p>
<p><em>See policy</em> <span class="target" id="index-0-policy:CMP0058"></span><a class="reference internal" href="../policy/CMP0058.html#policy:CMP0058" title="CMP0058"><code class="xref cmake cmake-policy docutils literal notranslate"><span class="pre">CMP0058</span></code></a> <em>for the motivation behind this feature.</em></p>
<p>Explicit specification of byproducts is supported by the
<span class="target" id="index-0-generator:Ninja"></span><a class="reference internal" href="../generator/Ninja.html#generator:Ninja" title="Ninja"><code class="xref cmake cmake-generator docutils literal notranslate"><span class="pre">Ninja</span></code></a> generator to tell the <code class="docutils literal notranslate"><span class="pre">ninja</span></code> build tool
how to regenerate byproducts when they are missing.  It is
also useful when other build rules (e.g. custom commands)
depend on the byproducts.  Ninja requires a build rule for any
generated file on which another rule depends even if there are
order-only dependencies to ensure the byproducts will be
available before their dependents build.</p>
<p>The <a class="reference internal" href="../manual/cmake-generators.7.html#makefile-generators"><span class="std std-ref">Makefile Generators</span></a> will remove <code class="docutils literal notranslate"><span class="pre">BYPRODUCTS</span></code> and other
<span class="target" id="index-1-prop_sf:GENERATED"></span><a class="reference internal" href="../prop_sf/GENERATED.html#prop_sf:GENERATED" title="GENERATED"><code class="xref cmake cmake-prop_sf docutils literal notranslate"><span class="pre">GENERATED</span></code></a> files during <code class="docutils literal notranslate"><span class="pre">make</span> <span class="pre">clean</span></code>.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.20: </span>Arguments to <code class="docutils literal notranslate"><span class="pre">BYPRODUCTS</span></code> may use a restricted set of
<span class="target" id="index-0-manual:cmake-generator-expressions(7)"></span><a class="reference internal" href="../manual/cmake-generator-expressions.7.html#manual:cmake-generator-expressions(7)" title="cmake-generator-expressions(7)"><code class="xref cmake cmake-manual docutils literal notranslate"><span class="pre">generator</span> <span class="pre">expressions</span></code></a>.
<a class="reference internal" href="../manual/cmake-generator-expressions.7.html#target-dependent-queries"><span class="std std-ref">Target-dependent expressions</span></a> are not
permitted.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.28: </span>In custom targets using <a class="reference internal" href="target_sources.html#file-sets"><span class="std std-ref">File Sets</span></a>, byproducts are now
considered private unless they are listed in a non-private file set.
See policy <span class="target" id="index-0-policy:CMP0154"></span><a class="reference internal" href="../policy/CMP0154.html#policy:CMP0154" title="CMP0154"><code class="xref cmake cmake-policy docutils literal notranslate"><span class="pre">CMP0154</span></code></a>.</p>
</div>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">COMMAND</span></code></dt><dd><p>Specify the command-line(s) to execute at build time.
If more than one <code class="docutils literal notranslate"><span class="pre">COMMAND</span></code> is specified they will be executed in order,
but <em>not</em> necessarily composed into a stateful shell or batch script.
(To run a full script, use the <span class="target" id="index-0-command:configure_file"></span><a class="reference internal" href="configure_file.html#command:configure_file" title="configure_file"><code class="xref cmake cmake-command docutils literal notranslate"><span class="pre">configure_file()</span></code></a> command or the
<span class="target" id="index-0-command:file"></span><a class="reference internal" href="file.html#generate" title="file(generate)"><code class="xref cmake cmake-command docutils literal notranslate"><span class="pre">file(GENERATE)</span></code></a> command to create it, and then specify
a <code class="docutils literal notranslate"><span class="pre">COMMAND</span></code> to launch it.)</p>
<p>If <code class="docutils literal notranslate"><span class="pre">COMMAND</span></code> specifies an executable target name (created by the
<span class="target" id="index-0-command:add_executable"></span><a class="reference internal" href="add_executable.html#command:add_executable" title="add_executable"><code class="xref cmake cmake-command docutils literal notranslate"><span class="pre">add_executable()</span></code></a> command), it will automatically be replaced
by the location of the executable created at build time if either of
the following is true:</p>
<ul>
<li><p>The target is not being cross-compiled (i.e. the
<span class="target" id="index-0-variable:CMAKE_CROSSCOMPILING"></span><a class="reference internal" href="../variable/CMAKE_CROSSCOMPILING.html#variable:CMAKE_CROSSCOMPILING" title="CMAKE_CROSSCOMPILING"><code class="xref cmake cmake-variable docutils literal notranslate"><span class="pre">CMAKE_CROSSCOMPILING</span></code></a> variable is not set to true).</p></li>
<li><div class="versionadded">
<p><span class="versionmodified added">New in version 3.6: </span>The target is being cross-compiled and an emulator is provided (i.e.
its <span class="target" id="index-0-prop_tgt:CROSSCOMPILING_EMULATOR"></span><a class="reference internal" href="../prop_tgt/CROSSCOMPILING_EMULATOR.html#prop_tgt:CROSSCOMPILING_EMULATOR" title="CROSSCOMPILING_EMULATOR"><code class="xref cmake cmake-prop_tgt docutils literal notranslate"><span class="pre">CROSSCOMPILING_EMULATOR</span></code></a> target property is set).
In this case, the contents of <span class="target" id="index-1-prop_tgt:CROSSCOMPILING_EMULATOR"></span><a class="reference internal" href="../prop_tgt/CROSSCOMPILING_EMULATOR.html#prop_tgt:CROSSCOMPILING_EMULATOR" title="CROSSCOMPILING_EMULATOR"><code class="xref cmake cmake-prop_tgt docutils literal notranslate"><span class="pre">CROSSCOMPILING_EMULATOR</span></code></a> will be
prepended to the command before the location of the target executable.</p>
</div>
</li>
</ul>
<p>If neither of the above conditions are met, it is assumed that the
command name is a program to be found on the <code class="docutils literal notranslate"><span class="pre">PATH</span></code> at build time.</p>
<p>Arguments to <code class="docutils literal notranslate"><span class="pre">COMMAND</span></code> may use
<span class="target" id="index-1-manual:cmake-generator-expressions(7)"></span><a class="reference internal" href="../manual/cmake-generator-expressions.7.html#manual:cmake-generator-expressions(7)" title="cmake-generator-expressions(7)"><code class="xref cmake cmake-manual docutils literal notranslate"><span class="pre">generator</span> <span class="pre">expressions</span></code></a>.
Use the <span class="target" id="index-0-genex:TARGET_FILE"></span><a class="reference internal" href="../manual/cmake-generator-expressions.7.html#genex:TARGET_FILE" title="TARGET_FILE"><code class="xref cmake cmake-genex docutils literal notranslate"><span class="pre">TARGET_FILE</span></code></a> generator expression to refer to the location
of a target later in the command line (i.e. as a command argument rather
than as the command to execute).</p>
<p>Whenever one of the following target based generator expressions are used as
a command to execute or is mentioned in a command argument, a target-level
dependency will be added automatically so that the mentioned target will be
built before this custom target (see policy <span class="target" id="index-0-policy:CMP0112"></span><a class="reference internal" href="../policy/CMP0112.html#policy:CMP0112" title="CMP0112"><code class="xref cmake cmake-policy docutils literal notranslate"><span class="pre">CMP0112</span></code></a>).</p>
<blockquote>
<div><ul class="simple">
<li><p><code class="docutils literal notranslate"><span class="pre">TARGET_FILE</span></code></p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">TARGET_LINKER_FILE</span></code></p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">TARGET_SONAME_FILE</span></code></p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">TARGET_PDB_FILE</span></code></p></li>
</ul>
</div></blockquote>
<p>The command and arguments are optional and if not specified an empty
target will be created.</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">COMMENT</span></code></dt><dd><p>Display the given message before the commands are executed at
build time.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.26: </span>Arguments to <code class="docutils literal notranslate"><span class="pre">COMMENT</span></code> may use
<span class="target" id="index-2-manual:cmake-generator-expressions(7)"></span><a class="reference internal" href="../manual/cmake-generator-expressions.7.html#manual:cmake-generator-expressions(7)" title="cmake-generator-expressions(7)"><code class="xref cmake cmake-manual docutils literal notranslate"><span class="pre">generator</span> <span class="pre">expressions</span></code></a>.</p>
</div>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">DEPENDS</span></code></dt><dd><p>Reference files and outputs of custom commands created with
<span class="target" id="index-1-command:add_custom_command"></span><a class="reference internal" href="add_custom_command.html#command:add_custom_command" title="add_custom_command"><code class="xref cmake cmake-command docutils literal notranslate"><span class="pre">add_custom_command()</span></code></a> command calls in the same directory
(<code class="docutils literal notranslate"><span class="pre">CMakeLists.txt</span></code> file).  They will be brought up to date when
the target is built.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.16: </span>A target-level dependency is added if any dependency is a byproduct
of a target or any of its build events in the same directory to ensure
the byproducts will be available before this target is built.</p>
</div>
<p>Use the <span class="target" id="index-1-command:add_dependencies"></span><a class="reference internal" href="add_dependencies.html#command:add_dependencies" title="add_dependencies"><code class="xref cmake cmake-command docutils literal notranslate"><span class="pre">add_dependencies()</span></code></a> command to add dependencies
on other targets.</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">COMMAND_EXPAND_LISTS</span></code></dt><dd><div class="versionadded">
<p><span class="versionmodified added">New in version 3.8.</span></p>
</div>
<p>Lists in <code class="docutils literal notranslate"><span class="pre">COMMAND</span></code> arguments will be expanded, including those
created with
<span class="target" id="index-3-manual:cmake-generator-expressions(7)"></span><a class="reference internal" href="../manual/cmake-generator-expressions.7.html#manual:cmake-generator-expressions(7)" title="cmake-generator-expressions(7)"><code class="xref cmake cmake-manual docutils literal notranslate"><span class="pre">generator</span> <span class="pre">expressions</span></code></a>,
allowing <code class="docutils literal notranslate"><span class="pre">COMMAND</span></code> arguments such as
<code class="docutils literal notranslate"><span class="pre">${CC}</span> <span class="pre">&quot;-I$&lt;JOIN:$&lt;TARGET_PROPERTY:foo,INCLUDE_DIRECTORIES&gt;,;-I&gt;&quot;</span> <span class="pre">foo.cc</span></code>
to be properly expanded.</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">JOB_POOL</span></code></dt><dd><div class="versionadded">
<p><span class="versionmodified added">New in version 3.15.</span></p>
</div>
<p>Specify a <span class="target" id="index-0-prop_gbl:JOB_POOLS"></span><a class="reference internal" href="../prop_gbl/JOB_POOLS.html#prop_gbl:JOB_POOLS" title="JOB_POOLS"><code class="xref cmake cmake-prop_gbl docutils literal notranslate"><span class="pre">pool</span></code></a> for the <span class="target" id="index-1-generator:Ninja"></span><a class="reference internal" href="../generator/Ninja.html#generator:Ninja" title="Ninja"><code class="xref cmake cmake-generator docutils literal notranslate"><span class="pre">Ninja</span></code></a>
generator. Incompatible with <code class="docutils literal notranslate"><span class="pre">USES_TERMINAL</span></code>, which implies
the <code class="docutils literal notranslate"><span class="pre">console</span></code> pool.
Using a pool that is not defined by <span class="target" id="index-1-prop_gbl:JOB_POOLS"></span><a class="reference internal" href="../prop_gbl/JOB_POOLS.html#prop_gbl:JOB_POOLS" title="JOB_POOLS"><code class="xref cmake cmake-prop_gbl docutils literal notranslate"><span class="pre">JOB_POOLS</span></code></a> causes
an error by ninja at build time.</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">JOB_SERVER_AWARE</span></code></dt><dd><div class="versionadded">
<p><span class="versionmodified added">New in version 3.28.</span></p>
</div>
<p>Specify that the command is GNU Make job server aware.</p>
<p>For the <span class="target" id="index-0-generator:Unix Makefiles"></span><a class="reference internal" href="../generator/Unix%20Makefiles.html#generator:Unix Makefiles" title="Unix Makefiles"><code class="xref cmake cmake-generator docutils literal notranslate"><span class="pre">Unix</span> <span class="pre">Makefiles</span></code></a>, <span class="target" id="index-0-generator:MSYS Makefiles"></span><a class="reference internal" href="../generator/MSYS%20Makefiles.html#generator:MSYS Makefiles" title="MSYS Makefiles"><code class="xref cmake cmake-generator docutils literal notranslate"><span class="pre">MSYS</span> <span class="pre">Makefiles</span></code></a>, and
<span class="target" id="index-0-generator:MinGW Makefiles"></span><a class="reference internal" href="../generator/MinGW%20Makefiles.html#generator:MinGW Makefiles" title="MinGW Makefiles"><code class="xref cmake cmake-generator docutils literal notranslate"><span class="pre">MinGW</span> <span class="pre">Makefiles</span></code></a> generators this will add the <code class="docutils literal notranslate"><span class="pre">+</span></code> prefix to the
recipe line. See the <a class="reference external" href="https://www.gnu.org/software/make/manual/html_node/MAKE-Variable.html">GNU Make Documentation</a> for more information.</p>
<p>This option is silently ignored by other generators.</p>
</dd>
</dl>
<dl>
<dt><code class="docutils literal notranslate"><span class="pre">SOURCES</span></code></dt><dd><p>Specify additional source files to be included in the custom target.
Specified source files will be added to IDE project files for
convenience in editing even if they have no build rules.</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">VERBATIM</span></code></dt><dd><p>All arguments to the commands will be escaped properly for the
build tool so that the invoked command receives each argument
unchanged.  Note that one level of escapes is still used by the
CMake language processor before <code class="docutils literal notranslate"><span class="pre">add_custom_target</span></code> even sees
the arguments.  Use of <code class="docutils literal notranslate"><span class="pre">VERBATIM</span></code> is recommended as it enables
correct behavior.  When <code class="docutils literal notranslate"><span class="pre">VERBATIM</span></code> is not given the behavior
is platform specific because there is no protection of
tool-specific special characters.</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">USES_TERMINAL</span></code></dt><dd><div class="versionadded">
<p><span class="versionmodified added">New in version 3.2.</span></p>
</div>
<p>The command will be given direct access to the terminal if possible.
With the <span class="target" id="index-2-generator:Ninja"></span><a class="reference internal" href="../generator/Ninja.html#generator:Ninja" title="Ninja"><code class="xref cmake cmake-generator docutils literal notranslate"><span class="pre">Ninja</span></code></a> generator, this places the command in
the <code class="docutils literal notranslate"><span class="pre">console</span></code> <span class="target" id="index-2-prop_gbl:JOB_POOLS"></span><a class="reference internal" href="../prop_gbl/JOB_POOLS.html#prop_gbl:JOB_POOLS" title="JOB_POOLS"><code class="xref cmake cmake-prop_gbl docutils literal notranslate"><span class="pre">pool</span></code></a>.</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">WORKING_DIRECTORY</span></code></dt><dd><p>Execute the command with the given current working directory.
If it is a relative path it will be interpreted relative to the
build tree directory corresponding to the current source directory.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.13: </span>Arguments to <code class="docutils literal notranslate"><span class="pre">WORKING_DIRECTORY</span></code> may use
<span class="target" id="index-4-manual:cmake-generator-expressions(7)"></span><a class="reference internal" href="../manual/cmake-generator-expressions.7.html#manual:cmake-generator-expressions(7)" title="cmake-generator-expressions(7)"><code class="xref cmake cmake-manual docutils literal notranslate"><span class="pre">generator</span> <span class="pre">expressions</span></code></a>.</p>
</div>
</dd>
</dl>
<section id="ninja-multi-config">
<h2>Ninja Multi-Config<a class="headerlink" href="#ninja-multi-config" title="Permalink to this heading">¶</a></h2>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.20: </span><code class="docutils literal notranslate"><span class="pre">add_custom_target</span></code> supports the <span class="target" id="index-0-generator:Ninja Multi-Config"></span><a class="reference internal" href="../generator/Ninja%20Multi-Config.html#generator:Ninja Multi-Config" title="Ninja Multi-Config"><code class="xref cmake cmake-generator docutils literal notranslate"><span class="pre">Ninja</span> <span class="pre">Multi-Config</span></code></a>
generator's cross-config capabilities. See the generator documentation
for more information.</p>
</div>
</section>
<section id="see-also">
<h2>See Also<a class="headerlink" href="#see-also" title="Permalink to this heading">¶</a></h2>
<ul class="simple">
<li><p><span class="target" id="index-2-command:add_custom_command"></span><a class="reference internal" href="add_custom_command.html#command:add_custom_command" title="add_custom_command"><code class="xref cmake cmake-command docutils literal notranslate"><span class="pre">add_custom_command()</span></code></a></p></li>
</ul>
</section>
</section>


            <div class="clearer"></div>
          </div>
        </div>
      </div>
      <div class="sphinxsidebar" role="navigation" aria-label="main navigation">
        <div class="sphinxsidebarwrapper">
  <div>
    <h3><a href="../index.html">Table of Contents</a></h3>
    <ul>
<li><a class="reference internal" href="#">add_custom_target</a><ul>
<li><a class="reference internal" href="#ninja-multi-config">Ninja Multi-Config</a></li>
<li><a class="reference internal" href="#see-also">See Also</a></li>
</ul>
</li>
</ul>

  </div>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="add_custom_command.html"
                          title="previous chapter">add_custom_command</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="add_definitions.html"
                          title="next chapter">add_definitions</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../_sources/command/add_custom_target.rst.txt"
            rel="nofollow">Show Source</a></li>
    </ul>
   </div>
<div id="searchbox" style="display: none" role="search">
  <h3 id="searchlabel">Quick search</h3>
    <div class="searchformwrapper">
    <form class="search" action="../search.html" method="get">
      <input type="text" name="q" aria-labelledby="searchlabel" autocomplete="off" autocorrect="off" autocapitalize="off" spellcheck="false"/>
      <input type="submit" value="Go" />
    </form>
    </div>
</div>
<script>document.getElementById('searchbox').style.display = "block"</script>
        </div>
      </div>
      <div class="clearer"></div>
    </div>
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             >index</a></li>
        <li class="right" >
          <a href="add_definitions.html" title="add_definitions"
             >next</a> |</li>
        <li class="right" >
          <a href="add_custom_command.html" title="add_custom_command"
             >previous</a> |</li>
  <li>
    <img src="../_static/cmake-logo-16.png" alt=""
         style="vertical-align: middle; margin-top: -2px" />
  </li>
  <li>
    <a href="https://cmake.org/">CMake</a> &#187;
  </li>
  <li>
    <a href="../index.html">3.28.1 Documentation</a> &#187;
  </li>

          <li class="nav-item nav-item-1"><a href="../manual/cmake-commands.7.html" >cmake-commands(7)</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href="">add_custom_target</a></li> 
      </ul>
    </div>

    <div class="footer" role="contentinfo">
        &#169; Copyright 2000-2023 Kitware, Inc. and Contributors.
      Created using <a href="https://www.sphinx-doc.org/">Sphinx</a> 6.2.1.
    </div>
  </body>
</html>