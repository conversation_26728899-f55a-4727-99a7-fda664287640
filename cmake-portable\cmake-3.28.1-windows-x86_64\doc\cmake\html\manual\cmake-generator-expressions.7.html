
<!DOCTYPE html>

<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="generator" content="Docutils 0.19: https://docutils.sourceforge.io/" />

  <title>cmake-generator-expressions(7) &mdash; CMake 3.28.1 Documentation</title>

    <link rel="stylesheet" type="text/css" href="../_static/pygments.css" />
    <link rel="stylesheet" type="text/css" href="../_static/cmake.css" />
    
    <script data-url_root="../" id="documentation_options" src="../_static/documentation_options.js"></script>
    <script src="../_static/doctools.js"></script>
    <script src="../_static/sphinx_highlight.js"></script>
    
    <link rel="icon" href="../_static/cmake-favicon.ico"/>
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="next" title="cmake-generators(7)" href="cmake-generators.7.html" />
    <link rel="prev" title="cmake-file-api(7)" href="cmake-file-api.7.html" />
 

  </head><body>

    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             accesskey="I">index</a></li>
        <li class="right" >
          <a href="cmake-generators.7.html" title="cmake-generators(7)"
             accesskey="N">next</a> |</li>
        <li class="right" >
          <a href="cmake-file-api.7.html" title="cmake-file-api(7)"
             accesskey="P">previous</a> |</li>
  <li>
    <img src="../_static/cmake-logo-16.png" alt=""
         style="vertical-align: middle; margin-top: -2px" />
  </li>
  <li>
    <a href="https://cmake.org/">CMake</a> &#187;
  </li>
  <li>
    <a href="../index.html">3.28.1 Documentation</a> &#187;
  </li>

        <li class="nav-item nav-item-this"><a href="">cmake-generator-expressions(7)</a></li> 
      </ul>
    </div>  

    <div class="document">
      <div class="documentwrapper">
        <div class="bodywrapper">
          <div class="body" role="main">
            
  <span class="target" id="manual:cmake-generator-expressions(7)"></span><section id="cmake-generator-expressions-7">
<h1><a class="toc-backref" href="#id1" role="doc-backlink">cmake-generator-expressions(7)</a><a class="headerlink" href="#cmake-generator-expressions-7" title="Permalink to this heading">¶</a></h1>
<nav class="contents" id="contents">
<p class="topic-title">Contents</p>
<ul class="simple">
<li><p><a class="reference internal" href="#cmake-generator-expressions-7" id="id1">cmake-generator-expressions(7)</a></p>
<ul>
<li><p><a class="reference internal" href="#introduction" id="id2">Introduction</a></p></li>
<li><p><a class="reference internal" href="#whitespace-and-quoting" id="id3">Whitespace And Quoting</a></p></li>
<li><p><a class="reference internal" href="#debugging" id="id4">Debugging</a></p></li>
<li><p><a class="reference internal" href="#generator-expression-reference" id="id5">Generator Expression Reference</a></p>
<ul>
<li><p><a class="reference internal" href="#conditional-expressions" id="id6">Conditional Expressions</a></p></li>
<li><p><a class="reference internal" href="#logical-operators" id="id7">Logical Operators</a></p></li>
<li><p><a class="reference internal" href="#primary-comparison-expressions" id="id8">Primary Comparison Expressions</a></p>
<ul>
<li><p><a class="reference internal" href="#string-comparisons" id="id9">String Comparisons</a></p></li>
<li><p><a class="reference internal" href="#version-comparisons" id="id10">Version Comparisons</a></p></li>
</ul>
</li>
<li><p><a class="reference internal" href="#string-transformations" id="id11">String Transformations</a></p></li>
<li><p><a class="reference internal" href="#list-expressions" id="id12">List Expressions</a></p>
<ul>
<li><p><a class="reference internal" href="#list-comparisons" id="id13">List Comparisons</a></p></li>
<li><p><a class="reference internal" href="#list-queries" id="id14">List Queries</a></p></li>
<li><p><a class="reference internal" href="#list-transformations" id="id15">List Transformations</a></p></li>
<li><p><a class="reference internal" href="#list-ordering" id="id16">List Ordering</a></p></li>
</ul>
</li>
<li><p><a class="reference internal" href="#path-expressions" id="id17">Path Expressions</a></p>
<ul>
<li><p><a class="reference internal" href="#path-comparisons" id="id18">Path Comparisons</a></p></li>
<li><p><a class="reference internal" href="#path-queries" id="id19">Path Queries</a></p></li>
<li><p><a class="reference internal" href="#path-decomposition" id="id20">Path Decomposition</a></p></li>
<li><p><a class="reference internal" href="#path-transformations" id="id21">Path Transformations</a></p></li>
<li><p><a class="reference internal" href="#shell-paths" id="id22">Shell Paths</a></p></li>
</ul>
</li>
<li><p><a class="reference internal" href="#configuration-expressions" id="id23">Configuration Expressions</a></p></li>
<li><p><a class="reference internal" href="#toolchain-and-language-expressions" id="id24">Toolchain And Language Expressions</a></p>
<ul>
<li><p><a class="reference internal" href="#platform" id="id25">Platform</a></p></li>
<li><p><a class="reference internal" href="#compiler-version" id="id26">Compiler Version</a></p></li>
<li><p><a class="reference internal" href="#compiler-language-and-id" id="id27">Compiler Language And ID</a></p></li>
<li><p><a class="reference internal" href="#compile-features" id="id28">Compile Features</a></p></li>
<li><p><a class="reference internal" href="#compile-context" id="id29">Compile Context</a></p></li>
<li><p><a class="reference internal" href="#linker-language-and-id" id="id30">Linker Language And ID</a></p></li>
<li><p><a class="reference internal" href="#link-features" id="id31">Link Features</a></p></li>
<li><p><a class="reference internal" href="#link-context" id="id32">Link Context</a></p></li>
</ul>
</li>
<li><p><a class="reference internal" href="#target-dependent-expressions" id="id33">Target-Dependent Expressions</a></p></li>
<li><p><a class="reference internal" href="#export-and-install-expressions" id="id34">Export And Install Expressions</a></p></li>
<li><p><a class="reference internal" href="#multi-level-expression-evaluation" id="id35">Multi-level Expression Evaluation</a></p></li>
<li><p><a class="reference internal" href="#escaped-characters" id="id36">Escaped Characters</a></p></li>
<li><p><a class="reference internal" href="#deprecated-expressions" id="id37">Deprecated Expressions</a></p></li>
</ul>
</li>
</ul>
</li>
</ul>
</nav>
<section id="introduction">
<h2><a class="toc-backref" href="#id2" role="doc-backlink">Introduction</a><a class="headerlink" href="#introduction" title="Permalink to this heading">¶</a></h2>
<p>Generator expressions are evaluated during build system generation to produce
information specific to each build configuration.  They have the form
<code class="docutils literal notranslate"><span class="pre">$&lt;...&gt;</span></code>.  For example:</p>
<div class="highlight-cmake notranslate"><div class="highlight"><pre><span></span><span class="nf">target_include_directories(</span><span class="nb">tgt</span><span class="w"> </span><span class="no">PRIVATE</span><span class="w"> </span><span class="na">/opt/include/</span><span class="o">$&lt;</span><span class="no">CXX_COMPILER_ID</span><span class="o">&gt;</span><span class="nf">)</span>
</pre></div>
</div>
<p>This would expand to <code class="docutils literal notranslate"><span class="pre">/opt/include/GNU</span></code>, <code class="docutils literal notranslate"><span class="pre">/opt/include/Clang</span></code>, etc.
depending on the C++ compiler used.</p>
<p>Generator expressions are allowed in the context of many target properties,
such as <span class="target" id="index-0-prop_tgt:LINK_LIBRARIES"></span><a class="reference internal" href="../prop_tgt/LINK_LIBRARIES.html#prop_tgt:LINK_LIBRARIES" title="LINK_LIBRARIES"><code class="xref cmake cmake-prop_tgt docutils literal notranslate"><span class="pre">LINK_LIBRARIES</span></code></a>, <span class="target" id="index-0-prop_tgt:INCLUDE_DIRECTORIES"></span><a class="reference internal" href="../prop_tgt/INCLUDE_DIRECTORIES.html#prop_tgt:INCLUDE_DIRECTORIES" title="INCLUDE_DIRECTORIES"><code class="xref cmake cmake-prop_tgt docutils literal notranslate"><span class="pre">INCLUDE_DIRECTORIES</span></code></a>,
<span class="target" id="index-0-prop_tgt:COMPILE_DEFINITIONS"></span><a class="reference internal" href="../prop_tgt/COMPILE_DEFINITIONS.html#prop_tgt:COMPILE_DEFINITIONS" title="COMPILE_DEFINITIONS"><code class="xref cmake cmake-prop_tgt docutils literal notranslate"><span class="pre">COMPILE_DEFINITIONS</span></code></a> and others.  They may also be used when using
commands to populate those properties, such as <span class="target" id="index-0-command:target_link_libraries"></span><a class="reference internal" href="../command/target_link_libraries.html#command:target_link_libraries" title="target_link_libraries"><code class="xref cmake cmake-command docutils literal notranslate"><span class="pre">target_link_libraries()</span></code></a>,
<span class="target" id="index-0-command:target_include_directories"></span><a class="reference internal" href="../command/target_include_directories.html#command:target_include_directories" title="target_include_directories"><code class="xref cmake cmake-command docutils literal notranslate"><span class="pre">target_include_directories()</span></code></a>, <span class="target" id="index-0-command:target_compile_definitions"></span><a class="reference internal" href="../command/target_compile_definitions.html#command:target_compile_definitions" title="target_compile_definitions"><code class="xref cmake cmake-command docutils literal notranslate"><span class="pre">target_compile_definitions()</span></code></a>
and others.  They enable conditional linking, conditional definitions used when
compiling, conditional include directories, and more.  The conditions may be
based on the build configuration, target properties, platform information,
or any other queryable information.</p>
<p>Generator expressions can be nested:</p>
<div class="highlight-cmake notranslate"><div class="highlight"><pre><span></span><span class="nf">target_compile_definitions(</span><span class="nb">tgt</span><span class="w"> </span><span class="no">PRIVATE</span>
<span class="w">  </span><span class="o">$&lt;$&lt;</span><span class="no">VERSION_LESS</span><span class="o">:$&lt;</span><span class="no">CXX_COMPILER_VERSION</span><span class="o">&gt;</span><span class="p">,</span><span class="m">4.2.0</span><span class="o">&gt;:</span><span class="no">OLD_COMPILER</span><span class="o">&gt;</span>
<span class="nf">)</span>
</pre></div>
</div>
<p>The above would expand to <code class="docutils literal notranslate"><span class="pre">OLD_COMPILER</span></code> if the
<span class="target" id="index-0-variable:CMAKE_&lt;LANG&gt;_COMPILER_VERSION"></span><a class="reference internal" href="../variable/CMAKE_LANG_COMPILER_VERSION.html#variable:CMAKE_&lt;LANG&gt;_COMPILER_VERSION" title="CMAKE_&lt;LANG&gt;_COMPILER_VERSION"><code class="xref cmake cmake-variable docutils literal notranslate"><span class="pre">CMAKE_CXX_COMPILER_VERSION</span></code></a> is less
than 4.2.0.</p>
</section>
<section id="whitespace-and-quoting">
<h2><a class="toc-backref" href="#id3" role="doc-backlink">Whitespace And Quoting</a><a class="headerlink" href="#whitespace-and-quoting" title="Permalink to this heading">¶</a></h2>
<p>Generator expressions are typically parsed after command arguments.
If a generator expression contains spaces, new lines, semicolons or
other characters that may be interpreted as command argument separators,
the whole expression should be surrounded by quotes when passed to a
command.  Failure to do so may result in the expression being split and
it may no longer be recognized as a generator expression.</p>
<p>When using <span class="target" id="index-0-command:add_custom_command"></span><a class="reference internal" href="../command/add_custom_command.html#command:add_custom_command" title="add_custom_command"><code class="xref cmake cmake-command docutils literal notranslate"><span class="pre">add_custom_command()</span></code></a> or <span class="target" id="index-0-command:add_custom_target"></span><a class="reference internal" href="../command/add_custom_target.html#command:add_custom_target" title="add_custom_target"><code class="xref cmake cmake-command docutils literal notranslate"><span class="pre">add_custom_target()</span></code></a>,
use the <code class="docutils literal notranslate"><span class="pre">VERBATIM</span></code> and <code class="docutils literal notranslate"><span class="pre">COMMAND_EXPAND_LISTS</span></code> options to obtain robust
argument splitting and quoting.</p>
<div class="highlight-cmake notranslate"><div class="highlight"><pre><span></span><span class="c"># WRONG: Embedded space will be treated as an argument separator.</span>
<span class="c"># This ends up not being seen as a generator expression at all.</span>
<span class="nf">add_custom_target(</span><span class="nb">run_some_tool</span>
<span class="w">  </span><span class="no">COMMAND</span><span class="w"> </span><span class="nb">some_tool</span><span class="w"> </span><span class="p">-</span><span class="no">I</span><span class="o">$&lt;</span><span class="no">JOIN</span><span class="o">:$&lt;</span><span class="no">TARGET_PROPERTY</span><span class="o">:</span><span class="nb">tgt</span><span class="p">,</span><span class="no">INCLUDE_DIRECTORIES</span><span class="o">&gt;</span><span class="p">,</span><span class="w"> </span><span class="p">-</span><span class="no">I</span><span class="o">&gt;</span>
<span class="w">  </span><span class="no">VERBATIM</span>
<span class="nf">)</span>
</pre></div>
</div>
<div class="highlight-cmake notranslate"><div class="highlight"><pre><span></span><span class="c"># Better, but still not robust. Quotes prevent the space from splitting the</span>
<span class="c"># expression. However, the tool will receive the expanded value as a single</span>
<span class="c"># argument.</span>
<span class="nf">add_custom_target(</span><span class="nb">run_some_tool</span>
<span class="w">  </span><span class="no">COMMAND</span><span class="w"> </span><span class="nb">some_tool</span><span class="w"> </span><span class="s">&quot;-I$&lt;JOIN:$&lt;TARGET_PROPERTY:tgt,INCLUDE_DIRECTORIES&gt;, -I&gt;&quot;</span>
<span class="w">  </span><span class="no">VERBATIM</span>
<span class="nf">)</span>
</pre></div>
</div>
<div class="highlight-cmake notranslate"><div class="highlight"><pre><span></span><span class="c"># Nearly correct. Using a semicolon to separate arguments and adding the</span>
<span class="c"># COMMAND_EXPAND_LISTS option means that paths with spaces will be handled</span>
<span class="c"># correctly. Quoting the whole expression ensures it is seen as a generator</span>
<span class="c"># expression. But if the target property is empty, we will get a bare -I</span>
<span class="c"># with nothing after it.</span>
<span class="nf">add_custom_target(</span><span class="nb">run_some_tool</span>
<span class="w">  </span><span class="no">COMMAND</span><span class="w"> </span><span class="nb">some_tool</span><span class="w"> </span><span class="s">&quot;-I$&lt;JOIN:$&lt;TARGET_PROPERTY:tgt,INCLUDE_DIRECTORIES&gt;,;-I&gt;&quot;</span>
<span class="w">  </span><span class="no">COMMAND_EXPAND_LISTS</span>
<span class="w">  </span><span class="no">VERBATIM</span>
<span class="nf">)</span>
</pre></div>
</div>
<p>Using variables to build up a more complex generator expression is also a
good way to reduce errors and improve readability.  The above example can be
improved further like so:</p>
<div class="highlight-cmake notranslate"><div class="highlight"><pre><span></span><span class="c"># The $&lt;BOOL:...&gt; check prevents adding anything if the property is empty,</span>
<span class="c"># assuming the property value cannot be one of CMake&#39;s false constants.</span>
<span class="nf">set(</span><span class="nb">prop</span><span class="w"> </span><span class="s">&quot;$&lt;TARGET_PROPERTY:tgt,INCLUDE_DIRECTORIES&gt;&quot;</span><span class="nf">)</span>
<span class="nf">add_custom_target(</span><span class="nb">run_some_tool</span>
<span class="w">  </span><span class="no">COMMAND</span><span class="w"> </span><span class="nb">some_tool</span><span class="w"> </span><span class="s">&quot;$&lt;$&lt;BOOL:${prop}&gt;:-I$&lt;JOIN:${prop},;-I&gt;&gt;&quot;</span>
<span class="w">  </span><span class="no">COMMAND_EXPAND_LISTS</span>
<span class="w">  </span><span class="no">VERBATIM</span>
<span class="nf">)</span>
</pre></div>
</div>
<p>Finally, the above example can be expressed in a more simple and robust way
using an alternate generator expression:</p>
<div class="highlight-cmake notranslate"><div class="highlight"><pre><span></span><span class="nf">add_custom_target(</span><span class="nb">run_some_tool</span>
<span class="w">  </span><span class="no">COMMAND</span><span class="w"> </span><span class="nb">some_tool</span><span class="w"> </span><span class="s">&quot;$&lt;LIST:TRANSFORM,$&lt;TARGET_PROPERTY:tgt,INCLUDE_DIRECTORIES&gt;,PREPEND,-I&gt;&quot;</span>
<span class="w">  </span><span class="no">COMMAND_EXPAND_LISTS</span>
<span class="w">  </span><span class="no">VERBATIM</span>
<span class="nf">)</span>
</pre></div>
</div>
<p>A common mistake is to try to split a generator expression across multiple
lines with indenting:</p>
<div class="highlight-cmake notranslate"><div class="highlight"><pre><span></span><span class="c"># WRONG: New lines and spaces all treated as argument separators, so the</span>
<span class="c"># generator expression is split and not recognized correctly.</span>
<span class="nf">target_compile_definitions(</span><span class="nb">tgt</span><span class="w"> </span><span class="no">PRIVATE</span>
<span class="w">  </span><span class="o">$&lt;$&lt;</span><span class="no">AND</span><span class="o">:</span>
<span class="w">      </span><span class="o">$&lt;</span><span class="no">CXX_COMPILER_ID</span><span class="o">:</span><span class="no">GNU</span><span class="o">&gt;</span><span class="p">,</span>
<span class="w">      </span><span class="o">$&lt;</span><span class="no">VERSION_GREATER_EQUAL</span><span class="o">:$&lt;</span><span class="no">CXX_COMPILER_VERSION</span><span class="o">&gt;</span><span class="p">,</span><span class="m">5</span><span class="o">&gt;</span>
<span class="w">    </span><span class="o">&gt;:</span><span class="no">HAVE_5_OR_LATER</span><span class="o">&gt;</span>
<span class="nf">)</span>
</pre></div>
</div>
<p>Again, use helper variables with well-chosen names to build up a readable
expression instead:</p>
<div class="highlight-cmake notranslate"><div class="highlight"><pre><span></span><span class="nf">set(</span><span class="nb">is_gnu</span><span class="w"> </span><span class="s">&quot;$&lt;CXX_COMPILER_ID:GNU&gt;&quot;</span><span class="nf">)</span>
<span class="nf">set(</span><span class="nb">v5_or_later</span><span class="w"> </span><span class="s">&quot;$&lt;VERSION_GREATER_EQUAL:$&lt;CXX_COMPILER_VERSION&gt;,5&gt;&quot;</span><span class="nf">)</span>
<span class="nf">set(</span><span class="nb">meet_requirements</span><span class="w"> </span><span class="s">&quot;$&lt;AND:${is_gnu},${v5_or_later}&gt;&quot;</span><span class="nf">)</span>
<span class="nf">target_compile_definitions(</span><span class="nb">tgt</span><span class="w"> </span><span class="no">PRIVATE</span>
<span class="w">  </span><span class="s">&quot;$&lt;${meet_requirements}:HAVE_5_OR_LATER&gt;&quot;</span>
<span class="nf">)</span>
</pre></div>
</div>
</section>
<section id="debugging">
<h2><a class="toc-backref" href="#id4" role="doc-backlink">Debugging</a><a class="headerlink" href="#debugging" title="Permalink to this heading">¶</a></h2>
<p>Since generator expressions are evaluated during generation of the buildsystem,
and not during processing of <code class="docutils literal notranslate"><span class="pre">CMakeLists.txt</span></code> files, it is not possible to
inspect their result with the <span class="target" id="index-0-command:message"></span><a class="reference internal" href="../command/message.html#command:message" title="message"><code class="xref cmake cmake-command docutils literal notranslate"><span class="pre">message()</span></code></a> command.  One possible way
to generate debug messages is to add a custom target:</p>
<div class="highlight-cmake notranslate"><div class="highlight"><pre><span></span><span class="nf">add_custom_target(</span><span class="nb">genexdebug</span><span class="w"> </span><span class="no">COMMAND</span><span class="w"> </span><span class="o">${</span><span class="nt">CMAKE_COMMAND</span><span class="o">}</span><span class="w"> </span><span class="p">-</span><span class="no">E</span><span class="w"> </span><span class="nb">echo</span><span class="w"> </span><span class="s">&quot;$&lt;...&gt;&quot;</span><span class="nf">)</span>
</pre></div>
</div>
<p>After running <strong class="program">cmake</strong>, you can then build the <code class="docutils literal notranslate"><span class="pre">genexdebug</span></code> target to print
the result of the <code class="docutils literal notranslate"><span class="pre">$&lt;...&gt;</span></code> expression (i.e. run the command
<a class="reference internal" href="cmake.1.html#cmdoption-cmake-build-t"><code class="xref std std-option docutils literal notranslate"><span class="pre">cmake</span> <span class="pre">--build</span> <span class="pre">...</span> <span class="pre">--target</span> <span class="pre">genexdebug</span></code></a>).</p>
<p>Another way is to write debug messages to a file with <span class="target" id="index-0-command:file"></span><a class="reference internal" href="../command/file.html#generate" title="file(generate)"><code class="xref cmake cmake-command docutils literal notranslate"><span class="pre">file(GENERATE)</span></code></a>:</p>
<div class="highlight-cmake notranslate"><div class="highlight"><pre><span></span><span class="nf">file(</span><span class="no">GENERATE</span><span class="w"> </span><span class="no">OUTPUT</span><span class="w"> </span><span class="nb">filename</span><span class="w"> </span><span class="no">CONTENT</span><span class="w"> </span><span class="s">&quot;$&lt;...&gt;&quot;</span><span class="nf">)</span>
</pre></div>
</div>
</section>
<section id="generator-expression-reference">
<h2><a class="toc-backref" href="#id5" role="doc-backlink">Generator Expression Reference</a><a class="headerlink" href="#generator-expression-reference" title="Permalink to this heading">¶</a></h2>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>This reference deviates from most of the CMake documentation in that it
omits angular brackets <code class="docutils literal notranslate"><span class="pre">&lt;...&gt;</span></code> around placeholders like <code class="docutils literal notranslate"><span class="pre">condition</span></code>,
<code class="docutils literal notranslate"><span class="pre">string</span></code>, <code class="docutils literal notranslate"><span class="pre">target</span></code>, etc.  This is to prevent an opportunity for those
placeholders to be misinterpreted as generator expressions.</p>
</div>
<section id="conditional-expressions">
<span id="conditional-generator-expressions"></span><h3><a class="toc-backref" href="#id6" role="doc-backlink">Conditional Expressions</a><a class="headerlink" href="#conditional-expressions" title="Permalink to this heading">¶</a></h3>
<p>A fundamental category of generator expressions relates to conditional logic.
Two forms of conditional generator expressions are supported:</p>
<dl class="cmake genex">
<dt class="sig sig-object cmake" id="genex:condition">
<span class="sig-name descname"><span class="pre">$&lt;condition:true_string&gt;</span></span><a class="headerlink" href="#genex:condition" title="Permalink to this definition">¶</a></dt>
<dd><p>Evaluates to <code class="docutils literal notranslate"><span class="pre">true_string</span></code> if <code class="docutils literal notranslate"><span class="pre">condition</span></code> is <code class="docutils literal notranslate"><span class="pre">1</span></code>, or an empty string
if <code class="docutils literal notranslate"><span class="pre">condition</span></code> evaluates to <code class="docutils literal notranslate"><span class="pre">0</span></code>.  Any other value for <code class="docutils literal notranslate"><span class="pre">condition</span></code>
results in an error.</p>
</dd></dl>

<dl class="cmake genex">
<dt class="sig sig-object cmake" id="genex:IF">
<span class="sig-name descname"><span class="pre">$&lt;IF:condition,true_string,false_string&gt;</span></span><a class="headerlink" href="#genex:IF" title="Permalink to this definition">¶</a></dt>
<dd><div class="versionadded">
<p><span class="versionmodified added">New in version 3.8.</span></p>
</div>
<p>Evaluates to <code class="docutils literal notranslate"><span class="pre">true_string</span></code> if <code class="docutils literal notranslate"><span class="pre">condition</span></code> is <code class="docutils literal notranslate"><span class="pre">1</span></code>, or <code class="docutils literal notranslate"><span class="pre">false_string</span></code>
if <code class="docutils literal notranslate"><span class="pre">condition</span></code> is <code class="docutils literal notranslate"><span class="pre">0</span></code>.  Any other value for <code class="docutils literal notranslate"><span class="pre">condition</span></code> results in an
error.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.28: </span>This generator expression short-circuits such that generator expressions in
<code class="docutils literal notranslate"><span class="pre">false_string</span></code> will not evaluate when <code class="docutils literal notranslate"><span class="pre">condition</span></code> is <code class="docutils literal notranslate"><span class="pre">1</span></code>, and generator
expressions in <code class="docutils literal notranslate"><span class="pre">true_string</span></code> will not evaluate when condition is <code class="docutils literal notranslate"><span class="pre">0</span></code>.</p>
</div>
</dd></dl>

<p>Typically, the <code class="docutils literal notranslate"><span class="pre">condition</span></code> is itself a generator expression.  For instance,
the following expression expands to <code class="docutils literal notranslate"><span class="pre">DEBUG_MODE</span></code> when the <code class="docutils literal notranslate"><span class="pre">Debug</span></code>
configuration is used, and the empty string for all other configurations:</p>
<div class="highlight-cmake notranslate"><div class="highlight"><pre><span></span><span class="o">$&lt;$&lt;</span><span class="no">CONFIG</span><span class="o">:</span><span class="nb">Debug</span><span class="o">&gt;:</span><span class="no">DEBUG_MODE</span><span class="o">&gt;</span>
</pre></div>
</div>
<p>Boolean-like <code class="docutils literal notranslate"><span class="pre">condition</span></code> values other than <code class="docutils literal notranslate"><span class="pre">1</span></code> or <code class="docutils literal notranslate"><span class="pre">0</span></code> can be handled
by wrapping them with the <code class="docutils literal notranslate"><span class="pre">$&lt;BOOL:...&gt;</span></code> generator expression:</p>
<dl class="cmake genex">
<dt class="sig sig-object cmake" id="genex:BOOL">
<span class="sig-name descname"><span class="pre">$&lt;BOOL:string&gt;</span></span><a class="headerlink" href="#genex:BOOL" title="Permalink to this definition">¶</a></dt>
<dd><p>Converts <code class="docutils literal notranslate"><span class="pre">string</span></code> to <code class="docutils literal notranslate"><span class="pre">0</span></code> or <code class="docutils literal notranslate"><span class="pre">1</span></code>. Evaluates to <code class="docutils literal notranslate"><span class="pre">0</span></code> if any of the
following is true:</p>
<ul class="simple">
<li><p><code class="docutils literal notranslate"><span class="pre">string</span></code> is empty,</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">string</span></code> is a case-insensitive equal of
<code class="docutils literal notranslate"><span class="pre">0</span></code>, <code class="docutils literal notranslate"><span class="pre">FALSE</span></code>, <code class="docutils literal notranslate"><span class="pre">OFF</span></code>, <code class="docutils literal notranslate"><span class="pre">N</span></code>, <code class="docutils literal notranslate"><span class="pre">NO</span></code>, <code class="docutils literal notranslate"><span class="pre">IGNORE</span></code>, or <code class="docutils literal notranslate"><span class="pre">NOTFOUND</span></code>, or</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">string</span></code> ends in the suffix <code class="docutils literal notranslate"><span class="pre">-NOTFOUND</span></code> (case-sensitive).</p></li>
</ul>
<p>Otherwise evaluates to <code class="docutils literal notranslate"><span class="pre">1</span></code>.</p>
</dd></dl>

<p>The <code class="docutils literal notranslate"><span class="pre">$&lt;BOOL:...&gt;</span></code> generator expression is often used when a <code class="docutils literal notranslate"><span class="pre">condition</span></code>
is provided by a CMake variable:</p>
<div class="highlight-cmake notranslate"><div class="highlight"><pre><span></span><span class="o">$&lt;$&lt;</span><span class="no">BOOL</span><span class="o">:${</span><span class="nt">HAVE_SOME_FEATURE</span><span class="o">}&gt;:</span><span class="p">-</span><span class="no">DENABLE_SOME_FEATURE</span><span class="o">&gt;</span>
</pre></div>
</div>
</section>
<section id="logical-operators">
<span id="boolean-generator-expressions"></span><h3><a class="toc-backref" href="#id7" role="doc-backlink">Logical Operators</a><a class="headerlink" href="#logical-operators" title="Permalink to this heading">¶</a></h3>
<p>The common boolean logic operators are supported:</p>
<dl class="cmake genex">
<dt class="sig sig-object cmake" id="genex:AND">
<span class="sig-name descname"><span class="pre">$&lt;AND:conditions&gt;</span></span><a class="headerlink" href="#genex:AND" title="Permalink to this definition">¶</a></dt>
<dd><p>where <code class="docutils literal notranslate"><span class="pre">conditions</span></code> is a comma-separated list of boolean expressions,
all of which must evaluate to either <code class="docutils literal notranslate"><span class="pre">1</span></code> or <code class="docutils literal notranslate"><span class="pre">0</span></code>.  The whole expression
evaluates to <code class="docutils literal notranslate"><span class="pre">1</span></code> if all conditions are <code class="docutils literal notranslate"><span class="pre">1</span></code>.  If any condition is <code class="docutils literal notranslate"><span class="pre">0</span></code>,
the whole expression evaluates to <code class="docutils literal notranslate"><span class="pre">0</span></code>.</p>
</dd></dl>

<dl class="cmake genex">
<dt class="sig sig-object cmake" id="genex:OR">
<span class="sig-name descname"><span class="pre">$&lt;OR:conditions&gt;</span></span><a class="headerlink" href="#genex:OR" title="Permalink to this definition">¶</a></dt>
<dd><p>where <code class="docutils literal notranslate"><span class="pre">conditions</span></code> is a comma-separated list of boolean expressions.
all of which must evaluate to either <code class="docutils literal notranslate"><span class="pre">1</span></code> or <code class="docutils literal notranslate"><span class="pre">0</span></code>.  The whole expression
evaluates to <code class="docutils literal notranslate"><span class="pre">1</span></code> if at least one of the <code class="docutils literal notranslate"><span class="pre">conditions</span></code> is <code class="docutils literal notranslate"><span class="pre">1</span></code>.  If all
<code class="docutils literal notranslate"><span class="pre">conditions</span></code> evaluate to <code class="docutils literal notranslate"><span class="pre">0</span></code>, the whole expression evaluates to <code class="docutils literal notranslate"><span class="pre">0</span></code>.</p>
</dd></dl>

<dl class="cmake genex">
<dt class="sig sig-object cmake" id="genex:NOT">
<span class="sig-name descname"><span class="pre">$&lt;NOT:condition&gt;</span></span><a class="headerlink" href="#genex:NOT" title="Permalink to this definition">¶</a></dt>
<dd><p><code class="docutils literal notranslate"><span class="pre">condition</span></code> must be <code class="docutils literal notranslate"><span class="pre">0</span></code> or <code class="docutils literal notranslate"><span class="pre">1</span></code>.  The result of the expression is
<code class="docutils literal notranslate"><span class="pre">0</span></code> if <code class="docutils literal notranslate"><span class="pre">condition</span></code> is <code class="docutils literal notranslate"><span class="pre">1</span></code>, else <code class="docutils literal notranslate"><span class="pre">1</span></code>.</p>
</dd></dl>

<div class="versionadded">
<p><span class="versionmodified added">New in version 3.28: </span>Logical operators short-circuit such that generator expressions in the
arguments list will not be evaluated once a return value can be determined.</p>
</div>
</section>
<section id="primary-comparison-expressions">
<span id="comparison-expressions"></span><h3><a class="toc-backref" href="#id8" role="doc-backlink">Primary Comparison Expressions</a><a class="headerlink" href="#primary-comparison-expressions" title="Permalink to this heading">¶</a></h3>
<p>CMake supports a variety of generator expressions that compare things.
This section covers the primary and most widely used comparison types.
Other more specific comparison types are documented in their own separate
sections further below.</p>
<section id="string-comparisons">
<h4><a class="toc-backref" href="#id9" role="doc-backlink">String Comparisons</a><a class="headerlink" href="#string-comparisons" title="Permalink to this heading">¶</a></h4>
<dl class="cmake genex">
<dt class="sig sig-object cmake" id="genex:STREQUAL">
<span class="sig-name descname"><span class="pre">$&lt;STREQUAL:string1,string2&gt;</span></span><a class="headerlink" href="#genex:STREQUAL" title="Permalink to this definition">¶</a></dt>
<dd><p><code class="docutils literal notranslate"><span class="pre">1</span></code> if <code class="docutils literal notranslate"><span class="pre">string1</span></code> and <code class="docutils literal notranslate"><span class="pre">string2</span></code> are equal, else <code class="docutils literal notranslate"><span class="pre">0</span></code>.
The comparison is case-sensitive.  For a case-insensitive comparison,
combine with a <a class="reference internal" href="#string-transforming-generator-expressions"><span class="std std-ref">string transforming generator expression</span></a>.  For example, the following
evaluates to <code class="docutils literal notranslate"><span class="pre">1</span></code> if <code class="docutils literal notranslate"><span class="pre">${foo}</span></code> is any of <code class="docutils literal notranslate"><span class="pre">BAR</span></code>, <code class="docutils literal notranslate"><span class="pre">Bar</span></code>, <code class="docutils literal notranslate"><span class="pre">bar</span></code>, etc.</p>
<div class="highlight-cmake notranslate"><div class="highlight"><pre><span></span><span class="o">$&lt;</span><span class="no">STREQUAL</span><span class="o">:$&lt;</span><span class="no">UPPER_CASE</span><span class="o">:${</span><span class="nt">foo</span><span class="o">}&gt;</span><span class="p">,</span><span class="no">BAR</span><span class="o">&gt;</span>
</pre></div>
</div>
</dd></dl>

<dl class="cmake genex">
<dt class="sig sig-object cmake" id="genex:EQUAL">
<span class="sig-name descname"><span class="pre">$&lt;EQUAL:value1,value2&gt;</span></span><a class="headerlink" href="#genex:EQUAL" title="Permalink to this definition">¶</a></dt>
<dd><p><code class="docutils literal notranslate"><span class="pre">1</span></code> if <code class="docutils literal notranslate"><span class="pre">value1</span></code> and <code class="docutils literal notranslate"><span class="pre">value2</span></code> are numerically equal, else <code class="docutils literal notranslate"><span class="pre">0</span></code>.</p>
</dd></dl>

</section>
<section id="version-comparisons">
<h4><a class="toc-backref" href="#id10" role="doc-backlink">Version Comparisons</a><a class="headerlink" href="#version-comparisons" title="Permalink to this heading">¶</a></h4>
<dl class="cmake genex">
<dt class="sig sig-object cmake" id="genex:VERSION_LESS">
<span class="sig-name descname"><span class="pre">$&lt;VERSION_LESS:v1,v2&gt;</span></span><a class="headerlink" href="#genex:VERSION_LESS" title="Permalink to this definition">¶</a></dt>
<dd><p><code class="docutils literal notranslate"><span class="pre">1</span></code> if <code class="docutils literal notranslate"><span class="pre">v1</span></code> is a version less than <code class="docutils literal notranslate"><span class="pre">v2</span></code>, else <code class="docutils literal notranslate"><span class="pre">0</span></code>.</p>
</dd></dl>

<dl class="cmake genex">
<dt class="sig sig-object cmake" id="genex:VERSION_GREATER">
<span class="sig-name descname"><span class="pre">$&lt;VERSION_GREATER:v1,v2&gt;</span></span><a class="headerlink" href="#genex:VERSION_GREATER" title="Permalink to this definition">¶</a></dt>
<dd><p><code class="docutils literal notranslate"><span class="pre">1</span></code> if <code class="docutils literal notranslate"><span class="pre">v1</span></code> is a version greater than <code class="docutils literal notranslate"><span class="pre">v2</span></code>, else <code class="docutils literal notranslate"><span class="pre">0</span></code>.</p>
</dd></dl>

<dl class="cmake genex">
<dt class="sig sig-object cmake" id="genex:VERSION_EQUAL">
<span class="sig-name descname"><span class="pre">$&lt;VERSION_EQUAL:v1,v2&gt;</span></span><a class="headerlink" href="#genex:VERSION_EQUAL" title="Permalink to this definition">¶</a></dt>
<dd><p><code class="docutils literal notranslate"><span class="pre">1</span></code> if <code class="docutils literal notranslate"><span class="pre">v1</span></code> is the same version as <code class="docutils literal notranslate"><span class="pre">v2</span></code>, else <code class="docutils literal notranslate"><span class="pre">0</span></code>.</p>
</dd></dl>

<dl class="cmake genex">
<dt class="sig sig-object cmake" id="genex:VERSION_LESS_EQUAL">
<span class="sig-name descname"><span class="pre">$&lt;VERSION_LESS_EQUAL:v1,v2&gt;</span></span><a class="headerlink" href="#genex:VERSION_LESS_EQUAL" title="Permalink to this definition">¶</a></dt>
<dd><div class="versionadded">
<p><span class="versionmodified added">New in version 3.7.</span></p>
</div>
<p><code class="docutils literal notranslate"><span class="pre">1</span></code> if <code class="docutils literal notranslate"><span class="pre">v1</span></code> is a version less than or equal to <code class="docutils literal notranslate"><span class="pre">v2</span></code>, else <code class="docutils literal notranslate"><span class="pre">0</span></code>.</p>
</dd></dl>

<dl class="cmake genex">
<dt class="sig sig-object cmake" id="genex:VERSION_GREATER_EQUAL">
<span class="sig-name descname"><span class="pre">$&lt;VERSION_GREATER_EQUAL:v1,v2&gt;</span></span><a class="headerlink" href="#genex:VERSION_GREATER_EQUAL" title="Permalink to this definition">¶</a></dt>
<dd><div class="versionadded">
<p><span class="versionmodified added">New in version 3.7.</span></p>
</div>
<p><code class="docutils literal notranslate"><span class="pre">1</span></code> if <code class="docutils literal notranslate"><span class="pre">v1</span></code> is a version greater than or equal to <code class="docutils literal notranslate"><span class="pre">v2</span></code>, else <code class="docutils literal notranslate"><span class="pre">0</span></code>.</p>
</dd></dl>

</section>
</section>
<section id="string-transformations">
<span id="string-transforming-generator-expressions"></span><h3><a class="toc-backref" href="#id11" role="doc-backlink">String Transformations</a><a class="headerlink" href="#string-transformations" title="Permalink to this heading">¶</a></h3>
<dl class="cmake genex">
<dt class="sig sig-object cmake" id="genex:LOWER_CASE">
<span class="sig-name descname"><span class="pre">$&lt;LOWER_CASE:string&gt;</span></span><a class="headerlink" href="#genex:LOWER_CASE" title="Permalink to this definition">¶</a></dt>
<dd><p>Content of <code class="docutils literal notranslate"><span class="pre">string</span></code> converted to lower case.</p>
</dd></dl>

<dl class="cmake genex">
<dt class="sig sig-object cmake" id="genex:UPPER_CASE">
<span class="sig-name descname"><span class="pre">$&lt;UPPER_CASE:string&gt;</span></span><a class="headerlink" href="#genex:UPPER_CASE" title="Permalink to this definition">¶</a></dt>
<dd><p>Content of <code class="docutils literal notranslate"><span class="pre">string</span></code> converted to upper case.</p>
</dd></dl>

<dl class="cmake genex">
<dt class="sig sig-object cmake" id="genex:MAKE_C_IDENTIFIER">
<span class="sig-name descname"><span class="pre">$&lt;MAKE_C_IDENTIFIER:...&gt;</span></span><a class="headerlink" href="#genex:MAKE_C_IDENTIFIER" title="Permalink to this definition">¶</a></dt>
<dd><p>Content of <code class="docutils literal notranslate"><span class="pre">...</span></code> converted to a C identifier.  The conversion follows the
same behavior as <span class="target" id="index-0-command:string"></span><a class="reference internal" href="../command/string.html#make-c-identifier" title="string(make_c_identifier)"><code class="xref cmake cmake-command docutils literal notranslate"><span class="pre">string(MAKE_C_IDENTIFIER)</span></code></a>.</p>
</dd></dl>

</section>
<section id="list-expressions">
<h3><a class="toc-backref" href="#id12" role="doc-backlink">List Expressions</a><a class="headerlink" href="#list-expressions" title="Permalink to this heading">¶</a></h3>
<p>Most of the expressions in this section are closely associated with the
<span class="target" id="index-0-command:list"></span><a class="reference internal" href="../command/list.html#command:list" title="list"><code class="xref cmake cmake-command docutils literal notranslate"><span class="pre">list()</span></code></a> command, providing the same capabilities, but in
the form of a generator expression.</p>
<p>In each of the following list-related generator expressions, the <code class="docutils literal notranslate"><span class="pre">list</span></code>
must not contain any commas if that generator expression expects something to
be provided after the <code class="docutils literal notranslate"><span class="pre">list</span></code>.  For example, the expression
<code class="docutils literal notranslate"><span class="pre">$&lt;LIST:FIND,list,value&gt;</span></code> requires a <code class="docutils literal notranslate"><span class="pre">value</span></code> after the <code class="docutils literal notranslate"><span class="pre">list</span></code>.
Since a comma is used to separate the <code class="docutils literal notranslate"><span class="pre">list</span></code> and the <code class="docutils literal notranslate"><span class="pre">value</span></code>, the <code class="docutils literal notranslate"><span class="pre">list</span></code>
cannot itself contain a comma.  This restriction does not apply to the
<span class="target" id="index-1-command:list"></span><a class="reference internal" href="../command/list.html#command:list" title="list"><code class="xref cmake cmake-command docutils literal notranslate"><span class="pre">list()</span></code></a> command, it is specific to the list-handling generator
expressions only.</p>
<section id="list-comparisons">
<span id="genex-list-comparisons"></span><h4><a class="toc-backref" href="#id13" role="doc-backlink">List Comparisons</a><a class="headerlink" href="#list-comparisons" title="Permalink to this heading">¶</a></h4>
<dl class="cmake genex">
<dt class="sig sig-object cmake" id="genex:IN_LIST">
<span class="sig-name descname"><span class="pre">$&lt;IN_LIST:string,list&gt;</span></span><a class="headerlink" href="#genex:IN_LIST" title="Permalink to this definition">¶</a></dt>
<dd><div class="versionadded">
<p><span class="versionmodified added">New in version 3.12.</span></p>
</div>
<p><code class="docutils literal notranslate"><span class="pre">1</span></code> if <code class="docutils literal notranslate"><span class="pre">string</span></code> is an item in the semicolon-separated <code class="docutils literal notranslate"><span class="pre">list</span></code>, else <code class="docutils literal notranslate"><span class="pre">0</span></code>.
It uses case-sensitive comparisons.</p>
</dd></dl>

</section>
<section id="list-queries">
<span id="genex-list-queries"></span><h4><a class="toc-backref" href="#id14" role="doc-backlink">List Queries</a><a class="headerlink" href="#list-queries" title="Permalink to this heading">¶</a></h4>
<dl class="cmake genex">
<dt class="sig sig-object cmake" id="genex:LIST">
<span class="sig-name descname"><span class="pre">$&lt;LIST:LENGTH,list&gt;</span></span><a class="headerlink" href="#genex:LIST" title="Permalink to this definition">¶</a></dt>
<dd><div class="versionadded">
<p><span class="versionmodified added">New in version 3.27.</span></p>
</div>
<p>The number of items in the <code class="docutils literal notranslate"><span class="pre">list</span></code>.</p>
</dd></dl>

<dl class="cmake genex">
<dt class="sig sig-object cmake">
<span class="sig-name descname"><span class="pre">$&lt;LIST:GET,list,index,...&gt;</span></span></dt>
<dd><div class="versionadded">
<p><span class="versionmodified added">New in version 3.27.</span></p>
</div>
<p>Expands to the list of items specified by indices from the <code class="docutils literal notranslate"><span class="pre">list</span></code>.</p>
</dd></dl>

<dl class="cmake genex">
<dt class="sig sig-object cmake">
<span class="sig-name descname"><span class="pre">$&lt;LIST:SUBLIST,list,begin,length&gt;</span></span></dt>
<dd><div class="versionadded">
<p><span class="versionmodified added">New in version 3.27.</span></p>
</div>
<p>A sublist of the given <code class="docutils literal notranslate"><span class="pre">list</span></code>.  If <code class="docutils literal notranslate"><span class="pre">length</span></code> is 0, an empty list
will be returned.  If <code class="docutils literal notranslate"><span class="pre">length</span></code> is -1 or the list is smaller than
<code class="docutils literal notranslate"><span class="pre">begin</span> <span class="pre">+</span> <span class="pre">length</span></code>, the remaining items of the list starting at
<code class="docutils literal notranslate"><span class="pre">begin</span></code> will be returned.</p>
</dd></dl>

<dl class="cmake genex">
<dt class="sig sig-object cmake">
<span class="sig-name descname"><span class="pre">$&lt;LIST:FIND,list,value&gt;</span></span></dt>
<dd><div class="versionadded">
<p><span class="versionmodified added">New in version 3.27.</span></p>
</div>
<p>The index of the first item in <code class="docutils literal notranslate"><span class="pre">list</span></code> with the specified <code class="docutils literal notranslate"><span class="pre">value</span></code>,
or -1 if <code class="docutils literal notranslate"><span class="pre">value</span></code> is not in the <code class="docutils literal notranslate"><span class="pre">list</span></code>.</p>
</dd></dl>

</section>
<section id="list-transformations">
<span id="genex-list-transformations"></span><h4><a class="toc-backref" href="#id15" role="doc-backlink">List Transformations</a><a class="headerlink" href="#list-transformations" title="Permalink to this heading">¶</a></h4>
<span class="target" id="genex-list-join"></span><dl class="cmake genex">
<dt class="sig sig-object cmake">
<span class="sig-name descname"><span class="pre">$&lt;LIST:JOIN,list,glue&gt;</span></span></dt>
<dd><div class="versionadded">
<p><span class="versionmodified added">New in version 3.27.</span></p>
</div>
<p>Converts <code class="docutils literal notranslate"><span class="pre">list</span></code> to a single string with the content of the <code class="docutils literal notranslate"><span class="pre">glue</span></code> string
inserted between each item.  This is conceptually the same operation as
<span class="target" id="index-0-genex:JOIN"></span><a class="reference internal" href="#genex:JOIN" title="JOIN"><code class="xref cmake cmake-genex docutils literal notranslate"><span class="pre">$&lt;JOIN:list,glue&gt;</span></code></a>, but the two have different behavior with regard
to empty items.  <code class="docutils literal notranslate"><span class="pre">$&lt;LIST:JOIN,list,glue&gt;</span></code> preserves all empty items,
whereas <span class="target" id="index-1-genex:JOIN"></span><a class="reference internal" href="#genex:JOIN" title="JOIN"><code class="xref cmake cmake-genex docutils literal notranslate"><span class="pre">$&lt;JOIN:list,glue&gt;</span></code></a> drops all empty items from the list.</p>
</dd></dl>

<dl class="cmake genex">
<dt class="sig sig-object cmake">
<span class="sig-name descname"><span class="pre">$&lt;LIST:APPEND,list,item,...&gt;</span></span></dt>
<dd><div class="versionadded">
<p><span class="versionmodified added">New in version 3.27.</span></p>
</div>
<p>The <code class="docutils literal notranslate"><span class="pre">list</span></code> with each <code class="docutils literal notranslate"><span class="pre">item</span></code> appended.  Multiple items should be
separated by commas.</p>
</dd></dl>

<dl class="cmake genex">
<dt class="sig sig-object cmake">
<span class="sig-name descname"><span class="pre">$&lt;LIST:PREPEND,list,item,...&gt;</span></span></dt>
<dd><div class="versionadded">
<p><span class="versionmodified added">New in version 3.27.</span></p>
</div>
<p>The <code class="docutils literal notranslate"><span class="pre">list</span></code> with each <code class="docutils literal notranslate"><span class="pre">item</span></code> inserted at the beginning.  If there are
multiple items, they should be separated by commas, and the order of the
prepended items will be preserved.</p>
</dd></dl>

<dl class="cmake genex">
<dt class="sig sig-object cmake">
<span class="sig-name descname"><span class="pre">$&lt;LIST:INSERT,list,index,item,...&gt;</span></span></dt>
<dd><div class="versionadded">
<p><span class="versionmodified added">New in version 3.27.</span></p>
</div>
<p>The <code class="docutils literal notranslate"><span class="pre">list</span></code> with the <code class="docutils literal notranslate"><span class="pre">item</span></code> (or multiple items) inserted at the specified
<code class="docutils literal notranslate"><span class="pre">index</span></code>.  Multiple items should be separated by commas.</p>
<p>It is an error to specify an out-of-range <code class="docutils literal notranslate"><span class="pre">index</span></code>. Valid indexes are 0 to N,
where N is the length of the list, inclusive. An empty list has length 0.</p>
</dd></dl>

<dl class="cmake genex">
<dt class="sig sig-object cmake">
<span class="sig-name descname"><span class="pre">$&lt;LIST:POP_BACK,list&gt;</span></span></dt>
<dd><div class="versionadded">
<p><span class="versionmodified added">New in version 3.27.</span></p>
</div>
<p>The <code class="docutils literal notranslate"><span class="pre">list</span></code> with the last item removed.</p>
</dd></dl>

<dl class="cmake genex">
<dt class="sig sig-object cmake">
<span class="sig-name descname"><span class="pre">$&lt;LIST:POP_FRONT,list&gt;</span></span></dt>
<dd><div class="versionadded">
<p><span class="versionmodified added">New in version 3.27.</span></p>
</div>
<p>The <code class="docutils literal notranslate"><span class="pre">list</span></code> with the first item removed.</p>
</dd></dl>

<dl class="cmake genex">
<dt class="sig sig-object cmake">
<span class="sig-name descname"><span class="pre">$&lt;LIST:REMOVE_ITEM,list,value,...&gt;</span></span></dt>
<dd><div class="versionadded">
<p><span class="versionmodified added">New in version 3.27.</span></p>
</div>
<p>The <code class="docutils literal notranslate"><span class="pre">list</span></code> with all instances of the given <code class="docutils literal notranslate"><span class="pre">value</span></code> (or values) removed.
If multiple values are given, they should be separated by commas.</p>
</dd></dl>

<dl class="cmake genex">
<dt class="sig sig-object cmake">
<span class="sig-name descname"><span class="pre">$&lt;LIST:REMOVE_AT,list,index,...&gt;</span></span></dt>
<dd><div class="versionadded">
<p><span class="versionmodified added">New in version 3.27.</span></p>
</div>
<p>The <code class="docutils literal notranslate"><span class="pre">list</span></code> with the item at each given <code class="docutils literal notranslate"><span class="pre">index</span></code> removed.</p>
</dd></dl>

<span class="target" id="genex-list-remove-duplicates"></span><dl class="cmake genex">
<dt class="sig sig-object cmake">
<span class="sig-name descname"><span class="pre">$&lt;LIST:REMOVE_DUPLICATES,list&gt;</span></span></dt>
<dd><div class="versionadded">
<p><span class="versionmodified added">New in version 3.27.</span></p>
</div>
<p>The <code class="docutils literal notranslate"><span class="pre">list</span></code> with all duplicated items removed.  The relative order of
items is preserved, but if duplicates are encountered, only the first
instance is preserved.  The result is the same as
<span class="target" id="index-0-genex:REMOVE_DUPLICATES"></span><a class="reference internal" href="#genex:REMOVE_DUPLICATES" title="REMOVE_DUPLICATES"><code class="xref cmake cmake-genex docutils literal notranslate"><span class="pre">$&lt;REMOVE_DUPLICATES:list&gt;</span></code></a>.</p>
</dd></dl>

<span class="target" id="genex-list-filter"></span><dl class="cmake genex">
<dt class="sig sig-object cmake">
<span class="sig-name descname"><span class="pre">$&lt;LIST:FILTER,list,INCLUDE|EXCLUDE,regex&gt;</span></span></dt>
<dd><div class="versionadded">
<p><span class="versionmodified added">New in version 3.27.</span></p>
</div>
<p>A list of items from the <code class="docutils literal notranslate"><span class="pre">list</span></code> which match (<code class="docutils literal notranslate"><span class="pre">INCLUDE</span></code>) or do not match
(<code class="docutils literal notranslate"><span class="pre">EXCLUDE</span></code>) the regular expression <code class="docutils literal notranslate"><span class="pre">regex</span></code>.  The result is the same as
<span class="target" id="index-0-genex:FILTER"></span><a class="reference internal" href="#genex:FILTER" title="FILTER"><code class="xref cmake cmake-genex docutils literal notranslate"><span class="pre">$&lt;FILTER:list,INCLUDE|EXCLUDE,regex&gt;</span></code></a>.</p>
</dd></dl>

<dl class="cmake genex">
<dt class="sig sig-object cmake">
<span class="sig-name descname"><span class="pre">$&lt;LIST:TRANSFORM,list,ACTION[,SELECTOR]&gt;</span></span></dt>
<dd><div class="versionadded">
<p><span class="versionmodified added">New in version 3.27.</span></p>
</div>
<p>The <code class="docutils literal notranslate"><span class="pre">list</span></code> transformed by applying an <code class="docutils literal notranslate"><span class="pre">ACTION</span></code> to all or, by
specifying a <code class="docutils literal notranslate"><span class="pre">SELECTOR</span></code>, to the selected list items.</p>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>The <code class="docutils literal notranslate"><span class="pre">TRANSFORM</span></code> sub-command does not change the number of items in the
list. If a <code class="docutils literal notranslate"><span class="pre">SELECTOR</span></code> is specified, only some items will be changed,
the other ones will remain the same as before the transformation.</p>
</div>
<p><code class="docutils literal notranslate"><span class="pre">ACTION</span></code> specifies the action to apply to the items of the list.
The actions have exactly the same semantics as for the
<span class="target" id="index-2-command:list"></span><a class="reference internal" href="../command/list.html#transform" title="list(transform)"><code class="xref cmake cmake-command docutils literal notranslate"><span class="pre">list(TRANSFORM)</span></code></a> command.  <code class="docutils literal notranslate"><span class="pre">ACTION</span></code> must be one of the following:</p>
<blockquote>
<div><dl>
<dt><span class="target" id="index-3-command:list"></span><a class="reference internal" href="../command/list.html#transform-append" title="list(transform_append)"><code class="xref cmake cmake-command docutils literal notranslate"><span class="pre">APPEND</span></code></a>, <span class="target" id="index-4-command:list"></span><a class="reference internal" href="../command/list.html#transform-append" title="list(transform_append)"><code class="xref cmake cmake-command docutils literal notranslate"><span class="pre">PREPEND</span></code></a></dt><dd><p>Append, prepend specified value to each item of the list.</p>
<div class="highlight-cmake notranslate"><div class="highlight"><pre><span></span><span class="o">$&lt;</span><span class="no">LIST</span><span class="o">:</span><span class="no">TRANSFORM</span><span class="p">,</span><span class="nb">list</span><span class="p">,</span><span class="nf">(</span><span class="no">APPEND</span><span class="p">|</span><span class="no">PREPEND</span><span class="nf">)</span><span class="p">,</span><span class="nb">value</span><span class="p">[,</span><span class="no">SELECTOR</span><span class="p">]</span><span class="o">&gt;</span>
</pre></div>
</div>
</dd>
<dt><span class="target" id="index-5-command:list"></span><a class="reference internal" href="../command/list.html#transform-tolower" title="list(transform_tolower)"><code class="xref cmake cmake-command docutils literal notranslate"><span class="pre">TOLOWER</span></code></a>, <span class="target" id="index-6-command:list"></span><a class="reference internal" href="../command/list.html#transform-tolower" title="list(transform_tolower)"><code class="xref cmake cmake-command docutils literal notranslate"><span class="pre">TOUPPER</span></code></a></dt><dd><p>Convert each item of the list to lower, upper characters.</p>
<div class="highlight-cmake notranslate"><div class="highlight"><pre><span></span><span class="o">$&lt;</span><span class="no">LIST</span><span class="o">:</span><span class="no">TRANSFORM</span><span class="p">,</span><span class="nb">list</span><span class="p">,</span><span class="nf">(</span><span class="no">TOLOWER</span><span class="p">|</span><span class="no">TOUPPER</span><span class="nf">)</span><span class="p">[,</span><span class="no">SELECTOR</span><span class="p">]</span><span class="o">&gt;</span>
</pre></div>
</div>
</dd>
<dt><span class="target" id="index-7-command:list"></span><a class="reference internal" href="../command/list.html#transform-strip" title="list(transform_strip)"><code class="xref cmake cmake-command docutils literal notranslate"><span class="pre">STRIP</span></code></a></dt><dd><p>Remove leading and trailing spaces from each item of the list.</p>
<div class="highlight-cmake notranslate"><div class="highlight"><pre><span></span><span class="o">$&lt;</span><span class="no">LIST</span><span class="o">:</span><span class="no">TRANSFORM</span><span class="p">,</span><span class="nb">list</span><span class="p">,</span><span class="no">STRIP</span><span class="p">[,</span><span class="no">SELECTOR</span><span class="p">]</span><span class="o">&gt;</span>
</pre></div>
</div>
</dd>
<dt><span class="target" id="index-8-command:list"></span><a class="reference internal" href="../command/list.html#transform-replace" title="list(transform_replace)"><code class="xref cmake cmake-command docutils literal notranslate"><span class="pre">REPLACE</span></code></a>:</dt><dd><p>Match the regular expression as many times as possible and substitute
the replacement expression for the match for each item of the list.</p>
<div class="highlight-cmake notranslate"><div class="highlight"><pre><span></span><span class="o">$&lt;</span><span class="no">LIST</span><span class="o">:</span><span class="no">TRANSFORM</span><span class="p">,</span><span class="nb">list</span><span class="p">,</span><span class="no">REPLACE</span><span class="p">,</span><span class="nb">regular_expression</span><span class="p">,</span><span class="nb">replace_expression</span><span class="p">[,</span><span class="no">SELECTOR</span><span class="p">]</span><span class="o">&gt;</span>
</pre></div>
</div>
</dd>
</dl>
</div></blockquote>
<p><code class="docutils literal notranslate"><span class="pre">SELECTOR</span></code> determines which items of the list will be transformed.
Only one type of selector can be specified at a time. When given,
<code class="docutils literal notranslate"><span class="pre">SELECTOR</span></code> must be one of the following:</p>
<blockquote>
<div><dl>
<dt><code class="docutils literal notranslate"><span class="pre">AT</span></code></dt><dd><p>Specify a list of indexes.</p>
<div class="highlight-cmake notranslate"><div class="highlight"><pre><span></span><span class="o">$&lt;</span><span class="no">LIST</span><span class="o">:</span><span class="no">TRANSFORM</span><span class="p">,</span><span class="nb">list</span><span class="p">,</span><span class="no">ACTION</span><span class="p">,</span><span class="no">AT</span><span class="p">,</span><span class="nb">index</span><span class="p">[,</span><span class="nb">index...</span><span class="p">]</span><span class="o">&gt;</span>
</pre></div>
</div>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">FOR</span></code></dt><dd><p>Specify a range with, optionally, an increment used to iterate over the
range.</p>
<div class="highlight-cmake notranslate"><div class="highlight"><pre><span></span><span class="o">$&lt;</span><span class="no">LIST</span><span class="o">:</span><span class="no">TRANSFORM</span><span class="p">,</span><span class="nb">list</span><span class="p">,</span><span class="no">ACTION</span><span class="p">,</span><span class="no">FOR</span><span class="p">,</span><span class="nb">start</span><span class="p">,</span><span class="nb">stop</span><span class="p">[,</span><span class="nb">step</span><span class="p">]</span><span class="o">&gt;</span>
</pre></div>
</div>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">REGEX</span></code></dt><dd><p>Specify a regular expression.
Only items matching the regular expression will be transformed.</p>
<div class="highlight-cmake notranslate"><div class="highlight"><pre><span></span><span class="o">$&lt;</span><span class="no">LIST</span><span class="o">:</span><span class="no">TRANSFORM</span><span class="p">,</span><span class="nb">list</span><span class="p">,</span><span class="no">ACTION</span><span class="p">,</span><span class="no">REGEX</span><span class="p">,</span><span class="nb">regular_expression</span><span class="o">&gt;</span>
</pre></div>
</div>
</dd>
</dl>
</div></blockquote>
</dd></dl>

<dl class="cmake genex">
<dt class="sig sig-object cmake" id="genex:JOIN">
<span class="sig-name descname"><span class="pre">$&lt;JOIN:list,glue&gt;</span></span><a class="headerlink" href="#genex:JOIN" title="Permalink to this definition">¶</a></dt>
<dd><p>Joins the <code class="docutils literal notranslate"><span class="pre">list</span></code> with the content of the <code class="docutils literal notranslate"><span class="pre">glue</span></code> string inserted between
each item.  This is conceptually the same operation as
<a class="reference internal" href="#genex-list-join"><span class="std std-ref">$&lt;LIST:JOIN,list,glue&gt;</span></a>, but the two have
different behavior with regard to empty items.
<a class="reference internal" href="#genex-list-join"><span class="std std-ref">$&lt;LIST:JOIN,list,glue&gt;</span></a> preserves all empty items,
whereas <code class="docutils literal notranslate"><span class="pre">$&lt;JOIN,list,glue&gt;</span></code> drops all empty items from the list.</p>
</dd></dl>

<dl class="cmake genex">
<dt class="sig sig-object cmake" id="genex:REMOVE_DUPLICATES">
<span class="sig-name descname"><span class="pre">$&lt;REMOVE_DUPLICATES:list&gt;</span></span><a class="headerlink" href="#genex:REMOVE_DUPLICATES" title="Permalink to this definition">¶</a></dt>
<dd><div class="versionadded">
<p><span class="versionmodified added">New in version 3.15.</span></p>
</div>
<p>Removes duplicated items in the given <code class="docutils literal notranslate"><span class="pre">list</span></code>. The relative order of items
is preserved, and if duplicates are encountered, only the first instance is
retained.  The result is the same as
<a class="reference internal" href="#genex-list-remove-duplicates"><span class="std std-ref">$&lt;LIST:REMOVE_DUPLICATES,list&gt;</span></a>.</p>
</dd></dl>

<dl class="cmake genex">
<dt class="sig sig-object cmake" id="genex:FILTER">
<span class="sig-name descname"><span class="pre">$&lt;FILTER:list,INCLUDE|EXCLUDE,regex&gt;</span></span><a class="headerlink" href="#genex:FILTER" title="Permalink to this definition">¶</a></dt>
<dd><div class="versionadded">
<p><span class="versionmodified added">New in version 3.15.</span></p>
</div>
<p>Includes or removes items from <code class="docutils literal notranslate"><span class="pre">list</span></code> that match the regular expression
<code class="docutils literal notranslate"><span class="pre">regex</span></code>.  The result is the same as
<a class="reference internal" href="#genex-list-filter"><span class="std std-ref">$&lt;LIST:FILTER,list,INCLUDE|EXCLUDE,regex&gt;</span></a>.</p>
</dd></dl>

</section>
<section id="list-ordering">
<span id="genex-list-ordering"></span><h4><a class="toc-backref" href="#id16" role="doc-backlink">List Ordering</a><a class="headerlink" href="#list-ordering" title="Permalink to this heading">¶</a></h4>
<dl class="cmake genex">
<dt class="sig sig-object cmake">
<span class="sig-name descname"><span class="pre">$&lt;LIST:REVERSE,list&gt;</span></span></dt>
<dd><div class="versionadded">
<p><span class="versionmodified added">New in version 3.27.</span></p>
</div>
<p>The <code class="docutils literal notranslate"><span class="pre">list</span></code> with the items in reverse order.</p>
</dd></dl>

<dl class="cmake genex">
<dt class="sig sig-object cmake">
<span class="sig-name descname"><span class="pre">$&lt;LIST:SORT,list[,(COMPARE:option|CASE:option|ORDER:option)]...&gt;</span></span></dt>
<dd><div class="versionadded">
<p><span class="versionmodified added">New in version 3.27.</span></p>
</div>
<p>The <code class="docutils literal notranslate"><span class="pre">list</span></code> sorted according to the specified options.</p>
<p>Use one of the <code class="docutils literal notranslate"><span class="pre">COMPARE</span></code> options to select the comparison method
for sorting:</p>
<blockquote>
<div><dl class="simple">
<dt><code class="docutils literal notranslate"><span class="pre">STRING</span></code></dt><dd><p>Sorts a list of strings alphabetically.
This is the default behavior if the <code class="docutils literal notranslate"><span class="pre">COMPARE</span></code> option is not given.</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">FILE_BASENAME</span></code></dt><dd><p>Sorts a list of file paths by their basenames.</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">NATURAL</span></code></dt><dd><p>Sorts a list of strings using natural order (see the man page for
<code class="docutils literal notranslate"><span class="pre">strverscmp(3)</span></code>), such that contiguous digits are compared as whole
numbers.  For example, the following list <code class="docutils literal notranslate"><span class="pre">10.0</span> <span class="pre">1.1</span> <span class="pre">2.1</span> <span class="pre">8.0</span> <span class="pre">2.0</span> <span class="pre">3.1</span></code>
will be sorted as <code class="docutils literal notranslate"><span class="pre">1.1</span> <span class="pre">2.0</span> <span class="pre">2.1</span> <span class="pre">3.1</span> <span class="pre">8.0</span> <span class="pre">10.0</span></code> if the <code class="docutils literal notranslate"><span class="pre">NATURAL</span></code>
comparison is selected, whereas it will be sorted as
<code class="docutils literal notranslate"><span class="pre">1.1</span> <span class="pre">10.0</span> <span class="pre">2.0</span> <span class="pre">2.1</span> <span class="pre">3.1</span> <span class="pre">8.0</span></code> with the <code class="docutils literal notranslate"><span class="pre">STRING</span></code> comparison.</p>
</dd>
</dl>
</div></blockquote>
<p>Use one of the <code class="docutils literal notranslate"><span class="pre">CASE</span></code> options to select a case-sensitive or
case-insensitive sort mode:</p>
<blockquote>
<div><dl class="simple">
<dt><code class="docutils literal notranslate"><span class="pre">SENSITIVE</span></code></dt><dd><p>List items are sorted in a case-sensitive manner.
This is the default behavior if the <code class="docutils literal notranslate"><span class="pre">CASE</span></code> option is not given.</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">INSENSITIVE</span></code></dt><dd><p>List items are sorted in a case-insensitive manner.  The order of
items which differ only by upper/lowercase is not specified.</p>
</dd>
</dl>
</div></blockquote>
<p>To control the sort order, one of the <code class="docutils literal notranslate"><span class="pre">ORDER</span></code> options can be given:</p>
<blockquote>
<div><dl class="simple">
<dt><code class="docutils literal notranslate"><span class="pre">ASCENDING</span></code></dt><dd><p>Sorts the list in ascending order.
This is the default behavior when the <code class="docutils literal notranslate"><span class="pre">ORDER</span></code> option is not given.</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">DESCENDING</span></code></dt><dd><p>Sorts the list in descending order.</p>
</dd>
</dl>
</div></blockquote>
<p>Options can be specified in any order, but it is an error to specify the
same option multiple times.</p>
<div class="highlight-cmake notranslate"><div class="highlight"><pre><span></span><span class="o">$&lt;</span><span class="no">LIST</span><span class="o">:</span><span class="no">SORT</span><span class="p">,</span><span class="nb">list</span><span class="p">,</span><span class="no">CASE</span><span class="o">:</span><span class="no">SENSITIVE</span><span class="p">,</span><span class="no">COMPARE</span><span class="o">:</span><span class="no">STRING</span><span class="p">,</span><span class="no">ORDER</span><span class="o">:</span><span class="no">DESCENDING</span><span class="o">&gt;</span>
</pre></div>
</div>
</dd></dl>

</section>
</section>
<section id="path-expressions">
<h3><a class="toc-backref" href="#id17" role="doc-backlink">Path Expressions</a><a class="headerlink" href="#path-expressions" title="Permalink to this heading">¶</a></h3>
<p>Most of the expressions in this section are closely associated with the
<span class="target" id="index-0-command:cmake_path"></span><a class="reference internal" href="../command/cmake_path.html#command:cmake_path" title="cmake_path"><code class="xref cmake cmake-command docutils literal notranslate"><span class="pre">cmake_path()</span></code></a> command, providing the same capabilities, but in
the form of a generator expression.</p>
<p>For all generator expressions in this section, paths are expected to be in
cmake-style format. The <a class="reference internal" href="#genex-path-cmake-path"><span class="std std-ref">$&lt;PATH:CMAKE_PATH&gt;</span></a>
generator expression can be used to convert a native path to a cmake-style
one.</p>
<section id="path-comparisons">
<span id="genex-path-comparisons"></span><h4><a class="toc-backref" href="#id18" role="doc-backlink">Path Comparisons</a><a class="headerlink" href="#path-comparisons" title="Permalink to this heading">¶</a></h4>
<dl class="cmake genex">
<dt class="sig sig-object cmake" id="genex:PATH_EQUAL">
<span class="sig-name descname"><span class="pre">$&lt;PATH_EQUAL:path1,path2&gt;</span></span><a class="headerlink" href="#genex:PATH_EQUAL" title="Permalink to this definition">¶</a></dt>
<dd><div class="versionadded">
<p><span class="versionmodified added">New in version 3.24.</span></p>
</div>
<p>Compares the lexical representations of two paths. No normalization is
performed on either path. Returns <code class="docutils literal notranslate"><span class="pre">1</span></code> if the paths are equal, <code class="docutils literal notranslate"><span class="pre">0</span></code>
otherwise.</p>
<p>See <a class="reference internal" href="../command/cmake_path.html#path-compare"><span class="std std-ref">cmake_path(COMPARE)</span></a> for more details.</p>
</dd></dl>

</section>
<section id="path-queries">
<span id="genex-path-queries"></span><h4><a class="toc-backref" href="#id19" role="doc-backlink">Path Queries</a><a class="headerlink" href="#path-queries" title="Permalink to this heading">¶</a></h4>
<p>These expressions provide the generation-time capabilities equivalent to the
<a class="reference internal" href="../command/cmake_path.html#path-query"><span class="std std-ref">Query</span></a> options of the <span class="target" id="index-1-command:cmake_path"></span><a class="reference internal" href="../command/cmake_path.html#command:cmake_path" title="cmake_path"><code class="xref cmake cmake-command docutils literal notranslate"><span class="pre">cmake_path()</span></code></a> command.
All paths are expected to be in cmake-style format.</p>
<dl class="cmake genex">
<dt class="sig sig-object cmake" id="genex:PATH">
<span class="sig-name descname"><span class="pre">$&lt;PATH:HAS_*,path&gt;</span></span><a class="headerlink" href="#genex:PATH" title="Permalink to this definition">¶</a></dt>
<dd><div class="versionadded">
<p><span class="versionmodified added">New in version 3.24.</span></p>
</div>
<p>The following operations return <code class="docutils literal notranslate"><span class="pre">1</span></code> if the particular path component is
present, <code class="docutils literal notranslate"><span class="pre">0</span></code> otherwise. See <a class="reference internal" href="../command/cmake_path.html#path-structure-and-terminology"><span class="std std-ref">Path Structure And Terminology</span></a> for the
meaning of each path component.</p>
<div class="highlight-none notranslate"><div class="highlight"><pre><span></span>$&lt;PATH:HAS_ROOT_NAME,path&gt;
$&lt;PATH:HAS_ROOT_DIRECTORY,path&gt;
$&lt;PATH:HAS_ROOT_PATH,path&gt;
$&lt;PATH:HAS_FILENAME,path&gt;
$&lt;PATH:HAS_EXTENSION,path&gt;
$&lt;PATH:HAS_STEM,path&gt;
$&lt;PATH:HAS_RELATIVE_PART,path&gt;
$&lt;PATH:HAS_PARENT_PATH,path&gt;
</pre></div>
</div>
<p>Note the following special cases:</p>
<ul class="simple">
<li><p>For <code class="docutils literal notranslate"><span class="pre">HAS_ROOT_PATH</span></code>, a true result will only be returned if at least one
of <code class="docutils literal notranslate"><span class="pre">root-name</span></code> or <code class="docutils literal notranslate"><span class="pre">root-directory</span></code> is non-empty.</p></li>
<li><p>For <code class="docutils literal notranslate"><span class="pre">HAS_PARENT_PATH</span></code>, the root directory is also considered to have a
parent, which will be itself.  The result is true except if the path
consists of just a <a class="reference internal" href="../command/cmake_path.html#filename-def"><span class="std std-ref">filename</span></a>.</p></li>
</ul>
</dd></dl>

<dl class="cmake genex">
<dt class="sig sig-object cmake">
<span class="sig-name descname"><span class="pre">$&lt;PATH:IS_ABSOLUTE,path&gt;</span></span></dt>
<dd><div class="versionadded">
<p><span class="versionmodified added">New in version 3.24.</span></p>
</div>
<p>Returns <code class="docutils literal notranslate"><span class="pre">1</span></code> if the path is <a class="reference internal" href="../command/cmake_path.html#is-absolute"><span class="std std-ref">absolute</span></a>, <code class="docutils literal notranslate"><span class="pre">0</span></code> otherwise.</p>
</dd></dl>

<dl class="cmake genex">
<dt class="sig sig-object cmake">
<span class="sig-name descname"><span class="pre">$&lt;PATH:IS_RELATIVE,path&gt;</span></span></dt>
<dd><div class="versionadded">
<p><span class="versionmodified added">New in version 3.24.</span></p>
</div>
<p>This will return the opposite of <code class="docutils literal notranslate"><span class="pre">IS_ABSOLUTE</span></code>.</p>
</dd></dl>

<dl class="cmake genex">
<dt class="sig sig-object cmake">
<span class="sig-name descname"><span class="pre">$&lt;PATH:IS_PREFIX[,NORMALIZE],path,input&gt;</span></span></dt>
<dd><div class="versionadded">
<p><span class="versionmodified added">New in version 3.24.</span></p>
</div>
<p>Returns <code class="docutils literal notranslate"><span class="pre">1</span></code> if <code class="docutils literal notranslate"><span class="pre">path</span></code> is the prefix of <code class="docutils literal notranslate"><span class="pre">input</span></code>, <code class="docutils literal notranslate"><span class="pre">0</span></code> otherwise.</p>
<p>When the <code class="docutils literal notranslate"><span class="pre">NORMALIZE</span></code> option is specified, <code class="docutils literal notranslate"><span class="pre">path</span></code> and <code class="docutils literal notranslate"><span class="pre">input</span></code> are
<a class="reference internal" href="../command/cmake_path.html#normalization"><span class="std std-ref">normalized</span></a> before the check.</p>
</dd></dl>

</section>
<section id="path-decomposition">
<span id="genex-path-decomposition"></span><h4><a class="toc-backref" href="#id20" role="doc-backlink">Path Decomposition</a><a class="headerlink" href="#path-decomposition" title="Permalink to this heading">¶</a></h4>
<p>These expressions provide the generation-time capabilities equivalent to the
<a class="reference internal" href="../command/cmake_path.html#path-decomposition"><span class="std std-ref">Decomposition</span></a> options of the <span class="target" id="index-2-command:cmake_path"></span><a class="reference internal" href="../command/cmake_path.html#command:cmake_path" title="cmake_path"><code class="xref cmake cmake-command docutils literal notranslate"><span class="pre">cmake_path()</span></code></a>
command.  All paths are expected to be in cmake-style format.</p>
<dl class="cmake genex">
<dt class="sig sig-object cmake">
<span class="sig-name descname"><span class="pre">$&lt;PATH:GET_*,...&gt;</span></span></dt>
<dd><div class="versionadded">
<p><span class="versionmodified added">New in version 3.24.</span></p>
</div>
<p>The following operations retrieve a different component or group of
components from a path. See <a class="reference internal" href="../command/cmake_path.html#path-structure-and-terminology"><span class="std std-ref">Path Structure And Terminology</span></a> for the
meaning of each path component.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.27: </span>All operations now accept a list of paths as argument. When a list of paths
is specified, the operation will be applied to each path.</p>
</div>
<div class="highlight-none notranslate"><div class="highlight"><pre><span></span>$&lt;PATH:GET_ROOT_NAME,path...&gt;
$&lt;PATH:GET_ROOT_DIRECTORY,path...&gt;
$&lt;PATH:GET_ROOT_PATH,path...&gt;
$&lt;PATH:GET_FILENAME,path...&gt;
$&lt;PATH:GET_EXTENSION[,LAST_ONLY],path...&gt;
$&lt;PATH:GET_STEM[,LAST_ONLY],path...&gt;
$&lt;PATH:GET_RELATIVE_PART,path...&gt;
$&lt;PATH:GET_PARENT_PATH,path...&gt;
</pre></div>
</div>
<p>If a requested component is not present in the path, an empty string is
returned.</p>
</dd></dl>

</section>
<section id="path-transformations">
<span id="genex-path-transformations"></span><h4><a class="toc-backref" href="#id21" role="doc-backlink">Path Transformations</a><a class="headerlink" href="#path-transformations" title="Permalink to this heading">¶</a></h4>
<p>These expressions provide the generation-time capabilities equivalent to the
<a class="reference internal" href="../command/cmake_path.html#path-modification"><span class="std std-ref">Modification</span></a> and <a class="reference internal" href="../command/cmake_path.html#path-generation"><span class="std std-ref">Generation</span></a>
options of the <span class="target" id="index-3-command:cmake_path"></span><a class="reference internal" href="../command/cmake_path.html#command:cmake_path" title="cmake_path"><code class="xref cmake cmake-command docutils literal notranslate"><span class="pre">cmake_path()</span></code></a> command.  All paths are expected to be
in cmake-style format.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.27: </span>All operations now accept a list of paths as argument. When a list of paths
is specified, the operation will be applied to each path.</p>
</div>
<span class="target" id="genex-path-cmake-path"></span><dl class="cmake genex">
<dt class="sig sig-object cmake">
<span class="sig-name descname"><span class="pre">$&lt;PATH:CMAKE_PATH[,NORMALIZE],path...&gt;</span></span></dt>
<dd><div class="versionadded">
<p><span class="versionmodified added">New in version 3.24.</span></p>
</div>
<p>Returns <code class="docutils literal notranslate"><span class="pre">path</span></code>. If <code class="docutils literal notranslate"><span class="pre">path</span></code> is a native path, it is converted into a
cmake-style path with forward-slashes (<code class="docutils literal notranslate"><span class="pre">/</span></code>). On Windows, the long filename
marker is taken into account.</p>
<p>When the <code class="docutils literal notranslate"><span class="pre">NORMALIZE</span></code> option is specified, the path is <a class="reference internal" href="../command/cmake_path.html#normalization"><span class="std std-ref">normalized</span></a> after the conversion.</p>
</dd></dl>

<dl class="cmake genex">
<dt class="sig sig-object cmake">
<span class="sig-name descname"><span class="pre">$&lt;PATH:APPEND,path...,input,...&gt;</span></span></dt>
<dd><div class="versionadded">
<p><span class="versionmodified added">New in version 3.24.</span></p>
</div>
<p>Returns all the <code class="docutils literal notranslate"><span class="pre">input</span></code> arguments appended to <code class="docutils literal notranslate"><span class="pre">path</span></code> using <code class="docutils literal notranslate"><span class="pre">/</span></code> as the
<code class="docutils literal notranslate"><span class="pre">directory-separator</span></code>. Depending on the <code class="docutils literal notranslate"><span class="pre">input</span></code>, the value of <code class="docutils literal notranslate"><span class="pre">path</span></code>
may be discarded.</p>
<p>See <a class="reference internal" href="../command/cmake_path.html#append"><span class="std std-ref">cmake_path(APPEND)</span></a> for more details.</p>
</dd></dl>

<dl class="cmake genex">
<dt class="sig sig-object cmake">
<span class="sig-name descname"><span class="pre">$&lt;PATH:REMOVE_FILENAME,path...&gt;</span></span></dt>
<dd><div class="versionadded">
<p><span class="versionmodified added">New in version 3.24.</span></p>
</div>
<p>Returns <code class="docutils literal notranslate"><span class="pre">path</span></code> with filename component (as returned by
<code class="docutils literal notranslate"><span class="pre">$&lt;PATH:GET_FILENAME&gt;</span></code>) removed. After removal, any trailing
<code class="docutils literal notranslate"><span class="pre">directory-separator</span></code> is left alone, if present.</p>
<p>See <a class="reference internal" href="../command/cmake_path.html#remove-filename"><span class="std std-ref">cmake_path(REMOVE_FILENAME)</span></a> for more details.</p>
</dd></dl>

<dl class="cmake genex">
<dt class="sig sig-object cmake">
<span class="sig-name descname"><span class="pre">$&lt;PATH:REPLACE_FILENAME,path...,input&gt;</span></span></dt>
<dd><div class="versionadded">
<p><span class="versionmodified added">New in version 3.24.</span></p>
</div>
<p>Returns <code class="docutils literal notranslate"><span class="pre">path</span></code> with the filename component replaced by <code class="docutils literal notranslate"><span class="pre">input</span></code>. If
<code class="docutils literal notranslate"><span class="pre">path</span></code> has no filename component (i.e. <code class="docutils literal notranslate"><span class="pre">$&lt;PATH:HAS_FILENAME&gt;</span></code> returns
<code class="docutils literal notranslate"><span class="pre">0</span></code>), <code class="docutils literal notranslate"><span class="pre">path</span></code> is unchanged.</p>
<p>See <a class="reference internal" href="../command/cmake_path.html#replace-filename"><span class="std std-ref">cmake_path(REPLACE_FILENAME)</span></a> for more details.</p>
</dd></dl>

<dl class="cmake genex">
<dt class="sig sig-object cmake">
<span class="sig-name descname"><span class="pre">$&lt;PATH:REMOVE_EXTENSION[,LAST_ONLY],path...&gt;</span></span></dt>
<dd><div class="versionadded">
<p><span class="versionmodified added">New in version 3.24.</span></p>
</div>
<p>Returns <code class="docutils literal notranslate"><span class="pre">path</span></code> with the <a class="reference internal" href="../command/cmake_path.html#extension-def"><span class="std std-ref">extension</span></a> removed, if any.</p>
<p>See <a class="reference internal" href="../command/cmake_path.html#remove-extension"><span class="std std-ref">cmake_path(REMOVE_EXTENSION)</span></a> for more details.</p>
</dd></dl>

<dl class="cmake genex">
<dt class="sig sig-object cmake">
<span class="sig-name descname"><span class="pre">$&lt;PATH:REPLACE_EXTENSION[,LAST_ONLY],path...,input&gt;</span></span></dt>
<dd><div class="versionadded">
<p><span class="versionmodified added">New in version 3.24.</span></p>
</div>
<p>Returns <code class="docutils literal notranslate"><span class="pre">path</span></code> with the <a class="reference internal" href="../command/cmake_path.html#extension-def"><span class="std std-ref">extension</span></a> replaced by
<code class="docutils literal notranslate"><span class="pre">input</span></code>, if any.</p>
<p>See <a class="reference internal" href="../command/cmake_path.html#replace-extension"><span class="std std-ref">cmake_path(REPLACE_EXTENSION)</span></a> for more details.</p>
</dd></dl>

<dl class="cmake genex">
<dt class="sig sig-object cmake">
<span class="sig-name descname"><span class="pre">$&lt;PATH:NORMAL_PATH,path...&gt;</span></span></dt>
<dd><div class="versionadded">
<p><span class="versionmodified added">New in version 3.24.</span></p>
</div>
<p>Returns <code class="docutils literal notranslate"><span class="pre">path</span></code> normalized according to the steps described in
<a class="reference internal" href="../command/cmake_path.html#normalization"><span class="std std-ref">Normalization</span></a>.</p>
</dd></dl>

<dl class="cmake genex">
<dt class="sig sig-object cmake">
<span class="sig-name descname"><span class="pre">$&lt;PATH:RELATIVE_PATH,path...,base_directory&gt;</span></span></dt>
<dd><div class="versionadded">
<p><span class="versionmodified added">New in version 3.24.</span></p>
</div>
<p>Returns <code class="docutils literal notranslate"><span class="pre">path</span></code>, modified to make it relative to the <code class="docutils literal notranslate"><span class="pre">base_directory</span></code>
argument.</p>
<p>See <a class="reference internal" href="../command/cmake_path.html#cmake-path-relative-path"><span class="std std-ref">cmake_path(RELATIVE_PATH)</span></a> for more
details.</p>
</dd></dl>

<dl class="cmake genex">
<dt class="sig sig-object cmake">
<span class="sig-name descname"><span class="pre">$&lt;PATH:ABSOLUTE_PATH[,NORMALIZE],path...,base_directory&gt;</span></span></dt>
<dd><div class="versionadded">
<p><span class="versionmodified added">New in version 3.24.</span></p>
</div>
<p>Returns <code class="docutils literal notranslate"><span class="pre">path</span></code> as absolute. If <code class="docutils literal notranslate"><span class="pre">path</span></code> is a relative path
(<code class="docutils literal notranslate"><span class="pre">$&lt;PATH:IS_RELATIVE&gt;</span></code> returns <code class="docutils literal notranslate"><span class="pre">1</span></code>), it is evaluated relative to the
given base directory specified by <code class="docutils literal notranslate"><span class="pre">base_directory</span></code> argument.</p>
<p>When the <code class="docutils literal notranslate"><span class="pre">NORMALIZE</span></code> option is specified, the path is
<a class="reference internal" href="../command/cmake_path.html#normalization"><span class="std std-ref">normalized</span></a> after the path computation.</p>
<p>See <a class="reference internal" href="../command/cmake_path.html#absolute-path"><span class="std std-ref">cmake_path(ABSOLUTE_PATH)</span></a> for more details.</p>
</dd></dl>

</section>
<section id="shell-paths">
<h4><a class="toc-backref" href="#id22" role="doc-backlink">Shell Paths</a><a class="headerlink" href="#shell-paths" title="Permalink to this heading">¶</a></h4>
<dl class="cmake genex">
<dt class="sig sig-object cmake" id="genex:SHELL_PATH">
<span class="sig-name descname"><span class="pre">$&lt;SHELL_PATH:...&gt;</span></span><a class="headerlink" href="#genex:SHELL_PATH" title="Permalink to this definition">¶</a></dt>
<dd><div class="versionadded">
<p><span class="versionmodified added">New in version 3.4.</span></p>
</div>
<p>Content of <code class="docutils literal notranslate"><span class="pre">...</span></code> converted to shell path style. For example, slashes are
converted to backslashes in Windows shells and drive letters are converted
to posix paths in MSYS shells. The <code class="docutils literal notranslate"><span class="pre">...</span></code> must be an absolute path.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.14: </span>The <code class="docutils literal notranslate"><span class="pre">...</span></code> may be a <a class="reference internal" href="cmake-language.7.html#cmake-language-lists"><span class="std std-ref">semicolon-separated list</span></a>
of paths, in which case each path is converted individually and a result
list is generated using the shell path separator (<code class="docutils literal notranslate"><span class="pre">:</span></code> on POSIX and
<code class="docutils literal notranslate"><span class="pre">;</span></code> on Windows).  Be sure to enclose the argument containing this genex
in double quotes in CMake source code so that <code class="docutils literal notranslate"><span class="pre">;</span></code> does not split arguments.</p>
</div>
</dd></dl>

</section>
</section>
<section id="configuration-expressions">
<h3><a class="toc-backref" href="#id23" role="doc-backlink">Configuration Expressions</a><a class="headerlink" href="#configuration-expressions" title="Permalink to this heading">¶</a></h3>
<dl class="cmake genex">
<dt class="sig sig-object cmake" id="genex:CONFIG">
<span class="sig-name descname"><span class="pre">$&lt;CONFIG&gt;</span></span><a class="headerlink" href="#genex:CONFIG" title="Permalink to this definition">¶</a></dt>
<dd><p>Configuration name. Use this instead of the deprecated <span class="target" id="index-0-genex:CONFIGURATION"></span><a class="reference internal" href="#genex:CONFIGURATION" title="CONFIGURATION"><code class="xref cmake cmake-genex docutils literal notranslate"><span class="pre">CONFIGURATION</span></code></a>
generator expression.</p>
</dd></dl>

<dl class="cmake genex">
<dt class="sig sig-object cmake">
<span class="sig-name descname"><span class="pre">$&lt;CONFIG:cfgs&gt;</span></span></dt>
<dd><p><code class="docutils literal notranslate"><span class="pre">1</span></code> if config is any one of the entries in comma-separated list
<code class="docutils literal notranslate"><span class="pre">cfgs</span></code>, else <code class="docutils literal notranslate"><span class="pre">0</span></code>. This is a case-insensitive comparison. The mapping in
<span class="target" id="index-0-prop_tgt:MAP_IMPORTED_CONFIG_&lt;CONFIG&gt;"></span><a class="reference internal" href="../prop_tgt/MAP_IMPORTED_CONFIG_CONFIG.html#prop_tgt:MAP_IMPORTED_CONFIG_&lt;CONFIG&gt;" title="MAP_IMPORTED_CONFIG_&lt;CONFIG&gt;"><code class="xref cmake cmake-prop_tgt docutils literal notranslate"><span class="pre">MAP_IMPORTED_CONFIG_&lt;CONFIG&gt;</span></code></a> is also considered by this
expression when it is evaluated on a property of an <span class="target" id="index-0-prop_tgt:IMPORTED"></span><a class="reference internal" href="../prop_tgt/IMPORTED.html#prop_tgt:IMPORTED" title="IMPORTED"><code class="xref cmake cmake-prop_tgt docutils literal notranslate"><span class="pre">IMPORTED</span></code></a>
target.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.19: </span>Multiple configurations can be specified for <code class="docutils literal notranslate"><span class="pre">cfgs</span></code>.
CMake 3.18 and earlier only accepted a single configuration.</p>
</div>
</dd></dl>

<dl class="cmake genex">
<dt class="sig sig-object cmake" id="genex:OUTPUT_CONFIG">
<span class="sig-name descname"><span class="pre">$&lt;OUTPUT_CONFIG:...&gt;</span></span><a class="headerlink" href="#genex:OUTPUT_CONFIG" title="Permalink to this definition">¶</a></dt>
<dd><div class="versionadded">
<p><span class="versionmodified added">New in version 3.20.</span></p>
</div>
<p>Only valid in <span class="target" id="index-1-command:add_custom_command"></span><a class="reference internal" href="../command/add_custom_command.html#command:add_custom_command" title="add_custom_command"><code class="xref cmake cmake-command docutils literal notranslate"><span class="pre">add_custom_command()</span></code></a> and <span class="target" id="index-1-command:add_custom_target"></span><a class="reference internal" href="../command/add_custom_target.html#command:add_custom_target" title="add_custom_target"><code class="xref cmake cmake-command docutils literal notranslate"><span class="pre">add_custom_target()</span></code></a>
as the outer-most generator expression in an argument.
With the <span class="target" id="index-0-generator:Ninja Multi-Config"></span><a class="reference internal" href="../generator/Ninja%20Multi-Config.html#generator:Ninja Multi-Config" title="Ninja Multi-Config"><code class="xref cmake cmake-generator docutils literal notranslate"><span class="pre">Ninja</span> <span class="pre">Multi-Config</span></code></a> generator, generator expressions
in <code class="docutils literal notranslate"><span class="pre">...</span></code> are evaluated using the custom command's &quot;output config&quot;.
With other generators, the content of <code class="docutils literal notranslate"><span class="pre">...</span></code> is evaluated normally.</p>
</dd></dl>

<dl class="cmake genex">
<dt class="sig sig-object cmake" id="genex:COMMAND_CONFIG">
<span class="sig-name descname"><span class="pre">$&lt;COMMAND_CONFIG:...&gt;</span></span><a class="headerlink" href="#genex:COMMAND_CONFIG" title="Permalink to this definition">¶</a></dt>
<dd><div class="versionadded">
<p><span class="versionmodified added">New in version 3.20.</span></p>
</div>
<p>Only valid in <span class="target" id="index-2-command:add_custom_command"></span><a class="reference internal" href="../command/add_custom_command.html#command:add_custom_command" title="add_custom_command"><code class="xref cmake cmake-command docutils literal notranslate"><span class="pre">add_custom_command()</span></code></a> and <span class="target" id="index-2-command:add_custom_target"></span><a class="reference internal" href="../command/add_custom_target.html#command:add_custom_target" title="add_custom_target"><code class="xref cmake cmake-command docutils literal notranslate"><span class="pre">add_custom_target()</span></code></a>
as the outer-most generator expression in an argument.
With the <span class="target" id="index-1-generator:Ninja Multi-Config"></span><a class="reference internal" href="../generator/Ninja%20Multi-Config.html#generator:Ninja Multi-Config" title="Ninja Multi-Config"><code class="xref cmake cmake-generator docutils literal notranslate"><span class="pre">Ninja</span> <span class="pre">Multi-Config</span></code></a> generator, generator expressions
in <code class="docutils literal notranslate"><span class="pre">...</span></code> are evaluated using the custom command's &quot;command config&quot;.
With other generators, the content of <code class="docutils literal notranslate"><span class="pre">...</span></code> is evaluated normally.</p>
</dd></dl>

</section>
<section id="toolchain-and-language-expressions">
<h3><a class="toc-backref" href="#id24" role="doc-backlink">Toolchain And Language Expressions</a><a class="headerlink" href="#toolchain-and-language-expressions" title="Permalink to this heading">¶</a></h3>
<section id="platform">
<h4><a class="toc-backref" href="#id25" role="doc-backlink">Platform</a><a class="headerlink" href="#platform" title="Permalink to this heading">¶</a></h4>
<dl class="cmake genex">
<dt class="sig sig-object cmake" id="genex:PLATFORM_ID">
<span class="sig-name descname"><span class="pre">$&lt;PLATFORM_ID&gt;</span></span><a class="headerlink" href="#genex:PLATFORM_ID" title="Permalink to this definition">¶</a></dt>
<dd><p>The current system's CMake platform id.
See also the <span class="target" id="index-0-variable:CMAKE_SYSTEM_NAME"></span><a class="reference internal" href="../variable/CMAKE_SYSTEM_NAME.html#variable:CMAKE_SYSTEM_NAME" title="CMAKE_SYSTEM_NAME"><code class="xref cmake cmake-variable docutils literal notranslate"><span class="pre">CMAKE_SYSTEM_NAME</span></code></a> variable.</p>
</dd></dl>

<dl class="cmake genex">
<dt class="sig sig-object cmake">
<span class="sig-name descname"><span class="pre">$&lt;PLATFORM_ID:platform_ids&gt;</span></span></dt>
<dd><p><code class="docutils literal notranslate"><span class="pre">1</span></code> if CMake's platform id matches any one of the entries in
comma-separated list <code class="docutils literal notranslate"><span class="pre">platform_ids</span></code>, otherwise <code class="docutils literal notranslate"><span class="pre">0</span></code>.
See also the <span class="target" id="index-1-variable:CMAKE_SYSTEM_NAME"></span><a class="reference internal" href="../variable/CMAKE_SYSTEM_NAME.html#variable:CMAKE_SYSTEM_NAME" title="CMAKE_SYSTEM_NAME"><code class="xref cmake cmake-variable docutils literal notranslate"><span class="pre">CMAKE_SYSTEM_NAME</span></code></a> variable.</p>
</dd></dl>

</section>
<section id="compiler-version">
<h4><a class="toc-backref" href="#id26" role="doc-backlink">Compiler Version</a><a class="headerlink" href="#compiler-version" title="Permalink to this heading">¶</a></h4>
<p>See also the <span class="target" id="index-1-variable:CMAKE_&lt;LANG&gt;_COMPILER_VERSION"></span><a class="reference internal" href="../variable/CMAKE_LANG_COMPILER_VERSION.html#variable:CMAKE_&lt;LANG&gt;_COMPILER_VERSION" title="CMAKE_&lt;LANG&gt;_COMPILER_VERSION"><code class="xref cmake cmake-variable docutils literal notranslate"><span class="pre">CMAKE_&lt;LANG&gt;_COMPILER_VERSION</span></code></a> variable, which is
closely related to the expressions in this sub-section.</p>
<dl class="cmake genex">
<dt class="sig sig-object cmake" id="genex:C_COMPILER_VERSION">
<span class="sig-name descname"><span class="pre">$&lt;C_COMPILER_VERSION&gt;</span></span><a class="headerlink" href="#genex:C_COMPILER_VERSION" title="Permalink to this definition">¶</a></dt>
<dd><p>The version of the C compiler used.</p>
</dd></dl>

<dl class="cmake genex">
<dt class="sig sig-object cmake">
<span class="sig-name descname"><span class="pre">$&lt;C_COMPILER_VERSION:version&gt;</span></span></dt>
<dd><p><code class="docutils literal notranslate"><span class="pre">1</span></code> if the version of the C compiler matches <code class="docutils literal notranslate"><span class="pre">version</span></code>, otherwise <code class="docutils literal notranslate"><span class="pre">0</span></code>.</p>
</dd></dl>

<dl class="cmake genex">
<dt class="sig sig-object cmake" id="genex:CXX_COMPILER_VERSION">
<span class="sig-name descname"><span class="pre">$&lt;CXX_COMPILER_VERSION&gt;</span></span><a class="headerlink" href="#genex:CXX_COMPILER_VERSION" title="Permalink to this definition">¶</a></dt>
<dd><p>The version of the CXX compiler used.</p>
</dd></dl>

<dl class="cmake genex">
<dt class="sig sig-object cmake">
<span class="sig-name descname"><span class="pre">$&lt;CXX_COMPILER_VERSION:version&gt;</span></span></dt>
<dd><p><code class="docutils literal notranslate"><span class="pre">1</span></code> if the version of the CXX compiler matches <code class="docutils literal notranslate"><span class="pre">version</span></code>, otherwise <code class="docutils literal notranslate"><span class="pre">0</span></code>.</p>
</dd></dl>

<dl class="cmake genex">
<dt class="sig sig-object cmake" id="genex:CUDA_COMPILER_VERSION">
<span class="sig-name descname"><span class="pre">$&lt;CUDA_COMPILER_VERSION&gt;</span></span><a class="headerlink" href="#genex:CUDA_COMPILER_VERSION" title="Permalink to this definition">¶</a></dt>
<dd><div class="versionadded">
<p><span class="versionmodified added">New in version 3.15.</span></p>
</div>
<p>The version of the CUDA compiler used.</p>
</dd></dl>

<dl class="cmake genex">
<dt class="sig sig-object cmake">
<span class="sig-name descname"><span class="pre">$&lt;CUDA_COMPILER_VERSION:version&gt;</span></span></dt>
<dd><div class="versionadded">
<p><span class="versionmodified added">New in version 3.15.</span></p>
</div>
<p><code class="docutils literal notranslate"><span class="pre">1</span></code> if the version of the CXX compiler matches <code class="docutils literal notranslate"><span class="pre">version</span></code>, otherwise <code class="docutils literal notranslate"><span class="pre">0</span></code>.</p>
</dd></dl>

<dl class="cmake genex">
<dt class="sig sig-object cmake" id="genex:OBJC_COMPILER_VERSION">
<span class="sig-name descname"><span class="pre">$&lt;OBJC_COMPILER_VERSION&gt;</span></span><a class="headerlink" href="#genex:OBJC_COMPILER_VERSION" title="Permalink to this definition">¶</a></dt>
<dd><div class="versionadded">
<p><span class="versionmodified added">New in version 3.16.</span></p>
</div>
<p>The version of the OBJC compiler used.</p>
</dd></dl>

<dl class="cmake genex">
<dt class="sig sig-object cmake">
<span class="sig-name descname"><span class="pre">$&lt;OBJC_COMPILER_VERSION:version&gt;</span></span></dt>
<dd><div class="versionadded">
<p><span class="versionmodified added">New in version 3.16.</span></p>
</div>
<p><code class="docutils literal notranslate"><span class="pre">1</span></code> if the version of the OBJC compiler matches <code class="docutils literal notranslate"><span class="pre">version</span></code>, otherwise <code class="docutils literal notranslate"><span class="pre">0</span></code>.</p>
</dd></dl>

<dl class="cmake genex">
<dt class="sig sig-object cmake" id="genex:OBJCXX_COMPILER_VERSION">
<span class="sig-name descname"><span class="pre">$&lt;OBJCXX_COMPILER_VERSION&gt;</span></span><a class="headerlink" href="#genex:OBJCXX_COMPILER_VERSION" title="Permalink to this definition">¶</a></dt>
<dd><div class="versionadded">
<p><span class="versionmodified added">New in version 3.16.</span></p>
</div>
<p>The version of the OBJCXX compiler used.</p>
</dd></dl>

<dl class="cmake genex">
<dt class="sig sig-object cmake">
<span class="sig-name descname"><span class="pre">$&lt;OBJCXX_COMPILER_VERSION:version&gt;</span></span></dt>
<dd><div class="versionadded">
<p><span class="versionmodified added">New in version 3.16.</span></p>
</div>
<p><code class="docutils literal notranslate"><span class="pre">1</span></code> if the version of the OBJCXX compiler matches <code class="docutils literal notranslate"><span class="pre">version</span></code>, otherwise <code class="docutils literal notranslate"><span class="pre">0</span></code>.</p>
</dd></dl>

<dl class="cmake genex">
<dt class="sig sig-object cmake" id="genex:Fortran_COMPILER_VERSION">
<span class="sig-name descname"><span class="pre">$&lt;Fortran_COMPILER_VERSION&gt;</span></span><a class="headerlink" href="#genex:Fortran_COMPILER_VERSION" title="Permalink to this definition">¶</a></dt>
<dd><p>The version of the Fortran compiler used.</p>
</dd></dl>

<dl class="cmake genex">
<dt class="sig sig-object cmake">
<span class="sig-name descname"><span class="pre">$&lt;Fortran_COMPILER_VERSION:version&gt;</span></span></dt>
<dd><p><code class="docutils literal notranslate"><span class="pre">1</span></code> if the version of the Fortran compiler matches <code class="docutils literal notranslate"><span class="pre">version</span></code>, otherwise <code class="docutils literal notranslate"><span class="pre">0</span></code>.</p>
</dd></dl>

<dl class="cmake genex">
<dt class="sig sig-object cmake" id="genex:HIP_COMPILER_VERSION">
<span class="sig-name descname"><span class="pre">$&lt;HIP_COMPILER_VERSION&gt;</span></span><a class="headerlink" href="#genex:HIP_COMPILER_VERSION" title="Permalink to this definition">¶</a></dt>
<dd><div class="versionadded">
<p><span class="versionmodified added">New in version 3.21.</span></p>
</div>
<p>The version of the HIP compiler used.</p>
</dd></dl>

<dl class="cmake genex">
<dt class="sig sig-object cmake">
<span class="sig-name descname"><span class="pre">$&lt;HIP_COMPILER_VERSION:version&gt;</span></span></dt>
<dd><div class="versionadded">
<p><span class="versionmodified added">New in version 3.21.</span></p>
</div>
<p><code class="docutils literal notranslate"><span class="pre">1</span></code> if the version of the HIP compiler matches <code class="docutils literal notranslate"><span class="pre">version</span></code>, otherwise <code class="docutils literal notranslate"><span class="pre">0</span></code>.</p>
</dd></dl>

<dl class="cmake genex">
<dt class="sig sig-object cmake" id="genex:ISPC_COMPILER_VERSION">
<span class="sig-name descname"><span class="pre">$&lt;ISPC_COMPILER_VERSION&gt;</span></span><a class="headerlink" href="#genex:ISPC_COMPILER_VERSION" title="Permalink to this definition">¶</a></dt>
<dd><div class="versionadded">
<p><span class="versionmodified added">New in version 3.19.</span></p>
</div>
<p>The version of the ISPC compiler used.</p>
</dd></dl>

<dl class="cmake genex">
<dt class="sig sig-object cmake">
<span class="sig-name descname"><span class="pre">$&lt;ISPC_COMPILER_VERSION:version&gt;</span></span></dt>
<dd><div class="versionadded">
<p><span class="versionmodified added">New in version 3.19.</span></p>
</div>
<p><code class="docutils literal notranslate"><span class="pre">1</span></code> if the version of the ISPC compiler matches <code class="docutils literal notranslate"><span class="pre">version</span></code>, otherwise <code class="docutils literal notranslate"><span class="pre">0</span></code>.</p>
</dd></dl>

</section>
<section id="compiler-language-and-id">
<h4><a class="toc-backref" href="#id27" role="doc-backlink">Compiler Language And ID</a><a class="headerlink" href="#compiler-language-and-id" title="Permalink to this heading">¶</a></h4>
<p>See also the <span class="target" id="index-0-variable:CMAKE_&lt;LANG&gt;_COMPILER_ID"></span><a class="reference internal" href="../variable/CMAKE_LANG_COMPILER_ID.html#variable:CMAKE_&lt;LANG&gt;_COMPILER_ID" title="CMAKE_&lt;LANG&gt;_COMPILER_ID"><code class="xref cmake cmake-variable docutils literal notranslate"><span class="pre">CMAKE_&lt;LANG&gt;_COMPILER_ID</span></code></a> variable, which is closely
related to most of the expressions in this sub-section.</p>
<dl class="cmake genex">
<dt class="sig sig-object cmake" id="genex:C_COMPILER_ID">
<span class="sig-name descname"><span class="pre">$&lt;C_COMPILER_ID&gt;</span></span><a class="headerlink" href="#genex:C_COMPILER_ID" title="Permalink to this definition">¶</a></dt>
<dd><p>CMake's compiler id of the C compiler used.</p>
</dd></dl>

<dl class="cmake genex">
<dt class="sig sig-object cmake">
<span class="sig-name descname"><span class="pre">$&lt;C_COMPILER_ID:compiler_ids&gt;</span></span></dt>
<dd><p>where <code class="docutils literal notranslate"><span class="pre">compiler_ids</span></code> is a comma-separated list.
<code class="docutils literal notranslate"><span class="pre">1</span></code> if CMake's compiler id of the C compiler matches any one
of the entries in <code class="docutils literal notranslate"><span class="pre">compiler_ids</span></code>, otherwise <code class="docutils literal notranslate"><span class="pre">0</span></code>.</p>
</dd></dl>

<dl class="cmake genex">
<dt class="sig sig-object cmake" id="genex:CXX_COMPILER_ID">
<span class="sig-name descname"><span class="pre">$&lt;CXX_COMPILER_ID&gt;</span></span><a class="headerlink" href="#genex:CXX_COMPILER_ID" title="Permalink to this definition">¶</a></dt>
<dd><p>CMake's compiler id of the CXX compiler used.</p>
</dd></dl>

<dl class="cmake genex">
<dt class="sig sig-object cmake">
<span class="sig-name descname"><span class="pre">$&lt;CXX_COMPILER_ID:compiler_ids&gt;</span></span></dt>
<dd><p>where <code class="docutils literal notranslate"><span class="pre">compiler_ids</span></code> is a comma-separated list.
<code class="docutils literal notranslate"><span class="pre">1</span></code> if CMake's compiler id of the CXX compiler matches any one
of the entries in <code class="docutils literal notranslate"><span class="pre">compiler_ids</span></code>, otherwise <code class="docutils literal notranslate"><span class="pre">0</span></code>.</p>
</dd></dl>

<dl class="cmake genex">
<dt class="sig sig-object cmake" id="genex:CUDA_COMPILER_ID">
<span class="sig-name descname"><span class="pre">$&lt;CUDA_COMPILER_ID&gt;</span></span><a class="headerlink" href="#genex:CUDA_COMPILER_ID" title="Permalink to this definition">¶</a></dt>
<dd><div class="versionadded">
<p><span class="versionmodified added">New in version 3.15.</span></p>
</div>
<p>CMake's compiler id of the CUDA compiler used.</p>
</dd></dl>

<dl class="cmake genex">
<dt class="sig sig-object cmake">
<span class="sig-name descname"><span class="pre">$&lt;CUDA_COMPILER_ID:compiler_ids&gt;</span></span></dt>
<dd><div class="versionadded">
<p><span class="versionmodified added">New in version 3.15.</span></p>
</div>
<p>where <code class="docutils literal notranslate"><span class="pre">compiler_ids</span></code> is a comma-separated list.
<code class="docutils literal notranslate"><span class="pre">1</span></code> if CMake's compiler id of the CUDA compiler matches any one
of the entries in <code class="docutils literal notranslate"><span class="pre">compiler_ids</span></code>, otherwise <code class="docutils literal notranslate"><span class="pre">0</span></code>.</p>
</dd></dl>

<dl class="cmake genex">
<dt class="sig sig-object cmake" id="genex:OBJC_COMPILER_ID">
<span class="sig-name descname"><span class="pre">$&lt;OBJC_COMPILER_ID&gt;</span></span><a class="headerlink" href="#genex:OBJC_COMPILER_ID" title="Permalink to this definition">¶</a></dt>
<dd><div class="versionadded">
<p><span class="versionmodified added">New in version 3.16.</span></p>
</div>
<p>CMake's compiler id of the OBJC compiler used.</p>
</dd></dl>

<dl class="cmake genex">
<dt class="sig sig-object cmake">
<span class="sig-name descname"><span class="pre">$&lt;OBJC_COMPILER_ID:compiler_ids&gt;</span></span></dt>
<dd><div class="versionadded">
<p><span class="versionmodified added">New in version 3.16.</span></p>
</div>
<p>where <code class="docutils literal notranslate"><span class="pre">compiler_ids</span></code> is a comma-separated list.
<code class="docutils literal notranslate"><span class="pre">1</span></code> if CMake's compiler id of the Objective-C compiler matches any one
of the entries in <code class="docutils literal notranslate"><span class="pre">compiler_ids</span></code>, otherwise <code class="docutils literal notranslate"><span class="pre">0</span></code>.</p>
</dd></dl>

<dl class="cmake genex">
<dt class="sig sig-object cmake" id="genex:OBJCXX_COMPILER_ID">
<span class="sig-name descname"><span class="pre">$&lt;OBJCXX_COMPILER_ID&gt;</span></span><a class="headerlink" href="#genex:OBJCXX_COMPILER_ID" title="Permalink to this definition">¶</a></dt>
<dd><div class="versionadded">
<p><span class="versionmodified added">New in version 3.16.</span></p>
</div>
<p>CMake's compiler id of the OBJCXX compiler used.</p>
</dd></dl>

<dl class="cmake genex">
<dt class="sig sig-object cmake">
<span class="sig-name descname"><span class="pre">$&lt;OBJCXX_COMPILER_ID:compiler_ids&gt;</span></span></dt>
<dd><div class="versionadded">
<p><span class="versionmodified added">New in version 3.16.</span></p>
</div>
<p>where <code class="docutils literal notranslate"><span class="pre">compiler_ids</span></code> is a comma-separated list.
<code class="docutils literal notranslate"><span class="pre">1</span></code> if CMake's compiler id of the Objective-C++ compiler matches any one
of the entries in <code class="docutils literal notranslate"><span class="pre">compiler_ids</span></code>, otherwise <code class="docutils literal notranslate"><span class="pre">0</span></code>.</p>
</dd></dl>

<dl class="cmake genex">
<dt class="sig sig-object cmake" id="genex:Fortran_COMPILER_ID">
<span class="sig-name descname"><span class="pre">$&lt;Fortran_COMPILER_ID&gt;</span></span><a class="headerlink" href="#genex:Fortran_COMPILER_ID" title="Permalink to this definition">¶</a></dt>
<dd><p>CMake's compiler id of the Fortran compiler used.</p>
</dd></dl>

<dl class="cmake genex">
<dt class="sig sig-object cmake">
<span class="sig-name descname"><span class="pre">$&lt;Fortran_COMPILER_ID:compiler_ids&gt;</span></span></dt>
<dd><p>where <code class="docutils literal notranslate"><span class="pre">compiler_ids</span></code> is a comma-separated list.
<code class="docutils literal notranslate"><span class="pre">1</span></code> if CMake's compiler id of the Fortran compiler matches any one
of the entries in <code class="docutils literal notranslate"><span class="pre">compiler_ids</span></code>, otherwise <code class="docutils literal notranslate"><span class="pre">0</span></code>.</p>
</dd></dl>

<dl class="cmake genex">
<dt class="sig sig-object cmake" id="genex:HIP_COMPILER_ID">
<span class="sig-name descname"><span class="pre">$&lt;HIP_COMPILER_ID&gt;</span></span><a class="headerlink" href="#genex:HIP_COMPILER_ID" title="Permalink to this definition">¶</a></dt>
<dd><div class="versionadded">
<p><span class="versionmodified added">New in version 3.21.</span></p>
</div>
<p>CMake's compiler id of the HIP compiler used.</p>
</dd></dl>

<dl class="cmake genex">
<dt class="sig sig-object cmake">
<span class="sig-name descname"><span class="pre">$&lt;HIP_COMPILER_ID:compiler_ids&gt;</span></span></dt>
<dd><div class="versionadded">
<p><span class="versionmodified added">New in version 3.21.</span></p>
</div>
<p>where <code class="docutils literal notranslate"><span class="pre">compiler_ids</span></code> is a comma-separated list.
<code class="docutils literal notranslate"><span class="pre">1</span></code> if CMake's compiler id of the HIP compiler matches any one
of the entries in <code class="docutils literal notranslate"><span class="pre">compiler_ids</span></code>, otherwise <code class="docutils literal notranslate"><span class="pre">0</span></code>.</p>
</dd></dl>

<dl class="cmake genex">
<dt class="sig sig-object cmake" id="genex:ISPC_COMPILER_ID">
<span class="sig-name descname"><span class="pre">$&lt;ISPC_COMPILER_ID&gt;</span></span><a class="headerlink" href="#genex:ISPC_COMPILER_ID" title="Permalink to this definition">¶</a></dt>
<dd><div class="versionadded">
<p><span class="versionmodified added">New in version 3.19.</span></p>
</div>
<p>CMake's compiler id of the ISPC compiler used.</p>
</dd></dl>

<dl class="cmake genex">
<dt class="sig sig-object cmake">
<span class="sig-name descname"><span class="pre">$&lt;ISPC_COMPILER_ID:compiler_ids&gt;</span></span></dt>
<dd><div class="versionadded">
<p><span class="versionmodified added">New in version 3.19.</span></p>
</div>
<p>where <code class="docutils literal notranslate"><span class="pre">compiler_ids</span></code> is a comma-separated list.
<code class="docutils literal notranslate"><span class="pre">1</span></code> if CMake's compiler id of the ISPC compiler matches any one
of the entries in <code class="docutils literal notranslate"><span class="pre">compiler_ids</span></code>, otherwise <code class="docutils literal notranslate"><span class="pre">0</span></code>.</p>
</dd></dl>

<dl class="cmake genex">
<dt class="sig sig-object cmake" id="genex:COMPILE_LANGUAGE">
<span class="sig-name descname"><span class="pre">$&lt;COMPILE_LANGUAGE&gt;</span></span><a class="headerlink" href="#genex:COMPILE_LANGUAGE" title="Permalink to this definition">¶</a></dt>
<dd><div class="versionadded">
<p><span class="versionmodified added">New in version 3.3.</span></p>
</div>
<p>The compile language of source files when evaluating compile options.
See <a class="reference internal" href="#boolean-compile-language-generator-expression"><span class="std std-ref">the related boolean expression</span></a>
<code class="docutils literal notranslate"><span class="pre">$&lt;COMPILE_LANGUAGE:language&gt;</span></code>
for notes about the portability of this generator expression.</p>
</dd></dl>

<span class="target" id="boolean-compile-language-generator-expression"></span><dl class="cmake genex">
<dt class="sig sig-object cmake">
<span class="sig-name descname"><span class="pre">$&lt;COMPILE_LANGUAGE:languages&gt;</span></span></dt>
<dd><div class="versionadded">
<p><span class="versionmodified added">New in version 3.3.</span></p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.15: </span>Multiple languages can be specified for <code class="docutils literal notranslate"><span class="pre">languages</span></code>.
CMake 3.14 and earlier only accepted a single language.</p>
</div>
<p><code class="docutils literal notranslate"><span class="pre">1</span></code> when the language used for compilation unit matches any of the
comma-separated entries in <code class="docutils literal notranslate"><span class="pre">languages</span></code>, otherwise <code class="docutils literal notranslate"><span class="pre">0</span></code>. This expression
may be used to specify compile options, compile definitions, and include
directories for source files of a particular language in a target. For
example:</p>
<div class="highlight-cmake notranslate"><div class="highlight"><pre><span></span><span class="nf">add_executable(</span><span class="nb">myapp</span><span class="w"> </span><span class="nb">main.cpp</span><span class="w"> </span><span class="nb">foo.c</span><span class="w"> </span><span class="nb">bar.cpp</span><span class="w"> </span><span class="nb">zot.cu</span><span class="nf">)</span>
<span class="nf">target_compile_options(</span><span class="nb">myapp</span>
<span class="w">  </span><span class="no">PRIVATE</span><span class="w"> </span><span class="o">$&lt;$&lt;</span><span class="no">COMPILE_LANGUAGE</span><span class="o">:</span><span class="no">CXX</span><span class="o">&gt;:</span><span class="p">-</span><span class="nb">fno-exceptions</span><span class="o">&gt;</span>
<span class="nf">)</span>
<span class="nf">target_compile_definitions(</span><span class="nb">myapp</span>
<span class="w">  </span><span class="no">PRIVATE</span><span class="w"> </span><span class="o">$&lt;$&lt;</span><span class="no">COMPILE_LANGUAGE</span><span class="o">:</span><span class="no">CXX</span><span class="o">&gt;:</span><span class="no">COMPILING_CXX</span><span class="o">&gt;</span>
<span class="w">          </span><span class="o">$&lt;$&lt;</span><span class="no">COMPILE_LANGUAGE</span><span class="o">:</span><span class="no">CUDA</span><span class="o">&gt;:</span><span class="no">COMPILING_CUDA</span><span class="o">&gt;</span>
<span class="nf">)</span>
<span class="nf">target_include_directories(</span><span class="nb">myapp</span>
<span class="w">  </span><span class="no">PRIVATE</span><span class="w"> </span><span class="o">$&lt;$&lt;</span><span class="no">COMPILE_LANGUAGE</span><span class="o">:</span><span class="no">CXX</span><span class="p">,</span><span class="no">CUDA</span><span class="o">&gt;:</span><span class="na">/opt/foo/headers</span><span class="o">&gt;</span>
<span class="nf">)</span>
</pre></div>
</div>
<p>This specifies the use of the <code class="docutils literal notranslate"><span class="pre">-fno-exceptions</span></code> compile option,
<code class="docutils literal notranslate"><span class="pre">COMPILING_CXX</span></code> compile definition, and <code class="docutils literal notranslate"><span class="pre">cxx_headers</span></code> include
directory for C++ only (compiler id checks elided).  It also specifies
a <code class="docutils literal notranslate"><span class="pre">COMPILING_CUDA</span></code> compile definition for CUDA.</p>
<p>Note that with <a class="reference internal" href="cmake-generators.7.html#visual-studio-generators"><span class="std std-ref">Visual Studio Generators</span></a> and <span class="target" id="index-0-generator:Xcode"></span><a class="reference internal" href="../generator/Xcode.html#generator:Xcode" title="Xcode"><code class="xref cmake cmake-generator docutils literal notranslate"><span class="pre">Xcode</span></code></a> there
is no way to represent target-wide compile definitions or include directories
separately for <code class="docutils literal notranslate"><span class="pre">C</span></code> and <code class="docutils literal notranslate"><span class="pre">CXX</span></code> languages.
Also, with <a class="reference internal" href="cmake-generators.7.html#visual-studio-generators"><span class="std std-ref">Visual Studio Generators</span></a> there is no way to represent
target-wide flags separately for <code class="docutils literal notranslate"><span class="pre">C</span></code> and <code class="docutils literal notranslate"><span class="pre">CXX</span></code> languages.  Under these
generators, expressions for both C and C++ sources will be evaluated
using <code class="docutils literal notranslate"><span class="pre">CXX</span></code> if there are any C++ sources and otherwise using <code class="docutils literal notranslate"><span class="pre">C</span></code>.
A workaround is to create separate libraries for each source file language
instead:</p>
<div class="highlight-cmake notranslate"><div class="highlight"><pre><span></span><span class="nf">add_library(</span><span class="nb">myapp_c</span><span class="w"> </span><span class="nb">foo.c</span><span class="nf">)</span>
<span class="nf">add_library(</span><span class="nb">myapp_cxx</span><span class="w"> </span><span class="nb">bar.cpp</span><span class="nf">)</span>
<span class="nf">target_compile_options(</span><span class="nb">myapp_cxx</span><span class="w"> </span><span class="no">PUBLIC</span><span class="w"> </span><span class="p">-</span><span class="nb">fno-exceptions</span><span class="nf">)</span>
<span class="nf">add_executable(</span><span class="nb">myapp</span><span class="w"> </span><span class="nb">main.cpp</span><span class="nf">)</span>
<span class="nf">target_link_libraries(</span><span class="nb">myapp</span><span class="w"> </span><span class="nb">myapp_c</span><span class="w"> </span><span class="nb">myapp_cxx</span><span class="nf">)</span>
</pre></div>
</div>
</dd></dl>

<dl class="cmake genex">
<dt class="sig sig-object cmake" id="genex:COMPILE_LANG_AND_ID">
<span class="sig-name descname"><span class="pre">$&lt;COMPILE_LANG_AND_ID:language,compiler_ids&gt;</span></span><a class="headerlink" href="#genex:COMPILE_LANG_AND_ID" title="Permalink to this definition">¶</a></dt>
<dd><div class="versionadded">
<p><span class="versionmodified added">New in version 3.15.</span></p>
</div>
<p><code class="docutils literal notranslate"><span class="pre">1</span></code> when the language used for compilation unit matches <code class="docutils literal notranslate"><span class="pre">language</span></code> and
CMake's compiler id of the <code class="docutils literal notranslate"><span class="pre">language</span></code> compiler matches any one of the
comma-separated entries in <code class="docutils literal notranslate"><span class="pre">compiler_ids</span></code>, otherwise <code class="docutils literal notranslate"><span class="pre">0</span></code>. This expression
is a short form for the combination of <code class="docutils literal notranslate"><span class="pre">$&lt;COMPILE_LANGUAGE:language&gt;</span></code> and
<code class="docutils literal notranslate"><span class="pre">$&lt;LANG_COMPILER_ID:compiler_ids&gt;</span></code>. This expression may be used to specify
compile options, compile definitions, and include directories for source
files of a particular language and compiler combination in a target.
For example:</p>
<div class="highlight-cmake notranslate"><div class="highlight"><pre><span></span><span class="nf">add_executable(</span><span class="nb">myapp</span><span class="w"> </span><span class="nb">main.cpp</span><span class="w"> </span><span class="nb">foo.c</span><span class="w"> </span><span class="nb">bar.cpp</span><span class="w"> </span><span class="nb">zot.cu</span><span class="nf">)</span>
<span class="nf">target_compile_definitions(</span><span class="nb">myapp</span>
<span class="w">  </span><span class="no">PRIVATE</span><span class="w"> </span><span class="o">$&lt;$&lt;</span><span class="no">COMPILE_LANG_AND_ID</span><span class="o">:</span><span class="no">CXX</span><span class="p">,</span><span class="nb">AppleClang</span><span class="p">,</span><span class="nb">Clang</span><span class="o">&gt;:</span><span class="no">COMPILING_CXX_WITH_CLANG</span><span class="o">&gt;</span>
<span class="w">          </span><span class="o">$&lt;$&lt;</span><span class="no">COMPILE_LANG_AND_ID</span><span class="o">:</span><span class="no">CXX</span><span class="p">,</span><span class="nb">Intel</span><span class="o">&gt;:</span><span class="no">COMPILING_CXX_WITH_INTEL</span><span class="o">&gt;</span>
<span class="w">          </span><span class="o">$&lt;$&lt;</span><span class="no">COMPILE_LANG_AND_ID</span><span class="o">:</span><span class="no">C</span><span class="p">,</span><span class="nb">Clang</span><span class="o">&gt;:</span><span class="no">COMPILING_C_WITH_CLANG</span><span class="o">&gt;</span>
<span class="nf">)</span>
</pre></div>
</div>
<p>This specifies the use of different compile definitions based on both
the compiler id and compilation language. This example will have a
<code class="docutils literal notranslate"><span class="pre">COMPILING_CXX_WITH_CLANG</span></code> compile definition when Clang is the CXX
compiler, and <code class="docutils literal notranslate"><span class="pre">COMPILING_CXX_WITH_INTEL</span></code> when Intel is the CXX compiler.
Likewise, when the C compiler is Clang, it will only see the
<code class="docutils literal notranslate"><span class="pre">COMPILING_C_WITH_CLANG</span></code> definition.</p>
<p>Without the <code class="docutils literal notranslate"><span class="pre">COMPILE_LANG_AND_ID</span></code> generator expression, the same logic
would be expressed as:</p>
<div class="highlight-cmake notranslate"><div class="highlight"><pre><span></span><span class="nf">target_compile_definitions(</span><span class="nb">myapp</span>
<span class="w">  </span><span class="no">PRIVATE</span><span class="w"> </span><span class="o">$&lt;$&lt;</span><span class="no">AND</span><span class="o">:$&lt;</span><span class="no">COMPILE_LANGUAGE</span><span class="o">:</span><span class="no">CXX</span><span class="o">&gt;</span><span class="p">,</span><span class="o">$&lt;</span><span class="no">CXX_COMPILER_ID</span><span class="o">:</span><span class="nb">AppleClang</span><span class="p">,</span><span class="nb">Clang</span><span class="o">&gt;&gt;:</span><span class="no">COMPILING_CXX_WITH_CLANG</span><span class="o">&gt;</span>
<span class="w">          </span><span class="o">$&lt;$&lt;</span><span class="no">AND</span><span class="o">:$&lt;</span><span class="no">COMPILE_LANGUAGE</span><span class="o">:</span><span class="no">CXX</span><span class="o">&gt;</span><span class="p">,</span><span class="o">$&lt;</span><span class="no">CXX_COMPILER_ID</span><span class="o">:</span><span class="nb">Intel</span><span class="o">&gt;&gt;:</span><span class="no">COMPILING_CXX_WITH_INTEL</span><span class="o">&gt;</span>
<span class="w">          </span><span class="o">$&lt;$&lt;</span><span class="no">AND</span><span class="o">:$&lt;</span><span class="no">COMPILE_LANGUAGE</span><span class="o">:</span><span class="no">C</span><span class="o">&gt;</span><span class="p">,</span><span class="o">$&lt;</span><span class="no">C_COMPILER_ID</span><span class="o">:</span><span class="nb">Clang</span><span class="o">&gt;&gt;:</span><span class="no">COMPILING_C_WITH_CLANG</span><span class="o">&gt;</span>
<span class="nf">)</span>
</pre></div>
</div>
</dd></dl>

</section>
<section id="compile-features">
<h4><a class="toc-backref" href="#id28" role="doc-backlink">Compile Features</a><a class="headerlink" href="#compile-features" title="Permalink to this heading">¶</a></h4>
<dl class="cmake genex">
<dt class="sig sig-object cmake" id="genex:COMPILE_FEATURES">
<span class="sig-name descname"><span class="pre">$&lt;COMPILE_FEATURES:features&gt;</span></span><a class="headerlink" href="#genex:COMPILE_FEATURES" title="Permalink to this definition">¶</a></dt>
<dd><div class="versionadded">
<p><span class="versionmodified added">New in version 3.1.</span></p>
</div>
<p>where <code class="docutils literal notranslate"><span class="pre">features</span></code> is a comma-separated list.
Evaluates to <code class="docutils literal notranslate"><span class="pre">1</span></code> if all of the <code class="docutils literal notranslate"><span class="pre">features</span></code> are available for the 'head'
target, and <code class="docutils literal notranslate"><span class="pre">0</span></code> otherwise. If this expression is used while evaluating
the link implementation of a target and if any dependency transitively
increases the required <span class="target" id="index-0-prop_tgt:C_STANDARD"></span><a class="reference internal" href="../prop_tgt/C_STANDARD.html#prop_tgt:C_STANDARD" title="C_STANDARD"><code class="xref cmake cmake-prop_tgt docutils literal notranslate"><span class="pre">C_STANDARD</span></code></a> or <span class="target" id="index-0-prop_tgt:CXX_STANDARD"></span><a class="reference internal" href="../prop_tgt/CXX_STANDARD.html#prop_tgt:CXX_STANDARD" title="CXX_STANDARD"><code class="xref cmake cmake-prop_tgt docutils literal notranslate"><span class="pre">CXX_STANDARD</span></code></a>
for the 'head' target, an error is reported.  See the
<span class="target" id="index-0-manual:cmake-compile-features(7)"></span><a class="reference internal" href="cmake-compile-features.7.html#manual:cmake-compile-features(7)" title="cmake-compile-features(7)"><code class="xref cmake cmake-manual docutils literal notranslate"><span class="pre">cmake-compile-features(7)</span></code></a> manual for information on
compile features and a list of supported compilers.</p>
</dd></dl>

</section>
<section id="compile-context">
<h4><a class="toc-backref" href="#id29" role="doc-backlink">Compile Context</a><a class="headerlink" href="#compile-context" title="Permalink to this heading">¶</a></h4>
<dl class="cmake genex">
<dt class="sig sig-object cmake" id="genex:COMPILE_ONLY">
<span class="sig-name descname"><span class="pre">$&lt;COMPILE_ONLY:...&gt;</span></span><a class="headerlink" href="#genex:COMPILE_ONLY" title="Permalink to this definition">¶</a></dt>
<dd><div class="versionadded">
<p><span class="versionmodified added">New in version 3.27.</span></p>
</div>
<p>Content of <code class="docutils literal notranslate"><span class="pre">...</span></code>, when collecting <a class="reference internal" href="cmake-buildsystem.7.html#target-usage-requirements"><span class="std std-ref">Transitive Usage Requirements</span></a>,
otherwise it is the empty string.  This is intended for use in an
<span class="target" id="index-0-prop_tgt:INTERFACE_LINK_LIBRARIES"></span><a class="reference internal" href="../prop_tgt/INTERFACE_LINK_LIBRARIES.html#prop_tgt:INTERFACE_LINK_LIBRARIES" title="INTERFACE_LINK_LIBRARIES"><code class="xref cmake cmake-prop_tgt docutils literal notranslate"><span class="pre">INTERFACE_LINK_LIBRARIES</span></code></a> and <span class="target" id="index-1-prop_tgt:LINK_LIBRARIES"></span><a class="reference internal" href="../prop_tgt/LINK_LIBRARIES.html#prop_tgt:LINK_LIBRARIES" title="LINK_LIBRARIES"><code class="xref cmake cmake-prop_tgt docutils literal notranslate"><span class="pre">LINK_LIBRARIES</span></code></a> target
properties, typically populated via the <span class="target" id="index-1-command:target_link_libraries"></span><a class="reference internal" href="../command/target_link_libraries.html#command:target_link_libraries" title="target_link_libraries"><code class="xref cmake cmake-command docutils literal notranslate"><span class="pre">target_link_libraries()</span></code></a> command.
Provides compilation usage requirements without any linking requirements.</p>
<p>Use cases include header-only usage where all usages are known to not have
linking requirements (e.g., all-<code class="docutils literal notranslate"><span class="pre">inline</span></code> or C++ template libraries).</p>
<p>Note that for proper evaluation of this expression requires policy <span class="target" id="index-0-policy:CMP0099"></span><a class="reference internal" href="../policy/CMP0099.html#policy:CMP0099" title="CMP0099"><code class="xref cmake cmake-policy docutils literal notranslate"><span class="pre">CMP0099</span></code></a>
to be set to <cite>NEW</cite>.</p>
</dd></dl>

</section>
<section id="linker-language-and-id">
<h4><a class="toc-backref" href="#id30" role="doc-backlink">Linker Language And ID</a><a class="headerlink" href="#linker-language-and-id" title="Permalink to this heading">¶</a></h4>
<dl class="cmake genex">
<dt class="sig sig-object cmake" id="genex:LINK_LANGUAGE">
<span class="sig-name descname"><span class="pre">$&lt;LINK_LANGUAGE&gt;</span></span><a class="headerlink" href="#genex:LINK_LANGUAGE" title="Permalink to this definition">¶</a></dt>
<dd><div class="versionadded">
<p><span class="versionmodified added">New in version 3.18.</span></p>
</div>
<p>The link language of the target when evaluating link options.
See <a class="reference internal" href="#boolean-link-language-generator-expression"><span class="std std-ref">the related boolean expression</span></a> <code class="docutils literal notranslate"><span class="pre">$&lt;LINK_LANGUAGE:languages&gt;</span></code>
for notes about the portability of this generator expression.</p>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>This generator expression is not supported by the link libraries
properties to avoid side-effects due to the double evaluation of
these properties.</p>
</div>
</dd></dl>

<span class="target" id="boolean-link-language-generator-expression"></span><dl class="cmake genex">
<dt class="sig sig-object cmake">
<span class="sig-name descname"><span class="pre">$&lt;LINK_LANGUAGE:languages&gt;</span></span></dt>
<dd><div class="versionadded">
<p><span class="versionmodified added">New in version 3.18.</span></p>
</div>
<p><code class="docutils literal notranslate"><span class="pre">1</span></code> when the language used for link step matches any of the comma-separated
entries in <code class="docutils literal notranslate"><span class="pre">languages</span></code>, otherwise <code class="docutils literal notranslate"><span class="pre">0</span></code>.  This expression may be used to
specify link libraries, link options, link directories and link dependencies
of a particular language in a target. For example:</p>
<div class="highlight-cmake notranslate"><div class="highlight"><pre><span></span><span class="nf">add_library(</span><span class="nb">api_C</span><span class="w"> </span><span class="p">...</span><span class="nf">)</span>
<span class="nf">add_library(</span><span class="nb">api_CXX</span><span class="w"> </span><span class="p">...</span><span class="nf">)</span>
<span class="nf">add_library(</span><span class="nb">api</span><span class="w"> </span><span class="no">INTERFACE</span><span class="nf">)</span>
<span class="nf">target_link_options(</span><span class="nb">api</span><span class="w">   </span><span class="no">INTERFACE</span><span class="w"> </span><span class="o">$&lt;$&lt;</span><span class="no">LINK_LANGUAGE</span><span class="o">:</span><span class="no">C</span><span class="o">&gt;:</span><span class="p">-</span><span class="nb">opt_c</span><span class="o">&gt;</span>
<span class="w">                                    </span><span class="o">$&lt;$&lt;</span><span class="no">LINK_LANGUAGE</span><span class="o">:</span><span class="no">CXX</span><span class="o">&gt;:</span><span class="p">-</span><span class="nb">opt_cxx</span><span class="o">&gt;</span><span class="nf">)</span>
<span class="nf">target_link_libraries(</span><span class="nb">api</span><span class="w"> </span><span class="no">INTERFACE</span><span class="w"> </span><span class="o">$&lt;$&lt;</span><span class="no">LINK_LANGUAGE</span><span class="o">:</span><span class="no">C</span><span class="o">&gt;:</span><span class="nb">api_C</span><span class="o">&gt;</span>
<span class="w">                                    </span><span class="o">$&lt;$&lt;</span><span class="no">LINK_LANGUAGE</span><span class="o">:</span><span class="no">CXX</span><span class="o">&gt;:</span><span class="nb">api_CXX</span><span class="o">&gt;</span><span class="nf">)</span>

<span class="nf">add_executable(</span><span class="nb">myapp1</span><span class="w"> </span><span class="nb">main.c</span><span class="nf">)</span>
<span class="nf">target_link_options(</span><span class="nb">myapp1</span><span class="w"> </span><span class="no">PRIVATE</span><span class="w"> </span><span class="nb">api</span><span class="nf">)</span>

<span class="nf">add_executable(</span><span class="nb">myapp2</span><span class="w"> </span><span class="nb">main.cpp</span><span class="nf">)</span>
<span class="nf">target_link_options(</span><span class="nb">myapp2</span><span class="w"> </span><span class="no">PRIVATE</span><span class="w"> </span><span class="nb">api</span><span class="nf">)</span>
</pre></div>
</div>
<p>This specifies to use the <code class="docutils literal notranslate"><span class="pre">api</span></code> target for linking targets <code class="docutils literal notranslate"><span class="pre">myapp1</span></code> and
<code class="docutils literal notranslate"><span class="pre">myapp2</span></code>. In practice, <code class="docutils literal notranslate"><span class="pre">myapp1</span></code> will link with target <code class="docutils literal notranslate"><span class="pre">api_C</span></code> and
option <code class="docutils literal notranslate"><span class="pre">-opt_c</span></code> because it will use <code class="docutils literal notranslate"><span class="pre">C</span></code> as link language. And <code class="docutils literal notranslate"><span class="pre">myapp2</span></code>
will link with <code class="docutils literal notranslate"><span class="pre">api_CXX</span></code> and option <code class="docutils literal notranslate"><span class="pre">-opt_cxx</span></code> because <code class="docutils literal notranslate"><span class="pre">CXX</span></code> will be
the link language.</p>
<div class="admonition note" id="constraints-link-language-generator-expression">
<p class="admonition-title">Note</p>
<p>To determine the link language of a target, it is required to collect,
transitively, all the targets which will be linked to it. So, for link
libraries properties, a double evaluation will be done. During the first
evaluation, <code class="docutils literal notranslate"><span class="pre">$&lt;LINK_LANGUAGE:..&gt;</span></code> expressions will always return <code class="docutils literal notranslate"><span class="pre">0</span></code>.
The link language computed after this first pass will be used to do the
second pass. To avoid inconsistency, it is required that the second pass
do not change the link language. Moreover, to avoid unexpected
side-effects, it is required to specify complete entities as part of the
<code class="docutils literal notranslate"><span class="pre">$&lt;LINK_LANGUAGE:..&gt;</span></code> expression. For example:</p>
<div class="highlight-cmake notranslate"><div class="highlight"><pre><span></span><span class="nf">add_library(</span><span class="nb">lib</span><span class="w"> </span><span class="no">STATIC</span><span class="w"> </span><span class="nb">file.cxx</span><span class="nf">)</span>
<span class="nf">add_library(</span><span class="nb">libother</span><span class="w"> </span><span class="no">STATIC</span><span class="w"> </span><span class="nb">file.c</span><span class="nf">)</span>

<span class="c"># bad usage</span>
<span class="nf">add_executable(</span><span class="nb">myapp1</span><span class="w"> </span><span class="nb">main.c</span><span class="nf">)</span>
<span class="nf">target_link_libraries(</span><span class="nb">myapp1</span><span class="w"> </span><span class="no">PRIVATE</span><span class="w"> </span><span class="nb">lib</span><span class="o">$&lt;$&lt;</span><span class="no">LINK_LANGUAGE</span><span class="o">:</span><span class="no">C</span><span class="o">&gt;:</span><span class="nb">other</span><span class="o">&gt;</span><span class="nf">)</span>

<span class="c"># correct usage</span>
<span class="nf">add_executable(</span><span class="nb">myapp2</span><span class="w"> </span><span class="nb">main.c</span><span class="nf">)</span>
<span class="nf">target_link_libraries(</span><span class="nb">myapp2</span><span class="w"> </span><span class="no">PRIVATE</span><span class="w"> </span><span class="o">$&lt;$&lt;</span><span class="no">LINK_LANGUAGE</span><span class="o">:</span><span class="no">C</span><span class="o">&gt;:</span><span class="nb">libother</span><span class="o">&gt;</span><span class="nf">)</span>
</pre></div>
</div>
<p>In this example, for <code class="docutils literal notranslate"><span class="pre">myapp1</span></code>, the first pass will, unexpectedly,
determine that the link language is <code class="docutils literal notranslate"><span class="pre">CXX</span></code> because the evaluation of the
generator expression will be an empty string so <code class="docutils literal notranslate"><span class="pre">myapp1</span></code> will depends on
target <code class="docutils literal notranslate"><span class="pre">lib</span></code> which is <code class="docutils literal notranslate"><span class="pre">C++</span></code>. On the contrary, for <code class="docutils literal notranslate"><span class="pre">myapp2</span></code>, the first
evaluation will give <code class="docutils literal notranslate"><span class="pre">C</span></code> as link language, so the second pass will
correctly add target <code class="docutils literal notranslate"><span class="pre">libother</span></code> as link dependency.</p>
</div>
</dd></dl>

<dl class="cmake genex">
<dt class="sig sig-object cmake" id="genex:LINK_LANG_AND_ID">
<span class="sig-name descname"><span class="pre">$&lt;LINK_LANG_AND_ID:language,compiler_ids&gt;</span></span><a class="headerlink" href="#genex:LINK_LANG_AND_ID" title="Permalink to this definition">¶</a></dt>
<dd><div class="versionadded">
<p><span class="versionmodified added">New in version 3.18.</span></p>
</div>
<p><code class="docutils literal notranslate"><span class="pre">1</span></code> when the language used for link step matches <code class="docutils literal notranslate"><span class="pre">language</span></code> and the
CMake's compiler id of the language linker matches any one of the comma-separated
entries in <code class="docutils literal notranslate"><span class="pre">compiler_ids</span></code>, otherwise <code class="docutils literal notranslate"><span class="pre">0</span></code>. This expression is a short form
for the combination of <code class="docutils literal notranslate"><span class="pre">$&lt;LINK_LANGUAGE:language&gt;</span></code> and
<code class="docutils literal notranslate"><span class="pre">$&lt;LANG_COMPILER_ID:compiler_ids&gt;</span></code>. This expression may be used to specify
link libraries, link options, link directories and link dependencies of a
particular language and linker combination in a target. For example:</p>
<div class="highlight-cmake notranslate"><div class="highlight"><pre><span></span><span class="nf">add_library(</span><span class="nb">libC_Clang</span><span class="w"> </span><span class="p">...</span><span class="nf">)</span>
<span class="nf">add_library(</span><span class="nb">libCXX_Clang</span><span class="w"> </span><span class="p">...</span><span class="nf">)</span>
<span class="nf">add_library(</span><span class="nb">libC_Intel</span><span class="w"> </span><span class="p">...</span><span class="nf">)</span>
<span class="nf">add_library(</span><span class="nb">libCXX_Intel</span><span class="w"> </span><span class="p">...</span><span class="nf">)</span>

<span class="nf">add_executable(</span><span class="nb">myapp</span><span class="w"> </span><span class="nb">main.c</span><span class="nf">)</span>
<span class="nf">if</span> <span class="nf">(</span><span class="no">CXX_CONFIG</span><span class="nf">)</span>
<span class="w">  </span><span class="nf">target_sources(</span><span class="nb">myapp</span><span class="w"> </span><span class="no">PRIVATE</span><span class="w"> </span><span class="nb">file.cxx</span><span class="nf">)</span>
<span class="nf">endif()</span>
<span class="nf">target_link_libraries(</span><span class="nb">myapp</span>
<span class="w">  </span><span class="no">PRIVATE</span><span class="w"> </span><span class="o">$&lt;$&lt;</span><span class="no">LINK_LANG_AND_ID</span><span class="o">:</span><span class="no">CXX</span><span class="p">,</span><span class="nb">Clang</span><span class="p">,</span><span class="nb">AppleClang</span><span class="o">&gt;:</span><span class="nb">libCXX_Clang</span><span class="o">&gt;</span>
<span class="w">          </span><span class="o">$&lt;$&lt;</span><span class="no">LINK_LANG_AND_ID</span><span class="o">:</span><span class="no">C</span><span class="p">,</span><span class="nb">Clang</span><span class="p">,</span><span class="nb">AppleClang</span><span class="o">&gt;:</span><span class="nb">libC_Clang</span><span class="o">&gt;</span>
<span class="w">          </span><span class="o">$&lt;$&lt;</span><span class="no">LINK_LANG_AND_ID</span><span class="o">:</span><span class="no">CXX</span><span class="p">,</span><span class="nb">Intel</span><span class="o">&gt;:</span><span class="nb">libCXX_Intel</span><span class="o">&gt;</span>
<span class="w">          </span><span class="o">$&lt;$&lt;</span><span class="no">LINK_LANG_AND_ID</span><span class="o">:</span><span class="no">C</span><span class="p">,</span><span class="nb">Intel</span><span class="o">&gt;:</span><span class="nb">libC_Intel</span><span class="o">&gt;</span><span class="nf">)</span>
</pre></div>
</div>
<p>This specifies the use of different link libraries based on both the
compiler id and link language. This example will have target <code class="docutils literal notranslate"><span class="pre">libCXX_Clang</span></code>
as link dependency when <code class="docutils literal notranslate"><span class="pre">Clang</span></code> or <code class="docutils literal notranslate"><span class="pre">AppleClang</span></code> is the <code class="docutils literal notranslate"><span class="pre">CXX</span></code>
linker, and <code class="docutils literal notranslate"><span class="pre">libCXX_Intel</span></code> when <code class="docutils literal notranslate"><span class="pre">Intel</span></code> is the <code class="docutils literal notranslate"><span class="pre">CXX</span></code> linker.
Likewise when the <code class="docutils literal notranslate"><span class="pre">C</span></code> linker is <code class="docutils literal notranslate"><span class="pre">Clang</span></code> or <code class="docutils literal notranslate"><span class="pre">AppleClang</span></code>, target
<code class="docutils literal notranslate"><span class="pre">libC_Clang</span></code> will be added as link dependency and <code class="docutils literal notranslate"><span class="pre">libC_Intel</span></code> when
<code class="docutils literal notranslate"><span class="pre">Intel</span></code> is the <code class="docutils literal notranslate"><span class="pre">C</span></code> linker.</p>
<p>See <a class="reference internal" href="#constraints-link-language-generator-expression"><span class="std std-ref">the note related to</span></a>
<code class="docutils literal notranslate"><span class="pre">$&lt;LINK_LANGUAGE:language&gt;</span></code> for constraints about the usage of this
generator expression.</p>
</dd></dl>

</section>
<section id="link-features">
<h4><a class="toc-backref" href="#id31" role="doc-backlink">Link Features</a><a class="headerlink" href="#link-features" title="Permalink to this heading">¶</a></h4>
<dl class="cmake genex">
<dt class="sig sig-object cmake" id="genex:LINK_LIBRARY">
<span class="sig-name descname"><span class="pre">$&lt;LINK_LIBRARY:feature,library-list&gt;</span></span><a class="headerlink" href="#genex:LINK_LIBRARY" title="Permalink to this definition">¶</a></dt>
<dd><div class="versionadded">
<p><span class="versionmodified added">New in version 3.24.</span></p>
</div>
<p>Specify a set of libraries to link to a target, along with a <code class="docutils literal notranslate"><span class="pre">feature</span></code>
which provides details about <em>how</em> they should be linked.  For example:</p>
<div class="highlight-cmake notranslate"><div class="highlight"><pre><span></span><span class="nf">add_library(</span><span class="nb">lib1</span><span class="w"> </span><span class="no">STATIC</span><span class="w"> </span><span class="p">...</span><span class="nf">)</span>
<span class="nf">add_library(</span><span class="nb">lib2</span><span class="w"> </span><span class="p">...</span><span class="nf">)</span>
<span class="nf">target_link_libraries(</span><span class="nb">lib2</span><span class="w"> </span><span class="no">PRIVATE</span><span class="w"> </span><span class="s">&quot;$&lt;LINK_LIBRARY:WHOLE_ARCHIVE,lib1&gt;&quot;</span><span class="nf">)</span>
</pre></div>
</div>
<p>This specifies that <code class="docutils literal notranslate"><span class="pre">lib2</span></code> should link to <code class="docutils literal notranslate"><span class="pre">lib1</span></code> and use the
<code class="docutils literal notranslate"><span class="pre">WHOLE_ARCHIVE</span></code> feature when doing so.</p>
<p>Feature names are case-sensitive and may only contain letters, numbers and
underscores.  Feature names defined in all uppercase are reserved for CMake's
own built-in features.  The pre-defined built-in library features are:</p>
<dl>
<dt><code class="docutils literal notranslate"><span class="pre">DEFAULT</span></code></dt><dd><p>This feature corresponds to standard linking, essentially equivalent to
using no feature at all.  It is typically only used with the
<span class="target" id="index-0-prop_tgt:LINK_LIBRARY_OVERRIDE"></span><a class="reference internal" href="../prop_tgt/LINK_LIBRARY_OVERRIDE.html#prop_tgt:LINK_LIBRARY_OVERRIDE" title="LINK_LIBRARY_OVERRIDE"><code class="xref cmake cmake-prop_tgt docutils literal notranslate"><span class="pre">LINK_LIBRARY_OVERRIDE</span></code></a> and
<span class="target" id="index-0-prop_tgt:LINK_LIBRARY_OVERRIDE_&lt;LIBRARY&gt;"></span><a class="reference internal" href="../prop_tgt/LINK_LIBRARY_OVERRIDE_LIBRARY.html#prop_tgt:LINK_LIBRARY_OVERRIDE_&lt;LIBRARY&gt;" title="LINK_LIBRARY_OVERRIDE_&lt;LIBRARY&gt;"><code class="xref cmake cmake-prop_tgt docutils literal notranslate"><span class="pre">LINK_LIBRARY_OVERRIDE_&lt;LIBRARY&gt;</span></code></a> target properties.</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">WHOLE_ARCHIVE</span></code></dt><dd><p>Force inclusion of all members of a static library.  This feature is only
supported for the following platforms, with limitations as noted:</p>
<ul class="simple">
<li><p>Linux.</p></li>
<li><p>All BSD variants.</p></li>
<li><p>SunOS.</p></li>
<li><p>All Apple variants.  The library must be specified as a CMake target name,
a library file name (such as <code class="docutils literal notranslate"><span class="pre">libfoo.a</span></code>), or a library file path (such as
<code class="docutils literal notranslate"><span class="pre">/path/to/libfoo.a</span></code>).  Due to a limitation of the Apple linker, it
cannot be specified as a plain library name like <code class="docutils literal notranslate"><span class="pre">foo</span></code>, where <code class="docutils literal notranslate"><span class="pre">foo</span></code>
is not a CMake target.</p></li>
<li><p>Windows.  When using a MSVC or MSVC-like toolchain, the MSVC version must
be greater than 1900.</p></li>
<li><p>Cygwin.</p></li>
<li><p>MSYS.</p></li>
</ul>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">FRAMEWORK</span></code></dt><dd><p>This option tells the linker to search for the specified framework using
the <code class="docutils literal notranslate"><span class="pre">-framework</span></code> linker option.  It can only be used on Apple platforms,
and only with a linker that understands the option used (i.e. the linker
provided with Xcode, or one compatible with it).</p>
<p>The framework can be specified as a CMake framework target, a bare framework
name, or a file path.  If a target is given, that target must have the
<span class="target" id="index-0-prop_tgt:FRAMEWORK"></span><a class="reference internal" href="../prop_tgt/FRAMEWORK.html#prop_tgt:FRAMEWORK" title="FRAMEWORK"><code class="xref cmake cmake-prop_tgt docutils literal notranslate"><span class="pre">FRAMEWORK</span></code></a> target property set to true.  For a file path, if it
contains a directory part, that directory will be added as a framework
search path.</p>
<div class="highlight-cmake notranslate"><div class="highlight"><pre><span></span><span class="nf">add_library(</span><span class="nb">lib</span><span class="w"> </span><span class="no">SHARED</span><span class="w"> </span><span class="p">...</span><span class="nf">)</span>
<span class="nf">target_link_libraries(</span><span class="nb">lib</span><span class="w"> </span><span class="no">PRIVATE</span><span class="w"> </span><span class="s">&quot;$&lt;LINK_LIBRARY:FRAMEWORK,/path/to/my_framework&gt;&quot;</span><span class="nf">)</span>

<span class="c"># The constructed linker command line will contain:</span>
<span class="c">#   -F/path/to -framework my_framework</span>
</pre></div>
</div>
<p>File paths must conform to one of the following patterns (<code class="docutils literal notranslate"><span class="pre">*</span></code> is a
wildcard, and optional parts are shown as <code class="docutils literal notranslate"><span class="pre">[...]</span></code>):</p>
<blockquote>
<div><ul class="simple">
<li><p><code class="docutils literal notranslate"><span class="pre">[/path/to/]FwName[.framework]</span></code></p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">[/path/to/]FwName.framework/FwName[suffix]</span></code></p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">[/path/to/]FwName.framework/Versions/*/FwName[suffix]</span></code></p></li>
</ul>
</div></blockquote>
<p>Note that CMake recognizes and automatically handles framework targets,
even without using the <span class="target" id="index-0-genex:LINK_LIBRARY"></span><a class="reference internal" href="#genex:LINK_LIBRARY" title="LINK_LIBRARY"><code class="xref cmake cmake-genex docutils literal notranslate"><span class="pre">$&lt;LINK_LIBRARY:FRAMEWORK,...&gt;</span></code></a> expression.
The generator expression can still be used with a CMake target if the
project wants to be explicit about it, but it is not required to do so.
The linker command line may have some differences between using the
generator expression or not, but the final result should be the same.
On the other hand, if a file path is given, CMake will recognize some paths
automatically, but not all cases.  The project may want to use
<span class="target" id="index-1-genex:LINK_LIBRARY"></span><a class="reference internal" href="#genex:LINK_LIBRARY" title="LINK_LIBRARY"><code class="xref cmake cmake-genex docutils literal notranslate"><span class="pre">$&lt;LINK_LIBRARY:FRAMEWORK,...&gt;</span></code></a> for file paths so that the expected
behavior is clear.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.25: </span>The <span class="target" id="index-0-prop_tgt:FRAMEWORK_MULTI_CONFIG_POSTFIX_&lt;CONFIG&gt;"></span><a class="reference internal" href="../prop_tgt/FRAMEWORK_MULTI_CONFIG_POSTFIX_CONFIG.html#prop_tgt:FRAMEWORK_MULTI_CONFIG_POSTFIX_&lt;CONFIG&gt;" title="FRAMEWORK_MULTI_CONFIG_POSTFIX_&lt;CONFIG&gt;"><code class="xref cmake cmake-prop_tgt docutils literal notranslate"><span class="pre">FRAMEWORK_MULTI_CONFIG_POSTFIX_&lt;CONFIG&gt;</span></code></a> target property as
well as the <code class="docutils literal notranslate"><span class="pre">suffix</span></code> of the framework library name are now supported by
the <code class="docutils literal notranslate"><span class="pre">FRAMEWORK</span></code> features.</p>
</div>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">NEEDED_FRAMEWORK</span></code></dt><dd><p>This is similar to the <code class="docutils literal notranslate"><span class="pre">FRAMEWORK</span></code> feature, except it forces the linker
to link with the framework even if no symbols are used from it.  It uses
the <code class="docutils literal notranslate"><span class="pre">-needed_framework</span></code> option and has the same linker constraints as
<code class="docutils literal notranslate"><span class="pre">FRAMEWORK</span></code>.</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">REEXPORT_FRAMEWORK</span></code></dt><dd><p>This is similar to the <code class="docutils literal notranslate"><span class="pre">FRAMEWORK</span></code> feature, except it tells the linker
that the framework should be available to clients linking to the library
being created.  It uses the <code class="docutils literal notranslate"><span class="pre">-reexport_framework</span></code> option and has the
same linker constraints as <code class="docutils literal notranslate"><span class="pre">FRAMEWORK</span></code>.</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">WEAK_FRAMEWORK</span></code></dt><dd><p>This is similar to the <code class="docutils literal notranslate"><span class="pre">FRAMEWORK</span></code> feature, except it forces the linker
to mark the framework and all references to it as weak imports.  It uses
the <code class="docutils literal notranslate"><span class="pre">-weak_framework</span></code> option and has the same linker constraints as
<code class="docutils literal notranslate"><span class="pre">FRAMEWORK</span></code>.</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">NEEDED_LIBRARY</span></code></dt><dd><p>This is similar to the <code class="docutils literal notranslate"><span class="pre">NEEDED_FRAMEWORK</span></code> feature, except it is for use
with non-framework targets or libraries (Apple platforms only).
It uses the <code class="docutils literal notranslate"><span class="pre">-needed_library</span></code> or <code class="docutils literal notranslate"><span class="pre">-needed-l</span></code> option as appropriate,
and has the same linker constraints as <code class="docutils literal notranslate"><span class="pre">NEEDED_FRAMEWORK</span></code>.</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">REEXPORT_LIBRARY</span></code></dt><dd><p>This is similar to the <code class="docutils literal notranslate"><span class="pre">REEXPORT_FRAMEWORK</span></code> feature,  except it is for use
with non-framework targets or libraries (Apple platforms only).
It uses the <code class="docutils literal notranslate"><span class="pre">-reexport_library</span></code> or <code class="docutils literal notranslate"><span class="pre">-reexport-l</span></code> option as appropriate,
and has the same linker constraints as <code class="docutils literal notranslate"><span class="pre">REEXPORT_FRAMEWORK</span></code>.</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">WEAK_LIBRARY</span></code></dt><dd><p>This is similar to the <code class="docutils literal notranslate"><span class="pre">WEAK_FRAMEWORK</span></code> feature, except it is for use
with non-framework targets or libraries (Apple platforms only).
It uses the <code class="docutils literal notranslate"><span class="pre">-weak_library</span></code> or <code class="docutils literal notranslate"><span class="pre">-weak-l</span></code> option as appropriate,
and has the same linker constraints as <code class="docutils literal notranslate"><span class="pre">WEAK_FRAMEWORK</span></code>.</p>
</dd>
</dl>
<p>Built-in and custom library features are defined in terms of the following
variables:</p>
<ul class="simple">
<li><p><span class="target" id="index-0-variable:CMAKE_&lt;LANG&gt;_LINK_LIBRARY_USING_&lt;FEATURE&gt;_SUPPORTED"></span><a class="reference internal" href="../variable/CMAKE_LANG_LINK_LIBRARY_USING_FEATURE_SUPPORTED.html#variable:CMAKE_&lt;LANG&gt;_LINK_LIBRARY_USING_&lt;FEATURE&gt;_SUPPORTED" title="CMAKE_&lt;LANG&gt;_LINK_LIBRARY_USING_&lt;FEATURE&gt;_SUPPORTED"><code class="xref cmake cmake-variable docutils literal notranslate"><span class="pre">CMAKE_&lt;LANG&gt;_LINK_LIBRARY_USING_&lt;FEATURE&gt;_SUPPORTED</span></code></a></p></li>
<li><p><span class="target" id="index-0-variable:CMAKE_&lt;LANG&gt;_LINK_LIBRARY_USING_&lt;FEATURE&gt;"></span><a class="reference internal" href="../variable/CMAKE_LANG_LINK_LIBRARY_USING_FEATURE.html#variable:CMAKE_&lt;LANG&gt;_LINK_LIBRARY_USING_&lt;FEATURE&gt;" title="CMAKE_&lt;LANG&gt;_LINK_LIBRARY_USING_&lt;FEATURE&gt;"><code class="xref cmake cmake-variable docutils literal notranslate"><span class="pre">CMAKE_&lt;LANG&gt;_LINK_LIBRARY_USING_&lt;FEATURE&gt;</span></code></a></p></li>
<li><p><span class="target" id="index-0-variable:CMAKE_LINK_LIBRARY_USING_&lt;FEATURE&gt;_SUPPORTED"></span><a class="reference internal" href="../variable/CMAKE_LINK_LIBRARY_USING_FEATURE_SUPPORTED.html#variable:CMAKE_LINK_LIBRARY_USING_&lt;FEATURE&gt;_SUPPORTED" title="CMAKE_LINK_LIBRARY_USING_&lt;FEATURE&gt;_SUPPORTED"><code class="xref cmake cmake-variable docutils literal notranslate"><span class="pre">CMAKE_LINK_LIBRARY_USING_&lt;FEATURE&gt;_SUPPORTED</span></code></a></p></li>
<li><p><span class="target" id="index-0-variable:CMAKE_LINK_LIBRARY_USING_&lt;FEATURE&gt;"></span><a class="reference internal" href="../variable/CMAKE_LINK_LIBRARY_USING_FEATURE.html#variable:CMAKE_LINK_LIBRARY_USING_&lt;FEATURE&gt;" title="CMAKE_LINK_LIBRARY_USING_&lt;FEATURE&gt;"><code class="xref cmake cmake-variable docutils literal notranslate"><span class="pre">CMAKE_LINK_LIBRARY_USING_&lt;FEATURE&gt;</span></code></a></p></li>
</ul>
<p>The value used for each of these variables is the value as set at the end of
the directory scope in which the target was created.  The usage is as follows:</p>
<ol class="arabic simple">
<li><p>If the language-specific
<span class="target" id="index-1-variable:CMAKE_&lt;LANG&gt;_LINK_LIBRARY_USING_&lt;FEATURE&gt;_SUPPORTED"></span><a class="reference internal" href="../variable/CMAKE_LANG_LINK_LIBRARY_USING_FEATURE_SUPPORTED.html#variable:CMAKE_&lt;LANG&gt;_LINK_LIBRARY_USING_&lt;FEATURE&gt;_SUPPORTED" title="CMAKE_&lt;LANG&gt;_LINK_LIBRARY_USING_&lt;FEATURE&gt;_SUPPORTED"><code class="xref cmake cmake-variable docutils literal notranslate"><span class="pre">CMAKE_&lt;LANG&gt;_LINK_LIBRARY_USING_&lt;FEATURE&gt;_SUPPORTED</span></code></a> variable
is true, the <code class="docutils literal notranslate"><span class="pre">feature</span></code> must be defined by the corresponding
<span class="target" id="index-1-variable:CMAKE_&lt;LANG&gt;_LINK_LIBRARY_USING_&lt;FEATURE&gt;"></span><a class="reference internal" href="../variable/CMAKE_LANG_LINK_LIBRARY_USING_FEATURE.html#variable:CMAKE_&lt;LANG&gt;_LINK_LIBRARY_USING_&lt;FEATURE&gt;" title="CMAKE_&lt;LANG&gt;_LINK_LIBRARY_USING_&lt;FEATURE&gt;"><code class="xref cmake cmake-variable docutils literal notranslate"><span class="pre">CMAKE_&lt;LANG&gt;_LINK_LIBRARY_USING_&lt;FEATURE&gt;</span></code></a> variable.</p></li>
<li><p>If no language-specific <code class="docutils literal notranslate"><span class="pre">feature</span></code> is supported, then the
<span class="target" id="index-1-variable:CMAKE_LINK_LIBRARY_USING_&lt;FEATURE&gt;_SUPPORTED"></span><a class="reference internal" href="../variable/CMAKE_LINK_LIBRARY_USING_FEATURE_SUPPORTED.html#variable:CMAKE_LINK_LIBRARY_USING_&lt;FEATURE&gt;_SUPPORTED" title="CMAKE_LINK_LIBRARY_USING_&lt;FEATURE&gt;_SUPPORTED"><code class="xref cmake cmake-variable docutils literal notranslate"><span class="pre">CMAKE_LINK_LIBRARY_USING_&lt;FEATURE&gt;_SUPPORTED</span></code></a> variable must be
true and the <code class="docutils literal notranslate"><span class="pre">feature</span></code> must be defined by the corresponding
<span class="target" id="index-1-variable:CMAKE_LINK_LIBRARY_USING_&lt;FEATURE&gt;"></span><a class="reference internal" href="../variable/CMAKE_LINK_LIBRARY_USING_FEATURE.html#variable:CMAKE_LINK_LIBRARY_USING_&lt;FEATURE&gt;" title="CMAKE_LINK_LIBRARY_USING_&lt;FEATURE&gt;"><code class="xref cmake cmake-variable docutils literal notranslate"><span class="pre">CMAKE_LINK_LIBRARY_USING_&lt;FEATURE&gt;</span></code></a> variable.</p></li>
</ol>
<p>The following limitations should be noted:</p>
<ul>
<li><p>The <code class="docutils literal notranslate"><span class="pre">library-list</span></code> can specify CMake targets or libraries.
Any CMake target of type <a class="reference internal" href="cmake-buildsystem.7.html#object-libraries"><span class="std std-ref">OBJECT</span></a>
or <a class="reference internal" href="cmake-buildsystem.7.html#interface-libraries"><span class="std std-ref">INTERFACE</span></a> will ignore the feature aspect
of the expression and instead be linked in the standard way.</p></li>
<li><p>The <code class="docutils literal notranslate"><span class="pre">$&lt;LINK_LIBRARY:...&gt;</span></code> generator expression can only be used to
specify link libraries.  In practice, this means it can appear in the
<span class="target" id="index-2-prop_tgt:LINK_LIBRARIES"></span><a class="reference internal" href="../prop_tgt/LINK_LIBRARIES.html#prop_tgt:LINK_LIBRARIES" title="LINK_LIBRARIES"><code class="xref cmake cmake-prop_tgt docutils literal notranslate"><span class="pre">LINK_LIBRARIES</span></code></a>, <span class="target" id="index-1-prop_tgt:INTERFACE_LINK_LIBRARIES"></span><a class="reference internal" href="../prop_tgt/INTERFACE_LINK_LIBRARIES.html#prop_tgt:INTERFACE_LINK_LIBRARIES" title="INTERFACE_LINK_LIBRARIES"><code class="xref cmake cmake-prop_tgt docutils literal notranslate"><span class="pre">INTERFACE_LINK_LIBRARIES</span></code></a>, and
<span class="target" id="index-0-prop_tgt:INTERFACE_LINK_LIBRARIES_DIRECT"></span><a class="reference internal" href="../prop_tgt/INTERFACE_LINK_LIBRARIES_DIRECT.html#prop_tgt:INTERFACE_LINK_LIBRARIES_DIRECT" title="INTERFACE_LINK_LIBRARIES_DIRECT"><code class="xref cmake cmake-prop_tgt docutils literal notranslate"><span class="pre">INTERFACE_LINK_LIBRARIES_DIRECT</span></code></a>  target properties, and be
specified in <span class="target" id="index-2-command:target_link_libraries"></span><a class="reference internal" href="../command/target_link_libraries.html#command:target_link_libraries" title="target_link_libraries"><code class="xref cmake cmake-command docutils literal notranslate"><span class="pre">target_link_libraries()</span></code></a> and <span class="target" id="index-0-command:link_libraries"></span><a class="reference internal" href="../command/link_libraries.html#command:link_libraries" title="link_libraries"><code class="xref cmake cmake-command docutils literal notranslate"><span class="pre">link_libraries()</span></code></a>
commands.</p></li>
<li><p>If a <code class="docutils literal notranslate"><span class="pre">$&lt;LINK_LIBRARY:...&gt;</span></code> generator expression appears in the
<span class="target" id="index-2-prop_tgt:INTERFACE_LINK_LIBRARIES"></span><a class="reference internal" href="../prop_tgt/INTERFACE_LINK_LIBRARIES.html#prop_tgt:INTERFACE_LINK_LIBRARIES" title="INTERFACE_LINK_LIBRARIES"><code class="xref cmake cmake-prop_tgt docutils literal notranslate"><span class="pre">INTERFACE_LINK_LIBRARIES</span></code></a> property of a target, it will be
included in the imported target generated by a <span class="target" id="index-0-command:install"></span><a class="reference internal" href="../command/install.html#export" title="install(export)"><code class="xref cmake cmake-command docutils literal notranslate"><span class="pre">install(EXPORT)</span></code></a>
command.  It is the responsibility of the environment consuming this
import to define the link feature used by this expression.</p></li>
<li><p>Each target or library involved in the link step must have at most only
one kind of library feature.  The absence of a feature is also incompatible
with all other features.  For example:</p>
<div class="highlight-cmake notranslate"><div class="highlight"><pre><span></span><span class="nf">add_library(</span><span class="nb">lib1</span><span class="w"> </span><span class="p">...</span><span class="nf">)</span>
<span class="nf">add_library(</span><span class="nb">lib2</span><span class="w"> </span><span class="p">...</span><span class="nf">)</span>
<span class="nf">add_library(</span><span class="nb">lib3</span><span class="w"> </span><span class="p">...</span><span class="nf">)</span>

<span class="c"># lib1 will be associated with feature1</span>
<span class="nf">target_link_libraries(</span><span class="nb">lib2</span><span class="w"> </span><span class="no">PUBLIC</span><span class="w"> </span><span class="s">&quot;$&lt;LINK_LIBRARY:feature1,lib1&gt;&quot;</span><span class="nf">)</span>

<span class="c"># lib1 is being linked with no feature here. This conflicts with the</span>
<span class="c"># use of feature1 in the line above and would result in an error.</span>
<span class="nf">target_link_libraries(</span><span class="nb">lib3</span><span class="w"> </span><span class="no">PRIVATE</span><span class="w"> </span><span class="nb">lib1</span><span class="w"> </span><span class="nb">lib2</span><span class="nf">)</span>
</pre></div>
</div>
<p>Where it isn't possible to use the same feature throughout a build for a
given target or library, the <span class="target" id="index-1-prop_tgt:LINK_LIBRARY_OVERRIDE"></span><a class="reference internal" href="../prop_tgt/LINK_LIBRARY_OVERRIDE.html#prop_tgt:LINK_LIBRARY_OVERRIDE" title="LINK_LIBRARY_OVERRIDE"><code class="xref cmake cmake-prop_tgt docutils literal notranslate"><span class="pre">LINK_LIBRARY_OVERRIDE</span></code></a> and
<span class="target" id="index-1-prop_tgt:LINK_LIBRARY_OVERRIDE_&lt;LIBRARY&gt;"></span><a class="reference internal" href="../prop_tgt/LINK_LIBRARY_OVERRIDE_LIBRARY.html#prop_tgt:LINK_LIBRARY_OVERRIDE_&lt;LIBRARY&gt;" title="LINK_LIBRARY_OVERRIDE_&lt;LIBRARY&gt;"><code class="xref cmake cmake-prop_tgt docutils literal notranslate"><span class="pre">LINK_LIBRARY_OVERRIDE_&lt;LIBRARY&gt;</span></code></a> target properties can be
used to resolve such incompatibilities.</p>
</li>
<li><p>The <code class="docutils literal notranslate"><span class="pre">$&lt;LINK_LIBRARY:...&gt;</span></code> generator expression does not guarantee
that the list of specified targets and libraries will be kept grouped
together.  To manage constructs like <code class="docutils literal notranslate"><span class="pre">--start-group</span></code> and <code class="docutils literal notranslate"><span class="pre">--end-group</span></code>,
as supported by the GNU <code class="docutils literal notranslate"><span class="pre">ld</span></code> linker, use the <span class="target" id="index-0-genex:LINK_GROUP"></span><a class="reference internal" href="#genex:LINK_GROUP" title="LINK_GROUP"><code class="xref cmake cmake-genex docutils literal notranslate"><span class="pre">LINK_GROUP</span></code></a>
generator expression instead.</p></li>
</ul>
</dd></dl>

<dl class="cmake genex">
<dt class="sig sig-object cmake" id="genex:LINK_GROUP">
<span class="sig-name descname"><span class="pre">$&lt;LINK_GROUP:feature,library-list&gt;</span></span><a class="headerlink" href="#genex:LINK_GROUP" title="Permalink to this definition">¶</a></dt>
<dd><div class="versionadded">
<p><span class="versionmodified added">New in version 3.24.</span></p>
</div>
<p>Specify a group of libraries to link to a target, along with a <code class="docutils literal notranslate"><span class="pre">feature</span></code>
which defines how that group should be linked.  For example:</p>
<div class="highlight-cmake notranslate"><div class="highlight"><pre><span></span><span class="nf">add_library(</span><span class="nb">lib1</span><span class="w"> </span><span class="no">STATIC</span><span class="w"> </span><span class="p">...</span><span class="nf">)</span>
<span class="nf">add_library(</span><span class="nb">lib2</span><span class="w"> </span><span class="p">...</span><span class="nf">)</span>
<span class="nf">target_link_libraries(</span><span class="nb">lib2</span><span class="w"> </span><span class="no">PRIVATE</span><span class="w"> </span><span class="s">&quot;$&lt;LINK_GROUP:RESCAN,lib1,external&gt;&quot;</span><span class="nf">)</span>
</pre></div>
</div>
<p>This specifies that <code class="docutils literal notranslate"><span class="pre">lib2</span></code> should link to <code class="docutils literal notranslate"><span class="pre">lib1</span></code> and <code class="docutils literal notranslate"><span class="pre">external</span></code>, and
that both of those two libraries should be included on the linker command
line according to the definition of the <code class="docutils literal notranslate"><span class="pre">RESCAN</span></code> feature.</p>
<p>Feature names are case-sensitive and may only contain letters, numbers and
underscores.  Feature names defined in all uppercase are reserved for CMake's
own built-in features.  Currently, there is only one pre-defined built-in
group feature:</p>
<dl>
<dt><code class="docutils literal notranslate"><span class="pre">RESCAN</span></code></dt><dd><p>Some linkers are single-pass only.  For such linkers, circular references
between libraries typically result in unresolved symbols.  This feature
instructs the linker to search the specified static libraries repeatedly
until no new undefined references are created.</p>
<p>Normally, a static library is searched only once in the order that it is
specified on the command line.  If a symbol in that library is needed to
resolve an undefined symbol referred to by an object in a library that
appears later on the command line, the linker would not be able to resolve
that reference.  By grouping the static libraries with the <code class="docutils literal notranslate"><span class="pre">RESCAN</span></code>
feature, they will all be searched repeatedly until all possible references
are resolved.  This will use linker options like <code class="docutils literal notranslate"><span class="pre">--start-group</span></code> and
<code class="docutils literal notranslate"><span class="pre">--end-group</span></code>, or on SunOS, <code class="docutils literal notranslate"><span class="pre">-z</span> <span class="pre">rescan-start</span></code> and <code class="docutils literal notranslate"><span class="pre">-z</span> <span class="pre">rescan-end</span></code>.</p>
<p>Using this feature has a significant performance cost. It is best to use it
only when there are unavoidable circular references between two or more
static libraries.</p>
<p>This feature is available when using toolchains that target Linux, BSD, and
SunOS.  It can also be used when targeting Windows platforms if the GNU
toolchain is used.</p>
</dd>
</dl>
<p>Built-in and custom group features are defined in terms of the following
variables:</p>
<ul class="simple">
<li><p><span class="target" id="index-0-variable:CMAKE_&lt;LANG&gt;_LINK_GROUP_USING_&lt;FEATURE&gt;_SUPPORTED"></span><a class="reference internal" href="../variable/CMAKE_LANG_LINK_GROUP_USING_FEATURE_SUPPORTED.html#variable:CMAKE_&lt;LANG&gt;_LINK_GROUP_USING_&lt;FEATURE&gt;_SUPPORTED" title="CMAKE_&lt;LANG&gt;_LINK_GROUP_USING_&lt;FEATURE&gt;_SUPPORTED"><code class="xref cmake cmake-variable docutils literal notranslate"><span class="pre">CMAKE_&lt;LANG&gt;_LINK_GROUP_USING_&lt;FEATURE&gt;_SUPPORTED</span></code></a></p></li>
<li><p><span class="target" id="index-0-variable:CMAKE_&lt;LANG&gt;_LINK_GROUP_USING_&lt;FEATURE&gt;"></span><a class="reference internal" href="../variable/CMAKE_LANG_LINK_GROUP_USING_FEATURE.html#variable:CMAKE_&lt;LANG&gt;_LINK_GROUP_USING_&lt;FEATURE&gt;" title="CMAKE_&lt;LANG&gt;_LINK_GROUP_USING_&lt;FEATURE&gt;"><code class="xref cmake cmake-variable docutils literal notranslate"><span class="pre">CMAKE_&lt;LANG&gt;_LINK_GROUP_USING_&lt;FEATURE&gt;</span></code></a></p></li>
<li><p><span class="target" id="index-0-variable:CMAKE_LINK_GROUP_USING_&lt;FEATURE&gt;_SUPPORTED"></span><a class="reference internal" href="../variable/CMAKE_LINK_GROUP_USING_FEATURE_SUPPORTED.html#variable:CMAKE_LINK_GROUP_USING_&lt;FEATURE&gt;_SUPPORTED" title="CMAKE_LINK_GROUP_USING_&lt;FEATURE&gt;_SUPPORTED"><code class="xref cmake cmake-variable docutils literal notranslate"><span class="pre">CMAKE_LINK_GROUP_USING_&lt;FEATURE&gt;_SUPPORTED</span></code></a></p></li>
<li><p><span class="target" id="index-0-variable:CMAKE_LINK_GROUP_USING_&lt;FEATURE&gt;"></span><a class="reference internal" href="../variable/CMAKE_LINK_GROUP_USING_FEATURE.html#variable:CMAKE_LINK_GROUP_USING_&lt;FEATURE&gt;" title="CMAKE_LINK_GROUP_USING_&lt;FEATURE&gt;"><code class="xref cmake cmake-variable docutils literal notranslate"><span class="pre">CMAKE_LINK_GROUP_USING_&lt;FEATURE&gt;</span></code></a></p></li>
</ul>
<p>The value used for each of these variables is the value as set at the end of
the directory scope in which the target was created.  The usage is as follows:</p>
<ol class="arabic simple">
<li><p>If the language-specific
<span class="target" id="index-1-variable:CMAKE_&lt;LANG&gt;_LINK_GROUP_USING_&lt;FEATURE&gt;_SUPPORTED"></span><a class="reference internal" href="../variable/CMAKE_LANG_LINK_GROUP_USING_FEATURE_SUPPORTED.html#variable:CMAKE_&lt;LANG&gt;_LINK_GROUP_USING_&lt;FEATURE&gt;_SUPPORTED" title="CMAKE_&lt;LANG&gt;_LINK_GROUP_USING_&lt;FEATURE&gt;_SUPPORTED"><code class="xref cmake cmake-variable docutils literal notranslate"><span class="pre">CMAKE_&lt;LANG&gt;_LINK_GROUP_USING_&lt;FEATURE&gt;_SUPPORTED</span></code></a> variable
is true, the <code class="docutils literal notranslate"><span class="pre">feature</span></code> must be defined by the corresponding
<span class="target" id="index-1-variable:CMAKE_&lt;LANG&gt;_LINK_GROUP_USING_&lt;FEATURE&gt;"></span><a class="reference internal" href="../variable/CMAKE_LANG_LINK_GROUP_USING_FEATURE.html#variable:CMAKE_&lt;LANG&gt;_LINK_GROUP_USING_&lt;FEATURE&gt;" title="CMAKE_&lt;LANG&gt;_LINK_GROUP_USING_&lt;FEATURE&gt;"><code class="xref cmake cmake-variable docutils literal notranslate"><span class="pre">CMAKE_&lt;LANG&gt;_LINK_GROUP_USING_&lt;FEATURE&gt;</span></code></a> variable.</p></li>
<li><p>If no language-specific <code class="docutils literal notranslate"><span class="pre">feature</span></code> is supported, then the
<span class="target" id="index-1-variable:CMAKE_LINK_GROUP_USING_&lt;FEATURE&gt;_SUPPORTED"></span><a class="reference internal" href="../variable/CMAKE_LINK_GROUP_USING_FEATURE_SUPPORTED.html#variable:CMAKE_LINK_GROUP_USING_&lt;FEATURE&gt;_SUPPORTED" title="CMAKE_LINK_GROUP_USING_&lt;FEATURE&gt;_SUPPORTED"><code class="xref cmake cmake-variable docutils literal notranslate"><span class="pre">CMAKE_LINK_GROUP_USING_&lt;FEATURE&gt;_SUPPORTED</span></code></a> variable must be
true and the <code class="docutils literal notranslate"><span class="pre">feature</span></code> must be defined by the corresponding
<span class="target" id="index-1-variable:CMAKE_LINK_GROUP_USING_&lt;FEATURE&gt;"></span><a class="reference internal" href="../variable/CMAKE_LINK_GROUP_USING_FEATURE.html#variable:CMAKE_LINK_GROUP_USING_&lt;FEATURE&gt;" title="CMAKE_LINK_GROUP_USING_&lt;FEATURE&gt;"><code class="xref cmake cmake-variable docutils literal notranslate"><span class="pre">CMAKE_LINK_GROUP_USING_&lt;FEATURE&gt;</span></code></a> variable.</p></li>
</ol>
<p>The <code class="docutils literal notranslate"><span class="pre">LINK_GROUP</span></code> generator expression is compatible with the
<span class="target" id="index-2-genex:LINK_LIBRARY"></span><a class="reference internal" href="#genex:LINK_LIBRARY" title="LINK_LIBRARY"><code class="xref cmake cmake-genex docutils literal notranslate"><span class="pre">LINK_LIBRARY</span></code></a> generator expression. The libraries involved in a
group can be specified using the <span class="target" id="index-3-genex:LINK_LIBRARY"></span><a class="reference internal" href="#genex:LINK_LIBRARY" title="LINK_LIBRARY"><code class="xref cmake cmake-genex docutils literal notranslate"><span class="pre">LINK_LIBRARY</span></code></a> generator expression.</p>
<p>Each target or external library involved in the link step is allowed to be
part of multiple groups, but only if all the groups involved specify the
same <code class="docutils literal notranslate"><span class="pre">feature</span></code>.  Such groups will not be merged on the linker command line,
the individual groups will still be preserved.  Mixing different group
features for the same target or library is forbidden.</p>
<div class="highlight-cmake notranslate"><div class="highlight"><pre><span></span><span class="nf">add_library(</span><span class="nb">lib1</span><span class="w"> </span><span class="p">...</span><span class="nf">)</span>
<span class="nf">add_library(</span><span class="nb">lib2</span><span class="w"> </span><span class="p">...</span><span class="nf">)</span>
<span class="nf">add_library(</span><span class="nb">lib3</span><span class="w"> </span><span class="p">...</span><span class="nf">)</span>
<span class="nf">add_library(</span><span class="nb">lib4</span><span class="w"> </span><span class="p">...</span><span class="nf">)</span>
<span class="nf">add_library(</span><span class="nb">lib5</span><span class="w"> </span><span class="p">...</span><span class="nf">)</span>

<span class="nf">target_link_libraries(</span><span class="nb">lib3</span><span class="w"> </span><span class="no">PUBLIC</span><span class="w">  </span><span class="s">&quot;$&lt;LINK_GROUP:feature1,lib1,lib2&gt;&quot;</span><span class="nf">)</span>
<span class="nf">target_link_libraries(</span><span class="nb">lib4</span><span class="w"> </span><span class="no">PRIVATE</span><span class="w"> </span><span class="s">&quot;$&lt;LINK_GROUP:feature1,lib1,lib3&gt;&quot;</span><span class="nf">)</span>
<span class="c"># lib4 will be linked with the groups {lib1,lib2} and {lib1,lib3}.</span>
<span class="c"># Both groups specify the same feature, so this is fine.</span>

<span class="nf">target_link_libraries(</span><span class="nb">lib5</span><span class="w"> </span><span class="no">PRIVATE</span><span class="w"> </span><span class="s">&quot;$&lt;LINK_GROUP:feature2,lib1,lib3&gt;&quot;</span><span class="nf">)</span>
<span class="c"># An error will be raised here because both lib1 and lib3 are part of two</span>
<span class="c"># groups with different features.</span>
</pre></div>
</div>
<p>When a target or an external library is involved in the link step as part of
a group and also as not part of any group, any occurrence of the non-group
link item will be replaced by the groups it belongs to.</p>
<div class="highlight-cmake notranslate"><div class="highlight"><pre><span></span><span class="nf">add_library(</span><span class="nb">lib1</span><span class="w"> </span><span class="p">...</span><span class="nf">)</span>
<span class="nf">add_library(</span><span class="nb">lib2</span><span class="w"> </span><span class="p">...</span><span class="nf">)</span>
<span class="nf">add_library(</span><span class="nb">lib3</span><span class="w"> </span><span class="p">...</span><span class="nf">)</span>
<span class="nf">add_library(</span><span class="nb">lib4</span><span class="w"> </span><span class="p">...</span><span class="nf">)</span>

<span class="nf">target_link_libraries(</span><span class="nb">lib3</span><span class="w"> </span><span class="no">PUBLIC</span><span class="w"> </span><span class="nb">lib1</span><span class="nf">)</span>

<span class="nf">target_link_libraries(</span><span class="nb">lib4</span><span class="w"> </span><span class="no">PRIVATE</span><span class="w"> </span><span class="nb">lib3</span><span class="w"> </span><span class="s">&quot;$&lt;LINK_GROUP:feature1,lib1,lib2&gt;&quot;</span><span class="nf">)</span>
<span class="c"># lib4 will only be linked with lib3 and the group {lib1,lib2}</span>
</pre></div>
</div>
<p>Because <code class="docutils literal notranslate"><span class="pre">lib1</span></code> is part of the group defined for <code class="docutils literal notranslate"><span class="pre">lib4</span></code>, that group then
gets applied back to the use of <code class="docutils literal notranslate"><span class="pre">lib1</span></code> for <code class="docutils literal notranslate"><span class="pre">lib3</span></code>.  The end result will
be as though the linking relationship for <code class="docutils literal notranslate"><span class="pre">lib3</span></code> had been specified as:</p>
<div class="highlight-cmake notranslate"><div class="highlight"><pre><span></span><span class="nf">target_link_libraries(</span><span class="nb">lib3</span><span class="w"> </span><span class="no">PUBLIC</span><span class="w"> </span><span class="s">&quot;$&lt;LINK_GROUP:feature1,lib1,lib2&gt;&quot;</span><span class="nf">)</span>
</pre></div>
</div>
<p>Be aware that the precedence of the group over the non-group link item can
result in circular dependencies between groups.  If this occurs, a fatal
error is raised because circular dependencies are not allowed for groups.</p>
<div class="highlight-cmake notranslate"><div class="highlight"><pre><span></span><span class="nf">add_library(</span><span class="nb">lib1A</span><span class="w"> </span><span class="p">...</span><span class="nf">)</span>
<span class="nf">add_library(</span><span class="nb">lib1B</span><span class="w"> </span><span class="p">...</span><span class="nf">)</span>
<span class="nf">add_library(</span><span class="nb">lib2A</span><span class="w"> </span><span class="p">...</span><span class="nf">)</span>
<span class="nf">add_library(</span><span class="nb">lib2B</span><span class="w"> </span><span class="p">...</span><span class="nf">)</span>
<span class="nf">add_library(</span><span class="nb">lib3</span><span class="w"> </span><span class="p">...</span><span class="nf">)</span>

<span class="c"># Non-group linking relationships, these are non-circular so far</span>
<span class="nf">target_link_libraries(</span><span class="nb">lib1A</span><span class="w"> </span><span class="no">PUBLIC</span><span class="w"> </span><span class="nb">lib2A</span><span class="nf">)</span>
<span class="nf">target_link_libraries(</span><span class="nb">lib2B</span><span class="w"> </span><span class="no">PUBLIC</span><span class="w"> </span><span class="nb">lib1B</span><span class="nf">)</span>

<span class="c"># The addition of these groups creates circular dependencies</span>
<span class="nf">target_link_libraries(</span><span class="nb">lib3</span><span class="w"> </span><span class="no">PRIVATE</span>
<span class="w">  </span><span class="s">&quot;$&lt;LINK_GROUP:feat,lib1A,lib1B&gt;&quot;</span>
<span class="w">  </span><span class="s">&quot;$&lt;LINK_GROUP:feat,lib2A,lib2B&gt;&quot;</span>
<span class="nf">)</span>
</pre></div>
</div>
<p>Because of the groups defined for <code class="docutils literal notranslate"><span class="pre">lib3</span></code>, the linking relationships for
<code class="docutils literal notranslate"><span class="pre">lib1A</span></code> and <code class="docutils literal notranslate"><span class="pre">lib2B</span></code> effectively get expanded to the equivalent of:</p>
<div class="highlight-cmake notranslate"><div class="highlight"><pre><span></span><span class="nf">target_link_libraries(</span><span class="nb">lib1A</span><span class="w"> </span><span class="no">PUBLIC</span><span class="w"> </span><span class="s">&quot;$&lt;LINK_GROUP:feat,lib2A,lib2B&gt;&quot;</span><span class="nf">)</span>
<span class="nf">target_link_libraries(</span><span class="nb">lib2B</span><span class="w"> </span><span class="no">PUBLIC</span><span class="w"> </span><span class="s">&quot;$&lt;LINK_GROUP:feat,lib1A,lib1B&gt;&quot;</span><span class="nf">)</span>
</pre></div>
</div>
<p>This creates a circular dependency between groups:
<code class="docutils literal notranslate"><span class="pre">lib1A</span> <span class="pre">--&gt;</span> <span class="pre">lib2B</span> <span class="pre">--&gt;</span> <span class="pre">lib1A</span></code>.</p>
<p>The following limitations should also be noted:</p>
<ul class="simple">
<li><p>The <code class="docutils literal notranslate"><span class="pre">library-list</span></code> can specify CMake targets or libraries.
Any CMake target of type <a class="reference internal" href="cmake-buildsystem.7.html#object-libraries"><span class="std std-ref">OBJECT</span></a>
or <a class="reference internal" href="cmake-buildsystem.7.html#interface-libraries"><span class="std std-ref">INTERFACE</span></a> will ignore the feature aspect
of the expression and instead be linked in the standard way.</p></li>
<li><p>The <code class="docutils literal notranslate"><span class="pre">$&lt;LINK_GROUP:...&gt;</span></code> generator expression can only be used to
specify link libraries.  In practice, this means it can appear in the
<span class="target" id="index-3-prop_tgt:LINK_LIBRARIES"></span><a class="reference internal" href="../prop_tgt/LINK_LIBRARIES.html#prop_tgt:LINK_LIBRARIES" title="LINK_LIBRARIES"><code class="xref cmake cmake-prop_tgt docutils literal notranslate"><span class="pre">LINK_LIBRARIES</span></code></a>, <span class="target" id="index-3-prop_tgt:INTERFACE_LINK_LIBRARIES"></span><a class="reference internal" href="../prop_tgt/INTERFACE_LINK_LIBRARIES.html#prop_tgt:INTERFACE_LINK_LIBRARIES" title="INTERFACE_LINK_LIBRARIES"><code class="xref cmake cmake-prop_tgt docutils literal notranslate"><span class="pre">INTERFACE_LINK_LIBRARIES</span></code></a>,and
<span class="target" id="index-1-prop_tgt:INTERFACE_LINK_LIBRARIES_DIRECT"></span><a class="reference internal" href="../prop_tgt/INTERFACE_LINK_LIBRARIES_DIRECT.html#prop_tgt:INTERFACE_LINK_LIBRARIES_DIRECT" title="INTERFACE_LINK_LIBRARIES_DIRECT"><code class="xref cmake cmake-prop_tgt docutils literal notranslate"><span class="pre">INTERFACE_LINK_LIBRARIES_DIRECT</span></code></a> target properties, and be
specified in <span class="target" id="index-3-command:target_link_libraries"></span><a class="reference internal" href="../command/target_link_libraries.html#command:target_link_libraries" title="target_link_libraries"><code class="xref cmake cmake-command docutils literal notranslate"><span class="pre">target_link_libraries()</span></code></a> and <span class="target" id="index-1-command:link_libraries"></span><a class="reference internal" href="../command/link_libraries.html#command:link_libraries" title="link_libraries"><code class="xref cmake cmake-command docutils literal notranslate"><span class="pre">link_libraries()</span></code></a>
commands.</p></li>
<li><p>If a <code class="docutils literal notranslate"><span class="pre">$&lt;LINK_GROUP:...&gt;</span></code> generator expression appears in the
<span class="target" id="index-4-prop_tgt:INTERFACE_LINK_LIBRARIES"></span><a class="reference internal" href="../prop_tgt/INTERFACE_LINK_LIBRARIES.html#prop_tgt:INTERFACE_LINK_LIBRARIES" title="INTERFACE_LINK_LIBRARIES"><code class="xref cmake cmake-prop_tgt docutils literal notranslate"><span class="pre">INTERFACE_LINK_LIBRARIES</span></code></a> property of a target, it will be
included in the imported target generated by a <span class="target" id="index-1-command:install"></span><a class="reference internal" href="../command/install.html#export" title="install(export)"><code class="xref cmake cmake-command docutils literal notranslate"><span class="pre">install(EXPORT)</span></code></a>
command.  It is the responsibility of the environment consuming this
import to define the link feature used by this expression.</p></li>
</ul>
</dd></dl>

</section>
<section id="link-context">
<h4><a class="toc-backref" href="#id32" role="doc-backlink">Link Context</a><a class="headerlink" href="#link-context" title="Permalink to this heading">¶</a></h4>
<dl class="cmake genex">
<dt class="sig sig-object cmake" id="genex:LINK_ONLY">
<span class="sig-name descname"><span class="pre">$&lt;LINK_ONLY:...&gt;</span></span><a class="headerlink" href="#genex:LINK_ONLY" title="Permalink to this definition">¶</a></dt>
<dd><div class="versionadded">
<p><span class="versionmodified added">New in version 3.1.</span></p>
</div>
<p>Content of <code class="docutils literal notranslate"><span class="pre">...</span></code>, except while collecting <a class="reference internal" href="cmake-buildsystem.7.html#target-usage-requirements"><span class="std std-ref">Transitive Usage Requirements</span></a>,
in which case it is the empty string.  This is intended for use in an
<span class="target" id="index-5-prop_tgt:INTERFACE_LINK_LIBRARIES"></span><a class="reference internal" href="../prop_tgt/INTERFACE_LINK_LIBRARIES.html#prop_tgt:INTERFACE_LINK_LIBRARIES" title="INTERFACE_LINK_LIBRARIES"><code class="xref cmake cmake-prop_tgt docutils literal notranslate"><span class="pre">INTERFACE_LINK_LIBRARIES</span></code></a> target property, typically populated
via the <span class="target" id="index-4-command:target_link_libraries"></span><a class="reference internal" href="../command/target_link_libraries.html#command:target_link_libraries" title="target_link_libraries"><code class="xref cmake cmake-command docutils literal notranslate"><span class="pre">target_link_libraries()</span></code></a> command, to specify private link
dependencies without other usage requirements such as include directories or
compile options.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.24: </span><code class="docutils literal notranslate"><span class="pre">LINK_ONLY</span></code> may also be used in a <span class="target" id="index-4-prop_tgt:LINK_LIBRARIES"></span><a class="reference internal" href="../prop_tgt/LINK_LIBRARIES.html#prop_tgt:LINK_LIBRARIES" title="LINK_LIBRARIES"><code class="xref cmake cmake-prop_tgt docutils literal notranslate"><span class="pre">LINK_LIBRARIES</span></code></a> target
property.  See policy <span class="target" id="index-0-policy:CMP0131"></span><a class="reference internal" href="../policy/CMP0131.html#policy:CMP0131" title="CMP0131"><code class="xref cmake cmake-policy docutils literal notranslate"><span class="pre">CMP0131</span></code></a>.</p>
</div>
</dd></dl>

<dl class="cmake genex">
<dt class="sig sig-object cmake" id="genex:DEVICE_LINK">
<span class="sig-name descname"><span class="pre">$&lt;DEVICE_LINK:list&gt;</span></span><a class="headerlink" href="#genex:DEVICE_LINK" title="Permalink to this definition">¶</a></dt>
<dd><div class="versionadded">
<p><span class="versionmodified added">New in version 3.18.</span></p>
</div>
<p>Returns the list if it is the device link step, an empty list otherwise.
The device link step is controlled by <span class="target" id="index-0-prop_tgt:CUDA_SEPARABLE_COMPILATION"></span><a class="reference internal" href="../prop_tgt/CUDA_SEPARABLE_COMPILATION.html#prop_tgt:CUDA_SEPARABLE_COMPILATION" title="CUDA_SEPARABLE_COMPILATION"><code class="xref cmake cmake-prop_tgt docutils literal notranslate"><span class="pre">CUDA_SEPARABLE_COMPILATION</span></code></a>
and <span class="target" id="index-0-prop_tgt:CUDA_RESOLVE_DEVICE_SYMBOLS"></span><a class="reference internal" href="../prop_tgt/CUDA_RESOLVE_DEVICE_SYMBOLS.html#prop_tgt:CUDA_RESOLVE_DEVICE_SYMBOLS" title="CUDA_RESOLVE_DEVICE_SYMBOLS"><code class="xref cmake cmake-prop_tgt docutils literal notranslate"><span class="pre">CUDA_RESOLVE_DEVICE_SYMBOLS</span></code></a> properties and
policy <span class="target" id="index-0-policy:CMP0105"></span><a class="reference internal" href="../policy/CMP0105.html#policy:CMP0105" title="CMP0105"><code class="xref cmake cmake-policy docutils literal notranslate"><span class="pre">CMP0105</span></code></a>. This expression can only be used to specify link
options.</p>
</dd></dl>

<dl class="cmake genex">
<dt class="sig sig-object cmake" id="genex:HOST_LINK">
<span class="sig-name descname"><span class="pre">$&lt;HOST_LINK:list&gt;</span></span><a class="headerlink" href="#genex:HOST_LINK" title="Permalink to this definition">¶</a></dt>
<dd><div class="versionadded">
<p><span class="versionmodified added">New in version 3.18.</span></p>
</div>
<p>Returns the list if it is the normal link step, an empty list otherwise.
This expression is mainly useful when a device link step is also involved
(see <span class="target" id="index-0-genex:DEVICE_LINK"></span><a class="reference internal" href="#genex:DEVICE_LINK" title="DEVICE_LINK"><code class="xref cmake cmake-genex docutils literal notranslate"><span class="pre">$&lt;DEVICE_LINK:list&gt;</span></code></a> generator expression). This expression can
only be used to specify link options.</p>
</dd></dl>

</section>
</section>
<section id="target-dependent-expressions">
<span id="target-dependent-queries"></span><h3><a class="toc-backref" href="#id33" role="doc-backlink">Target-Dependent Expressions</a><a class="headerlink" href="#target-dependent-expressions" title="Permalink to this heading">¶</a></h3>
<p>These queries refer to a target <code class="docutils literal notranslate"><span class="pre">tgt</span></code>. Unless otherwise stated, this can
be any runtime artifact, namely:</p>
<ul class="simple">
<li><p>An executable target created by <span class="target" id="index-0-command:add_executable"></span><a class="reference internal" href="../command/add_executable.html#command:add_executable" title="add_executable"><code class="xref cmake cmake-command docutils literal notranslate"><span class="pre">add_executable()</span></code></a>.</p></li>
<li><p>A shared library target (<code class="docutils literal notranslate"><span class="pre">.so</span></code>, <code class="docutils literal notranslate"><span class="pre">.dll</span></code> but not their <code class="docutils literal notranslate"><span class="pre">.lib</span></code> import
library) created by <span class="target" id="index-0-command:add_library"></span><a class="reference internal" href="../command/add_library.html#command:add_library" title="add_library"><code class="xref cmake cmake-command docutils literal notranslate"><span class="pre">add_library()</span></code></a>.</p></li>
<li><p>A static library target created by <span class="target" id="index-1-command:add_library"></span><a class="reference internal" href="../command/add_library.html#command:add_library" title="add_library"><code class="xref cmake cmake-command docutils literal notranslate"><span class="pre">add_library()</span></code></a>.</p></li>
</ul>
<p>In the following, the phrase &quot;the <code class="docutils literal notranslate"><span class="pre">tgt</span></code> filename&quot; means the name of the
<code class="docutils literal notranslate"><span class="pre">tgt</span></code> binary file. This has to be distinguished from the phrase
&quot;the target name&quot;, which is just the string <code class="docutils literal notranslate"><span class="pre">tgt</span></code>.</p>
<dl class="cmake genex">
<dt class="sig sig-object cmake" id="genex:TARGET_EXISTS">
<span class="sig-name descname"><span class="pre">$&lt;TARGET_EXISTS:tgt&gt;</span></span><a class="headerlink" href="#genex:TARGET_EXISTS" title="Permalink to this definition">¶</a></dt>
<dd><div class="versionadded">
<p><span class="versionmodified added">New in version 3.12.</span></p>
</div>
<p><code class="docutils literal notranslate"><span class="pre">1</span></code> if <code class="docutils literal notranslate"><span class="pre">tgt</span></code> exists as a CMake target, else <code class="docutils literal notranslate"><span class="pre">0</span></code>.</p>
</dd></dl>

<dl class="cmake genex">
<dt class="sig sig-object cmake" id="genex:TARGET_NAME_IF_EXISTS">
<span class="sig-name descname"><span class="pre">$&lt;TARGET_NAME_IF_EXISTS:tgt&gt;</span></span><a class="headerlink" href="#genex:TARGET_NAME_IF_EXISTS" title="Permalink to this definition">¶</a></dt>
<dd><div class="versionadded">
<p><span class="versionmodified added">New in version 3.12.</span></p>
</div>
<p>The target name <code class="docutils literal notranslate"><span class="pre">tgt</span></code> if the target exists, an empty string otherwise.</p>
<p>Note that <code class="docutils literal notranslate"><span class="pre">tgt</span></code> is not added as a dependency of the target this
expression is evaluated on.</p>
</dd></dl>

<dl class="cmake genex">
<dt class="sig sig-object cmake" id="genex:TARGET_NAME">
<span class="sig-name descname"><span class="pre">$&lt;TARGET_NAME:...&gt;</span></span><a class="headerlink" href="#genex:TARGET_NAME" title="Permalink to this definition">¶</a></dt>
<dd><p>Marks <code class="docutils literal notranslate"><span class="pre">...</span></code> as being the name of a target.  This is required if exporting
targets to multiple dependent export sets.  The <code class="docutils literal notranslate"><span class="pre">...</span></code> must be a literal
name of a target, it may not contain generator expressions.</p>
</dd></dl>

<dl class="cmake genex">
<dt class="sig sig-object cmake" id="genex:TARGET_PROPERTY">
<span class="sig-name descname"><span class="pre">$&lt;TARGET_PROPERTY:tgt,prop&gt;</span></span><a class="headerlink" href="#genex:TARGET_PROPERTY" title="Permalink to this definition">¶</a></dt>
<dd><p>Value of the property <code class="docutils literal notranslate"><span class="pre">prop</span></code> on the target <code class="docutils literal notranslate"><span class="pre">tgt</span></code>.</p>
<p>Note that <code class="docutils literal notranslate"><span class="pre">tgt</span></code> is not added as a dependency of the target this
expression is evaluated on.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.26: </span>When encountered during evaluation of <a class="reference internal" href="cmake-buildsystem.7.html#target-usage-requirements"><span class="std std-ref">Transitive Usage Requirements</span></a>,
typically in an <code class="docutils literal notranslate"><span class="pre">INTERFACE_*</span></code> target property, lookup of the <code class="docutils literal notranslate"><span class="pre">tgt</span></code>
name occurs in the directory of the target specifying the requirement,
rather than the directory of the consuming target for which the
expression is being evaluated.</p>
</div>
</dd></dl>

<dl class="cmake genex">
<dt class="sig sig-object cmake" id="genex:TARGET_PROPERTY:prop">
<span class="sig-name descname"><span class="pre">$&lt;TARGET_PROPERTY:prop&gt;</span></span><a class="headerlink" href="#genex:TARGET_PROPERTY:prop" title="Permalink to this definition">¶</a></dt>
<dd><p>Value of the property <code class="docutils literal notranslate"><span class="pre">prop</span></code> on the target for which the expression
is being evaluated. Note that for generator expressions in
<a class="reference internal" href="cmake-buildsystem.7.html#target-usage-requirements"><span class="std std-ref">Transitive Usage Requirements</span></a> this is the consuming target rather
than the target specifying the requirement.</p>
</dd></dl>

<dl class="cmake genex">
<dt class="sig sig-object cmake" id="genex:TARGET_OBJECTS">
<span class="sig-name descname"><span class="pre">$&lt;TARGET_OBJECTS:tgt&gt;</span></span><a class="headerlink" href="#genex:TARGET_OBJECTS" title="Permalink to this definition">¶</a></dt>
<dd><div class="versionadded">
<p><span class="versionmodified added">New in version 3.1.</span></p>
</div>
<p>List of objects resulting from building <code class="docutils literal notranslate"><span class="pre">tgt</span></code>.  This would typically be
used on <a class="reference internal" href="cmake-buildsystem.7.html#object-libraries"><span class="std std-ref">object library</span></a> targets.</p>
</dd></dl>

<dl class="cmake genex">
<dt class="sig sig-object cmake" id="genex:TARGET_POLICY">
<span class="sig-name descname"><span class="pre">$&lt;TARGET_POLICY:policy&gt;</span></span><a class="headerlink" href="#genex:TARGET_POLICY" title="Permalink to this definition">¶</a></dt>
<dd><p><code class="docutils literal notranslate"><span class="pre">1</span></code> if the <code class="docutils literal notranslate"><span class="pre">policy</span></code> was <code class="docutils literal notranslate"><span class="pre">NEW</span></code> when the 'head' target was created,
else <code class="docutils literal notranslate"><span class="pre">0</span></code>.  If the <code class="docutils literal notranslate"><span class="pre">policy</span></code> was not set, the warning message for the policy
will be emitted. This generator expression only works for a subset of
policies.</p>
</dd></dl>

<dl class="cmake genex">
<dt class="sig sig-object cmake" id="genex:TARGET_FILE">
<span class="sig-name descname"><span class="pre">$&lt;TARGET_FILE:tgt&gt;</span></span><a class="headerlink" href="#genex:TARGET_FILE" title="Permalink to this definition">¶</a></dt>
<dd><p>Full path to the <code class="docutils literal notranslate"><span class="pre">tgt</span></code> binary file.</p>
<p>Note that <code class="docutils literal notranslate"><span class="pre">tgt</span></code> is not added as a dependency of the target this
expression is evaluated on, unless the expression is being used in
<span class="target" id="index-3-command:add_custom_command"></span><a class="reference internal" href="../command/add_custom_command.html#command:add_custom_command" title="add_custom_command"><code class="xref cmake cmake-command docutils literal notranslate"><span class="pre">add_custom_command()</span></code></a> or <span class="target" id="index-3-command:add_custom_target"></span><a class="reference internal" href="../command/add_custom_target.html#command:add_custom_target" title="add_custom_target"><code class="xref cmake cmake-command docutils literal notranslate"><span class="pre">add_custom_target()</span></code></a>.</p>
</dd></dl>

<dl class="cmake genex">
<dt class="sig sig-object cmake" id="genex:TARGET_FILE_BASE_NAME">
<span class="sig-name descname"><span class="pre">$&lt;TARGET_FILE_BASE_NAME:tgt&gt;</span></span><a class="headerlink" href="#genex:TARGET_FILE_BASE_NAME" title="Permalink to this definition">¶</a></dt>
<dd><div class="versionadded">
<p><span class="versionmodified added">New in version 3.15.</span></p>
</div>
<p>Base name of <code class="docutils literal notranslate"><span class="pre">tgt</span></code>, i.e. <code class="docutils literal notranslate"><span class="pre">$&lt;TARGET_FILE_NAME:tgt&gt;</span></code> without prefix and
suffix.
For example, if the <code class="docutils literal notranslate"><span class="pre">tgt</span></code> filename is <code class="docutils literal notranslate"><span class="pre">libbase.so</span></code>, the base name is <code class="docutils literal notranslate"><span class="pre">base</span></code>.</p>
<p>See also the <span class="target" id="index-0-prop_tgt:OUTPUT_NAME"></span><a class="reference internal" href="../prop_tgt/OUTPUT_NAME.html#prop_tgt:OUTPUT_NAME" title="OUTPUT_NAME"><code class="xref cmake cmake-prop_tgt docutils literal notranslate"><span class="pre">OUTPUT_NAME</span></code></a>, <span class="target" id="index-0-prop_tgt:ARCHIVE_OUTPUT_NAME"></span><a class="reference internal" href="../prop_tgt/ARCHIVE_OUTPUT_NAME.html#prop_tgt:ARCHIVE_OUTPUT_NAME" title="ARCHIVE_OUTPUT_NAME"><code class="xref cmake cmake-prop_tgt docutils literal notranslate"><span class="pre">ARCHIVE_OUTPUT_NAME</span></code></a>,
<span class="target" id="index-0-prop_tgt:LIBRARY_OUTPUT_NAME"></span><a class="reference internal" href="../prop_tgt/LIBRARY_OUTPUT_NAME.html#prop_tgt:LIBRARY_OUTPUT_NAME" title="LIBRARY_OUTPUT_NAME"><code class="xref cmake cmake-prop_tgt docutils literal notranslate"><span class="pre">LIBRARY_OUTPUT_NAME</span></code></a> and <span class="target" id="index-0-prop_tgt:RUNTIME_OUTPUT_NAME"></span><a class="reference internal" href="../prop_tgt/RUNTIME_OUTPUT_NAME.html#prop_tgt:RUNTIME_OUTPUT_NAME" title="RUNTIME_OUTPUT_NAME"><code class="xref cmake cmake-prop_tgt docutils literal notranslate"><span class="pre">RUNTIME_OUTPUT_NAME</span></code></a>
target properties and their configuration specific variants
<span class="target" id="index-0-prop_tgt:OUTPUT_NAME_&lt;CONFIG&gt;"></span><a class="reference internal" href="../prop_tgt/OUTPUT_NAME_CONFIG.html#prop_tgt:OUTPUT_NAME_&lt;CONFIG&gt;" title="OUTPUT_NAME_&lt;CONFIG&gt;"><code class="xref cmake cmake-prop_tgt docutils literal notranslate"><span class="pre">OUTPUT_NAME_&lt;CONFIG&gt;</span></code></a>, <span class="target" id="index-0-prop_tgt:ARCHIVE_OUTPUT_NAME_&lt;CONFIG&gt;"></span><a class="reference internal" href="../prop_tgt/ARCHIVE_OUTPUT_NAME_CONFIG.html#prop_tgt:ARCHIVE_OUTPUT_NAME_&lt;CONFIG&gt;" title="ARCHIVE_OUTPUT_NAME_&lt;CONFIG&gt;"><code class="xref cmake cmake-prop_tgt docutils literal notranslate"><span class="pre">ARCHIVE_OUTPUT_NAME_&lt;CONFIG&gt;</span></code></a>,
<span class="target" id="index-0-prop_tgt:LIBRARY_OUTPUT_NAME_&lt;CONFIG&gt;"></span><a class="reference internal" href="../prop_tgt/LIBRARY_OUTPUT_NAME_CONFIG.html#prop_tgt:LIBRARY_OUTPUT_NAME_&lt;CONFIG&gt;" title="LIBRARY_OUTPUT_NAME_&lt;CONFIG&gt;"><code class="xref cmake cmake-prop_tgt docutils literal notranslate"><span class="pre">LIBRARY_OUTPUT_NAME_&lt;CONFIG&gt;</span></code></a> and
<span class="target" id="index-0-prop_tgt:RUNTIME_OUTPUT_NAME_&lt;CONFIG&gt;"></span><a class="reference internal" href="../prop_tgt/RUNTIME_OUTPUT_NAME_CONFIG.html#prop_tgt:RUNTIME_OUTPUT_NAME_&lt;CONFIG&gt;" title="RUNTIME_OUTPUT_NAME_&lt;CONFIG&gt;"><code class="xref cmake cmake-prop_tgt docutils literal notranslate"><span class="pre">RUNTIME_OUTPUT_NAME_&lt;CONFIG&gt;</span></code></a>.</p>
<p>The <span class="target" id="index-0-prop_tgt:&lt;CONFIG&gt;_POSTFIX"></span><a class="reference internal" href="../prop_tgt/CONFIG_POSTFIX.html#prop_tgt:&lt;CONFIG&gt;_POSTFIX" title="&lt;CONFIG&gt;_POSTFIX"><code class="xref cmake cmake-prop_tgt docutils literal notranslate"><span class="pre">&lt;CONFIG&gt;_POSTFIX</span></code></a> and <span class="target" id="index-0-prop_tgt:DEBUG_POSTFIX"></span><a class="reference internal" href="../prop_tgt/DEBUG_POSTFIX.html#prop_tgt:DEBUG_POSTFIX" title="DEBUG_POSTFIX"><code class="xref cmake cmake-prop_tgt docutils literal notranslate"><span class="pre">DEBUG_POSTFIX</span></code></a> target
properties can also be considered.</p>
<p>Note that <code class="docutils literal notranslate"><span class="pre">tgt</span></code> is not added as a dependency of the target this
expression is evaluated on.</p>
</dd></dl>

<dl class="cmake genex">
<dt class="sig sig-object cmake" id="genex:TARGET_FILE_PREFIX">
<span class="sig-name descname"><span class="pre">$&lt;TARGET_FILE_PREFIX:tgt&gt;</span></span><a class="headerlink" href="#genex:TARGET_FILE_PREFIX" title="Permalink to this definition">¶</a></dt>
<dd><div class="versionadded">
<p><span class="versionmodified added">New in version 3.15.</span></p>
</div>
<p>Prefix of the <code class="docutils literal notranslate"><span class="pre">tgt</span></code> filename (such as <code class="docutils literal notranslate"><span class="pre">lib</span></code>).</p>
<p>See also the <span class="target" id="index-0-prop_tgt:PREFIX"></span><a class="reference internal" href="../prop_tgt/PREFIX.html#prop_tgt:PREFIX" title="PREFIX"><code class="xref cmake cmake-prop_tgt docutils literal notranslate"><span class="pre">PREFIX</span></code></a> target property.</p>
<p>Note that <code class="docutils literal notranslate"><span class="pre">tgt</span></code> is not added as a dependency of the target this
expression is evaluated on.</p>
</dd></dl>

<dl class="cmake genex">
<dt class="sig sig-object cmake" id="genex:TARGET_FILE_SUFFIX">
<span class="sig-name descname"><span class="pre">$&lt;TARGET_FILE_SUFFIX:tgt&gt;</span></span><a class="headerlink" href="#genex:TARGET_FILE_SUFFIX" title="Permalink to this definition">¶</a></dt>
<dd><div class="versionadded">
<p><span class="versionmodified added">New in version 3.15.</span></p>
</div>
<p>Suffix of the <code class="docutils literal notranslate"><span class="pre">tgt</span></code> filename (extension such as <code class="docutils literal notranslate"><span class="pre">.so</span></code> or <code class="docutils literal notranslate"><span class="pre">.exe</span></code>).</p>
<p>See also the <span class="target" id="index-0-prop_tgt:SUFFIX"></span><a class="reference internal" href="../prop_tgt/SUFFIX.html#prop_tgt:SUFFIX" title="SUFFIX"><code class="xref cmake cmake-prop_tgt docutils literal notranslate"><span class="pre">SUFFIX</span></code></a> target property.</p>
<p>Note that <code class="docutils literal notranslate"><span class="pre">tgt</span></code> is not added as a dependency of the target this
expression is evaluated on.</p>
</dd></dl>

<dl class="cmake genex">
<dt class="sig sig-object cmake" id="genex:TARGET_FILE_NAME">
<span class="sig-name descname"><span class="pre">$&lt;TARGET_FILE_NAME:tgt&gt;</span></span><a class="headerlink" href="#genex:TARGET_FILE_NAME" title="Permalink to this definition">¶</a></dt>
<dd><p>The <code class="docutils literal notranslate"><span class="pre">tgt</span></code> filename.</p>
<p>Note that <code class="docutils literal notranslate"><span class="pre">tgt</span></code> is not added as a dependency of the target this
expression is evaluated on (see policy <span class="target" id="index-0-policy:CMP0112"></span><a class="reference internal" href="../policy/CMP0112.html#policy:CMP0112" title="CMP0112"><code class="xref cmake cmake-policy docutils literal notranslate"><span class="pre">CMP0112</span></code></a>).</p>
</dd></dl>

<dl class="cmake genex">
<dt class="sig sig-object cmake" id="genex:TARGET_FILE_DIR">
<span class="sig-name descname"><span class="pre">$&lt;TARGET_FILE_DIR:tgt&gt;</span></span><a class="headerlink" href="#genex:TARGET_FILE_DIR" title="Permalink to this definition">¶</a></dt>
<dd><p>Directory of the <code class="docutils literal notranslate"><span class="pre">tgt</span></code> binary file.</p>
<p>Note that <code class="docutils literal notranslate"><span class="pre">tgt</span></code> is not added as a dependency of the target this
expression is evaluated on (see policy <span class="target" id="index-1-policy:CMP0112"></span><a class="reference internal" href="../policy/CMP0112.html#policy:CMP0112" title="CMP0112"><code class="xref cmake cmake-policy docutils literal notranslate"><span class="pre">CMP0112</span></code></a>).</p>
</dd></dl>

<dl class="cmake genex">
<dt class="sig sig-object cmake" id="genex:TARGET_IMPORT_FILE">
<span class="sig-name descname"><span class="pre">$&lt;TARGET_IMPORT_FILE:tgt&gt;</span></span><a class="headerlink" href="#genex:TARGET_IMPORT_FILE" title="Permalink to this definition">¶</a></dt>
<dd><div class="versionadded">
<p><span class="versionmodified added">New in version 3.27.</span></p>
</div>
<p>Full path to the linker import file. On DLL platforms, it would be the
<code class="docutils literal notranslate"><span class="pre">.lib</span></code> file. For executables on AIX, and for shared libraries on macOS,
it could be, respectively, the <code class="docutils literal notranslate"><span class="pre">.imp</span></code> or <code class="docutils literal notranslate"><span class="pre">.tbd</span></code> import file,
depending on the value of the <span class="target" id="index-0-prop_tgt:ENABLE_EXPORTS"></span><a class="reference internal" href="../prop_tgt/ENABLE_EXPORTS.html#prop_tgt:ENABLE_EXPORTS" title="ENABLE_EXPORTS"><code class="xref cmake cmake-prop_tgt docutils literal notranslate"><span class="pre">ENABLE_EXPORTS</span></code></a> property.</p>
<p>This expands to an empty string when there is no import file associated
with the target.</p>
</dd></dl>

<dl class="cmake genex">
<dt class="sig sig-object cmake" id="genex:TARGET_IMPORT_FILE_BASE_NAME">
<span class="sig-name descname"><span class="pre">$&lt;TARGET_IMPORT_FILE_BASE_NAME:tgt&gt;</span></span><a class="headerlink" href="#genex:TARGET_IMPORT_FILE_BASE_NAME" title="Permalink to this definition">¶</a></dt>
<dd><div class="versionadded">
<p><span class="versionmodified added">New in version 3.27.</span></p>
</div>
<p>Base name of the linker import file of the target <code class="docutils literal notranslate"><span class="pre">tgt</span></code> without prefix or
suffix. For example, if the target file name is <code class="docutils literal notranslate"><span class="pre">libbase.tbd</span></code>, the base
name is <code class="docutils literal notranslate"><span class="pre">base</span></code>.</p>
<p>See also the <span class="target" id="index-1-prop_tgt:OUTPUT_NAME"></span><a class="reference internal" href="../prop_tgt/OUTPUT_NAME.html#prop_tgt:OUTPUT_NAME" title="OUTPUT_NAME"><code class="xref cmake cmake-prop_tgt docutils literal notranslate"><span class="pre">OUTPUT_NAME</span></code></a> and <span class="target" id="index-1-prop_tgt:ARCHIVE_OUTPUT_NAME"></span><a class="reference internal" href="../prop_tgt/ARCHIVE_OUTPUT_NAME.html#prop_tgt:ARCHIVE_OUTPUT_NAME" title="ARCHIVE_OUTPUT_NAME"><code class="xref cmake cmake-prop_tgt docutils literal notranslate"><span class="pre">ARCHIVE_OUTPUT_NAME</span></code></a>
target properties and their configuration specific variants
<span class="target" id="index-1-prop_tgt:OUTPUT_NAME_&lt;CONFIG&gt;"></span><a class="reference internal" href="../prop_tgt/OUTPUT_NAME_CONFIG.html#prop_tgt:OUTPUT_NAME_&lt;CONFIG&gt;" title="OUTPUT_NAME_&lt;CONFIG&gt;"><code class="xref cmake cmake-prop_tgt docutils literal notranslate"><span class="pre">OUTPUT_NAME_&lt;CONFIG&gt;</span></code></a> and <span class="target" id="index-1-prop_tgt:ARCHIVE_OUTPUT_NAME_&lt;CONFIG&gt;"></span><a class="reference internal" href="../prop_tgt/ARCHIVE_OUTPUT_NAME_CONFIG.html#prop_tgt:ARCHIVE_OUTPUT_NAME_&lt;CONFIG&gt;" title="ARCHIVE_OUTPUT_NAME_&lt;CONFIG&gt;"><code class="xref cmake cmake-prop_tgt docutils literal notranslate"><span class="pre">ARCHIVE_OUTPUT_NAME_&lt;CONFIG&gt;</span></code></a>.</p>
<p>The <span class="target" id="index-1-prop_tgt:&lt;CONFIG&gt;_POSTFIX"></span><a class="reference internal" href="../prop_tgt/CONFIG_POSTFIX.html#prop_tgt:&lt;CONFIG&gt;_POSTFIX" title="&lt;CONFIG&gt;_POSTFIX"><code class="xref cmake cmake-prop_tgt docutils literal notranslate"><span class="pre">&lt;CONFIG&gt;_POSTFIX</span></code></a> and <span class="target" id="index-1-prop_tgt:DEBUG_POSTFIX"></span><a class="reference internal" href="../prop_tgt/DEBUG_POSTFIX.html#prop_tgt:DEBUG_POSTFIX" title="DEBUG_POSTFIX"><code class="xref cmake cmake-prop_tgt docutils literal notranslate"><span class="pre">DEBUG_POSTFIX</span></code></a> target
properties can also be considered.</p>
<p>Note that <code class="docutils literal notranslate"><span class="pre">tgt</span></code> is not added as a dependency of the target this
expression is evaluated on.</p>
</dd></dl>

<dl class="cmake genex">
<dt class="sig sig-object cmake" id="genex:TARGET_IMPORT_FILE_PREFIX">
<span class="sig-name descname"><span class="pre">$&lt;TARGET_IMPORT_FILE_PREFIX:tgt&gt;</span></span><a class="headerlink" href="#genex:TARGET_IMPORT_FILE_PREFIX" title="Permalink to this definition">¶</a></dt>
<dd><div class="versionadded">
<p><span class="versionmodified added">New in version 3.27.</span></p>
</div>
<p>Prefix of the import file of the target <code class="docutils literal notranslate"><span class="pre">tgt</span></code>.</p>
<p>See also the <span class="target" id="index-0-prop_tgt:IMPORT_PREFIX"></span><a class="reference internal" href="../prop_tgt/IMPORT_PREFIX.html#prop_tgt:IMPORT_PREFIX" title="IMPORT_PREFIX"><code class="xref cmake cmake-prop_tgt docutils literal notranslate"><span class="pre">IMPORT_PREFIX</span></code></a> target property.</p>
<p>Note that <code class="docutils literal notranslate"><span class="pre">tgt</span></code> is not added as a dependency of the target this
expression is evaluated on.</p>
</dd></dl>

<dl class="cmake genex">
<dt class="sig sig-object cmake" id="genex:TARGET_IMPORT_FILE_SUFFIX">
<span class="sig-name descname"><span class="pre">$&lt;TARGET_IMPORT_FILE_SUFFIX:tgt&gt;</span></span><a class="headerlink" href="#genex:TARGET_IMPORT_FILE_SUFFIX" title="Permalink to this definition">¶</a></dt>
<dd><div class="versionadded">
<p><span class="versionmodified added">New in version 3.27.</span></p>
</div>
<p>Suffix of the import file of the target <code class="docutils literal notranslate"><span class="pre">tgt</span></code>.</p>
<p>The suffix corresponds to the file extension (such as <code class="docutils literal notranslate"><span class="pre">.lib</span></code> or <code class="docutils literal notranslate"><span class="pre">.tbd</span></code>).</p>
<p>See also the <span class="target" id="index-0-prop_tgt:IMPORT_SUFFIX"></span><a class="reference internal" href="../prop_tgt/IMPORT_SUFFIX.html#prop_tgt:IMPORT_SUFFIX" title="IMPORT_SUFFIX"><code class="xref cmake cmake-prop_tgt docutils literal notranslate"><span class="pre">IMPORT_SUFFIX</span></code></a> target property.</p>
<p>Note that <code class="docutils literal notranslate"><span class="pre">tgt</span></code> is not added as a dependency of the target this
expression is evaluated on.</p>
</dd></dl>

<dl class="cmake genex">
<dt class="sig sig-object cmake" id="genex:TARGET_IMPORT_FILE_NAME">
<span class="sig-name descname"><span class="pre">$&lt;TARGET_IMPORT_FILE_NAME:tgt&gt;</span></span><a class="headerlink" href="#genex:TARGET_IMPORT_FILE_NAME" title="Permalink to this definition">¶</a></dt>
<dd><div class="versionadded">
<p><span class="versionmodified added">New in version 3.27.</span></p>
</div>
<p>Name of the import file of the target <code class="docutils literal notranslate"><span class="pre">tgt</span></code>.</p>
<p>Note that <code class="docutils literal notranslate"><span class="pre">tgt</span></code> is not added as a dependency of the target this
expression is evaluated on.</p>
</dd></dl>

<dl class="cmake genex">
<dt class="sig sig-object cmake" id="genex:TARGET_IMPORT_FILE_DIR">
<span class="sig-name descname"><span class="pre">$&lt;TARGET_IMPORT_FILE_DIR:tgt&gt;</span></span><a class="headerlink" href="#genex:TARGET_IMPORT_FILE_DIR" title="Permalink to this definition">¶</a></dt>
<dd><div class="versionadded">
<p><span class="versionmodified added">New in version 3.27.</span></p>
</div>
<p>Directory of the import file of the target <code class="docutils literal notranslate"><span class="pre">tgt</span></code>.</p>
<p>Note that <code class="docutils literal notranslate"><span class="pre">tgt</span></code> is not added as a dependency of the target this
expression is evaluated on.</p>
</dd></dl>

<dl class="cmake genex">
<dt class="sig sig-object cmake" id="genex:TARGET_LINKER_FILE">
<span class="sig-name descname"><span class="pre">$&lt;TARGET_LINKER_FILE:tgt&gt;</span></span><a class="headerlink" href="#genex:TARGET_LINKER_FILE" title="Permalink to this definition">¶</a></dt>
<dd><p>File used when linking to the <code class="docutils literal notranslate"><span class="pre">tgt</span></code> target.  This will usually
be the library that <code class="docutils literal notranslate"><span class="pre">tgt</span></code> represents (<code class="docutils literal notranslate"><span class="pre">.a</span></code>, <code class="docutils literal notranslate"><span class="pre">.lib</span></code>, <code class="docutils literal notranslate"><span class="pre">.so</span></code>),
but for a shared library on DLL platforms, it would be the <code class="docutils literal notranslate"><span class="pre">.lib</span></code>
import library associated with the DLL.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.27: </span>On macOS, it could be the <code class="docutils literal notranslate"><span class="pre">.tbd</span></code> import file associated with the shared
library, depending on the value of the <span class="target" id="index-1-prop_tgt:ENABLE_EXPORTS"></span><a class="reference internal" href="../prop_tgt/ENABLE_EXPORTS.html#prop_tgt:ENABLE_EXPORTS" title="ENABLE_EXPORTS"><code class="xref cmake cmake-prop_tgt docutils literal notranslate"><span class="pre">ENABLE_EXPORTS</span></code></a> property.</p>
</div>
<p>This generator expression is equivalent to
<span class="target" id="index-0-genex:TARGET_LINKER_LIBRARY_FILE"></span><a class="reference internal" href="#genex:TARGET_LINKER_LIBRARY_FILE" title="TARGET_LINKER_LIBRARY_FILE"><code class="xref cmake cmake-genex docutils literal notranslate"><span class="pre">$&lt;TARGET_LINKER_LIBRARY_FILE&gt;</span></code></a> or
<span class="target" id="index-0-genex:TARGET_LINKER_IMPORT_FILE"></span><a class="reference internal" href="#genex:TARGET_LINKER_IMPORT_FILE" title="TARGET_LINKER_IMPORT_FILE"><code class="xref cmake cmake-genex docutils literal notranslate"><span class="pre">$&lt;TARGET_LINKER_IMPORT_FILE&gt;</span></code></a> generator expressions, depending on the
characteristics of the target and the platform.</p>
</dd></dl>

<dl class="cmake genex">
<dt class="sig sig-object cmake" id="genex:TARGET_LINKER_FILE_BASE_NAME">
<span class="sig-name descname"><span class="pre">$&lt;TARGET_LINKER_FILE_BASE_NAME:tgt&gt;</span></span><a class="headerlink" href="#genex:TARGET_LINKER_FILE_BASE_NAME" title="Permalink to this definition">¶</a></dt>
<dd><div class="versionadded">
<p><span class="versionmodified added">New in version 3.15.</span></p>
</div>
<p>Base name of file used to link the target <code class="docutils literal notranslate"><span class="pre">tgt</span></code>, i.e.
<span class="target" id="index-0-genex:TARGET_LINKER_FILE_NAME"></span><a class="reference internal" href="#genex:TARGET_LINKER_FILE_NAME" title="TARGET_LINKER_FILE_NAME"><code class="xref cmake cmake-genex docutils literal notranslate"><span class="pre">$&lt;TARGET_LINKER_FILE_NAME:tgt&gt;</span></code></a> without prefix and suffix. For
example, if target file name is <code class="docutils literal notranslate"><span class="pre">libbase.a</span></code>, the base name is <code class="docutils literal notranslate"><span class="pre">base</span></code>.</p>
<p>See also the <span class="target" id="index-2-prop_tgt:OUTPUT_NAME"></span><a class="reference internal" href="../prop_tgt/OUTPUT_NAME.html#prop_tgt:OUTPUT_NAME" title="OUTPUT_NAME"><code class="xref cmake cmake-prop_tgt docutils literal notranslate"><span class="pre">OUTPUT_NAME</span></code></a>, <span class="target" id="index-2-prop_tgt:ARCHIVE_OUTPUT_NAME"></span><a class="reference internal" href="../prop_tgt/ARCHIVE_OUTPUT_NAME.html#prop_tgt:ARCHIVE_OUTPUT_NAME" title="ARCHIVE_OUTPUT_NAME"><code class="xref cmake cmake-prop_tgt docutils literal notranslate"><span class="pre">ARCHIVE_OUTPUT_NAME</span></code></a>,
and <span class="target" id="index-1-prop_tgt:LIBRARY_OUTPUT_NAME"></span><a class="reference internal" href="../prop_tgt/LIBRARY_OUTPUT_NAME.html#prop_tgt:LIBRARY_OUTPUT_NAME" title="LIBRARY_OUTPUT_NAME"><code class="xref cmake cmake-prop_tgt docutils literal notranslate"><span class="pre">LIBRARY_OUTPUT_NAME</span></code></a> target properties and their configuration
specific variants <span class="target" id="index-2-prop_tgt:OUTPUT_NAME_&lt;CONFIG&gt;"></span><a class="reference internal" href="../prop_tgt/OUTPUT_NAME_CONFIG.html#prop_tgt:OUTPUT_NAME_&lt;CONFIG&gt;" title="OUTPUT_NAME_&lt;CONFIG&gt;"><code class="xref cmake cmake-prop_tgt docutils literal notranslate"><span class="pre">OUTPUT_NAME_&lt;CONFIG&gt;</span></code></a>,
<span class="target" id="index-2-prop_tgt:ARCHIVE_OUTPUT_NAME_&lt;CONFIG&gt;"></span><a class="reference internal" href="../prop_tgt/ARCHIVE_OUTPUT_NAME_CONFIG.html#prop_tgt:ARCHIVE_OUTPUT_NAME_&lt;CONFIG&gt;" title="ARCHIVE_OUTPUT_NAME_&lt;CONFIG&gt;"><code class="xref cmake cmake-prop_tgt docutils literal notranslate"><span class="pre">ARCHIVE_OUTPUT_NAME_&lt;CONFIG&gt;</span></code></a> and
<span class="target" id="index-1-prop_tgt:LIBRARY_OUTPUT_NAME_&lt;CONFIG&gt;"></span><a class="reference internal" href="../prop_tgt/LIBRARY_OUTPUT_NAME_CONFIG.html#prop_tgt:LIBRARY_OUTPUT_NAME_&lt;CONFIG&gt;" title="LIBRARY_OUTPUT_NAME_&lt;CONFIG&gt;"><code class="xref cmake cmake-prop_tgt docutils literal notranslate"><span class="pre">LIBRARY_OUTPUT_NAME_&lt;CONFIG&gt;</span></code></a>.</p>
<p>The <span class="target" id="index-2-prop_tgt:&lt;CONFIG&gt;_POSTFIX"></span><a class="reference internal" href="../prop_tgt/CONFIG_POSTFIX.html#prop_tgt:&lt;CONFIG&gt;_POSTFIX" title="&lt;CONFIG&gt;_POSTFIX"><code class="xref cmake cmake-prop_tgt docutils literal notranslate"><span class="pre">&lt;CONFIG&gt;_POSTFIX</span></code></a> and <span class="target" id="index-2-prop_tgt:DEBUG_POSTFIX"></span><a class="reference internal" href="../prop_tgt/DEBUG_POSTFIX.html#prop_tgt:DEBUG_POSTFIX" title="DEBUG_POSTFIX"><code class="xref cmake cmake-prop_tgt docutils literal notranslate"><span class="pre">DEBUG_POSTFIX</span></code></a> target
properties can also be considered.</p>
<p>Note that <code class="docutils literal notranslate"><span class="pre">tgt</span></code> is not added as a dependency of the target this
expression is evaluated on.</p>
</dd></dl>

<dl class="cmake genex">
<dt class="sig sig-object cmake" id="genex:TARGET_LINKER_FILE_PREFIX">
<span class="sig-name descname"><span class="pre">$&lt;TARGET_LINKER_FILE_PREFIX:tgt&gt;</span></span><a class="headerlink" href="#genex:TARGET_LINKER_FILE_PREFIX" title="Permalink to this definition">¶</a></dt>
<dd><div class="versionadded">
<p><span class="versionmodified added">New in version 3.15.</span></p>
</div>
<p>Prefix of file used to link target <code class="docutils literal notranslate"><span class="pre">tgt</span></code>.</p>
<p>See also the <span class="target" id="index-1-prop_tgt:PREFIX"></span><a class="reference internal" href="../prop_tgt/PREFIX.html#prop_tgt:PREFIX" title="PREFIX"><code class="xref cmake cmake-prop_tgt docutils literal notranslate"><span class="pre">PREFIX</span></code></a> and <span class="target" id="index-1-prop_tgt:IMPORT_PREFIX"></span><a class="reference internal" href="../prop_tgt/IMPORT_PREFIX.html#prop_tgt:IMPORT_PREFIX" title="IMPORT_PREFIX"><code class="xref cmake cmake-prop_tgt docutils literal notranslate"><span class="pre">IMPORT_PREFIX</span></code></a> target
properties.</p>
<p>Note that <code class="docutils literal notranslate"><span class="pre">tgt</span></code> is not added as a dependency of the target this
expression is evaluated on.</p>
</dd></dl>

<dl class="cmake genex">
<dt class="sig sig-object cmake" id="genex:TARGET_LINKER_FILE_SUFFIX">
<span class="sig-name descname"><span class="pre">$&lt;TARGET_LINKER_FILE_SUFFIX:tgt&gt;</span></span><a class="headerlink" href="#genex:TARGET_LINKER_FILE_SUFFIX" title="Permalink to this definition">¶</a></dt>
<dd><div class="versionadded">
<p><span class="versionmodified added">New in version 3.15.</span></p>
</div>
<p>Suffix of file used to link where <code class="docutils literal notranslate"><span class="pre">tgt</span></code> is the name of a target.</p>
<p>The suffix corresponds to the file extension (such as &quot;.so&quot; or &quot;.lib&quot;).</p>
<p>See also the <span class="target" id="index-1-prop_tgt:SUFFIX"></span><a class="reference internal" href="../prop_tgt/SUFFIX.html#prop_tgt:SUFFIX" title="SUFFIX"><code class="xref cmake cmake-prop_tgt docutils literal notranslate"><span class="pre">SUFFIX</span></code></a> and <span class="target" id="index-1-prop_tgt:IMPORT_SUFFIX"></span><a class="reference internal" href="../prop_tgt/IMPORT_SUFFIX.html#prop_tgt:IMPORT_SUFFIX" title="IMPORT_SUFFIX"><code class="xref cmake cmake-prop_tgt docutils literal notranslate"><span class="pre">IMPORT_SUFFIX</span></code></a> target
properties.</p>
<p>Note that <code class="docutils literal notranslate"><span class="pre">tgt</span></code> is not added as a dependency of the target this
expression is evaluated on.</p>
</dd></dl>

<dl class="cmake genex">
<dt class="sig sig-object cmake" id="genex:TARGET_LINKER_FILE_NAME">
<span class="sig-name descname"><span class="pre">$&lt;TARGET_LINKER_FILE_NAME:tgt&gt;</span></span><a class="headerlink" href="#genex:TARGET_LINKER_FILE_NAME" title="Permalink to this definition">¶</a></dt>
<dd><p>Name of file used to link target <code class="docutils literal notranslate"><span class="pre">tgt</span></code>.</p>
<p>Note that <code class="docutils literal notranslate"><span class="pre">tgt</span></code> is not added as a dependency of the target this
expression is evaluated on (see policy <span class="target" id="index-2-policy:CMP0112"></span><a class="reference internal" href="../policy/CMP0112.html#policy:CMP0112" title="CMP0112"><code class="xref cmake cmake-policy docutils literal notranslate"><span class="pre">CMP0112</span></code></a>).</p>
</dd></dl>

<dl class="cmake genex">
<dt class="sig sig-object cmake" id="genex:TARGET_LINKER_FILE_DIR">
<span class="sig-name descname"><span class="pre">$&lt;TARGET_LINKER_FILE_DIR:tgt&gt;</span></span><a class="headerlink" href="#genex:TARGET_LINKER_FILE_DIR" title="Permalink to this definition">¶</a></dt>
<dd><p>Directory of file used to link target <code class="docutils literal notranslate"><span class="pre">tgt</span></code>.</p>
<p>Note that <code class="docutils literal notranslate"><span class="pre">tgt</span></code> is not added as a dependency of the target this
expression is evaluated on (see policy <span class="target" id="index-3-policy:CMP0112"></span><a class="reference internal" href="../policy/CMP0112.html#policy:CMP0112" title="CMP0112"><code class="xref cmake cmake-policy docutils literal notranslate"><span class="pre">CMP0112</span></code></a>).</p>
</dd></dl>

<dl class="cmake genex">
<dt class="sig sig-object cmake" id="genex:TARGET_LINKER_LIBRARY_FILE">
<span class="sig-name descname"><span class="pre">$&lt;TARGET_LINKER_LIBRARY_FILE:tgt&gt;</span></span><a class="headerlink" href="#genex:TARGET_LINKER_LIBRARY_FILE" title="Permalink to this definition">¶</a></dt>
<dd><div class="versionadded">
<p><span class="versionmodified added">New in version 3.27.</span></p>
</div>
<p>File used when linking o the <code class="docutils literal notranslate"><span class="pre">tgt</span></code> target is done using directly the
library, and not an import file. This will usually be the library that
<code class="docutils literal notranslate"><span class="pre">tgt</span></code> represents (<code class="docutils literal notranslate"><span class="pre">.a</span></code>, <code class="docutils literal notranslate"><span class="pre">.so</span></code>, <code class="docutils literal notranslate"><span class="pre">.dylib</span></code>). So, on DLL platforms, it
will be an empty string.</p>
</dd></dl>

<dl class="cmake genex">
<dt class="sig sig-object cmake" id="genex:TARGET_LINKER_LIBRARY_FILE_BASE_NAME">
<span class="sig-name descname"><span class="pre">$&lt;TARGET_LINKER_LIBRARY_FILE_BASE_NAME:tgt&gt;</span></span><a class="headerlink" href="#genex:TARGET_LINKER_LIBRARY_FILE_BASE_NAME" title="Permalink to this definition">¶</a></dt>
<dd><div class="versionadded">
<p><span class="versionmodified added">New in version 3.27.</span></p>
</div>
<p>Base name of library file used to link the target <code class="docutils literal notranslate"><span class="pre">tgt</span></code>, i.e.
<span class="target" id="index-0-genex:TARGET_LINKER_LIBRARY_FILE_NAME"></span><a class="reference internal" href="#genex:TARGET_LINKER_LIBRARY_FILE_NAME" title="TARGET_LINKER_LIBRARY_FILE_NAME"><code class="xref cmake cmake-genex docutils literal notranslate"><span class="pre">$&lt;TARGET_LINKER_LIBRARY_FILE_NAME:tgt&gt;</span></code></a> without prefix and suffix.
For example, if target file name is <code class="docutils literal notranslate"><span class="pre">libbase.a</span></code>, the base name is <code class="docutils literal notranslate"><span class="pre">base</span></code>.</p>
<p>See also the <span class="target" id="index-3-prop_tgt:OUTPUT_NAME"></span><a class="reference internal" href="../prop_tgt/OUTPUT_NAME.html#prop_tgt:OUTPUT_NAME" title="OUTPUT_NAME"><code class="xref cmake cmake-prop_tgt docutils literal notranslate"><span class="pre">OUTPUT_NAME</span></code></a>, <span class="target" id="index-3-prop_tgt:ARCHIVE_OUTPUT_NAME"></span><a class="reference internal" href="../prop_tgt/ARCHIVE_OUTPUT_NAME.html#prop_tgt:ARCHIVE_OUTPUT_NAME" title="ARCHIVE_OUTPUT_NAME"><code class="xref cmake cmake-prop_tgt docutils literal notranslate"><span class="pre">ARCHIVE_OUTPUT_NAME</span></code></a>,
and <span class="target" id="index-2-prop_tgt:LIBRARY_OUTPUT_NAME"></span><a class="reference internal" href="../prop_tgt/LIBRARY_OUTPUT_NAME.html#prop_tgt:LIBRARY_OUTPUT_NAME" title="LIBRARY_OUTPUT_NAME"><code class="xref cmake cmake-prop_tgt docutils literal notranslate"><span class="pre">LIBRARY_OUTPUT_NAME</span></code></a> target properties and their configuration
specific variants <span class="target" id="index-3-prop_tgt:OUTPUT_NAME_&lt;CONFIG&gt;"></span><a class="reference internal" href="../prop_tgt/OUTPUT_NAME_CONFIG.html#prop_tgt:OUTPUT_NAME_&lt;CONFIG&gt;" title="OUTPUT_NAME_&lt;CONFIG&gt;"><code class="xref cmake cmake-prop_tgt docutils literal notranslate"><span class="pre">OUTPUT_NAME_&lt;CONFIG&gt;</span></code></a>,
<span class="target" id="index-3-prop_tgt:ARCHIVE_OUTPUT_NAME_&lt;CONFIG&gt;"></span><a class="reference internal" href="../prop_tgt/ARCHIVE_OUTPUT_NAME_CONFIG.html#prop_tgt:ARCHIVE_OUTPUT_NAME_&lt;CONFIG&gt;" title="ARCHIVE_OUTPUT_NAME_&lt;CONFIG&gt;"><code class="xref cmake cmake-prop_tgt docutils literal notranslate"><span class="pre">ARCHIVE_OUTPUT_NAME_&lt;CONFIG&gt;</span></code></a> and
<span class="target" id="index-2-prop_tgt:LIBRARY_OUTPUT_NAME_&lt;CONFIG&gt;"></span><a class="reference internal" href="../prop_tgt/LIBRARY_OUTPUT_NAME_CONFIG.html#prop_tgt:LIBRARY_OUTPUT_NAME_&lt;CONFIG&gt;" title="LIBRARY_OUTPUT_NAME_&lt;CONFIG&gt;"><code class="xref cmake cmake-prop_tgt docutils literal notranslate"><span class="pre">LIBRARY_OUTPUT_NAME_&lt;CONFIG&gt;</span></code></a>.</p>
<p>The <span class="target" id="index-3-prop_tgt:&lt;CONFIG&gt;_POSTFIX"></span><a class="reference internal" href="../prop_tgt/CONFIG_POSTFIX.html#prop_tgt:&lt;CONFIG&gt;_POSTFIX" title="&lt;CONFIG&gt;_POSTFIX"><code class="xref cmake cmake-prop_tgt docutils literal notranslate"><span class="pre">&lt;CONFIG&gt;_POSTFIX</span></code></a> and <span class="target" id="index-3-prop_tgt:DEBUG_POSTFIX"></span><a class="reference internal" href="../prop_tgt/DEBUG_POSTFIX.html#prop_tgt:DEBUG_POSTFIX" title="DEBUG_POSTFIX"><code class="xref cmake cmake-prop_tgt docutils literal notranslate"><span class="pre">DEBUG_POSTFIX</span></code></a> target
properties can also be considered.</p>
<p>Note that <code class="docutils literal notranslate"><span class="pre">tgt</span></code> is not added as a dependency of the target this
expression is evaluated on.</p>
</dd></dl>

<dl class="cmake genex">
<dt class="sig sig-object cmake" id="genex:TARGET_LINKER_LIBRARY_FILE_PREFIX">
<span class="sig-name descname"><span class="pre">$&lt;TARGET_LINKER_LIBRARY_FILE_PREFIX:tgt&gt;</span></span><a class="headerlink" href="#genex:TARGET_LINKER_LIBRARY_FILE_PREFIX" title="Permalink to this definition">¶</a></dt>
<dd><div class="versionadded">
<p><span class="versionmodified added">New in version 3.27.</span></p>
</div>
<p>Prefix of the library file used to link target <code class="docutils literal notranslate"><span class="pre">tgt</span></code>.</p>
<p>See also the <span class="target" id="index-2-prop_tgt:PREFIX"></span><a class="reference internal" href="../prop_tgt/PREFIX.html#prop_tgt:PREFIX" title="PREFIX"><code class="xref cmake cmake-prop_tgt docutils literal notranslate"><span class="pre">PREFIX</span></code></a> target property.</p>
<p>Note that <code class="docutils literal notranslate"><span class="pre">tgt</span></code> is not added as a dependency of the target this
expression is evaluated on.</p>
</dd></dl>

<dl class="cmake genex">
<dt class="sig sig-object cmake" id="genex:TARGET_LINKER_LIBRARY_FILE_SUFFIX">
<span class="sig-name descname"><span class="pre">$&lt;TARGET_LINKER_LIBRARY_FILE_SUFFIX:tgt&gt;</span></span><a class="headerlink" href="#genex:TARGET_LINKER_LIBRARY_FILE_SUFFIX" title="Permalink to this definition">¶</a></dt>
<dd><div class="versionadded">
<p><span class="versionmodified added">New in version 3.27.</span></p>
</div>
<p>Suffix of the library file used to link target <code class="docutils literal notranslate"><span class="pre">tgt</span></code>.</p>
<p>The suffix corresponds to the file extension (such as &quot;.a&quot; or &quot;.dylib&quot;).</p>
<p>See also the <span class="target" id="index-2-prop_tgt:SUFFIX"></span><a class="reference internal" href="../prop_tgt/SUFFIX.html#prop_tgt:SUFFIX" title="SUFFIX"><code class="xref cmake cmake-prop_tgt docutils literal notranslate"><span class="pre">SUFFIX</span></code></a> target property.</p>
<p>Note that <code class="docutils literal notranslate"><span class="pre">tgt</span></code> is not added as a dependency of the target this
expression is evaluated on.</p>
</dd></dl>

<dl class="cmake genex">
<dt class="sig sig-object cmake" id="genex:TARGET_LINKER_LIBRARY_FILE_NAME">
<span class="sig-name descname"><span class="pre">$&lt;TARGET_LINKER_LIBRARY_FILE_NAME:tgt&gt;</span></span><a class="headerlink" href="#genex:TARGET_LINKER_LIBRARY_FILE_NAME" title="Permalink to this definition">¶</a></dt>
<dd><div class="versionadded">
<p><span class="versionmodified added">New in version 3.27.</span></p>
</div>
<p>Name of the library file used to link target <code class="docutils literal notranslate"><span class="pre">tgt</span></code>.</p>
<p>Note that <code class="docutils literal notranslate"><span class="pre">tgt</span></code> is not added as a dependency of the target this
expression is evaluated on.</p>
</dd></dl>

<dl class="cmake genex">
<dt class="sig sig-object cmake" id="genex:TARGET_LINKER_LIBRARY_FILE_DIR">
<span class="sig-name descname"><span class="pre">$&lt;TARGET_LINKER_LIBRARY_FILE_DIR:tgt&gt;</span></span><a class="headerlink" href="#genex:TARGET_LINKER_LIBRARY_FILE_DIR" title="Permalink to this definition">¶</a></dt>
<dd><div class="versionadded">
<p><span class="versionmodified added">New in version 3.27.</span></p>
</div>
<p>Directory of the library file used to link target <code class="docutils literal notranslate"><span class="pre">tgt</span></code>.</p>
<p>Note that <code class="docutils literal notranslate"><span class="pre">tgt</span></code> is not added as a dependency of the target this
expression is evaluated on.</p>
</dd></dl>

<dl class="cmake genex">
<dt class="sig sig-object cmake" id="genex:TARGET_LINKER_IMPORT_FILE">
<span class="sig-name descname"><span class="pre">$&lt;TARGET_LINKER_IMPORT_FILE:tgt&gt;</span></span><a class="headerlink" href="#genex:TARGET_LINKER_IMPORT_FILE" title="Permalink to this definition">¶</a></dt>
<dd><div class="versionadded">
<p><span class="versionmodified added">New in version 3.27.</span></p>
</div>
<p>File used when linking to the <code class="docutils literal notranslate"><span class="pre">tgt</span></code> target is done using an import
file.  This will usually be the import file that <code class="docutils literal notranslate"><span class="pre">tgt</span></code> represents
(<code class="docutils literal notranslate"><span class="pre">.lib</span></code>, <code class="docutils literal notranslate"><span class="pre">.tbd</span></code>). So, when no import file is involved in the link step,
an empty string is returned.</p>
</dd></dl>

<dl class="cmake genex">
<dt class="sig sig-object cmake" id="genex:TARGET_LINKER_IMPORT_FILE_BASE_NAME">
<span class="sig-name descname"><span class="pre">$&lt;TARGET_LINKER_IMPORT_FILE_BASE_NAME:tgt&gt;</span></span><a class="headerlink" href="#genex:TARGET_LINKER_IMPORT_FILE_BASE_NAME" title="Permalink to this definition">¶</a></dt>
<dd><div class="versionadded">
<p><span class="versionmodified added">New in version 3.27.</span></p>
</div>
<p>Base name of the import file used to link the target <code class="docutils literal notranslate"><span class="pre">tgt</span></code>, i.e.
<span class="target" id="index-0-genex:TARGET_LINKER_IMPORT_FILE_NAME"></span><a class="reference internal" href="#genex:TARGET_LINKER_IMPORT_FILE_NAME" title="TARGET_LINKER_IMPORT_FILE_NAME"><code class="xref cmake cmake-genex docutils literal notranslate"><span class="pre">$&lt;TARGET_LINKER_IMPORT_FILE_NAME:tgt&gt;</span></code></a> without prefix and suffix.
For example, if target file name is <code class="docutils literal notranslate"><span class="pre">libbase.tbd</span></code>, the base name is <code class="docutils literal notranslate"><span class="pre">base</span></code>.</p>
<p>See also the <span class="target" id="index-4-prop_tgt:OUTPUT_NAME"></span><a class="reference internal" href="../prop_tgt/OUTPUT_NAME.html#prop_tgt:OUTPUT_NAME" title="OUTPUT_NAME"><code class="xref cmake cmake-prop_tgt docutils literal notranslate"><span class="pre">OUTPUT_NAME</span></code></a> and <span class="target" id="index-4-prop_tgt:ARCHIVE_OUTPUT_NAME"></span><a class="reference internal" href="../prop_tgt/ARCHIVE_OUTPUT_NAME.html#prop_tgt:ARCHIVE_OUTPUT_NAME" title="ARCHIVE_OUTPUT_NAME"><code class="xref cmake cmake-prop_tgt docutils literal notranslate"><span class="pre">ARCHIVE_OUTPUT_NAME</span></code></a>,
target properties and their configuration
specific variants <span class="target" id="index-4-prop_tgt:OUTPUT_NAME_&lt;CONFIG&gt;"></span><a class="reference internal" href="../prop_tgt/OUTPUT_NAME_CONFIG.html#prop_tgt:OUTPUT_NAME_&lt;CONFIG&gt;" title="OUTPUT_NAME_&lt;CONFIG&gt;"><code class="xref cmake cmake-prop_tgt docutils literal notranslate"><span class="pre">OUTPUT_NAME_&lt;CONFIG&gt;</span></code></a> and
<span class="target" id="index-4-prop_tgt:ARCHIVE_OUTPUT_NAME_&lt;CONFIG&gt;"></span><a class="reference internal" href="../prop_tgt/ARCHIVE_OUTPUT_NAME_CONFIG.html#prop_tgt:ARCHIVE_OUTPUT_NAME_&lt;CONFIG&gt;" title="ARCHIVE_OUTPUT_NAME_&lt;CONFIG&gt;"><code class="xref cmake cmake-prop_tgt docutils literal notranslate"><span class="pre">ARCHIVE_OUTPUT_NAME_&lt;CONFIG&gt;</span></code></a>.</p>
<p>The <span class="target" id="index-4-prop_tgt:&lt;CONFIG&gt;_POSTFIX"></span><a class="reference internal" href="../prop_tgt/CONFIG_POSTFIX.html#prop_tgt:&lt;CONFIG&gt;_POSTFIX" title="&lt;CONFIG&gt;_POSTFIX"><code class="xref cmake cmake-prop_tgt docutils literal notranslate"><span class="pre">&lt;CONFIG&gt;_POSTFIX</span></code></a> and <span class="target" id="index-4-prop_tgt:DEBUG_POSTFIX"></span><a class="reference internal" href="../prop_tgt/DEBUG_POSTFIX.html#prop_tgt:DEBUG_POSTFIX" title="DEBUG_POSTFIX"><code class="xref cmake cmake-prop_tgt docutils literal notranslate"><span class="pre">DEBUG_POSTFIX</span></code></a> target
properties can also be considered.</p>
<p>Note that <code class="docutils literal notranslate"><span class="pre">tgt</span></code> is not added as a dependency of the target this
expression is evaluated on.</p>
</dd></dl>

<dl class="cmake genex">
<dt class="sig sig-object cmake" id="genex:TARGET_LINKER_IMPORT_FILE_PREFIX">
<span class="sig-name descname"><span class="pre">$&lt;TARGET_LINKER_IMPORT_FILE_PREFIX:tgt&gt;</span></span><a class="headerlink" href="#genex:TARGET_LINKER_IMPORT_FILE_PREFIX" title="Permalink to this definition">¶</a></dt>
<dd><div class="versionadded">
<p><span class="versionmodified added">New in version 3.27.</span></p>
</div>
<p>Prefix of the import file used to link target <code class="docutils literal notranslate"><span class="pre">tgt</span></code>.</p>
<p>See also the <span class="target" id="index-2-prop_tgt:IMPORT_PREFIX"></span><a class="reference internal" href="../prop_tgt/IMPORT_PREFIX.html#prop_tgt:IMPORT_PREFIX" title="IMPORT_PREFIX"><code class="xref cmake cmake-prop_tgt docutils literal notranslate"><span class="pre">IMPORT_PREFIX</span></code></a> target property.</p>
<p>Note that <code class="docutils literal notranslate"><span class="pre">tgt</span></code> is not added as a dependency of the target this
expression is evaluated on.</p>
</dd></dl>

<dl class="cmake genex">
<dt class="sig sig-object cmake" id="genex:TARGET_LINKER_IMPORT_FILE_SUFFIX">
<span class="sig-name descname"><span class="pre">$&lt;TARGET_LINKER_IMPORT_FILE_SUFFIX:tgt&gt;</span></span><a class="headerlink" href="#genex:TARGET_LINKER_IMPORT_FILE_SUFFIX" title="Permalink to this definition">¶</a></dt>
<dd><div class="versionadded">
<p><span class="versionmodified added">New in version 3.27.</span></p>
</div>
<p>Suffix of the import file used to link target <code class="docutils literal notranslate"><span class="pre">tgt</span></code>.</p>
<p>The suffix corresponds to the file extension (such as &quot;.lib&quot; or &quot;.tbd&quot;).</p>
<p>See also the <span class="target" id="index-2-prop_tgt:IMPORT_SUFFIX"></span><a class="reference internal" href="../prop_tgt/IMPORT_SUFFIX.html#prop_tgt:IMPORT_SUFFIX" title="IMPORT_SUFFIX"><code class="xref cmake cmake-prop_tgt docutils literal notranslate"><span class="pre">IMPORT_SUFFIX</span></code></a> target property.</p>
<p>Note that <code class="docutils literal notranslate"><span class="pre">tgt</span></code> is not added as a dependency of the target this
expression is evaluated on.</p>
</dd></dl>

<dl class="cmake genex">
<dt class="sig sig-object cmake" id="genex:TARGET_LINKER_IMPORT_FILE_NAME">
<span class="sig-name descname"><span class="pre">$&lt;TARGET_LINKER_IMPORT_FILE_NAME:tgt&gt;</span></span><a class="headerlink" href="#genex:TARGET_LINKER_IMPORT_FILE_NAME" title="Permalink to this definition">¶</a></dt>
<dd><div class="versionadded">
<p><span class="versionmodified added">New in version 3.27.</span></p>
</div>
<p>Name of the import file used to link target <code class="docutils literal notranslate"><span class="pre">tgt</span></code>.</p>
<p>Note that <code class="docutils literal notranslate"><span class="pre">tgt</span></code> is not added as a dependency of the target this
expression is evaluated on.</p>
</dd></dl>

<dl class="cmake genex">
<dt class="sig sig-object cmake" id="genex:TARGET_LINKER_IMPORT_FILE_DIR">
<span class="sig-name descname"><span class="pre">$&lt;TARGET_LINKER_IMPORT_FILE_DIR:tgt&gt;</span></span><a class="headerlink" href="#genex:TARGET_LINKER_IMPORT_FILE_DIR" title="Permalink to this definition">¶</a></dt>
<dd><div class="versionadded">
<p><span class="versionmodified added">New in version 3.27.</span></p>
</div>
<p>Directory of the import file used to link target <code class="docutils literal notranslate"><span class="pre">tgt</span></code>.</p>
<p>Note that <code class="docutils literal notranslate"><span class="pre">tgt</span></code> is not added as a dependency of the target this
expression is evaluated on.</p>
</dd></dl>

<dl class="cmake genex">
<dt class="sig sig-object cmake" id="genex:TARGET_SONAME_FILE">
<span class="sig-name descname"><span class="pre">$&lt;TARGET_SONAME_FILE:tgt&gt;</span></span><a class="headerlink" href="#genex:TARGET_SONAME_FILE" title="Permalink to this definition">¶</a></dt>
<dd><p>File with soname (<code class="docutils literal notranslate"><span class="pre">.so.3</span></code>) where <code class="docutils literal notranslate"><span class="pre">tgt</span></code> is the name of a target.</p>
</dd></dl>

<dl class="cmake genex">
<dt class="sig sig-object cmake" id="genex:TARGET_SONAME_FILE_NAME">
<span class="sig-name descname"><span class="pre">$&lt;TARGET_SONAME_FILE_NAME:tgt&gt;</span></span><a class="headerlink" href="#genex:TARGET_SONAME_FILE_NAME" title="Permalink to this definition">¶</a></dt>
<dd><p>Name of file with soname (<code class="docutils literal notranslate"><span class="pre">.so.3</span></code>).</p>
<p>Note that <code class="docutils literal notranslate"><span class="pre">tgt</span></code> is not added as a dependency of the target this
expression is evaluated on (see policy <span class="target" id="index-4-policy:CMP0112"></span><a class="reference internal" href="../policy/CMP0112.html#policy:CMP0112" title="CMP0112"><code class="xref cmake cmake-policy docutils literal notranslate"><span class="pre">CMP0112</span></code></a>).</p>
</dd></dl>

<dl class="cmake genex">
<dt class="sig sig-object cmake" id="genex:TARGET_SONAME_FILE_DIR">
<span class="sig-name descname"><span class="pre">$&lt;TARGET_SONAME_FILE_DIR:tgt&gt;</span></span><a class="headerlink" href="#genex:TARGET_SONAME_FILE_DIR" title="Permalink to this definition">¶</a></dt>
<dd><p>Directory of file with soname (<code class="docutils literal notranslate"><span class="pre">.so.3</span></code>).</p>
<p>Note that <code class="docutils literal notranslate"><span class="pre">tgt</span></code> is not added as a dependency of the target this
expression is evaluated on (see policy <span class="target" id="index-5-policy:CMP0112"></span><a class="reference internal" href="../policy/CMP0112.html#policy:CMP0112" title="CMP0112"><code class="xref cmake cmake-policy docutils literal notranslate"><span class="pre">CMP0112</span></code></a>).</p>
</dd></dl>

<dl class="cmake genex">
<dt class="sig sig-object cmake" id="genex:TARGET_SONAME_IMPORT_FILE">
<span class="sig-name descname"><span class="pre">$&lt;TARGET_SONAME_IMPORT_FILE:tgt&gt;</span></span><a class="headerlink" href="#genex:TARGET_SONAME_IMPORT_FILE" title="Permalink to this definition">¶</a></dt>
<dd><div class="versionadded">
<p><span class="versionmodified added">New in version 3.27.</span></p>
</div>
<p>Import file with soname (<code class="docutils literal notranslate"><span class="pre">.3.tbd</span></code>) where <code class="docutils literal notranslate"><span class="pre">tgt</span></code> is the name of a target.</p>
</dd></dl>

<dl class="cmake genex">
<dt class="sig sig-object cmake" id="genex:TARGET_SONAME_IMPORT_FILE_NAME">
<span class="sig-name descname"><span class="pre">$&lt;TARGET_SONAME_IMPORT_FILE_NAME:tgt&gt;</span></span><a class="headerlink" href="#genex:TARGET_SONAME_IMPORT_FILE_NAME" title="Permalink to this definition">¶</a></dt>
<dd><div class="versionadded">
<p><span class="versionmodified added">New in version 3.27.</span></p>
</div>
<p>Name of the import file with soname (<code class="docutils literal notranslate"><span class="pre">.3.tbd</span></code>).</p>
<p>Note that <code class="docutils literal notranslate"><span class="pre">tgt</span></code> is not added as a dependency of the target this
expression is evaluated on.</p>
</dd></dl>

<dl class="cmake genex">
<dt class="sig sig-object cmake" id="genex:TARGET_SONAME_IMPORT_FILE_DIR">
<span class="sig-name descname"><span class="pre">$&lt;TARGET_SONAME_IMPORT_FILE_DIR:tgt&gt;</span></span><a class="headerlink" href="#genex:TARGET_SONAME_IMPORT_FILE_DIR" title="Permalink to this definition">¶</a></dt>
<dd><div class="versionadded">
<p><span class="versionmodified added">New in version 3.27.</span></p>
</div>
<p>Directory of the import file with soname (<code class="docutils literal notranslate"><span class="pre">.3.tbd</span></code>).</p>
<p>Note that <code class="docutils literal notranslate"><span class="pre">tgt</span></code> is not added as a dependency of the target this
expression is evaluated on.</p>
</dd></dl>

<dl class="cmake genex">
<dt class="sig sig-object cmake" id="genex:TARGET_PDB_FILE">
<span class="sig-name descname"><span class="pre">$&lt;TARGET_PDB_FILE:tgt&gt;</span></span><a class="headerlink" href="#genex:TARGET_PDB_FILE" title="Permalink to this definition">¶</a></dt>
<dd><div class="versionadded">
<p><span class="versionmodified added">New in version 3.1.</span></p>
</div>
<p>Full path to the linker generated program database file (.pdb)
where <code class="docutils literal notranslate"><span class="pre">tgt</span></code> is the name of a target.</p>
<p>See also the <span class="target" id="index-0-prop_tgt:PDB_NAME"></span><a class="reference internal" href="../prop_tgt/PDB_NAME.html#prop_tgt:PDB_NAME" title="PDB_NAME"><code class="xref cmake cmake-prop_tgt docutils literal notranslate"><span class="pre">PDB_NAME</span></code></a> and <span class="target" id="index-0-prop_tgt:PDB_OUTPUT_DIRECTORY"></span><a class="reference internal" href="../prop_tgt/PDB_OUTPUT_DIRECTORY.html#prop_tgt:PDB_OUTPUT_DIRECTORY" title="PDB_OUTPUT_DIRECTORY"><code class="xref cmake cmake-prop_tgt docutils literal notranslate"><span class="pre">PDB_OUTPUT_DIRECTORY</span></code></a>
target properties and their configuration specific variants
<span class="target" id="index-0-prop_tgt:PDB_NAME_&lt;CONFIG&gt;"></span><a class="reference internal" href="../prop_tgt/PDB_NAME_CONFIG.html#prop_tgt:PDB_NAME_&lt;CONFIG&gt;" title="PDB_NAME_&lt;CONFIG&gt;"><code class="xref cmake cmake-prop_tgt docutils literal notranslate"><span class="pre">PDB_NAME_&lt;CONFIG&gt;</span></code></a> and <span class="target" id="index-0-prop_tgt:PDB_OUTPUT_DIRECTORY_&lt;CONFIG&gt;"></span><a class="reference internal" href="../prop_tgt/PDB_OUTPUT_DIRECTORY_CONFIG.html#prop_tgt:PDB_OUTPUT_DIRECTORY_&lt;CONFIG&gt;" title="PDB_OUTPUT_DIRECTORY_&lt;CONFIG&gt;"><code class="xref cmake cmake-prop_tgt docutils literal notranslate"><span class="pre">PDB_OUTPUT_DIRECTORY_&lt;CONFIG&gt;</span></code></a>.</p>
</dd></dl>

<dl class="cmake genex">
<dt class="sig sig-object cmake" id="genex:TARGET_PDB_FILE_BASE_NAME">
<span class="sig-name descname"><span class="pre">$&lt;TARGET_PDB_FILE_BASE_NAME:tgt&gt;</span></span><a class="headerlink" href="#genex:TARGET_PDB_FILE_BASE_NAME" title="Permalink to this definition">¶</a></dt>
<dd><div class="versionadded">
<p><span class="versionmodified added">New in version 3.15.</span></p>
</div>
<p>Base name of the linker generated program database file (.pdb)
where <code class="docutils literal notranslate"><span class="pre">tgt</span></code> is the name of a target.</p>
<p>The base name corresponds to the target PDB file name (see
<code class="docutils literal notranslate"><span class="pre">$&lt;TARGET_PDB_FILE_NAME:tgt&gt;</span></code>) without prefix and suffix. For example,
if target file name is <code class="docutils literal notranslate"><span class="pre">base.pdb</span></code>, the base name is <code class="docutils literal notranslate"><span class="pre">base</span></code>.</p>
<p>See also the <span class="target" id="index-1-prop_tgt:PDB_NAME"></span><a class="reference internal" href="../prop_tgt/PDB_NAME.html#prop_tgt:PDB_NAME" title="PDB_NAME"><code class="xref cmake cmake-prop_tgt docutils literal notranslate"><span class="pre">PDB_NAME</span></code></a> target property and its configuration
specific variant <span class="target" id="index-1-prop_tgt:PDB_NAME_&lt;CONFIG&gt;"></span><a class="reference internal" href="../prop_tgt/PDB_NAME_CONFIG.html#prop_tgt:PDB_NAME_&lt;CONFIG&gt;" title="PDB_NAME_&lt;CONFIG&gt;"><code class="xref cmake cmake-prop_tgt docutils literal notranslate"><span class="pre">PDB_NAME_&lt;CONFIG&gt;</span></code></a>.</p>
<p>The <span class="target" id="index-5-prop_tgt:&lt;CONFIG&gt;_POSTFIX"></span><a class="reference internal" href="../prop_tgt/CONFIG_POSTFIX.html#prop_tgt:&lt;CONFIG&gt;_POSTFIX" title="&lt;CONFIG&gt;_POSTFIX"><code class="xref cmake cmake-prop_tgt docutils literal notranslate"><span class="pre">&lt;CONFIG&gt;_POSTFIX</span></code></a> and <span class="target" id="index-5-prop_tgt:DEBUG_POSTFIX"></span><a class="reference internal" href="../prop_tgt/DEBUG_POSTFIX.html#prop_tgt:DEBUG_POSTFIX" title="DEBUG_POSTFIX"><code class="xref cmake cmake-prop_tgt docutils literal notranslate"><span class="pre">DEBUG_POSTFIX</span></code></a> target
properties can also be considered.</p>
<p>Note that <code class="docutils literal notranslate"><span class="pre">tgt</span></code> is not added as a dependency of the target this
expression is evaluated on.</p>
</dd></dl>

<dl class="cmake genex">
<dt class="sig sig-object cmake" id="genex:TARGET_PDB_FILE_NAME">
<span class="sig-name descname"><span class="pre">$&lt;TARGET_PDB_FILE_NAME:tgt&gt;</span></span><a class="headerlink" href="#genex:TARGET_PDB_FILE_NAME" title="Permalink to this definition">¶</a></dt>
<dd><div class="versionadded">
<p><span class="versionmodified added">New in version 3.1.</span></p>
</div>
<p>Name of the linker generated program database file (.pdb).</p>
<p>Note that <code class="docutils literal notranslate"><span class="pre">tgt</span></code> is not added as a dependency of the target this
expression is evaluated on (see policy <span class="target" id="index-6-policy:CMP0112"></span><a class="reference internal" href="../policy/CMP0112.html#policy:CMP0112" title="CMP0112"><code class="xref cmake cmake-policy docutils literal notranslate"><span class="pre">CMP0112</span></code></a>).</p>
</dd></dl>

<dl class="cmake genex">
<dt class="sig sig-object cmake" id="genex:TARGET_PDB_FILE_DIR">
<span class="sig-name descname"><span class="pre">$&lt;TARGET_PDB_FILE_DIR:tgt&gt;</span></span><a class="headerlink" href="#genex:TARGET_PDB_FILE_DIR" title="Permalink to this definition">¶</a></dt>
<dd><div class="versionadded">
<p><span class="versionmodified added">New in version 3.1.</span></p>
</div>
<p>Directory of the linker generated program database file (.pdb).</p>
<p>Note that <code class="docutils literal notranslate"><span class="pre">tgt</span></code> is not added as a dependency of the target this
expression is evaluated on (see policy <span class="target" id="index-7-policy:CMP0112"></span><a class="reference internal" href="../policy/CMP0112.html#policy:CMP0112" title="CMP0112"><code class="xref cmake cmake-policy docutils literal notranslate"><span class="pre">CMP0112</span></code></a>).</p>
</dd></dl>

<dl class="cmake genex">
<dt class="sig sig-object cmake" id="genex:TARGET_BUNDLE_DIR">
<span class="sig-name descname"><span class="pre">$&lt;TARGET_BUNDLE_DIR:tgt&gt;</span></span><a class="headerlink" href="#genex:TARGET_BUNDLE_DIR" title="Permalink to this definition">¶</a></dt>
<dd><div class="versionadded">
<p><span class="versionmodified added">New in version 3.9.</span></p>
</div>
<p>Full path to the bundle directory (<code class="docutils literal notranslate"><span class="pre">/path/to/my.app</span></code>,
<code class="docutils literal notranslate"><span class="pre">/path/to/my.framework</span></code>, or <code class="docutils literal notranslate"><span class="pre">/path/to/my.bundle</span></code>),
where <code class="docutils literal notranslate"><span class="pre">tgt</span></code> is the name of a target.</p>
<p>Note that <code class="docutils literal notranslate"><span class="pre">tgt</span></code> is not added as a dependency of the target this
expression is evaluated on (see policy <span class="target" id="index-8-policy:CMP0112"></span><a class="reference internal" href="../policy/CMP0112.html#policy:CMP0112" title="CMP0112"><code class="xref cmake cmake-policy docutils literal notranslate"><span class="pre">CMP0112</span></code></a>).</p>
</dd></dl>

<dl class="cmake genex">
<dt class="sig sig-object cmake" id="genex:TARGET_BUNDLE_DIR_NAME">
<span class="sig-name descname"><span class="pre">$&lt;TARGET_BUNDLE_DIR_NAME:tgt&gt;</span></span><a class="headerlink" href="#genex:TARGET_BUNDLE_DIR_NAME" title="Permalink to this definition">¶</a></dt>
<dd><div class="versionadded">
<p><span class="versionmodified added">New in version 3.24.</span></p>
</div>
<p>Name of the bundle directory (<code class="docutils literal notranslate"><span class="pre">my.app</span></code>, <code class="docutils literal notranslate"><span class="pre">my.framework</span></code>, or
<code class="docutils literal notranslate"><span class="pre">my.bundle</span></code>), where <code class="docutils literal notranslate"><span class="pre">tgt</span></code> is the name of a target.</p>
<p>Note that <code class="docutils literal notranslate"><span class="pre">tgt</span></code> is not added as a dependency of the target this
expression is evaluated on (see policy <span class="target" id="index-9-policy:CMP0112"></span><a class="reference internal" href="../policy/CMP0112.html#policy:CMP0112" title="CMP0112"><code class="xref cmake cmake-policy docutils literal notranslate"><span class="pre">CMP0112</span></code></a>).</p>
</dd></dl>

<dl class="cmake genex">
<dt class="sig sig-object cmake" id="genex:TARGET_BUNDLE_CONTENT_DIR">
<span class="sig-name descname"><span class="pre">$&lt;TARGET_BUNDLE_CONTENT_DIR:tgt&gt;</span></span><a class="headerlink" href="#genex:TARGET_BUNDLE_CONTENT_DIR" title="Permalink to this definition">¶</a></dt>
<dd><div class="versionadded">
<p><span class="versionmodified added">New in version 3.9.</span></p>
</div>
<p>Full path to the bundle content directory where <code class="docutils literal notranslate"><span class="pre">tgt</span></code> is the name of a
target.  For the macOS SDK it leads to <code class="docutils literal notranslate"><span class="pre">/path/to/my.app/Contents</span></code>,
<code class="docutils literal notranslate"><span class="pre">/path/to/my.framework</span></code>, or <code class="docutils literal notranslate"><span class="pre">/path/to/my.bundle/Contents</span></code>.
For all other SDKs (e.g. iOS) it leads to <code class="docutils literal notranslate"><span class="pre">/path/to/my.app</span></code>,
<code class="docutils literal notranslate"><span class="pre">/path/to/my.framework</span></code>, or <code class="docutils literal notranslate"><span class="pre">/path/to/my.bundle</span></code> due to the flat
bundle structure.</p>
<p>Note that <code class="docutils literal notranslate"><span class="pre">tgt</span></code> is not added as a dependency of the target this
expression is evaluated on (see policy <span class="target" id="index-10-policy:CMP0112"></span><a class="reference internal" href="../policy/CMP0112.html#policy:CMP0112" title="CMP0112"><code class="xref cmake cmake-policy docutils literal notranslate"><span class="pre">CMP0112</span></code></a>).</p>
</dd></dl>

<dl class="cmake genex">
<dt class="sig sig-object cmake" id="genex:TARGET_RUNTIME_DLLS">
<span class="sig-name descname"><span class="pre">$&lt;TARGET_RUNTIME_DLLS:tgt&gt;</span></span><a class="headerlink" href="#genex:TARGET_RUNTIME_DLLS" title="Permalink to this definition">¶</a></dt>
<dd><div class="versionadded">
<p><span class="versionmodified added">New in version 3.21.</span></p>
</div>
<p>List of DLLs that the target depends on at runtime. This is determined by
the locations of all the <code class="docutils literal notranslate"><span class="pre">SHARED</span></code> targets in the target's transitive
dependencies. If only the directories of the DLLs are needed, see the
<span class="target" id="index-0-genex:TARGET_RUNTIME_DLL_DIRS"></span><a class="reference internal" href="#genex:TARGET_RUNTIME_DLL_DIRS" title="TARGET_RUNTIME_DLL_DIRS"><code class="xref cmake cmake-genex docutils literal notranslate"><span class="pre">TARGET_RUNTIME_DLL_DIRS</span></code></a> generator expression.
Using this generator expression on targets other than
executables, <code class="docutils literal notranslate"><span class="pre">SHARED</span></code> libraries, and <code class="docutils literal notranslate"><span class="pre">MODULE</span></code> libraries is an error.
<strong>On non-DLL platforms, this expression always evaluates to an empty string</strong>.</p>
<p>This generator expression can be used to copy all of the DLLs that a target
depends on into its output directory in a <code class="docutils literal notranslate"><span class="pre">POST_BUILD</span></code> custom command using
the <a class="reference internal" href="cmake.1.html#cmdoption-cmake-E-arg-copy"><code class="xref std std-option docutils literal notranslate"><span class="pre">cmake</span> <span class="pre">-E</span> <span class="pre">copy</span> <span class="pre">-t</span></code></a> command. For example:</p>
<div class="highlight-cmake notranslate"><div class="highlight"><pre><span></span><span class="nf">find_package(</span><span class="nb">foo</span><span class="w"> </span><span class="no">CONFIG</span><span class="w"> </span><span class="no">REQUIRED</span><span class="nf">)</span><span class="w"> </span><span class="c"># package generated by install(EXPORT)</span>

<span class="nf">add_executable(</span><span class="nb">exe</span><span class="w"> </span><span class="nb">main.c</span><span class="nf">)</span>
<span class="nf">target_link_libraries(</span><span class="nb">exe</span><span class="w"> </span><span class="no">PRIVATE</span><span class="w"> </span><span class="nb">foo</span><span class="o">::</span><span class="nb">foo</span><span class="w"> </span><span class="nb">foo</span><span class="o">::</span><span class="nb">bar</span><span class="nf">)</span>
<span class="nf">add_custom_command(</span><span class="no">TARGET</span><span class="w"> </span><span class="nb">exe</span><span class="w"> </span><span class="no">POST_BUILD</span>
<span class="w">  </span><span class="no">COMMAND</span><span class="w"> </span><span class="o">${</span><span class="nt">CMAKE_COMMAND</span><span class="o">}</span><span class="w"> </span><span class="p">-</span><span class="no">E</span><span class="w"> </span><span class="nb">copy</span><span class="w"> </span><span class="p">-</span><span class="nb">t</span><span class="w"> </span><span class="o">$&lt;</span><span class="no">TARGET_FILE_DIR</span><span class="o">:</span><span class="nb">exe</span><span class="o">&gt;</span><span class="w"> </span><span class="o">$&lt;</span><span class="no">TARGET_RUNTIME_DLLS</span><span class="o">:</span><span class="nb">exe</span><span class="o">&gt;</span>
<span class="w">  </span><span class="no">COMMAND_EXPAND_LISTS</span>
<span class="nf">)</span>
</pre></div>
</div>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p><a class="reference internal" href="cmake-buildsystem.7.html#imported-targets"><span class="std std-ref">Imported Targets</span></a> are supported only if they know the location
of their <code class="docutils literal notranslate"><span class="pre">.dll</span></code> files.  An imported <code class="docutils literal notranslate"><span class="pre">SHARED</span></code> library must have
<span class="target" id="index-0-prop_tgt:IMPORTED_LOCATION"></span><a class="reference internal" href="../prop_tgt/IMPORTED_LOCATION.html#prop_tgt:IMPORTED_LOCATION" title="IMPORTED_LOCATION"><code class="xref cmake cmake-prop_tgt docutils literal notranslate"><span class="pre">IMPORTED_LOCATION</span></code></a> set to its <code class="docutils literal notranslate"><span class="pre">.dll</span></code> file.  See the
<a class="reference internal" href="../command/add_library.html#add-library-imported-libraries"><span class="std std-ref">add_library imported libraries</span></a>
section for details.  Many <a class="reference internal" href="cmake-developer.7.html#find-modules"><span class="std std-ref">Find Modules</span></a> produce imported targets
with the <code class="docutils literal notranslate"><span class="pre">UNKNOWN</span></code> type and therefore will be ignored.</p>
</div>
<p>On platforms that support runtime paths (<code class="docutils literal notranslate"><span class="pre">RPATH</span></code>), refer to the
<span class="target" id="index-0-prop_tgt:INSTALL_RPATH"></span><a class="reference internal" href="../prop_tgt/INSTALL_RPATH.html#prop_tgt:INSTALL_RPATH" title="INSTALL_RPATH"><code class="xref cmake cmake-prop_tgt docutils literal notranslate"><span class="pre">INSTALL_RPATH</span></code></a> target property.
On Apple platforms, refer to the <span class="target" id="index-0-prop_tgt:INSTALL_NAME_DIR"></span><a class="reference internal" href="../prop_tgt/INSTALL_NAME_DIR.html#prop_tgt:INSTALL_NAME_DIR" title="INSTALL_NAME_DIR"><code class="xref cmake cmake-prop_tgt docutils literal notranslate"><span class="pre">INSTALL_NAME_DIR</span></code></a> target property.</p>
</dd></dl>

<dl class="cmake genex">
<dt class="sig sig-object cmake" id="genex:TARGET_RUNTIME_DLL_DIRS">
<span class="sig-name descname"><span class="pre">$&lt;TARGET_RUNTIME_DLL_DIRS:tgt&gt;</span></span><a class="headerlink" href="#genex:TARGET_RUNTIME_DLL_DIRS" title="Permalink to this definition">¶</a></dt>
<dd><div class="versionadded">
<p><span class="versionmodified added">New in version 3.27.</span></p>
</div>
<p>List of the directories which contain the DLLs that the target depends on at
runtime (see <span class="target" id="index-0-genex:TARGET_RUNTIME_DLLS"></span><a class="reference internal" href="#genex:TARGET_RUNTIME_DLLS" title="TARGET_RUNTIME_DLLS"><code class="xref cmake cmake-genex docutils literal notranslate"><span class="pre">TARGET_RUNTIME_DLLS</span></code></a>). This is determined by
the locations of all the <code class="docutils literal notranslate"><span class="pre">SHARED</span></code> targets in the target's transitive
dependencies. Using this generator expression on targets other than
executables, <code class="docutils literal notranslate"><span class="pre">SHARED</span></code> libraries, and <code class="docutils literal notranslate"><span class="pre">MODULE</span></code> libraries is an error.
<strong>On non-DLL platforms, this expression always evaluates to an empty string</strong>.</p>
<p>This generator expression can e.g. be used to create a batch file using
<span class="target" id="index-1-command:file"></span><a class="reference internal" href="../command/file.html#generate" title="file(generate)"><code class="xref cmake cmake-command docutils literal notranslate"><span class="pre">file(GENERATE)</span></code></a> which sets the PATH environment variable accordingly.</p>
</dd></dl>

</section>
<section id="export-and-install-expressions">
<h3><a class="toc-backref" href="#id34" role="doc-backlink">Export And Install Expressions</a><a class="headerlink" href="#export-and-install-expressions" title="Permalink to this heading">¶</a></h3>
<dl class="cmake genex">
<dt class="sig sig-object cmake" id="genex:INSTALL_INTERFACE">
<span class="sig-name descname"><span class="pre">$&lt;INSTALL_INTERFACE:...&gt;</span></span><a class="headerlink" href="#genex:INSTALL_INTERFACE" title="Permalink to this definition">¶</a></dt>
<dd><p>Content of <code class="docutils literal notranslate"><span class="pre">...</span></code> when the property is exported using
<span class="target" id="index-2-command:install"></span><a class="reference internal" href="../command/install.html#export" title="install(export)"><code class="xref cmake cmake-command docutils literal notranslate"><span class="pre">install(EXPORT)</span></code></a>, and empty otherwise.</p>
</dd></dl>

<dl class="cmake genex">
<dt class="sig sig-object cmake" id="genex:BUILD_INTERFACE">
<span class="sig-name descname"><span class="pre">$&lt;BUILD_INTERFACE:...&gt;</span></span><a class="headerlink" href="#genex:BUILD_INTERFACE" title="Permalink to this definition">¶</a></dt>
<dd><p>Content of <code class="docutils literal notranslate"><span class="pre">...</span></code> when the property is exported using <span class="target" id="index-0-command:export"></span><a class="reference internal" href="../command/export.html#command:export" title="export"><code class="xref cmake cmake-command docutils literal notranslate"><span class="pre">export()</span></code></a>, or
when the target is used by another target in the same buildsystem. Expands to
the empty string otherwise.</p>
</dd></dl>

<dl class="cmake genex">
<dt class="sig sig-object cmake" id="genex:BUILD_LOCAL_INTERFACE">
<span class="sig-name descname"><span class="pre">$&lt;BUILD_LOCAL_INTERFACE:...&gt;</span></span><a class="headerlink" href="#genex:BUILD_LOCAL_INTERFACE" title="Permalink to this definition">¶</a></dt>
<dd><div class="versionadded">
<p><span class="versionmodified added">New in version 3.26.</span></p>
</div>
<p>Content of <code class="docutils literal notranslate"><span class="pre">...</span></code> when the target is used by another target in the same
buildsystem. Expands to the empty string otherwise.</p>
</dd></dl>

<dl class="cmake genex">
<dt class="sig sig-object cmake" id="genex:INSTALL_PREFIX">
<span class="sig-name descname"><span class="pre">$&lt;INSTALL_PREFIX&gt;</span></span><a class="headerlink" href="#genex:INSTALL_PREFIX" title="Permalink to this definition">¶</a></dt>
<dd><p>Content of the install prefix when the target is exported via
<span class="target" id="index-3-command:install"></span><a class="reference internal" href="../command/install.html#export" title="install(export)"><code class="xref cmake cmake-command docutils literal notranslate"><span class="pre">install(EXPORT)</span></code></a>, or when evaluated in the
<span class="target" id="index-1-prop_tgt:INSTALL_NAME_DIR"></span><a class="reference internal" href="../prop_tgt/INSTALL_NAME_DIR.html#prop_tgt:INSTALL_NAME_DIR" title="INSTALL_NAME_DIR"><code class="xref cmake cmake-prop_tgt docutils literal notranslate"><span class="pre">INSTALL_NAME_DIR</span></code></a> property, the <code class="docutils literal notranslate"><span class="pre">INSTALL_NAME_DIR</span></code> argument of
<span class="target" id="index-4-command:install"></span><a class="reference internal" href="../command/install.html#runtime-dependency-set" title="install(runtime_dependency_set)"><code class="xref cmake cmake-command docutils literal notranslate"><span class="pre">install(RUNTIME_DEPENDENCY_SET)</span></code></a>, the code argument of
<span class="target" id="index-5-command:install"></span><a class="reference internal" href="../command/install.html#code" title="install(code)"><code class="xref cmake cmake-command docutils literal notranslate"><span class="pre">install(CODE)</span></code></a>, or the file argument of <span class="target" id="index-6-command:install"></span><a class="reference internal" href="../command/install.html#script" title="install(script)"><code class="xref cmake cmake-command docutils literal notranslate"><span class="pre">install(SCRIPT)</span></code></a>,
and empty otherwise.</p>
</dd></dl>

</section>
<section id="multi-level-expression-evaluation">
<h3><a class="toc-backref" href="#id35" role="doc-backlink">Multi-level Expression Evaluation</a><a class="headerlink" href="#multi-level-expression-evaluation" title="Permalink to this heading">¶</a></h3>
<dl class="cmake genex">
<dt class="sig sig-object cmake" id="genex:GENEX_EVAL">
<span class="sig-name descname"><span class="pre">$&lt;GENEX_EVAL:expr&gt;</span></span><a class="headerlink" href="#genex:GENEX_EVAL" title="Permalink to this definition">¶</a></dt>
<dd><div class="versionadded">
<p><span class="versionmodified added">New in version 3.12.</span></p>
</div>
<p>Content of <code class="docutils literal notranslate"><span class="pre">expr</span></code> evaluated as a generator expression in the current
context. This enables consumption of generator expressions whose
evaluation results itself in generator expressions.</p>
</dd></dl>

<dl class="cmake genex">
<dt class="sig sig-object cmake" id="genex:TARGET_GENEX_EVAL">
<span class="sig-name descname"><span class="pre">$&lt;TARGET_GENEX_EVAL:tgt,expr&gt;</span></span><a class="headerlink" href="#genex:TARGET_GENEX_EVAL" title="Permalink to this definition">¶</a></dt>
<dd><div class="versionadded">
<p><span class="versionmodified added">New in version 3.12.</span></p>
</div>
<p>Content of <code class="docutils literal notranslate"><span class="pre">expr</span></code> evaluated as a generator expression in the context of
<code class="docutils literal notranslate"><span class="pre">tgt</span></code> target. This enables consumption of custom target properties that
themselves contain generator expressions.</p>
<p>Having the capability to evaluate generator expressions is very useful when
you want to manage custom properties supporting generator expressions.
For example:</p>
<div class="highlight-cmake notranslate"><div class="highlight"><pre><span></span><span class="nf">add_library(</span><span class="nb">foo</span><span class="w"> </span><span class="p">...</span><span class="nf">)</span>

<span class="nf">set_property(</span><span class="no">TARGET</span><span class="w"> </span><span class="nb">foo</span><span class="w"> </span><span class="no">PROPERTY</span>
<span class="w">  </span><span class="no">CUSTOM_KEYS</span><span class="w"> </span><span class="o">$&lt;$&lt;</span><span class="no">CONFIG</span><span class="o">:</span><span class="no">DEBUG</span><span class="o">&gt;:</span><span class="no">FOO_EXTRA_THINGS</span><span class="o">&gt;</span>
<span class="nf">)</span>

<span class="nf">add_custom_target(</span><span class="nb">printFooKeys</span>
<span class="w">  </span><span class="no">COMMAND</span><span class="w"> </span><span class="o">${</span><span class="nt">CMAKE_COMMAND</span><span class="o">}</span><span class="w"> </span><span class="p">-</span><span class="no">E</span><span class="w"> </span><span class="nb">echo</span><span class="w"> </span><span class="o">$&lt;</span><span class="no">TARGET_PROPERTY</span><span class="o">:</span><span class="nb">foo</span><span class="p">,</span><span class="no">CUSTOM_KEYS</span><span class="o">&gt;</span>
<span class="nf">)</span>
</pre></div>
</div>
<p>This naive implementation of the <code class="docutils literal notranslate"><span class="pre">printFooKeys</span></code> custom command is wrong
because <code class="docutils literal notranslate"><span class="pre">CUSTOM_KEYS</span></code> target property is not evaluated and the content
is passed as is (i.e. <code class="docutils literal notranslate"><span class="pre">$&lt;$&lt;CONFIG:DEBUG&gt;:FOO_EXTRA_THINGS&gt;</span></code>).</p>
<p>To have the expected result (i.e. <code class="docutils literal notranslate"><span class="pre">FOO_EXTRA_THINGS</span></code> if config is
<code class="docutils literal notranslate"><span class="pre">Debug</span></code>), it is required to evaluate the output of
<code class="docutils literal notranslate"><span class="pre">$&lt;TARGET_PROPERTY:foo,CUSTOM_KEYS&gt;</span></code>:</p>
<div class="highlight-cmake notranslate"><div class="highlight"><pre><span></span><span class="nf">add_custom_target(</span><span class="nb">printFooKeys</span>
<span class="w">  </span><span class="no">COMMAND</span><span class="w"> </span><span class="o">${</span><span class="nt">CMAKE_COMMAND</span><span class="o">}</span><span class="w"> </span><span class="p">-</span><span class="no">E</span>
<span class="w">    </span><span class="nb">echo</span><span class="w"> </span><span class="o">$&lt;</span><span class="no">TARGET_GENEX_EVAL</span><span class="o">:</span><span class="nb">foo</span><span class="p">,</span><span class="o">$&lt;</span><span class="no">TARGET_PROPERTY</span><span class="o">:</span><span class="nb">foo</span><span class="p">,</span><span class="no">CUSTOM_KEYS</span><span class="o">&gt;&gt;</span>
<span class="nf">)</span>
</pre></div>
</div>
</dd></dl>

</section>
<section id="escaped-characters">
<h3><a class="toc-backref" href="#id36" role="doc-backlink">Escaped Characters</a><a class="headerlink" href="#escaped-characters" title="Permalink to this heading">¶</a></h3>
<p>These expressions evaluate to specific string literals. Use them in place of
the actual string literal where you need to prevent them from having their
special meaning.</p>
<dl class="cmake genex">
<dt class="sig sig-object cmake" id="genex:ANGLE-R">
<span class="sig-name descname"><span class="pre">$&lt;ANGLE-R&gt;</span></span><a class="headerlink" href="#genex:ANGLE-R" title="Permalink to this definition">¶</a></dt>
<dd><p>A literal <code class="docutils literal notranslate"><span class="pre">&gt;</span></code>. Used for example to compare strings that contain a <code class="docutils literal notranslate"><span class="pre">&gt;</span></code>.</p>
</dd></dl>

<dl class="cmake genex">
<dt class="sig sig-object cmake" id="genex:COMMA">
<span class="sig-name descname"><span class="pre">$&lt;COMMA&gt;</span></span><a class="headerlink" href="#genex:COMMA" title="Permalink to this definition">¶</a></dt>
<dd><p>A literal <code class="docutils literal notranslate"><span class="pre">,</span></code>. Used for example to compare strings which contain a <code class="docutils literal notranslate"><span class="pre">,</span></code>.</p>
</dd></dl>

<dl class="cmake genex">
<dt class="sig sig-object cmake" id="genex:SEMICOLON">
<span class="sig-name descname"><span class="pre">$&lt;SEMICOLON&gt;</span></span><a class="headerlink" href="#genex:SEMICOLON" title="Permalink to this definition">¶</a></dt>
<dd><p>A literal <code class="docutils literal notranslate"><span class="pre">;</span></code>. Used to prevent list expansion on an argument with <code class="docutils literal notranslate"><span class="pre">;</span></code>.</p>
</dd></dl>

</section>
<section id="deprecated-expressions">
<h3><a class="toc-backref" href="#id37" role="doc-backlink">Deprecated Expressions</a><a class="headerlink" href="#deprecated-expressions" title="Permalink to this heading">¶</a></h3>
<dl class="cmake genex">
<dt class="sig sig-object cmake" id="genex:CONFIGURATION">
<span class="sig-name descname"><span class="pre">$&lt;CONFIGURATION&gt;</span></span><a class="headerlink" href="#genex:CONFIGURATION" title="Permalink to this definition">¶</a></dt>
<dd><p>Configuration name. Deprecated since CMake 3.0. Use <span class="target" id="index-0-genex:CONFIG"></span><a class="reference internal" href="#genex:CONFIG" title="CONFIG"><code class="xref cmake cmake-genex docutils literal notranslate"><span class="pre">CONFIG</span></code></a> instead.</p>
</dd></dl>

</section>
</section>
</section>


            <div class="clearer"></div>
          </div>
        </div>
      </div>
      <div class="sphinxsidebar" role="navigation" aria-label="main navigation">
        <div class="sphinxsidebarwrapper">
  <div>
    <h3><a href="../index.html">Table of Contents</a></h3>
    <ul>
<li><a class="reference internal" href="#">cmake-generator-expressions(7)</a><ul>
<li><a class="reference internal" href="#introduction">Introduction</a></li>
<li><a class="reference internal" href="#whitespace-and-quoting">Whitespace And Quoting</a></li>
<li><a class="reference internal" href="#debugging">Debugging</a></li>
<li><a class="reference internal" href="#generator-expression-reference">Generator Expression Reference</a><ul>
<li><a class="reference internal" href="#conditional-expressions">Conditional Expressions</a></li>
<li><a class="reference internal" href="#logical-operators">Logical Operators</a></li>
<li><a class="reference internal" href="#primary-comparison-expressions">Primary Comparison Expressions</a><ul>
<li><a class="reference internal" href="#string-comparisons">String Comparisons</a></li>
<li><a class="reference internal" href="#version-comparisons">Version Comparisons</a></li>
</ul>
</li>
<li><a class="reference internal" href="#string-transformations">String Transformations</a></li>
<li><a class="reference internal" href="#list-expressions">List Expressions</a><ul>
<li><a class="reference internal" href="#list-comparisons">List Comparisons</a></li>
<li><a class="reference internal" href="#list-queries">List Queries</a></li>
<li><a class="reference internal" href="#list-transformations">List Transformations</a></li>
<li><a class="reference internal" href="#list-ordering">List Ordering</a></li>
</ul>
</li>
<li><a class="reference internal" href="#path-expressions">Path Expressions</a><ul>
<li><a class="reference internal" href="#path-comparisons">Path Comparisons</a></li>
<li><a class="reference internal" href="#path-queries">Path Queries</a></li>
<li><a class="reference internal" href="#path-decomposition">Path Decomposition</a></li>
<li><a class="reference internal" href="#path-transformations">Path Transformations</a></li>
<li><a class="reference internal" href="#shell-paths">Shell Paths</a></li>
</ul>
</li>
<li><a class="reference internal" href="#configuration-expressions">Configuration Expressions</a></li>
<li><a class="reference internal" href="#toolchain-and-language-expressions">Toolchain And Language Expressions</a><ul>
<li><a class="reference internal" href="#platform">Platform</a></li>
<li><a class="reference internal" href="#compiler-version">Compiler Version</a></li>
<li><a class="reference internal" href="#compiler-language-and-id">Compiler Language And ID</a></li>
<li><a class="reference internal" href="#compile-features">Compile Features</a></li>
<li><a class="reference internal" href="#compile-context">Compile Context</a></li>
<li><a class="reference internal" href="#linker-language-and-id">Linker Language And ID</a></li>
<li><a class="reference internal" href="#link-features">Link Features</a></li>
<li><a class="reference internal" href="#link-context">Link Context</a></li>
</ul>
</li>
<li><a class="reference internal" href="#target-dependent-expressions">Target-Dependent Expressions</a></li>
<li><a class="reference internal" href="#export-and-install-expressions">Export And Install Expressions</a></li>
<li><a class="reference internal" href="#multi-level-expression-evaluation">Multi-level Expression Evaluation</a></li>
<li><a class="reference internal" href="#escaped-characters">Escaped Characters</a></li>
<li><a class="reference internal" href="#deprecated-expressions">Deprecated Expressions</a></li>
</ul>
</li>
</ul>
</li>
</ul>

  </div>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="cmake-file-api.7.html"
                          title="previous chapter">cmake-file-api(7)</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="cmake-generators.7.html"
                          title="next chapter">cmake-generators(7)</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../_sources/manual/cmake-generator-expressions.7.rst.txt"
            rel="nofollow">Show Source</a></li>
    </ul>
   </div>
<div id="searchbox" style="display: none" role="search">
  <h3 id="searchlabel">Quick search</h3>
    <div class="searchformwrapper">
    <form class="search" action="../search.html" method="get">
      <input type="text" name="q" aria-labelledby="searchlabel" autocomplete="off" autocorrect="off" autocapitalize="off" spellcheck="false"/>
      <input type="submit" value="Go" />
    </form>
    </div>
</div>
<script>document.getElementById('searchbox').style.display = "block"</script>
        </div>
      </div>
      <div class="clearer"></div>
    </div>
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             >index</a></li>
        <li class="right" >
          <a href="cmake-generators.7.html" title="cmake-generators(7)"
             >next</a> |</li>
        <li class="right" >
          <a href="cmake-file-api.7.html" title="cmake-file-api(7)"
             >previous</a> |</li>
  <li>
    <img src="../_static/cmake-logo-16.png" alt=""
         style="vertical-align: middle; margin-top: -2px" />
  </li>
  <li>
    <a href="https://cmake.org/">CMake</a> &#187;
  </li>
  <li>
    <a href="../index.html">3.28.1 Documentation</a> &#187;
  </li>

        <li class="nav-item nav-item-this"><a href="">cmake-generator-expressions(7)</a></li> 
      </ul>
    </div>

    <div class="footer" role="contentinfo">
        &#169; Copyright 2000-2023 Kitware, Inc. and Contributors.
      Created using <a href="https://www.sphinx-doc.org/">Sphinx</a> 6.2.1.
    </div>
  </body>
</html>