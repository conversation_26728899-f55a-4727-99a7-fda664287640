
<!DOCTYPE html>

<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="generator" content="Docutils 0.19: https://docutils.sourceforge.io/" />

  <title>FindosgVolume &mdash; CMake 3.28.1 Documentation</title>

    <link rel="stylesheet" type="text/css" href="../_static/pygments.css" />
    <link rel="stylesheet" type="text/css" href="../_static/cmake.css" />
    
    <script data-url_root="../" id="documentation_options" src="../_static/documentation_options.js"></script>
    <script src="../_static/doctools.js"></script>
    <script src="../_static/sphinx_highlight.js"></script>
    
    <link rel="icon" href="../_static/cmake-favicon.ico"/>
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="next" title="FindosgWidget" href="FindosgWidget.html" />
    <link rel="prev" title="FindosgViewer" href="FindosgViewer.html" />
 

  </head><body>

    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             accesskey="I">index</a></li>
        <li class="right" >
          <a href="FindosgWidget.html" title="FindosgWidget"
             accesskey="N">next</a> |</li>
        <li class="right" >
          <a href="FindosgViewer.html" title="FindosgViewer"
             accesskey="P">previous</a> |</li>
  <li>
    <img src="../_static/cmake-logo-16.png" alt=""
         style="vertical-align: middle; margin-top: -2px" />
  </li>
  <li>
    <a href="https://cmake.org/">CMake</a> &#187;
  </li>
  <li>
    <a href="../index.html">3.28.1 Documentation</a> &#187;
  </li>

          <li class="nav-item nav-item-1"><a href="../manual/cmake-modules.7.html" accesskey="U">cmake-modules(7)</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href="">FindosgVolume</a></li> 
      </ul>
    </div>  

    <div class="document">
      <div class="documentwrapper">
        <div class="bodywrapper">
          <div class="body" role="main">
            
  <section id="findosgvolume">
<span id="module:FindosgVolume"></span><h1>FindosgVolume<a class="headerlink" href="#findosgvolume" title="Permalink to this heading">¶</a></h1>
<p>This is part of the <code class="docutils literal notranslate"><span class="pre">Findosg*</span></code> suite used to find OpenSceneGraph
components.  Each component is separate and you must opt in to each
module.  You must also opt into OpenGL and OpenThreads (and Producer
if needed) as these modules won't do it for you.  This is to allow you
control over your own system piece by piece in case you need to opt
out of certain components or change the Find behavior for a particular
module (perhaps because the default <span class="target" id="index-0-module:FindOpenGL"></span><a class="reference internal" href="FindOpenGL.html#module:FindOpenGL" title="FindOpenGL"><code class="xref cmake cmake-module docutils literal notranslate"><span class="pre">FindOpenGL</span></code></a> module doesn't
work with your system as an example).  If you want to use a more
convenient module that includes everything, use the
<span class="target" id="index-0-module:FindOpenSceneGraph"></span><a class="reference internal" href="FindOpenSceneGraph.html#module:FindOpenSceneGraph" title="FindOpenSceneGraph"><code class="xref cmake cmake-module docutils literal notranslate"><span class="pre">FindOpenSceneGraph</span></code></a> instead of the <code class="docutils literal notranslate"><span class="pre">Findosg*.cmake</span></code> modules.</p>
<p>Locate osgVolume This module defines:</p>
<dl class="simple">
<dt><code class="docutils literal notranslate"><span class="pre">OSGVOLUME_FOUND</span></code></dt><dd><p>Was osgVolume found?</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">OSGVOLUME_INCLUDE_DIR</span></code></dt><dd><p>Where to find the headers</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">OSGVOLUME_LIBRARIES</span></code></dt><dd><p>The libraries to link for osgVolume (use this)</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">OSGVOLUME_LIBRARY</span></code></dt><dd><p>The osgVolume library</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">OSGVOLUME_LIBRARY_DEBUG</span></code></dt><dd><p>The osgVolume debug library</p>
</dd>
</dl>
<p><code class="docutils literal notranslate"><span class="pre">$OSGDIR</span></code> is an environment variable that would correspond to:</p>
<div class="highlight-none notranslate"><div class="highlight"><pre><span></span>./configure --prefix=$OSGDIR
</pre></div>
</div>
<p>used in building osg.</p>
<p>Created by Eric Wing.</p>
</section>


            <div class="clearer"></div>
          </div>
        </div>
      </div>
      <div class="sphinxsidebar" role="navigation" aria-label="main navigation">
        <div class="sphinxsidebarwrapper">
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="FindosgViewer.html"
                          title="previous chapter">FindosgViewer</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="FindosgWidget.html"
                          title="next chapter">FindosgWidget</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../_sources/module/FindosgVolume.rst.txt"
            rel="nofollow">Show Source</a></li>
    </ul>
   </div>
<div id="searchbox" style="display: none" role="search">
  <h3 id="searchlabel">Quick search</h3>
    <div class="searchformwrapper">
    <form class="search" action="../search.html" method="get">
      <input type="text" name="q" aria-labelledby="searchlabel" autocomplete="off" autocorrect="off" autocapitalize="off" spellcheck="false"/>
      <input type="submit" value="Go" />
    </form>
    </div>
</div>
<script>document.getElementById('searchbox').style.display = "block"</script>
        </div>
      </div>
      <div class="clearer"></div>
    </div>
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             >index</a></li>
        <li class="right" >
          <a href="FindosgWidget.html" title="FindosgWidget"
             >next</a> |</li>
        <li class="right" >
          <a href="FindosgViewer.html" title="FindosgViewer"
             >previous</a> |</li>
  <li>
    <img src="../_static/cmake-logo-16.png" alt=""
         style="vertical-align: middle; margin-top: -2px" />
  </li>
  <li>
    <a href="https://cmake.org/">CMake</a> &#187;
  </li>
  <li>
    <a href="../index.html">3.28.1 Documentation</a> &#187;
  </li>

          <li class="nav-item nav-item-1"><a href="../manual/cmake-modules.7.html" >cmake-modules(7)</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href="">FindosgVolume</a></li> 
      </ul>
    </div>

    <div class="footer" role="contentinfo">
        &#169; Copyright 2000-2023 Kitware, Inc. and Contributors.
      Created using <a href="https://www.sphinx-doc.org/">Sphinx</a> 6.2.1.
    </div>
  </body>
</html>