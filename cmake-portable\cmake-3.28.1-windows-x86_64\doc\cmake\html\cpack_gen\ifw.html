
<!DOCTYPE html>

<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="generator" content="Docutils 0.19: https://docutils.sourceforge.io/" />

  <title>CPack IFW Generator &mdash; CMake 3.28.1 Documentation</title>

    <link rel="stylesheet" type="text/css" href="../_static/pygments.css" />
    <link rel="stylesheet" type="text/css" href="../_static/cmake.css" />
    
    <script data-url_root="../" id="documentation_options" src="../_static/documentation_options.js"></script>
    <script src="../_static/doctools.js"></script>
    <script src="../_static/sphinx_highlight.js"></script>
    
    <link rel="icon" href="../_static/cmake-favicon.ico"/>
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="next" title="CPack NSIS Generator" href="nsis.html" />
    <link rel="prev" title="CPack Inno Setup Generator" href="innosetup.html" />
 

  </head><body>

    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             accesskey="I">index</a></li>
        <li class="right" >
          <a href="nsis.html" title="CPack NSIS Generator"
             accesskey="N">next</a> |</li>
        <li class="right" >
          <a href="innosetup.html" title="CPack Inno Setup Generator"
             accesskey="P">previous</a> |</li>
  <li>
    <img src="../_static/cmake-logo-16.png" alt=""
         style="vertical-align: middle; margin-top: -2px" />
  </li>
  <li>
    <a href="https://cmake.org/">CMake</a> &#187;
  </li>
  <li>
    <a href="../index.html">3.28.1 Documentation</a> &#187;
  </li>

          <li class="nav-item nav-item-1"><a href="../manual/cpack-generators.7.html" accesskey="U">cpack-generators(7)</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href="">CPack IFW Generator</a></li> 
      </ul>
    </div>  

    <div class="document">
      <div class="documentwrapper">
        <div class="bodywrapper">
          <div class="body" role="main">
            
  <section id="cpack-ifw-generator">
<span id="cpack_gen:CPack IFW Generator"></span><h1><a class="toc-backref" href="#id1" role="doc-backlink">CPack IFW Generator</a><a class="headerlink" href="#cpack-ifw-generator" title="Permalink to this heading">¶</a></h1>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.1.</span></p>
</div>
<p>Configure and run the Qt Installer Framework to generate a Qt installer.</p>
<nav class="contents" id="contents">
<p class="topic-title">Contents</p>
<ul class="simple">
<li><p><a class="reference internal" href="#cpack-ifw-generator" id="id1">CPack IFW Generator</a></p>
<ul>
<li><p><a class="reference internal" href="#overview" id="id2">Overview</a></p></li>
<li><p><a class="reference internal" href="#variables" id="id3">Variables</a></p>
<ul>
<li><p><a class="reference internal" href="#debug" id="id4">Debug</a></p></li>
<li><p><a class="reference internal" href="#package" id="id5">Package</a></p></li>
<li><p><a class="reference internal" href="#components" id="id6">Components</a></p></li>
<li><p><a class="reference internal" href="#qtifw-tools" id="id7">QtIFW Tools</a></p></li>
<li><p><a class="reference internal" href="#hints-for-finding-qtifw" id="id8">Hints for Finding QtIFW</a></p></li>
</ul>
</li>
<li><p><a class="reference internal" href="#other-settings" id="id9">Other Settings</a></p>
<ul>
<li><p><a class="reference internal" href="#online-installer" id="id10">Online installer</a></p></li>
<li><p><a class="reference internal" href="#internationalization" id="id11">Internationalization</a></p></li>
</ul>
</li>
<li><p><a class="reference internal" href="#see-also" id="id12">See Also</a></p></li>
</ul>
</li>
</ul>
</nav>
<section id="overview">
<h2><a class="toc-backref" href="#id2" role="doc-backlink">Overview</a><a class="headerlink" href="#overview" title="Permalink to this heading">¶</a></h2>
<p>This <span class="target" id="index-0-manual:cpack-generators(7)"></span><a class="reference internal" href="../manual/cpack-generators.7.html#manual:cpack-generators(7)" title="cpack-generators(7)"><code class="xref cmake cmake-manual docutils literal notranslate"><span class="pre">cpack</span> <span class="pre">generator</span></code></a> generates
configuration and meta information for the <a class="reference external" href="https://doc.qt.io/qtinstallerframework/index.html">Qt Installer Framework</a> (QtIFW),
and runs QtIFW tools to generate a Qt installer.</p>
<p>QtIFW provides tools and utilities to create installers for
the platforms supported by <a class="reference external" href="https://www.qt.io">Qt</a>: Linux,
Microsoft Windows, and macOS.</p>
<p>To make use of this generator, QtIFW needs to be installed.
The <span class="target" id="index-0-module:CPackIFW"></span><a class="reference internal" href="../module/CPackIFW.html#module:CPackIFW" title="CPackIFW"><code class="xref cmake cmake-module docutils literal notranslate"><span class="pre">CPackIFW</span></code></a> module looks for the location of the
QtIFW command-line utilities, and defines several commands to
control the behavior of this generator. See <a class="reference internal" href="#hints-for-finding-qtifw">Hints for Finding QtIFW</a>.</p>
</section>
<section id="variables">
<h2><a class="toc-backref" href="#id3" role="doc-backlink">Variables</a><a class="headerlink" href="#variables" title="Permalink to this heading">¶</a></h2>
<p>You can use the following variables to change the behavior of the CPack <code class="docutils literal notranslate"><span class="pre">IFW</span></code>
generator.</p>
<section id="debug">
<h3><a class="toc-backref" href="#id4" role="doc-backlink">Debug</a><a class="headerlink" href="#debug" title="Permalink to this heading">¶</a></h3>
<dl class="cmake variable">
<dt class="sig sig-object cmake" id="variable:CPACK_IFW_VERBOSE">
<span class="sig-name descname"><span class="pre">CPACK_IFW_VERBOSE</span></span><a class="headerlink" href="#variable:CPACK_IFW_VERBOSE" title="Permalink to this definition">¶</a></dt>
<dd><div class="versionadded">
<p><span class="versionmodified added">New in version 3.3.</span></p>
</div>
<p>Set to <code class="docutils literal notranslate"><span class="pre">ON</span></code> to enable addition debug output.
By default is <code class="docutils literal notranslate"><span class="pre">OFF</span></code>.</p>
</dd></dl>

</section>
<section id="package">
<h3><a class="toc-backref" href="#id5" role="doc-backlink">Package</a><a class="headerlink" href="#package" title="Permalink to this heading">¶</a></h3>
<dl class="cmake variable">
<dt class="sig sig-object cmake" id="variable:CPACK_IFW_PACKAGE_TITLE">
<span class="sig-name descname"><span class="pre">CPACK_IFW_PACKAGE_TITLE</span></span><a class="headerlink" href="#variable:CPACK_IFW_PACKAGE_TITLE" title="Permalink to this definition">¶</a></dt>
<dd><p>Name of the installer as displayed on the title bar.
If not specified, it defaults to <span class="target" id="index-0-variable:CPACK_PACKAGE_DESCRIPTION_SUMMARY"></span><a class="reference internal" href="../module/CPack.html#variable:CPACK_PACKAGE_DESCRIPTION_SUMMARY" title="CPACK_PACKAGE_DESCRIPTION_SUMMARY"><code class="xref cmake cmake-variable docutils literal notranslate"><span class="pre">CPACK_PACKAGE_DESCRIPTION_SUMMARY</span></code></a>.</p>
</dd></dl>

<dl class="cmake variable">
<dt class="sig sig-object cmake" id="variable:CPACK_IFW_PACKAGE_PUBLISHER">
<span class="sig-name descname"><span class="pre">CPACK_IFW_PACKAGE_PUBLISHER</span></span><a class="headerlink" href="#variable:CPACK_IFW_PACKAGE_PUBLISHER" title="Permalink to this definition">¶</a></dt>
<dd><p>Publisher of the software (as shown in the Windows Control Panel).
If not specified, it defaults to <span class="target" id="index-0-variable:CPACK_PACKAGE_VENDOR"></span><a class="reference internal" href="../module/CPack.html#variable:CPACK_PACKAGE_VENDOR" title="CPACK_PACKAGE_VENDOR"><code class="xref cmake cmake-variable docutils literal notranslate"><span class="pre">CPACK_PACKAGE_VENDOR</span></code></a>.</p>
</dd></dl>

<dl class="cmake variable">
<dt class="sig sig-object cmake" id="variable:CPACK_IFW_PRODUCT_URL">
<span class="sig-name descname"><span class="pre">CPACK_IFW_PRODUCT_URL</span></span><a class="headerlink" href="#variable:CPACK_IFW_PRODUCT_URL" title="Permalink to this definition">¶</a></dt>
<dd><p>URL to a page that contains product information on your web site.</p>
</dd></dl>

<dl class="cmake variable">
<dt class="sig sig-object cmake" id="variable:CPACK_IFW_PACKAGE_ICON">
<span class="sig-name descname"><span class="pre">CPACK_IFW_PACKAGE_ICON</span></span><a class="headerlink" href="#variable:CPACK_IFW_PACKAGE_ICON" title="Permalink to this definition">¶</a></dt>
<dd><p>Filename for a custom installer icon. It must be an absolute path.
This should be a <code class="docutils literal notranslate"><span class="pre">.icns</span></code> file on macOS and a <code class="docutils literal notranslate"><span class="pre">.ico</span></code> file on Windows.
It is ignored on other platforms.</p>
</dd></dl>

<dl class="cmake variable">
<dt class="sig sig-object cmake" id="variable:CPACK_IFW_PACKAGE_WINDOW_ICON">
<span class="sig-name descname"><span class="pre">CPACK_IFW_PACKAGE_WINDOW_ICON</span></span><a class="headerlink" href="#variable:CPACK_IFW_PACKAGE_WINDOW_ICON" title="Permalink to this definition">¶</a></dt>
<dd><p>Filename for a custom window icon in PNG format for the Installer
application. It must be an absolute path.</p>
</dd></dl>

<dl class="cmake variable">
<dt class="sig sig-object cmake" id="variable:CPACK_IFW_PACKAGE_LOGO">
<span class="sig-name descname"><span class="pre">CPACK_IFW_PACKAGE_LOGO</span></span><a class="headerlink" href="#variable:CPACK_IFW_PACKAGE_LOGO" title="Permalink to this definition">¶</a></dt>
<dd><p>Filename for a logo image in PNG format, used as <code class="docutils literal notranslate"><span class="pre">QWizard::LogoPixmap</span></code>.
It must be an absolute path.</p>
</dd></dl>

<dl class="cmake variable">
<dt class="sig sig-object cmake" id="variable:CPACK_IFW_PACKAGE_WATERMARK">
<span class="sig-name descname"><span class="pre">CPACK_IFW_PACKAGE_WATERMARK</span></span><a class="headerlink" href="#variable:CPACK_IFW_PACKAGE_WATERMARK" title="Permalink to this definition">¶</a></dt>
<dd><div class="versionadded">
<p><span class="versionmodified added">New in version 3.8.</span></p>
</div>
<p>Filename for a watermark image in PNG format, used as
<code class="docutils literal notranslate"><span class="pre">QWizard::WatermarkPixmap</span></code>. It must be an absolute path.</p>
</dd></dl>

<dl class="cmake variable">
<dt class="sig sig-object cmake" id="variable:CPACK_IFW_PACKAGE_BANNER">
<span class="sig-name descname"><span class="pre">CPACK_IFW_PACKAGE_BANNER</span></span><a class="headerlink" href="#variable:CPACK_IFW_PACKAGE_BANNER" title="Permalink to this definition">¶</a></dt>
<dd><div class="versionadded">
<p><span class="versionmodified added">New in version 3.8.</span></p>
</div>
<p>Filename for a banner image in PNG format, used as <code class="docutils literal notranslate"><span class="pre">QWizard::BannerPixmap</span></code>.
It must be an absolute path.</p>
</dd></dl>

<dl class="cmake variable">
<dt class="sig sig-object cmake" id="variable:CPACK_IFW_PACKAGE_BACKGROUND">
<span class="sig-name descname"><span class="pre">CPACK_IFW_PACKAGE_BACKGROUND</span></span><a class="headerlink" href="#variable:CPACK_IFW_PACKAGE_BACKGROUND" title="Permalink to this definition">¶</a></dt>
<dd><div class="versionadded">
<p><span class="versionmodified added">New in version 3.8.</span></p>
</div>
<p>Filename for a background image in PNG format, used as
<code class="docutils literal notranslate"><span class="pre">QWizard::BackgroundPixmap</span></code> (only used by <code class="docutils literal notranslate"><span class="pre">MacStyle</span></code>). It must be an
absolute path.</p>
</dd></dl>

<dl class="cmake variable">
<dt class="sig sig-object cmake" id="variable:CPACK_IFW_PACKAGE_WIZARD_STYLE">
<span class="sig-name descname"><span class="pre">CPACK_IFW_PACKAGE_WIZARD_STYLE</span></span><a class="headerlink" href="#variable:CPACK_IFW_PACKAGE_WIZARD_STYLE" title="Permalink to this definition">¶</a></dt>
<dd><div class="versionadded">
<p><span class="versionmodified added">New in version 3.8.</span></p>
</div>
<p>Wizard style to be used (<code class="docutils literal notranslate"><span class="pre">Modern</span></code>, <code class="docutils literal notranslate"><span class="pre">Mac</span></code>, <code class="docutils literal notranslate"><span class="pre">Aero</span></code> or <code class="docutils literal notranslate"><span class="pre">Classic</span></code>).</p>
</dd></dl>

<dl class="cmake variable">
<dt class="sig sig-object cmake" id="variable:CPACK_IFW_PACKAGE_WIZARD_DEFAULT_WIDTH">
<span class="sig-name descname"><span class="pre">CPACK_IFW_PACKAGE_WIZARD_DEFAULT_WIDTH</span></span><a class="headerlink" href="#variable:CPACK_IFW_PACKAGE_WIZARD_DEFAULT_WIDTH" title="Permalink to this definition">¶</a></dt>
<dd><div class="versionadded">
<p><span class="versionmodified added">New in version 3.8.</span></p>
</div>
<p>Default width of the wizard in pixels. Setting a banner image will override
this.</p>
</dd></dl>

<dl class="cmake variable">
<dt class="sig sig-object cmake" id="variable:CPACK_IFW_PACKAGE_WIZARD_DEFAULT_HEIGHT">
<span class="sig-name descname"><span class="pre">CPACK_IFW_PACKAGE_WIZARD_DEFAULT_HEIGHT</span></span><a class="headerlink" href="#variable:CPACK_IFW_PACKAGE_WIZARD_DEFAULT_HEIGHT" title="Permalink to this definition">¶</a></dt>
<dd><div class="versionadded">
<p><span class="versionmodified added">New in version 3.8.</span></p>
</div>
<p>Default height of the wizard in pixels. Setting a watermark image will
override this.</p>
</dd></dl>

<dl class="cmake variable">
<dt class="sig sig-object cmake" id="variable:CPACK_IFW_PACKAGE_WIZARD_SHOW_PAGE_LIST">
<span class="sig-name descname"><span class="pre">CPACK_IFW_PACKAGE_WIZARD_SHOW_PAGE_LIST</span></span><a class="headerlink" href="#variable:CPACK_IFW_PACKAGE_WIZARD_SHOW_PAGE_LIST" title="Permalink to this definition">¶</a></dt>
<dd><div class="versionadded">
<p><span class="versionmodified added">New in version 3.20.</span></p>
</div>
<p>Set to <code class="docutils literal notranslate"><span class="pre">OFF</span></code> if the widget listing installer pages on the left side of the
wizard should not be shown.</p>
<p>It is <code class="docutils literal notranslate"><span class="pre">ON</span></code> by default, but will only have an effect if using QtIFW 4.0 or
later.</p>
</dd></dl>

<dl class="cmake variable">
<dt class="sig sig-object cmake" id="variable:CPACK_IFW_PACKAGE_TITLE_COLOR">
<span class="sig-name descname"><span class="pre">CPACK_IFW_PACKAGE_TITLE_COLOR</span></span><a class="headerlink" href="#variable:CPACK_IFW_PACKAGE_TITLE_COLOR" title="Permalink to this definition">¶</a></dt>
<dd><div class="versionadded">
<p><span class="versionmodified added">New in version 3.8.</span></p>
</div>
<p>Color of the titles and subtitles (takes an HTML color code, such as
<code class="docutils literal notranslate"><span class="pre">#88FF33</span></code>).</p>
</dd></dl>

<dl class="cmake variable">
<dt class="sig sig-object cmake" id="variable:CPACK_IFW_PACKAGE_STYLE_SHEET">
<span class="sig-name descname"><span class="pre">CPACK_IFW_PACKAGE_STYLE_SHEET</span></span><a class="headerlink" href="#variable:CPACK_IFW_PACKAGE_STYLE_SHEET" title="Permalink to this definition">¶</a></dt>
<dd><div class="versionadded">
<p><span class="versionmodified added">New in version 3.15.</span></p>
</div>
<p>Filename for a stylesheet. It must be an absolute path.</p>
</dd></dl>

<dl class="cmake variable">
<dt class="sig sig-object cmake" id="variable:CPACK_IFW_TARGET_DIRECTORY">
<span class="sig-name descname"><span class="pre">CPACK_IFW_TARGET_DIRECTORY</span></span><a class="headerlink" href="#variable:CPACK_IFW_TARGET_DIRECTORY" title="Permalink to this definition">¶</a></dt>
<dd><p>Default target directory for installation.
If <span class="target" id="index-0-variable:CPACK_PACKAGE_INSTALL_DIRECTORY"></span><a class="reference internal" href="../module/CPack.html#variable:CPACK_PACKAGE_INSTALL_DIRECTORY" title="CPACK_PACKAGE_INSTALL_DIRECTORY"><code class="xref cmake cmake-variable docutils literal notranslate"><span class="pre">CPACK_PACKAGE_INSTALL_DIRECTORY</span></code></a> is set, this defaults to
<code class="docutils literal notranslate"><span class="pre">&#64;ApplicationsDir&#64;/${CPACK_PACKAGE_INSTALL_DIRECTORY}</span></code>. If that variable
isn't set either, the default used is <code class="docutils literal notranslate"><span class="pre">&#64;RootDir&#64;/usr/local</span></code>.
Predefined variables of the form <code class="docutils literal notranslate"><span class="pre">&#64;...&#64;</span></code> are expanded by the
<a class="reference external" href="https://doc.qt.io/qtinstallerframework/scripting.html">QtIFW scripting engine</a>.</p>
</dd></dl>

<dl class="cmake variable">
<dt class="sig sig-object cmake" id="variable:CPACK_IFW_ADMIN_TARGET_DIRECTORY">
<span class="sig-name descname"><span class="pre">CPACK_IFW_ADMIN_TARGET_DIRECTORY</span></span><a class="headerlink" href="#variable:CPACK_IFW_ADMIN_TARGET_DIRECTORY" title="Permalink to this definition">¶</a></dt>
<dd><p>Default target directory for installation with administrator rights.</p>
<p>You can use predefined variables.</p>
</dd></dl>

<dl class="cmake variable">
<dt class="sig sig-object cmake" id="variable:CPACK_IFW_PACKAGE_REMOVE_TARGET_DIR">
<span class="sig-name descname"><span class="pre">CPACK_IFW_PACKAGE_REMOVE_TARGET_DIR</span></span><a class="headerlink" href="#variable:CPACK_IFW_PACKAGE_REMOVE_TARGET_DIR" title="Permalink to this definition">¶</a></dt>
<dd><div class="versionadded">
<p><span class="versionmodified added">New in version 3.11.</span></p>
</div>
<p>Set to <code class="docutils literal notranslate"><span class="pre">OFF</span></code> if the target directory should not be deleted when uninstalling.</p>
<p>Is <code class="docutils literal notranslate"><span class="pre">ON</span></code> by default</p>
</dd></dl>

<dl class="cmake variable">
<dt class="sig sig-object cmake" id="variable:CPACK_IFW_PACKAGE_GROUP">
<span class="sig-name descname"><span class="pre">CPACK_IFW_PACKAGE_GROUP</span></span><a class="headerlink" href="#variable:CPACK_IFW_PACKAGE_GROUP" title="Permalink to this definition">¶</a></dt>
<dd><p>The group, which will be used to configure the root package.</p>
</dd></dl>

<dl class="cmake variable">
<dt class="sig sig-object cmake" id="variable:CPACK_IFW_PACKAGE_NAME">
<span class="sig-name descname"><span class="pre">CPACK_IFW_PACKAGE_NAME</span></span><a class="headerlink" href="#variable:CPACK_IFW_PACKAGE_NAME" title="Permalink to this definition">¶</a></dt>
<dd><p>The root package name, which will be used if the configuration group is not
specified.</p>
</dd></dl>

<dl class="cmake variable">
<dt class="sig sig-object cmake" id="variable:CPACK_IFW_PACKAGE_START_MENU_DIRECTORY">
<span class="sig-name descname"><span class="pre">CPACK_IFW_PACKAGE_START_MENU_DIRECTORY</span></span><a class="headerlink" href="#variable:CPACK_IFW_PACKAGE_START_MENU_DIRECTORY" title="Permalink to this definition">¶</a></dt>
<dd><div class="versionadded">
<p><span class="versionmodified added">New in version 3.3.</span></p>
</div>
<p>Name of the default program group for the product in the Windows Start menu.
If not specified, it defaults to <span class="target" id="index-0-variable:CPACK_IFW_PACKAGE_NAME"></span><a class="reference internal" href="#variable:CPACK_IFW_PACKAGE_NAME" title="CPACK_IFW_PACKAGE_NAME"><code class="xref cmake cmake-variable docutils literal notranslate"><span class="pre">CPACK_IFW_PACKAGE_NAME</span></code></a>.</p>
</dd></dl>

<dl class="cmake variable">
<dt class="sig sig-object cmake" id="variable:CPACK_IFW_PACKAGE_MAINTENANCE_TOOL_NAME">
<span class="sig-name descname"><span class="pre">CPACK_IFW_PACKAGE_MAINTENANCE_TOOL_NAME</span></span><a class="headerlink" href="#variable:CPACK_IFW_PACKAGE_MAINTENANCE_TOOL_NAME" title="Permalink to this definition">¶</a></dt>
<dd><div class="versionadded">
<p><span class="versionmodified added">New in version 3.3.</span></p>
</div>
<p>Filename of the generated maintenance tool.
The platform-specific executable file extension will be appended.</p>
<p>If not specified, QtIFW provides a default name (<code class="docutils literal notranslate"><span class="pre">maintenancetool</span></code>).</p>
</dd></dl>

<dl class="cmake variable">
<dt class="sig sig-object cmake" id="variable:CPACK_IFW_PACKAGE_MAINTENANCE_TOOL_INI_FILE">
<span class="sig-name descname"><span class="pre">CPACK_IFW_PACKAGE_MAINTENANCE_TOOL_INI_FILE</span></span><a class="headerlink" href="#variable:CPACK_IFW_PACKAGE_MAINTENANCE_TOOL_INI_FILE" title="Permalink to this definition">¶</a></dt>
<dd><div class="versionadded">
<p><span class="versionmodified added">New in version 3.3.</span></p>
</div>
<p>Filename for the configuration of the generated maintenance tool.</p>
<p>If not specified, QtIFW uses a default file name (<code class="docutils literal notranslate"><span class="pre">maintenancetool.ini</span></code>).</p>
</dd></dl>

<dl class="cmake variable">
<dt class="sig sig-object cmake" id="variable:CPACK_IFW_PACKAGE_ALLOW_NON_ASCII_CHARACTERS">
<span class="sig-name descname"><span class="pre">CPACK_IFW_PACKAGE_ALLOW_NON_ASCII_CHARACTERS</span></span><a class="headerlink" href="#variable:CPACK_IFW_PACKAGE_ALLOW_NON_ASCII_CHARACTERS" title="Permalink to this definition">¶</a></dt>
<dd><div class="versionadded">
<p><span class="versionmodified added">New in version 3.3.</span></p>
</div>
<p>Set to <code class="docutils literal notranslate"><span class="pre">ON</span></code> if the installation path can contain non-ASCII characters.
Only supported for QtIFW 2.0 and later. Older QtIFW versions will always
allow non-ASCII characters.</p>
</dd></dl>

<dl class="cmake variable">
<dt class="sig sig-object cmake" id="variable:CPACK_IFW_PACKAGE_ALLOW_SPACE_IN_PATH">
<span class="sig-name descname"><span class="pre">CPACK_IFW_PACKAGE_ALLOW_SPACE_IN_PATH</span></span><a class="headerlink" href="#variable:CPACK_IFW_PACKAGE_ALLOW_SPACE_IN_PATH" title="Permalink to this definition">¶</a></dt>
<dd><div class="versionadded">
<p><span class="versionmodified added">New in version 3.3.</span></p>
</div>
<p>Set to <code class="docutils literal notranslate"><span class="pre">OFF</span></code> if the installation path cannot contain space characters.</p>
<p>Is <code class="docutils literal notranslate"><span class="pre">ON</span></code> for QtIFW less 2.0 tools.</p>
</dd></dl>

<dl class="cmake variable">
<dt class="sig sig-object cmake" id="variable:CPACK_IFW_PACKAGE_DISABLE_COMMAND_LINE_INTERFACE">
<span class="sig-name descname"><span class="pre">CPACK_IFW_PACKAGE_DISABLE_COMMAND_LINE_INTERFACE</span></span><a class="headerlink" href="#variable:CPACK_IFW_PACKAGE_DISABLE_COMMAND_LINE_INTERFACE" title="Permalink to this definition">¶</a></dt>
<dd><div class="versionadded">
<p><span class="versionmodified added">New in version 3.23.</span></p>
</div>
<p>Set to <code class="docutils literal notranslate"><span class="pre">ON</span></code> if command line interface features should be disabled.
It is <code class="docutils literal notranslate"><span class="pre">OFF</span></code> by default and will only have an effect if using QtIFW 4.0 or
later.</p>
</dd></dl>

<dl class="cmake variable">
<dt class="sig sig-object cmake" id="variable:CPACK_IFW_PACKAGE_CONTROL_SCRIPT">
<span class="sig-name descname"><span class="pre">CPACK_IFW_PACKAGE_CONTROL_SCRIPT</span></span><a class="headerlink" href="#variable:CPACK_IFW_PACKAGE_CONTROL_SCRIPT" title="Permalink to this definition">¶</a></dt>
<dd><div class="versionadded">
<p><span class="versionmodified added">New in version 3.3.</span></p>
</div>
<p>Filename for a custom installer control script.</p>
</dd></dl>

<dl class="cmake variable">
<dt class="sig sig-object cmake" id="variable:CPACK_IFW_PACKAGE_RESOURCES">
<span class="sig-name descname"><span class="pre">CPACK_IFW_PACKAGE_RESOURCES</span></span><a class="headerlink" href="#variable:CPACK_IFW_PACKAGE_RESOURCES" title="Permalink to this definition">¶</a></dt>
<dd><div class="versionadded">
<p><span class="versionmodified added">New in version 3.7.</span></p>
</div>
<p>List of additional resources (<code class="docutils literal notranslate"><span class="pre">.qrc</span></code> files) to include in the installer
binary. They should be specified as absolute paths and no two resource files
can have the same file name.</p>
<p>You can use the <span class="target" id="index-0-command:cpack_ifw_add_package_resources"></span><a class="reference internal" href="../module/CPackIFW.html#command:cpack_ifw_add_package_resources" title="cpack_ifw_add_package_resources"><code class="xref cmake cmake-command docutils literal notranslate"><span class="pre">cpack_ifw_add_package_resources()</span></code></a> command to resolve
relative paths.</p>
</dd></dl>

<dl class="cmake variable">
<dt class="sig sig-object cmake" id="variable:CPACK_IFW_PACKAGE_FILE_EXTENSION">
<span class="sig-name descname"><span class="pre">CPACK_IFW_PACKAGE_FILE_EXTENSION</span></span><a class="headerlink" href="#variable:CPACK_IFW_PACKAGE_FILE_EXTENSION" title="Permalink to this definition">¶</a></dt>
<dd><div class="versionadded">
<p><span class="versionmodified added">New in version 3.10.</span></p>
</div>
<p>The target binary extension.</p>
<p>On Linux, the name of the target binary is automatically extended with
<code class="docutils literal notranslate"><span class="pre">.run</span></code>, if you do not specify the extension.</p>
<p>On Windows, the target is created as an application with the extension
<code class="docutils literal notranslate"><span class="pre">.exe</span></code>, which is automatically added, if not supplied.</p>
<p>On Mac, the target is created as an DMG disk image with the extension
<code class="docutils literal notranslate"><span class="pre">.dmg</span></code>, which is automatically added, if not supplied.</p>
</dd></dl>

<dl class="cmake variable">
<dt class="sig sig-object cmake" id="variable:CPACK_IFW_REPOSITORIES_ALL">
<span class="sig-name descname"><span class="pre">CPACK_IFW_REPOSITORIES_ALL</span></span><a class="headerlink" href="#variable:CPACK_IFW_REPOSITORIES_ALL" title="Permalink to this definition">¶</a></dt>
<dd><p>The list of remote repositories.</p>
<p>The default value of this variable is computed by CPack and contains
all repositories added with <span class="target" id="index-0-command:cpack_ifw_add_repository"></span><a class="reference internal" href="../module/CPackIFW.html#command:cpack_ifw_add_repository" title="cpack_ifw_add_repository"><code class="xref cmake cmake-command docutils literal notranslate"><span class="pre">cpack_ifw_add_repository()</span></code></a>
or updated with <span class="target" id="index-0-command:cpack_ifw_update_repository"></span><a class="reference internal" href="../module/CPackIFW.html#command:cpack_ifw_update_repository" title="cpack_ifw_update_repository"><code class="xref cmake cmake-command docutils literal notranslate"><span class="pre">cpack_ifw_update_repository()</span></code></a>.</p>
</dd></dl>

<dl class="cmake variable">
<dt class="sig sig-object cmake" id="variable:CPACK_IFW_DOWNLOAD_ALL">
<span class="sig-name descname"><span class="pre">CPACK_IFW_DOWNLOAD_ALL</span></span><a class="headerlink" href="#variable:CPACK_IFW_DOWNLOAD_ALL" title="Permalink to this definition">¶</a></dt>
<dd><p>If this is <code class="docutils literal notranslate"><span class="pre">ON</span></code>, all components will be downloaded. If not set, the
behavior is determined by whether <span class="target" id="index-0-command:cpack_configure_downloads"></span><a class="reference internal" href="../module/CPackComponent.html#command:cpack_configure_downloads" title="cpack_configure_downloads"><code class="xref cmake cmake-command docutils literal notranslate"><span class="pre">cpack_configure_downloads()</span></code></a> has
been called with the <code class="docutils literal notranslate"><span class="pre">ALL</span></code> option or not.</p>
</dd></dl>

<dl class="cmake variable">
<dt class="sig sig-object cmake" id="variable:CPACK_IFW_PACKAGE_PRODUCT_IMAGES">
<span class="sig-name descname"><span class="pre">CPACK_IFW_PACKAGE_PRODUCT_IMAGES</span></span><a class="headerlink" href="#variable:CPACK_IFW_PACKAGE_PRODUCT_IMAGES" title="Permalink to this definition">¶</a></dt>
<dd><div class="versionadded">
<p><span class="versionmodified added">New in version 3.23.</span></p>
</div>
<p>A list of images to be shown on the <code class="docutils literal notranslate"><span class="pre">PerformInstallationPage</span></code>. These
must be absolute paths and the images must be in PNG format.</p>
<p>This feature is available for QtIFW 4.0.0 and later.</p>
</dd></dl>

<dl class="cmake variable">
<dt class="sig sig-object cmake" id="variable:CPACK_IFW_PACKAGE_RUN_PROGRAM">
<span class="sig-name descname"><span class="pre">CPACK_IFW_PACKAGE_RUN_PROGRAM</span></span><a class="headerlink" href="#variable:CPACK_IFW_PACKAGE_RUN_PROGRAM" title="Permalink to this definition">¶</a></dt>
<dd><div class="versionadded">
<p><span class="versionmodified added">New in version 3.23.</span></p>
</div>
<p>Command executed after the installer is finished, if the user accepts the
action. Provide the full path to the application, as found when installed.
This typically means the path should begin with the QtIFW predefined variable
<code class="docutils literal notranslate"><span class="pre">&#64;TargetDir&#64;</span></code>.</p>
<p>This feature is available for QtIFW 4.0.0 and later.</p>
</dd></dl>

<dl class="cmake variable">
<dt class="sig sig-object cmake" id="variable:CPACK_IFW_PACKAGE_RUN_PROGRAM_ARGUMENTS">
<span class="sig-name descname"><span class="pre">CPACK_IFW_PACKAGE_RUN_PROGRAM_ARGUMENTS</span></span><a class="headerlink" href="#variable:CPACK_IFW_PACKAGE_RUN_PROGRAM_ARGUMENTS" title="Permalink to this definition">¶</a></dt>
<dd><div class="versionadded">
<p><span class="versionmodified added">New in version 3.23.</span></p>
</div>
<p>List of arguments passed to the program specified in
<span class="target" id="index-0-variable:CPACK_IFW_PACKAGE_RUN_PROGRAM"></span><a class="reference internal" href="#variable:CPACK_IFW_PACKAGE_RUN_PROGRAM" title="CPACK_IFW_PACKAGE_RUN_PROGRAM"><code class="xref cmake cmake-variable docutils literal notranslate"><span class="pre">CPACK_IFW_PACKAGE_RUN_PROGRAM</span></code></a>.</p>
<p>This feature is available for QtIFW 4.0.0 and later.</p>
</dd></dl>

<dl class="cmake variable">
<dt class="sig sig-object cmake" id="variable:CPACK_IFW_PACKAGE_RUN_PROGRAM_DESCRIPTION">
<span class="sig-name descname"><span class="pre">CPACK_IFW_PACKAGE_RUN_PROGRAM_DESCRIPTION</span></span><a class="headerlink" href="#variable:CPACK_IFW_PACKAGE_RUN_PROGRAM_DESCRIPTION" title="Permalink to this definition">¶</a></dt>
<dd><div class="versionadded">
<p><span class="versionmodified added">New in version 3.23.</span></p>
</div>
<p>Text shown next to the check box for running the program after the
installation. If <span class="target" id="index-1-variable:CPACK_IFW_PACKAGE_RUN_PROGRAM"></span><a class="reference internal" href="#variable:CPACK_IFW_PACKAGE_RUN_PROGRAM" title="CPACK_IFW_PACKAGE_RUN_PROGRAM"><code class="xref cmake cmake-variable docutils literal notranslate"><span class="pre">CPACK_IFW_PACKAGE_RUN_PROGRAM</span></code></a> is set but no
description is provided, QtIFW will use a default message like
<code class="docutils literal notranslate"><span class="pre">Run</span> <span class="pre">&lt;Name&gt;</span> <span class="pre">now</span></code>.</p>
<p>This feature is available for QtIFW 4.0.0 and later.</p>
</dd></dl>

<dl class="cmake variable">
<dt class="sig sig-object cmake" id="variable:CPACK_IFW_PACKAGE_SIGNING_IDENTITY">
<span class="sig-name descname"><span class="pre">CPACK_IFW_PACKAGE_SIGNING_IDENTITY</span></span><a class="headerlink" href="#variable:CPACK_IFW_PACKAGE_SIGNING_IDENTITY" title="Permalink to this definition">¶</a></dt>
<dd><div class="versionadded">
<p><span class="versionmodified added">New in version 3.23.</span></p>
</div>
<p>Allows specifying a code signing identity to be used for signing the generated
app bundle. Only available on macOS, ignored on other platforms.</p>
</dd></dl>

<dl class="cmake variable">
<dt class="sig sig-object cmake" id="variable:CPACK_IFW_ARCHIVE_FORMAT">
<span class="sig-name descname"><span class="pre">CPACK_IFW_ARCHIVE_FORMAT</span></span><a class="headerlink" href="#variable:CPACK_IFW_ARCHIVE_FORMAT" title="Permalink to this definition">¶</a></dt>
<dd><div class="versionadded">
<p><span class="versionmodified added">New in version 3.23.</span></p>
</div>
<p>Set the format used when packaging new component data archives. If you omit
this option, the <code class="docutils literal notranslate"><span class="pre">7z</span></code> format will be used as a default. Supported formats:</p>
<ul class="simple">
<li><p>7z</p></li>
<li><p>zip</p></li>
<li><p>tar.gz</p></li>
<li><p>tar.bz2</p></li>
<li><p>tar.xz</p></li>
</ul>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>If the Qt Installer Framework tools were built without libarchive support,
only <code class="docutils literal notranslate"><span class="pre">7z</span></code> format is supported.</p>
</div>
<p>This feature is available for QtIFW 4.2.0 and later.</p>
</dd></dl>

<dl class="cmake variable">
<dt class="sig sig-object cmake" id="variable:CPACK_IFW_ARCHIVE_COMPRESSION">
<span class="sig-name descname"><span class="pre">CPACK_IFW_ARCHIVE_COMPRESSION</span></span><a class="headerlink" href="#variable:CPACK_IFW_ARCHIVE_COMPRESSION" title="Permalink to this definition">¶</a></dt>
<dd><div class="versionadded">
<p><span class="versionmodified added">New in version 3.23.</span></p>
</div>
<p>Archive compression level. The allowable values are:</p>
<blockquote>
<div><ul class="simple">
<li><p>0 (<em>No compression</em>)</p></li>
<li><p>1 (<em>Fastest compression</em>)</p></li>
<li><p>3 (<em>Fast compression</em>)</p></li>
<li><p>5 (<em>Normal compression</em>)</p></li>
<li><p>7 (<em>Maximum compression</em>)</p></li>
<li><p>9 (<em>Ultra compression</em>)</p></li>
</ul>
</div></blockquote>
<p>If this variable is not set, QtIFW will use a default compression level,
which will typically be 5 (<em>Normal compression</em>).</p>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>Some formats do not support all the possible values. For example <code class="docutils literal notranslate"><span class="pre">zip</span></code>
compression only supports values from 1 to 7.</p>
</div>
<p>This feature is available for QtIFW 4.2.0 and later.</p>
</dd></dl>

</section>
<section id="components">
<h3><a class="toc-backref" href="#id6" role="doc-backlink">Components</a><a class="headerlink" href="#components" title="Permalink to this heading">¶</a></h3>
<dl class="cmake variable">
<dt class="sig sig-object cmake" id="variable:CPACK_IFW_RESOLVE_DUPLICATE_NAMES">
<span class="sig-name descname"><span class="pre">CPACK_IFW_RESOLVE_DUPLICATE_NAMES</span></span><a class="headerlink" href="#variable:CPACK_IFW_RESOLVE_DUPLICATE_NAMES" title="Permalink to this definition">¶</a></dt>
<dd><p>Resolve duplicate names when installing components with groups.</p>
</dd></dl>

<dl class="cmake variable">
<dt class="sig sig-object cmake" id="variable:CPACK_IFW_PACKAGES_DIRECTORIES">
<span class="sig-name descname"><span class="pre">CPACK_IFW_PACKAGES_DIRECTORIES</span></span><a class="headerlink" href="#variable:CPACK_IFW_PACKAGES_DIRECTORIES" title="Permalink to this definition">¶</a></dt>
<dd><p>Additional prepared packages directories that will be used to resolve
dependent components.</p>
</dd></dl>

<dl class="cmake variable">
<dt class="sig sig-object cmake" id="variable:CPACK_IFW_REPOSITORIES_DIRECTORIES">
<span class="sig-name descname"><span class="pre">CPACK_IFW_REPOSITORIES_DIRECTORIES</span></span><a class="headerlink" href="#variable:CPACK_IFW_REPOSITORIES_DIRECTORIES" title="Permalink to this definition">¶</a></dt>
<dd><div class="versionadded">
<p><span class="versionmodified added">New in version 3.10.</span></p>
</div>
<p>Additional prepared repository directories that will be used to resolve and
repack dependent components.</p>
<p>This feature is available for QtIFW 3.1 and later.</p>
</dd></dl>

</section>
<section id="qtifw-tools">
<h3><a class="toc-backref" href="#id7" role="doc-backlink">QtIFW Tools</a><a class="headerlink" href="#qtifw-tools" title="Permalink to this heading">¶</a></h3>
<dl class="cmake variable">
<dt class="sig sig-object cmake" id="variable:CPACK_IFW_FRAMEWORK_VERSION">
<span class="sig-name descname"><span class="pre">CPACK_IFW_FRAMEWORK_VERSION</span></span><a class="headerlink" href="#variable:CPACK_IFW_FRAMEWORK_VERSION" title="Permalink to this definition">¶</a></dt>
<dd><div class="versionadded">
<p><span class="versionmodified added">New in version 3.3.</span></p>
</div>
<p>The version of the QtIFW tools that will be used. This variable is set
by the <span class="target" id="index-1-module:CPackIFW"></span><a class="reference internal" href="../module/CPackIFW.html#module:CPackIFW" title="CPackIFW"><code class="xref cmake cmake-module docutils literal notranslate"><span class="pre">CPackIFW</span></code></a> module.</p>
</dd></dl>

<p>The following variables provide the locations of the QtIFW
command-line tools as discovered by the <span class="target" id="index-2-module:CPackIFW"></span><a class="reference internal" href="../module/CPackIFW.html#module:CPackIFW" title="CPackIFW"><code class="xref cmake cmake-module docutils literal notranslate"><span class="pre">CPackIFW</span></code></a> module.
These variables are cached, and may be configured if needed.</p>
<dl class="cmake variable">
<dt class="sig sig-object cmake" id="variable:CPACK_IFW_ARCHIVEGEN_EXECUTABLE">
<span class="sig-name descname"><span class="pre">CPACK_IFW_ARCHIVEGEN_EXECUTABLE</span></span><a class="headerlink" href="#variable:CPACK_IFW_ARCHIVEGEN_EXECUTABLE" title="Permalink to this definition">¶</a></dt>
<dd><div class="versionadded">
<p><span class="versionmodified added">New in version 3.19.</span></p>
</div>
<p>The path to <code class="docutils literal notranslate"><span class="pre">archivegen</span></code>.</p>
</dd></dl>

<dl class="cmake variable">
<dt class="sig sig-object cmake" id="variable:CPACK_IFW_BINARYCREATOR_EXECUTABLE">
<span class="sig-name descname"><span class="pre">CPACK_IFW_BINARYCREATOR_EXECUTABLE</span></span><a class="headerlink" href="#variable:CPACK_IFW_BINARYCREATOR_EXECUTABLE" title="Permalink to this definition">¶</a></dt>
<dd><p>The path to <code class="docutils literal notranslate"><span class="pre">binarycreator</span></code>.</p>
</dd></dl>

<dl class="cmake variable">
<dt class="sig sig-object cmake" id="variable:CPACK_IFW_REPOGEN_EXECUTABLE">
<span class="sig-name descname"><span class="pre">CPACK_IFW_REPOGEN_EXECUTABLE</span></span><a class="headerlink" href="#variable:CPACK_IFW_REPOGEN_EXECUTABLE" title="Permalink to this definition">¶</a></dt>
<dd><p>The path to <code class="docutils literal notranslate"><span class="pre">repogen</span></code>.</p>
</dd></dl>

<dl class="cmake variable">
<dt class="sig sig-object cmake" id="variable:CPACK_IFW_INSTALLERBASE_EXECUTABLE">
<span class="sig-name descname"><span class="pre">CPACK_IFW_INSTALLERBASE_EXECUTABLE</span></span><a class="headerlink" href="#variable:CPACK_IFW_INSTALLERBASE_EXECUTABLE" title="Permalink to this definition">¶</a></dt>
<dd><p>The path to <code class="docutils literal notranslate"><span class="pre">installerbase</span></code>.</p>
</dd></dl>

<dl class="cmake variable">
<dt class="sig sig-object cmake" id="variable:CPACK_IFW_DEVTOOL_EXECUTABLE">
<span class="sig-name descname"><span class="pre">CPACK_IFW_DEVTOOL_EXECUTABLE</span></span><a class="headerlink" href="#variable:CPACK_IFW_DEVTOOL_EXECUTABLE" title="Permalink to this definition">¶</a></dt>
<dd><p>The path to <code class="docutils literal notranslate"><span class="pre">devtool</span></code>.</p>
</dd></dl>

</section>
<section id="hints-for-finding-qtifw">
<h3><a class="toc-backref" href="#id8" role="doc-backlink">Hints for Finding QtIFW</a><a class="headerlink" href="#hints-for-finding-qtifw" title="Permalink to this heading">¶</a></h3>
<p>Generally, the CPack <code class="docutils literal notranslate"><span class="pre">IFW</span></code> generator automatically finds QtIFW tools.
The following (in order of precedence) can also be set to augment the
locations normally searched by <span class="target" id="index-0-command:find_program"></span><a class="reference internal" href="../command/find_program.html#command:find_program" title="find_program"><code class="xref cmake cmake-command docutils literal notranslate"><span class="pre">find_program()</span></code></a>:</p>
<dl class="cmake variable">
<dt class="sig sig-object cmake" id="variable:CPACK_IFW_ROOT">
<span class="sig-name descname"><span class="pre">CPACK_IFW_ROOT</span></span><a class="headerlink" href="#variable:CPACK_IFW_ROOT" title="Permalink to this definition">¶</a></dt>
<dd><div class="versionadded">
<p><span class="versionmodified added">New in version 3.9.</span></p>
</div>
<p>CMake variable</p>
</dd></dl>

<dl class="cmake envvar">
<dt class="sig sig-object cmake" id="envvar:CPACK_IFW_ROOT">
<span class="sig-name descname"><span class="pre">CPACK_IFW_ROOT</span></span><a class="headerlink" href="#envvar:CPACK_IFW_ROOT" title="Permalink to this definition">¶</a></dt>
<dd><div class="versionadded">
<p><span class="versionmodified added">New in version 3.9.</span></p>
</div>
<p>Environment variable</p>
</dd></dl>

<dl class="cmake variable">
<dt class="sig sig-object cmake" id="variable:QTIFWDIR">
<span class="sig-name descname"><span class="pre">QTIFWDIR</span></span><a class="headerlink" href="#variable:QTIFWDIR" title="Permalink to this definition">¶</a></dt>
<dd><p>CMake variable</p>
</dd></dl>

<dl class="cmake envvar">
<dt class="sig sig-object cmake" id="envvar:QTIFWDIR">
<span class="sig-name descname"><span class="pre">QTIFWDIR</span></span><a class="headerlink" href="#envvar:QTIFWDIR" title="Permalink to this definition">¶</a></dt>
<dd><p>Environment variable</p>
</dd></dl>

<div class="admonition note">
<p class="admonition-title">Note</p>
<p>The specified path should not contain <code class="docutils literal notranslate"><span class="pre">bin</span></code> at the end
(for example: <code class="docutils literal notranslate"><span class="pre">D:\\DevTools\\QtIFW2.0.5</span></code>).</p>
</div>
</section>
</section>
<section id="other-settings">
<h2><a class="toc-backref" href="#id9" role="doc-backlink">Other Settings</a><a class="headerlink" href="#other-settings" title="Permalink to this heading">¶</a></h2>
<section id="online-installer">
<h3><a class="toc-backref" href="#id10" role="doc-backlink">Online installer</a><a class="headerlink" href="#online-installer" title="Permalink to this heading">¶</a></h3>
<p>By default, this generator generates an <em>offline installer</em>. This means
that all packaged files are fully contained in the installer executable.</p>
<p>In contrast, an <em>online installer</em> will download some or all components from
a remote server.</p>
<p>The <code class="docutils literal notranslate"><span class="pre">DOWNLOADED</span></code> option in the <span class="target" id="index-0-command:cpack_add_component"></span><a class="reference internal" href="../module/CPackComponent.html#command:cpack_add_component" title="cpack_add_component"><code class="xref cmake cmake-command docutils literal notranslate"><span class="pre">cpack_add_component()</span></code></a> command
specifies that a component is to be downloaded. Alternatively, the <code class="docutils literal notranslate"><span class="pre">ALL</span></code>
option in the <span class="target" id="index-1-command:cpack_configure_downloads"></span><a class="reference internal" href="../module/CPackComponent.html#command:cpack_configure_downloads" title="cpack_configure_downloads"><code class="xref cmake cmake-command docutils literal notranslate"><span class="pre">cpack_configure_downloads()</span></code></a> command specifies that
<cite>all</cite> components are to be be downloaded.</p>
<p>The <span class="target" id="index-1-command:cpack_ifw_add_repository"></span><a class="reference internal" href="../module/CPackIFW.html#command:cpack_ifw_add_repository" title="cpack_ifw_add_repository"><code class="xref cmake cmake-command docutils literal notranslate"><span class="pre">cpack_ifw_add_repository()</span></code></a> command and the
<span class="target" id="index-0-variable:CPACK_IFW_DOWNLOAD_ALL"></span><a class="reference internal" href="#variable:CPACK_IFW_DOWNLOAD_ALL" title="CPACK_IFW_DOWNLOAD_ALL"><code class="xref cmake cmake-variable docutils literal notranslate"><span class="pre">CPACK_IFW_DOWNLOAD_ALL</span></code></a> variable allow for more specific
configuration.</p>
<p>When there are online components, CPack will write them to archive files.
The help page of the <span class="target" id="index-0-module:CPackComponent"></span><a class="reference internal" href="../module/CPackComponent.html#module:CPackComponent" title="CPackComponent"><code class="xref cmake cmake-module docutils literal notranslate"><span class="pre">CPackComponent</span></code></a> module, especially the section
on the <span class="target" id="index-2-command:cpack_configure_downloads"></span><a class="reference internal" href="../module/CPackComponent.html#command:cpack_configure_downloads" title="cpack_configure_downloads"><code class="xref cmake cmake-command docutils literal notranslate"><span class="pre">cpack_configure_downloads()</span></code></a> function, explains how to make
these files accessible from a download URL.</p>
</section>
<section id="internationalization">
<h3><a class="toc-backref" href="#id11" role="doc-backlink">Internationalization</a><a class="headerlink" href="#internationalization" title="Permalink to this heading">¶</a></h3>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.9.</span></p>
</div>
<p>Some variables and command arguments support internationalization via
CMake script. This is an optional feature.</p>
<p>Installers created by QtIFW tools have built-in support for
internationalization and many phrases are localized to many languages,
but this does not apply to the description of your components and groups.</p>
<p>Localization of the description of your components and groups is useful for
users of your installers.</p>
<p>A localized variable or argument can contain a single default value, and
after that a set of pairs with the name of the locale and the localized value.</p>
<p>For example:</p>
<div class="highlight-cmake notranslate"><div class="highlight"><pre><span></span><span class="nf">set(</span><span class="no">LOCALIZABLE_VARIABLE</span><span class="w"> </span><span class="s">&quot;Default value&quot;</span>
<span class="w">  </span><span class="nb">en</span><span class="w"> </span><span class="s">&quot;English value&quot;</span>
<span class="w">  </span><span class="nb">en_US</span><span class="w"> </span><span class="s">&quot;American value&quot;</span>
<span class="w">  </span><span class="nb">en_GB</span><span class="w"> </span><span class="s">&quot;Great Britain value&quot;</span>
<span class="w">  </span><span class="nf">)</span>
</pre></div>
</div>
</section>
</section>
<section id="see-also">
<h2><a class="toc-backref" href="#id12" role="doc-backlink">See Also</a><a class="headerlink" href="#see-also" title="Permalink to this heading">¶</a></h2>
<p>Qt Installer Framework Manual:</p>
<ul class="simple">
<li><p>Index page:
<a class="reference external" href="https://doc.qt.io/qtinstallerframework/index.html">https://doc.qt.io/qtinstallerframework/index.html</a></p></li>
<li><p>Component Scripting:
<a class="reference external" href="https://doc.qt.io/qtinstallerframework/scripting.html">https://doc.qt.io/qtinstallerframework/scripting.html</a></p></li>
<li><p>Predefined Variables:
<a class="reference external" href="https://doc.qt.io/qtinstallerframework/scripting.html#predefined-variables">https://doc.qt.io/qtinstallerframework/scripting.html#predefined-variables</a></p></li>
<li><p>Promoting Updates:
<a class="reference external" href="https://doc.qt.io/qtinstallerframework/ifw-updates.html">https://doc.qt.io/qtinstallerframework/ifw-updates.html</a></p></li>
</ul>
<dl class="simple">
<dt>Download Qt Installer Framework for your platform from Qt site:</dt><dd><p><a class="reference external" href="https://download.qt.io/official_releases/qt-installer-framework">https://download.qt.io/official_releases/qt-installer-framework</a></p>
</dd>
</dl>
</section>
</section>


            <div class="clearer"></div>
          </div>
        </div>
      </div>
      <div class="sphinxsidebar" role="navigation" aria-label="main navigation">
        <div class="sphinxsidebarwrapper">
  <div>
    <h3><a href="../index.html">Table of Contents</a></h3>
    <ul>
<li><a class="reference internal" href="#">CPack IFW Generator</a><ul>
<li><a class="reference internal" href="#overview">Overview</a></li>
<li><a class="reference internal" href="#variables">Variables</a><ul>
<li><a class="reference internal" href="#debug">Debug</a></li>
<li><a class="reference internal" href="#package">Package</a></li>
<li><a class="reference internal" href="#components">Components</a></li>
<li><a class="reference internal" href="#qtifw-tools">QtIFW Tools</a></li>
<li><a class="reference internal" href="#hints-for-finding-qtifw">Hints for Finding QtIFW</a></li>
</ul>
</li>
<li><a class="reference internal" href="#other-settings">Other Settings</a><ul>
<li><a class="reference internal" href="#online-installer">Online installer</a></li>
<li><a class="reference internal" href="#internationalization">Internationalization</a></li>
</ul>
</li>
<li><a class="reference internal" href="#see-also">See Also</a></li>
</ul>
</li>
</ul>

  </div>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="innosetup.html"
                          title="previous chapter">CPack Inno Setup Generator</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="nsis.html"
                          title="next chapter">CPack NSIS Generator</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../_sources/cpack_gen/ifw.rst.txt"
            rel="nofollow">Show Source</a></li>
    </ul>
   </div>
<div id="searchbox" style="display: none" role="search">
  <h3 id="searchlabel">Quick search</h3>
    <div class="searchformwrapper">
    <form class="search" action="../search.html" method="get">
      <input type="text" name="q" aria-labelledby="searchlabel" autocomplete="off" autocorrect="off" autocapitalize="off" spellcheck="false"/>
      <input type="submit" value="Go" />
    </form>
    </div>
</div>
<script>document.getElementById('searchbox').style.display = "block"</script>
        </div>
      </div>
      <div class="clearer"></div>
    </div>
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             >index</a></li>
        <li class="right" >
          <a href="nsis.html" title="CPack NSIS Generator"
             >next</a> |</li>
        <li class="right" >
          <a href="innosetup.html" title="CPack Inno Setup Generator"
             >previous</a> |</li>
  <li>
    <img src="../_static/cmake-logo-16.png" alt=""
         style="vertical-align: middle; margin-top: -2px" />
  </li>
  <li>
    <a href="https://cmake.org/">CMake</a> &#187;
  </li>
  <li>
    <a href="../index.html">3.28.1 Documentation</a> &#187;
  </li>

          <li class="nav-item nav-item-1"><a href="../manual/cpack-generators.7.html" >cpack-generators(7)</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href="">CPack IFW Generator</a></li> 
      </ul>
    </div>

    <div class="footer" role="contentinfo">
        &#169; Copyright 2000-2023 Kitware, Inc. and Contributors.
      Created using <a href="https://www.sphinx-doc.org/">Sphinx</a> 6.2.1.
    </div>
  </body>
</html>