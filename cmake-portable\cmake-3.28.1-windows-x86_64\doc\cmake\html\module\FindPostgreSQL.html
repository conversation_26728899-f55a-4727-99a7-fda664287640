
<!DOCTYPE html>

<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="generator" content="Docutils 0.19: https://docutils.sourceforge.io/" />

  <title>FindPostgreSQL &mdash; CMake 3.28.1 Documentation</title>

    <link rel="stylesheet" type="text/css" href="../_static/pygments.css" />
    <link rel="stylesheet" type="text/css" href="../_static/cmake.css" />
    
    <script data-url_root="../" id="documentation_options" src="../_static/documentation_options.js"></script>
    <script src="../_static/doctools.js"></script>
    <script src="../_static/sphinx_highlight.js"></script>
    
    <link rel="icon" href="../_static/cmake-favicon.ico"/>
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="next" title="FindProducer" href="FindProducer.html" />
    <link rel="prev" title="FindPNG" href="FindPNG.html" />
 

  </head><body>

    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             accesskey="I">index</a></li>
        <li class="right" >
          <a href="FindProducer.html" title="FindProducer"
             accesskey="N">next</a> |</li>
        <li class="right" >
          <a href="FindPNG.html" title="FindPNG"
             accesskey="P">previous</a> |</li>
  <li>
    <img src="../_static/cmake-logo-16.png" alt=""
         style="vertical-align: middle; margin-top: -2px" />
  </li>
  <li>
    <a href="https://cmake.org/">CMake</a> &#187;
  </li>
  <li>
    <a href="../index.html">3.28.1 Documentation</a> &#187;
  </li>

          <li class="nav-item nav-item-1"><a href="../manual/cmake-modules.7.html" accesskey="U">cmake-modules(7)</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href="">FindPostgreSQL</a></li> 
      </ul>
    </div>  

    <div class="document">
      <div class="documentwrapper">
        <div class="bodywrapper">
          <div class="body" role="main">
            
  <section id="findpostgresql">
<span id="module:FindPostgreSQL"></span><h1>FindPostgreSQL<a class="headerlink" href="#findpostgresql" title="Permalink to this heading">¶</a></h1>
<p>Find the PostgreSQL installation.</p>
<section id="imported-targets">
<h2>IMPORTED Targets<a class="headerlink" href="#imported-targets" title="Permalink to this heading">¶</a></h2>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.14.</span></p>
</div>
<p>This module defines <span class="target" id="index-0-prop_tgt:IMPORTED"></span><a class="reference internal" href="../prop_tgt/IMPORTED.html#prop_tgt:IMPORTED" title="IMPORTED"><code class="xref cmake cmake-prop_tgt docutils literal notranslate"><span class="pre">IMPORTED</span></code></a> target <code class="docutils literal notranslate"><span class="pre">PostgreSQL::PostgreSQL</span></code>
if PostgreSQL has been found.</p>
</section>
<section id="result-variables">
<h2>Result Variables<a class="headerlink" href="#result-variables" title="Permalink to this heading">¶</a></h2>
<p>This module will set the following variables in your project:</p>
<dl class="simple">
<dt><code class="docutils literal notranslate"><span class="pre">PostgreSQL_FOUND</span></code></dt><dd><p>True if PostgreSQL is found.</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">PostgreSQL_LIBRARIES</span></code></dt><dd><p>the PostgreSQL libraries needed for linking</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">PostgreSQL_INCLUDE_DIRS</span></code></dt><dd><p>the directories of the PostgreSQL headers</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">PostgreSQL_LIBRARY_DIRS</span></code></dt><dd><p>the link directories for PostgreSQL libraries</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">PostgreSQL_VERSION_STRING</span></code></dt><dd><p>the version of PostgreSQL found</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">PostgreSQL_TYPE_INCLUDE_DIR</span></code></dt><dd><p>the directories of the PostgreSQL server headers</p>
</dd>
</dl>
</section>
<section id="components">
<h2>Components<a class="headerlink" href="#components" title="Permalink to this heading">¶</a></h2>
<p>This module contains additional <code class="docutils literal notranslate"><span class="pre">Server</span></code> component, that forcibly checks
for the presence of server headers. Note that <code class="docutils literal notranslate"><span class="pre">PostgreSQL_TYPE_INCLUDE_DIR</span></code>
is set regardless of the presence of the <code class="docutils literal notranslate"><span class="pre">Server</span></code> component in find_package call.</p>
</section>
</section>


            <div class="clearer"></div>
          </div>
        </div>
      </div>
      <div class="sphinxsidebar" role="navigation" aria-label="main navigation">
        <div class="sphinxsidebarwrapper">
  <div>
    <h3><a href="../index.html">Table of Contents</a></h3>
    <ul>
<li><a class="reference internal" href="#">FindPostgreSQL</a><ul>
<li><a class="reference internal" href="#imported-targets">IMPORTED Targets</a></li>
<li><a class="reference internal" href="#result-variables">Result Variables</a></li>
<li><a class="reference internal" href="#components">Components</a></li>
</ul>
</li>
</ul>

  </div>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="FindPNG.html"
                          title="previous chapter">FindPNG</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="FindProducer.html"
                          title="next chapter">FindProducer</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../_sources/module/FindPostgreSQL.rst.txt"
            rel="nofollow">Show Source</a></li>
    </ul>
   </div>
<div id="searchbox" style="display: none" role="search">
  <h3 id="searchlabel">Quick search</h3>
    <div class="searchformwrapper">
    <form class="search" action="../search.html" method="get">
      <input type="text" name="q" aria-labelledby="searchlabel" autocomplete="off" autocorrect="off" autocapitalize="off" spellcheck="false"/>
      <input type="submit" value="Go" />
    </form>
    </div>
</div>
<script>document.getElementById('searchbox').style.display = "block"</script>
        </div>
      </div>
      <div class="clearer"></div>
    </div>
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             >index</a></li>
        <li class="right" >
          <a href="FindProducer.html" title="FindProducer"
             >next</a> |</li>
        <li class="right" >
          <a href="FindPNG.html" title="FindPNG"
             >previous</a> |</li>
  <li>
    <img src="../_static/cmake-logo-16.png" alt=""
         style="vertical-align: middle; margin-top: -2px" />
  </li>
  <li>
    <a href="https://cmake.org/">CMake</a> &#187;
  </li>
  <li>
    <a href="../index.html">3.28.1 Documentation</a> &#187;
  </li>

          <li class="nav-item nav-item-1"><a href="../manual/cmake-modules.7.html" >cmake-modules(7)</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href="">FindPostgreSQL</a></li> 
      </ul>
    </div>

    <div class="footer" role="contentinfo">
        &#169; Copyright 2000-2023 Kitware, Inc. and Contributors.
      Created using <a href="https://www.sphinx-doc.org/">Sphinx</a> 6.2.1.
    </div>
  </body>
</html>