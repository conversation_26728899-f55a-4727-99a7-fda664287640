
<!DOCTYPE html>

<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="generator" content="Docutils 0.19: https://docutils.sourceforge.io/" />

  <title>string &mdash; CMake 3.28.1 Documentation</title>

    <link rel="stylesheet" type="text/css" href="../_static/pygments.css" />
    <link rel="stylesheet" type="text/css" href="../_static/cmake.css" />
    
    <script data-url_root="../" id="documentation_options" src="../_static/documentation_options.js"></script>
    <script src="../_static/doctools.js"></script>
    <script src="../_static/sphinx_highlight.js"></script>
    
    <link rel="icon" href="../_static/cmake-favicon.ico"/>
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="next" title="unset" href="unset.html" />
    <link rel="prev" title="site_name" href="site_name.html" />
 

  </head><body>

    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             accesskey="I">index</a></li>
        <li class="right" >
          <a href="unset.html" title="unset"
             accesskey="N">next</a> |</li>
        <li class="right" >
          <a href="site_name.html" title="site_name"
             accesskey="P">previous</a> |</li>
  <li>
    <img src="../_static/cmake-logo-16.png" alt=""
         style="vertical-align: middle; margin-top: -2px" />
  </li>
  <li>
    <a href="https://cmake.org/">CMake</a> &#187;
  </li>
  <li>
    <a href="../index.html">3.28.1 Documentation</a> &#187;
  </li>

          <li class="nav-item nav-item-1"><a href="../manual/cmake-commands.7.html" accesskey="U">cmake-commands(7)</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href="">string</a></li> 
      </ul>
    </div>  

    <div class="document">
      <div class="documentwrapper">
        <div class="bodywrapper">
          <div class="body" role="main">
            
  <section id="string">
<span id="command:string"></span><h1>string<a class="headerlink" href="#string" title="Permalink to this heading">¶</a></h1>
<p>String operations.</p>
<section id="synopsis">
<h2>Synopsis<a class="headerlink" href="#synopsis" title="Permalink to this heading">¶</a></h2>
<pre class="literal-block"><a class="reference internal" href="#search-and-replace">Search and Replace</a>
  string(<a class="reference internal" href="#find">FIND</a> &lt;string&gt; &lt;substring&gt; &lt;out-var&gt; [...])
  string(<a class="reference internal" href="#replace">REPLACE</a> &lt;match-string&gt; &lt;replace-string&gt; &lt;out-var&gt; &lt;input&gt;...)
  string(<a class="reference internal" href="#regex-match">REGEX MATCH</a> &lt;match-regex&gt; &lt;out-var&gt; &lt;input&gt;...)
  string(<a class="reference internal" href="#regex-matchall">REGEX MATCHALL</a> &lt;match-regex&gt; &lt;out-var&gt; &lt;input&gt;...)
  string(<a class="reference internal" href="#regex-replace">REGEX REPLACE</a> &lt;match-regex&gt; &lt;replace-expr&gt; &lt;out-var&gt; &lt;input&gt;...)

<a class="reference internal" href="#manipulation">Manipulation</a>
  string(<a class="reference internal" href="#append">APPEND</a> &lt;string-var&gt; [&lt;input&gt;...])
  string(<a class="reference internal" href="#prepend">PREPEND</a> &lt;string-var&gt; [&lt;input&gt;...])
  string(<a class="reference internal" href="#concat">CONCAT</a> &lt;out-var&gt; [&lt;input&gt;...])
  string(<a class="reference internal" href="#join">JOIN</a> &lt;glue&gt; &lt;out-var&gt; [&lt;input&gt;...])
  string(<a class="reference internal" href="#tolower">TOLOWER</a> &lt;string&gt; &lt;out-var&gt;)
  string(<a class="reference internal" href="#toupper">TOUPPER</a> &lt;string&gt; &lt;out-var&gt;)
  string(<a class="reference internal" href="#json-length">LENGTH</a> &lt;string&gt; &lt;out-var&gt;)
  string(<a class="reference internal" href="#substring">SUBSTRING</a> &lt;string&gt; &lt;begin&gt; &lt;length&gt; &lt;out-var&gt;)
  string(<a class="reference internal" href="#strip">STRIP</a> &lt;string&gt; &lt;out-var&gt;)
  string(<a class="reference internal" href="#genex-strip">GENEX_STRIP</a> &lt;string&gt; &lt;out-var&gt;)
  string(<a class="reference internal" href="#repeat">REPEAT</a> &lt;string&gt; &lt;count&gt; &lt;out-var&gt;)

<a class="reference internal" href="#comparison">Comparison</a>
  string(<a class="reference internal" href="#compare">COMPARE</a> &lt;op&gt; &lt;string1&gt; &lt;string2&gt; &lt;out-var&gt;)

<a class="reference internal" href="#hashing">Hashing</a>
  string(<a class="reference internal" href="#hash">&lt;HASH&gt;</a> &lt;out-var&gt; &lt;input&gt;)

<a class="reference internal" href="#generation">Generation</a>
  string(<a class="reference internal" href="#ascii">ASCII</a> &lt;number&gt;... &lt;out-var&gt;)
  string(<a class="reference internal" href="#hex">HEX</a> &lt;string&gt; &lt;out-var&gt;)
  string(<a class="reference internal" href="#configure">CONFIGURE</a> &lt;string&gt; &lt;out-var&gt; [...])
  string(<a class="reference internal" href="#make-c-identifier">MAKE_C_IDENTIFIER</a> &lt;string&gt; &lt;out-var&gt;)
  string(<a class="reference internal" href="#random">RANDOM</a> [&lt;option&gt;...] &lt;out-var&gt;)
  string(<a class="reference internal" href="#timestamp">TIMESTAMP</a> &lt;out-var&gt; [&lt;format string&gt;] [UTC])
  string(<a class="reference internal" href="#uuid">UUID</a> &lt;out-var&gt; ...)

<a class="reference internal" href="#json">JSON</a>
  string(JSON &lt;out-var&gt; [ERROR_VARIABLE &lt;error-var&gt;]
         {<a class="reference internal" href="#json-get">GET</a> | <a class="reference internal" href="#json-type">TYPE</a> | <a class="reference internal" href="#json-length">LENGTH</a> | <a class="reference internal" href="#json-remove">REMOVE</a>}
         &lt;json-string&gt; &lt;member|index&gt; [&lt;member|index&gt; ...])
  string(JSON &lt;out-var&gt; [ERROR_VARIABLE &lt;error-var&gt;]
         <a class="reference internal" href="#json-member">MEMBER</a> &lt;json-string&gt;
         [&lt;member|index&gt; ...] &lt;index&gt;)
  string(JSON &lt;out-var&gt; [ERROR_VARIABLE &lt;error-var&gt;]
         <a class="reference internal" href="#json-set">SET</a> &lt;json-string&gt;
         &lt;member|index&gt; [&lt;member|index&gt; ...] &lt;value&gt;)
  string(JSON &lt;out-var&gt; [ERROR_VARIABLE &lt;error-var&gt;]
         <a class="reference internal" href="#json-equal">EQUAL</a> &lt;json-string1&gt; &lt;json-string2&gt;)</pre>
</section>
<section id="search-and-replace">
<h2>Search and Replace<a class="headerlink" href="#search-and-replace" title="Permalink to this heading">¶</a></h2>
<section id="search-and-replace-with-plain-strings">
<h3>Search and Replace With Plain Strings<a class="headerlink" href="#search-and-replace-with-plain-strings" title="Permalink to this heading">¶</a></h3>
<dl class="cmake signature">
<dt class="sig sig-object cmake" id="find">
<span class="code cmake highlight sig-name descname"><span class="nf"><span class="pre">string(</span></span><span class="no"><span class="pre">FIND</span></span><span class="w"> </span><span class="nv"><span class="pre">&lt;string&gt;</span></span><span class="w"> </span><span class="nv"><span class="pre">&lt;substring&gt;</span></span><span class="w"> </span><span class="nv"><span class="pre">&lt;output_variable&gt;</span></span><span class="w"> </span><span class="p"><span class="pre">[</span></span><span class="no"><span class="pre">REVERSE</span></span><span class="p"><span class="pre">]</span></span><span class="nf"><span class="pre">)</span></span></span><a class="headerlink" href="#find" title="Permalink to this definition">¶</a></dt>
<dd><p>Return the position where the given <code class="docutils literal notranslate"><span class="pre">&lt;substring&gt;</span></code> was found in
the supplied <code class="docutils literal notranslate"><span class="pre">&lt;string&gt;</span></code>.  If the <code class="docutils literal notranslate"><span class="pre">REVERSE</span></code> flag was used, the command
will search for the position of the last occurrence of the specified
<code class="docutils literal notranslate"><span class="pre">&lt;substring&gt;</span></code>.  If the <code class="docutils literal notranslate"><span class="pre">&lt;substring&gt;</span></code> is not found, a position of -1 is
returned.</p>
<p>The <code class="docutils literal notranslate"><span class="pre">string(FIND)</span></code> subcommand treats all strings as ASCII-only characters.
The index stored in <code class="docutils literal notranslate"><span class="pre">&lt;output_variable&gt;</span></code> will also be counted in bytes,
so strings containing multi-byte characters may lead to unexpected results.</p>
</dd></dl>

<dl class="cmake signature">
<dt class="sig sig-object cmake" id="replace">
<span class="code cmake highlight sig-name descname"><span class="nf"><span class="pre">string(</span></span><span class="no"><span class="pre">REPLACE</span></span><span class="w"> </span><span class="nv"><span class="pre">&lt;match_string&gt;</span></span><span class="w"> </span><span class="nv"><span class="pre">&lt;replace_string&gt;</span></span><span class="w"> </span><span class="nv"><span class="pre">&lt;output_variable&gt;</span></span><span class="w"> </span><span class="nv"><span class="pre">&lt;input&gt;</span></span><span class="w"> </span><span class="p"><span class="pre">[</span></span><span class="nv"><span class="pre">&lt;input&gt;...</span></span><span class="p"><span class="pre">]</span></span><span class="nf"><span class="pre">)</span></span></span><a class="headerlink" href="#replace" title="Permalink to this definition">¶</a></dt>
<dd><p>Replace all occurrences of <code class="docutils literal notranslate"><span class="pre">&lt;match_string&gt;</span></code> in the <code class="docutils literal notranslate"><span class="pre">&lt;input&gt;</span></code>
with <code class="docutils literal notranslate"><span class="pre">&lt;replace_string&gt;</span></code> and store the result in the <code class="docutils literal notranslate"><span class="pre">&lt;output_variable&gt;</span></code>.</p>
</dd></dl>

</section>
<section id="search-and-replace-with-regular-expressions">
<h3>Search and Replace With Regular Expressions<a class="headerlink" href="#search-and-replace-with-regular-expressions" title="Permalink to this heading">¶</a></h3>
<dl class="cmake signature">
<dt class="sig sig-object cmake" id="regex-match">
<span class="code cmake highlight sig-name descname"><span class="nf"><span class="pre">string(</span></span><span class="no"><span class="pre">REGEX</span></span><span class="w"> </span><span class="no"><span class="pre">MATCH</span></span><span class="w"> </span><span class="nv"><span class="pre">&lt;regular_expression&gt;</span></span><span class="w"> </span><span class="nv"><span class="pre">&lt;output_variable&gt;</span></span><span class="w"> </span><span class="nv"><span class="pre">&lt;input&gt;</span></span><span class="w"> </span><span class="p"><span class="pre">[</span></span><span class="nv"><span class="pre">&lt;input&gt;...</span></span><span class="p"><span class="pre">]</span></span><span class="nf"><span class="pre">)</span></span></span><a class="headerlink" href="#regex-match" title="Permalink to this definition">¶</a></dt>
<dd><p>Match the <code class="docutils literal notranslate"><span class="pre">&lt;regular_expression&gt;</span></code> once and store the match in the
<code class="docutils literal notranslate"><span class="pre">&lt;output_variable&gt;</span></code>.
All <code class="docutils literal notranslate"><span class="pre">&lt;input&gt;</span></code> arguments are concatenated before matching.
Regular expressions are specified in the subsection just below.</p>
</dd></dl>

<dl class="cmake signature">
<dt class="sig sig-object cmake" id="regex-matchall">
<span class="code cmake highlight sig-name descname"><span class="nf"><span class="pre">string(</span></span><span class="no"><span class="pre">REGEX</span></span><span class="w"> </span><span class="no"><span class="pre">MATCHALL</span></span><span class="w"> </span><span class="nv"><span class="pre">&lt;regular_expression&gt;</span></span><span class="w"> </span><span class="nv"><span class="pre">&lt;output_variable&gt;</span></span><span class="w"> </span><span class="nv"><span class="pre">&lt;input&gt;</span></span><span class="w"> </span><span class="p"><span class="pre">[</span></span><span class="nv"><span class="pre">&lt;input&gt;...</span></span><span class="p"><span class="pre">]</span></span><span class="nf"><span class="pre">)</span></span></span><a class="headerlink" href="#regex-matchall" title="Permalink to this definition">¶</a></dt>
<dd><p>Match the <code class="docutils literal notranslate"><span class="pre">&lt;regular_expression&gt;</span></code> as many times as possible and store the
matches in the <code class="docutils literal notranslate"><span class="pre">&lt;output_variable&gt;</span></code> as a list.
All <code class="docutils literal notranslate"><span class="pre">&lt;input&gt;</span></code> arguments are concatenated before matching.</p>
</dd></dl>

<dl class="cmake signature">
<dt class="sig sig-object cmake" id="regex-replace">
<span class="code cmake highlight sig-name descname"><span class="nf"><span class="pre">string(</span></span><span class="no"><span class="pre">REGEX</span></span><span class="w"> </span><span class="no"><span class="pre">REPLACE</span></span><span class="w"> </span><span class="nv"><span class="pre">&lt;regular_expression&gt;</span></span><span class="w"> </span><span class="nv"><span class="pre">&lt;replacement_expression&gt;</span></span><span class="w"> </span><span class="nv"><span class="pre">&lt;output_variable&gt;</span></span><span class="w"> </span><span class="nv"><span class="pre">&lt;input&gt;</span></span><span class="w"> </span><span class="p"><span class="pre">[</span></span><span class="nv"><span class="pre">&lt;input&gt;...</span></span><span class="p"><span class="pre">]</span></span><span class="nf"><span class="pre">)</span></span></span><a class="headerlink" href="#regex-replace" title="Permalink to this definition">¶</a></dt>
<dd><p>Match the <code class="docutils literal notranslate"><span class="pre">&lt;regular_expression&gt;</span></code> as many times as possible and substitute
the <code class="docutils literal notranslate"><span class="pre">&lt;replacement_expression&gt;</span></code> for the match in the output.
All <code class="docutils literal notranslate"><span class="pre">&lt;input&gt;</span></code> arguments are concatenated before matching.</p>
<p>The <code class="docutils literal notranslate"><span class="pre">&lt;replacement_expression&gt;</span></code> may refer to parenthesis-delimited
subexpressions of the match using <code class="docutils literal notranslate"><span class="pre">\1</span></code>, <code class="docutils literal notranslate"><span class="pre">\2</span></code>, ..., <code class="docutils literal notranslate"><span class="pre">\9</span></code>.  Note that
two backslashes (<code class="docutils literal notranslate"><span class="pre">\\1</span></code>) are required in CMake code to get a backslash
through argument parsing.</p>
</dd></dl>

</section>
<section id="regex-specification">
<span id="id1"></span><h3>Regex Specification<a class="headerlink" href="#regex-specification" title="Permalink to this heading">¶</a></h3>
<p>The following characters have special meaning in regular expressions:</p>
<dl>
<dt><code class="docutils literal notranslate"><span class="pre">^</span></code></dt><dd><p>Matches at beginning of input</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">$</span></code></dt><dd><p>Matches at end of input</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">.</span></code></dt><dd><p>Matches any single character</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">\&lt;char&gt;</span></code></dt><dd><p>Matches the single character specified by <code class="docutils literal notranslate"><span class="pre">&lt;char&gt;</span></code>.  Use this to
match special regex characters, e.g. <code class="docutils literal notranslate"><span class="pre">\.</span></code> for a literal <code class="docutils literal notranslate"><span class="pre">.</span></code>
or <code class="docutils literal notranslate"><span class="pre">\\</span></code> for a literal backslash <code class="docutils literal notranslate"><span class="pre">\</span></code>.  Escaping a non-special
character is unnecessary but allowed, e.g. <code class="docutils literal notranslate"><span class="pre">\a</span></code> matches <code class="docutils literal notranslate"><span class="pre">a</span></code>.</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">[</span> <span class="pre">]</span></code></dt><dd><p>Matches any character(s) inside the brackets</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">[^</span> <span class="pre">]</span></code></dt><dd><p>Matches any character(s) not inside the brackets</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">-</span></code></dt><dd><p>Inside brackets, specifies an inclusive range between
characters on either side e.g. <code class="docutils literal notranslate"><span class="pre">[a-f]</span></code> is <code class="docutils literal notranslate"><span class="pre">[abcdef]</span></code>
To match a literal <code class="docutils literal notranslate"><span class="pre">-</span></code> using brackets, make it the first
or the last character e.g. <code class="docutils literal notranslate"><span class="pre">[+*/-]</span></code> matches basic
mathematical operators.</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">*</span></code></dt><dd><p>Matches preceding pattern zero or more times</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">+</span></code></dt><dd><p>Matches preceding pattern one or more times</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">?</span></code></dt><dd><p>Matches preceding pattern zero or once only</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">|</span></code></dt><dd><p>Matches a pattern on either side of the <code class="docutils literal notranslate"><span class="pre">|</span></code></p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">()</span></code></dt><dd><p>Saves a matched subexpression, which can be referenced
in the <code class="docutils literal notranslate"><span class="pre">REGEX</span> <span class="pre">REPLACE</span></code> operation.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.9: </span>All regular expression-related commands, including e.g.
<span class="target" id="index-0-command:if"></span><a class="reference internal" href="if.html#matches" title="if(matches)"><code class="xref cmake cmake-command docutils literal notranslate"><span class="pre">if(MATCHES)</span></code></a>, save subgroup matches in the variables
<span class="target" id="index-0-variable:CMAKE_MATCH_&lt;n&gt;"></span><a class="reference internal" href="../variable/CMAKE_MATCH_n.html#variable:CMAKE_MATCH_&lt;n&gt;" title="CMAKE_MATCH_&lt;n&gt;"><code class="xref cmake cmake-variable docutils literal notranslate"><span class="pre">CMAKE_MATCH_&lt;n&gt;</span></code></a> for <code class="docutils literal notranslate"><span class="pre">&lt;n&gt;</span></code> 0..9.</p>
</div>
</dd>
</dl>
<p><code class="docutils literal notranslate"><span class="pre">*</span></code>, <code class="docutils literal notranslate"><span class="pre">+</span></code> and <code class="docutils literal notranslate"><span class="pre">?</span></code> have higher precedence than concatenation.  <code class="docutils literal notranslate"><span class="pre">|</span></code>
has lower precedence than concatenation.  This means that the regular
expression <code class="docutils literal notranslate"><span class="pre">^ab+d$</span></code> matches <code class="docutils literal notranslate"><span class="pre">abbd</span></code> but not <code class="docutils literal notranslate"><span class="pre">ababd</span></code>, and the regular
expression <code class="docutils literal notranslate"><span class="pre">^(ab|cd)$</span></code> matches <code class="docutils literal notranslate"><span class="pre">ab</span></code> but not <code class="docutils literal notranslate"><span class="pre">abd</span></code>.</p>
<p>CMake language <a class="reference internal" href="../manual/cmake-language.7.html#escape-sequences"><span class="std std-ref">Escape Sequences</span></a> such as <code class="docutils literal notranslate"><span class="pre">\t</span></code>, <code class="docutils literal notranslate"><span class="pre">\r</span></code>, <code class="docutils literal notranslate"><span class="pre">\n</span></code>,
and <code class="docutils literal notranslate"><span class="pre">\\</span></code> may be used to construct literal tabs, carriage returns,
newlines, and backslashes (respectively) to pass in a regex.  For example:</p>
<ul class="simple">
<li><p>The quoted argument <code class="docutils literal notranslate"><span class="pre">&quot;[</span> <span class="pre">\t\r\n]&quot;</span></code> specifies a regex that matches
any single whitespace character.</p></li>
<li><p>The quoted argument <code class="docutils literal notranslate"><span class="pre">&quot;[/\\]&quot;</span></code> specifies a regex that matches
a single forward slash <code class="docutils literal notranslate"><span class="pre">/</span></code> or backslash <code class="docutils literal notranslate"><span class="pre">\</span></code>.</p></li>
<li><p>The quoted argument <code class="docutils literal notranslate"><span class="pre">&quot;[A-Za-z0-9_]&quot;</span></code> specifies a regex that matches
any single &quot;word&quot; character in the C locale.</p></li>
<li><p>The quoted argument <code class="docutils literal notranslate"><span class="pre">&quot;\\(\\a\\+b\\)&quot;</span></code> specifies a regex that matches
the exact string <code class="docutils literal notranslate"><span class="pre">(a+b)</span></code>.  Each <code class="docutils literal notranslate"><span class="pre">\\</span></code> is parsed in a quoted argument
as just <code class="docutils literal notranslate"><span class="pre">\</span></code>, so the regex itself is actually <code class="docutils literal notranslate"><span class="pre">\(\a\+\b\)</span></code>.  This
can alternatively be specified in a <a class="reference internal" href="../manual/cmake-language.7.html#bracket-argument"><span class="std std-ref">Bracket Argument</span></a> without
having to escape the backslashes, e.g. <code class="docutils literal notranslate"><span class="pre">[[\(\a\+\b\)]]</span></code>.</p></li>
</ul>
</section>
</section>
<section id="manipulation">
<h2>Manipulation<a class="headerlink" href="#manipulation" title="Permalink to this heading">¶</a></h2>
<dl class="cmake signature">
<dt class="sig sig-object cmake" id="append">
<span class="code cmake highlight sig-name descname"><span class="nf"><span class="pre">string(</span></span><span class="no"><span class="pre">APPEND</span></span><span class="w"> </span><span class="nv"><span class="pre">&lt;string_variable&gt;</span></span><span class="w"> </span><span class="p"><span class="pre">[</span></span><span class="nv"><span class="pre">&lt;input&gt;...</span></span><span class="p"><span class="pre">]</span></span><span class="nf"><span class="pre">)</span></span></span><a class="headerlink" href="#append" title="Permalink to this definition">¶</a></dt>
<dd><div class="versionadded">
<p><span class="versionmodified added">New in version 3.4.</span></p>
</div>
<p>Append all the <code class="docutils literal notranslate"><span class="pre">&lt;input&gt;</span></code> arguments to the string.</p>
</dd></dl>

<dl class="cmake signature">
<dt class="sig sig-object cmake" id="prepend">
<span class="code cmake highlight sig-name descname"><span class="nf"><span class="pre">string(</span></span><span class="no"><span class="pre">PREPEND</span></span><span class="w"> </span><span class="nv"><span class="pre">&lt;string_variable&gt;</span></span><span class="w"> </span><span class="p"><span class="pre">[</span></span><span class="nv"><span class="pre">&lt;input&gt;...</span></span><span class="p"><span class="pre">]</span></span><span class="nf"><span class="pre">)</span></span></span><a class="headerlink" href="#prepend" title="Permalink to this definition">¶</a></dt>
<dd><div class="versionadded">
<p><span class="versionmodified added">New in version 3.10.</span></p>
</div>
<p>Prepend all the <code class="docutils literal notranslate"><span class="pre">&lt;input&gt;</span></code> arguments to the string.</p>
</dd></dl>

<dl class="cmake signature">
<dt class="sig sig-object cmake" id="concat">
<span class="code cmake highlight sig-name descname"><span class="nf"><span class="pre">string(</span></span><span class="no"><span class="pre">CONCAT</span></span><span class="w"> </span><span class="nv"><span class="pre">&lt;output_variable&gt;</span></span><span class="w"> </span><span class="p"><span class="pre">[</span></span><span class="nv"><span class="pre">&lt;input&gt;...</span></span><span class="p"><span class="pre">]</span></span><span class="nf"><span class="pre">)</span></span></span><a class="headerlink" href="#concat" title="Permalink to this definition">¶</a></dt>
<dd><p>Concatenate all the <code class="docutils literal notranslate"><span class="pre">&lt;input&gt;</span></code> arguments together and store
the result in the named <code class="docutils literal notranslate"><span class="pre">&lt;output_variable&gt;</span></code>.</p>
</dd></dl>

<dl class="cmake signature">
<dt class="sig sig-object cmake" id="join">
<span class="code cmake highlight sig-name descname"><span class="nf"><span class="pre">string(</span></span><span class="no"><span class="pre">JOIN</span></span><span class="w"> </span><span class="nv"><span class="pre">&lt;glue&gt;</span></span><span class="w"> </span><span class="nv"><span class="pre">&lt;output_variable&gt;</span></span><span class="w"> </span><span class="p"><span class="pre">[</span></span><span class="nv"><span class="pre">&lt;input&gt;...</span></span><span class="p"><span class="pre">]</span></span><span class="nf"><span class="pre">)</span></span></span><a class="headerlink" href="#join" title="Permalink to this definition">¶</a></dt>
<dd><div class="versionadded">
<p><span class="versionmodified added">New in version 3.12.</span></p>
</div>
<p>Join all the <code class="docutils literal notranslate"><span class="pre">&lt;input&gt;</span></code> arguments together using the <code class="docutils literal notranslate"><span class="pre">&lt;glue&gt;</span></code>
string and store the result in the named <code class="docutils literal notranslate"><span class="pre">&lt;output_variable&gt;</span></code>.</p>
<p>To join a list's elements, prefer to use the <code class="docutils literal notranslate"><span class="pre">JOIN</span></code> operator
from the <span class="target" id="index-0-command:list"></span><a class="reference internal" href="list.html#command:list" title="list"><code class="xref cmake cmake-command docutils literal notranslate"><span class="pre">list()</span></code></a> command.  This allows for the elements to have
special characters like <code class="docutils literal notranslate"><span class="pre">;</span></code> in them.</p>
</dd></dl>

<dl class="cmake signature">
<dt class="sig sig-object cmake" id="tolower">
<span class="code cmake highlight sig-name descname"><span class="nf"><span class="pre">string(</span></span><span class="no"><span class="pre">TOLOWER</span></span><span class="w"> </span><span class="nv"><span class="pre">&lt;string&gt;</span></span><span class="w"> </span><span class="nv"><span class="pre">&lt;output_variable&gt;</span></span><span class="nf"><span class="pre">)</span></span></span><a class="headerlink" href="#tolower" title="Permalink to this definition">¶</a></dt>
<dd><p>Convert <code class="docutils literal notranslate"><span class="pre">&lt;string&gt;</span></code> to lower characters.</p>
</dd></dl>

<dl class="cmake signature">
<dt class="sig sig-object cmake" id="toupper">
<span class="code cmake highlight sig-name descname"><span class="nf"><span class="pre">string(</span></span><span class="no"><span class="pre">TOUPPER</span></span><span class="w"> </span><span class="nv"><span class="pre">&lt;string&gt;</span></span><span class="w"> </span><span class="nv"><span class="pre">&lt;output_variable&gt;</span></span><span class="nf"><span class="pre">)</span></span></span><a class="headerlink" href="#toupper" title="Permalink to this definition">¶</a></dt>
<dd><p>Convert <code class="docutils literal notranslate"><span class="pre">&lt;string&gt;</span></code> to upper characters.</p>
</dd></dl>

<dl class="cmake signature">
<dt class="sig sig-object cmake" id="length">
<span class="code cmake highlight sig-name descname"><span class="nf"><span class="pre">string(</span></span><span class="no"><span class="pre">LENGTH</span></span><span class="w"> </span><span class="nv"><span class="pre">&lt;string&gt;</span></span><span class="w"> </span><span class="nv"><span class="pre">&lt;output_variable&gt;</span></span><span class="nf"><span class="pre">)</span></span></span><a class="headerlink" href="#length" title="Permalink to this definition">¶</a></dt>
<dd><p>Store in an <code class="docutils literal notranslate"><span class="pre">&lt;output_variable&gt;</span></code> a given string's length in bytes.
Note that this means if <code class="docutils literal notranslate"><span class="pre">&lt;string&gt;</span></code> contains multi-byte characters,
the result stored in <code class="docutils literal notranslate"><span class="pre">&lt;output_variable&gt;</span></code> will <em>not</em> be
the number of characters.</p>
</dd></dl>

<dl class="cmake signature">
<dt class="sig sig-object cmake" id="substring">
<span class="code cmake highlight sig-name descname"><span class="nf"><span class="pre">string(</span></span><span class="no"><span class="pre">SUBSTRING</span></span><span class="w"> </span><span class="nv"><span class="pre">&lt;string&gt;</span></span><span class="w"> </span><span class="nv"><span class="pre">&lt;begin&gt;</span></span><span class="w"> </span><span class="nv"><span class="pre">&lt;length&gt;</span></span><span class="w"> </span><span class="nv"><span class="pre">&lt;output_variable&gt;</span></span><span class="nf"><span class="pre">)</span></span></span><a class="headerlink" href="#substring" title="Permalink to this definition">¶</a></dt>
<dd><p>Store in an <code class="docutils literal notranslate"><span class="pre">&lt;output_variable&gt;</span></code> a substring of a given <code class="docutils literal notranslate"><span class="pre">&lt;string&gt;</span></code>.  If
<code class="docutils literal notranslate"><span class="pre">&lt;length&gt;</span></code> is <code class="docutils literal notranslate"><span class="pre">-1</span></code> the remainder of the string starting at <code class="docutils literal notranslate"><span class="pre">&lt;begin&gt;</span></code>
will be returned.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.2: </span>If <code class="docutils literal notranslate"><span class="pre">&lt;string&gt;</span></code> is shorter than <code class="docutils literal notranslate"><span class="pre">&lt;length&gt;</span></code>
then the end of the string is used instead.
Previous versions of CMake reported an error in this case.</p>
</div>
<p>Both <code class="docutils literal notranslate"><span class="pre">&lt;begin&gt;</span></code> and <code class="docutils literal notranslate"><span class="pre">&lt;length&gt;</span></code> are counted in bytes, so care must
be exercised if <code class="docutils literal notranslate"><span class="pre">&lt;string&gt;</span></code> could contain multi-byte characters.</p>
</dd></dl>

<dl class="cmake signature">
<dt class="sig sig-object cmake" id="strip">
<span class="code cmake highlight sig-name descname"><span class="nf"><span class="pre">string(</span></span><span class="no"><span class="pre">STRIP</span></span><span class="w"> </span><span class="nv"><span class="pre">&lt;string&gt;</span></span><span class="w"> </span><span class="nv"><span class="pre">&lt;output_variable&gt;</span></span><span class="nf"><span class="pre">)</span></span></span><a class="headerlink" href="#strip" title="Permalink to this definition">¶</a></dt>
<dd><p>Store in an <code class="docutils literal notranslate"><span class="pre">&lt;output_variable&gt;</span></code> a substring of a given <code class="docutils literal notranslate"><span class="pre">&lt;string&gt;</span></code>
with leading and trailing spaces removed.</p>
</dd></dl>

<dl class="cmake signature">
<dt class="sig sig-object cmake" id="genex-strip">
<span class="code cmake highlight sig-name descname"><span class="nf"><span class="pre">string(</span></span><span class="no"><span class="pre">GENEX_STRIP</span></span><span class="w"> </span><span class="nv"><span class="pre">&lt;string&gt;</span></span><span class="w"> </span><span class="nv"><span class="pre">&lt;output_variable&gt;</span></span><span class="nf"><span class="pre">)</span></span></span><a class="headerlink" href="#genex-strip" title="Permalink to this definition">¶</a></dt>
<dd><div class="versionadded">
<p><span class="versionmodified added">New in version 3.1.</span></p>
</div>
<p>Strip any <span class="target" id="index-0-manual:cmake-generator-expressions(7)"></span><a class="reference internal" href="../manual/cmake-generator-expressions.7.html#manual:cmake-generator-expressions(7)" title="cmake-generator-expressions(7)"><code class="xref cmake cmake-manual docutils literal notranslate"><span class="pre">generator</span> <span class="pre">expressions</span></code></a>
from the input <code class="docutils literal notranslate"><span class="pre">&lt;string&gt;</span></code> and store the result
in the <code class="docutils literal notranslate"><span class="pre">&lt;output_variable&gt;</span></code>.</p>
</dd></dl>

<dl class="cmake signature">
<dt class="sig sig-object cmake" id="repeat">
<span class="code cmake highlight sig-name descname"><span class="nf"><span class="pre">string(</span></span><span class="no"><span class="pre">REPEAT</span></span><span class="w"> </span><span class="nv"><span class="pre">&lt;string&gt;</span></span><span class="w"> </span><span class="nv"><span class="pre">&lt;count&gt;</span></span><span class="w"> </span><span class="nv"><span class="pre">&lt;output_variable&gt;</span></span><span class="nf"><span class="pre">)</span></span></span><a class="headerlink" href="#repeat" title="Permalink to this definition">¶</a></dt>
<dd><div class="versionadded">
<p><span class="versionmodified added">New in version 3.15.</span></p>
</div>
<p>Produce the output string as the input <code class="docutils literal notranslate"><span class="pre">&lt;string&gt;</span></code>
repeated <code class="docutils literal notranslate"><span class="pre">&lt;count&gt;</span></code> times.</p>
</dd></dl>

</section>
<section id="comparison">
<h2>Comparison<a class="headerlink" href="#comparison" title="Permalink to this heading">¶</a></h2>
<span class="target" id="compare"></span><dl class="cmake signature">
<dt class="sig sig-object cmake" id="compare-less">
<span class="code cmake highlight sig-name descname"><span class="nf"><span class="pre">string(</span></span><span class="no"><span class="pre">COMPARE</span></span><span class="w"> </span><span class="no"><span class="pre">LESS</span></span><span class="w"> </span><span class="nv"><span class="pre">&lt;string1&gt;</span></span><span class="w"> </span><span class="nv"><span class="pre">&lt;string2&gt;</span></span><span class="w"> </span><span class="nv"><span class="pre">&lt;output_variable&gt;</span></span><span class="nf"><span class="pre">)</span></span></span><a class="headerlink" href="#compare-less" title="Permalink to this definition">¶</a></dt>
<dt class="sig sig-object cmake" id="compare-greater">
<span class="code cmake highlight sig-name descname"><span class="nf"><span class="pre">string(</span></span><span class="no"><span class="pre">COMPARE</span></span><span class="w"> </span><span class="no"><span class="pre">GREATER</span></span><span class="w"> </span><span class="nv"><span class="pre">&lt;string1&gt;</span></span><span class="w"> </span><span class="nv"><span class="pre">&lt;string2&gt;</span></span><span class="w"> </span><span class="nv"><span class="pre">&lt;output_variable&gt;</span></span><span class="nf"><span class="pre">)</span></span></span><a class="headerlink" href="#compare-greater" title="Permalink to this definition">¶</a></dt>
<dt class="sig sig-object cmake" id="compare-equal">
<span class="code cmake highlight sig-name descname"><span class="nf"><span class="pre">string(</span></span><span class="no"><span class="pre">COMPARE</span></span><span class="w"> </span><span class="no"><span class="pre">EQUAL</span></span><span class="w"> </span><span class="nv"><span class="pre">&lt;string1&gt;</span></span><span class="w"> </span><span class="nv"><span class="pre">&lt;string2&gt;</span></span><span class="w"> </span><span class="nv"><span class="pre">&lt;output_variable&gt;</span></span><span class="nf"><span class="pre">)</span></span></span><a class="headerlink" href="#compare-equal" title="Permalink to this definition">¶</a></dt>
<dt class="sig sig-object cmake" id="compare-notequal">
<span class="code cmake highlight sig-name descname"><span class="nf"><span class="pre">string(</span></span><span class="no"><span class="pre">COMPARE</span></span><span class="w"> </span><span class="no"><span class="pre">NOTEQUAL</span></span><span class="w"> </span><span class="nv"><span class="pre">&lt;string1&gt;</span></span><span class="w"> </span><span class="nv"><span class="pre">&lt;string2&gt;</span></span><span class="w"> </span><span class="nv"><span class="pre">&lt;output_variable&gt;</span></span><span class="nf"><span class="pre">)</span></span></span><a class="headerlink" href="#compare-notequal" title="Permalink to this definition">¶</a></dt>
<dt class="sig sig-object cmake" id="compare-less-equal">
<span class="code cmake highlight sig-name descname"><span class="nf"><span class="pre">string(</span></span><span class="no"><span class="pre">COMPARE</span></span><span class="w"> </span><span class="no"><span class="pre">LESS_EQUAL</span></span><span class="w"> </span><span class="nv"><span class="pre">&lt;string1&gt;</span></span><span class="w"> </span><span class="nv"><span class="pre">&lt;string2&gt;</span></span><span class="w"> </span><span class="nv"><span class="pre">&lt;output_variable&gt;</span></span><span class="nf"><span class="pre">)</span></span></span><a class="headerlink" href="#compare-less-equal" title="Permalink to this definition">¶</a></dt>
<dt class="sig sig-object cmake" id="compare-greater-equal">
<span class="code cmake highlight sig-name descname"><span class="nf"><span class="pre">string(</span></span><span class="no"><span class="pre">COMPARE</span></span><span class="w"> </span><span class="no"><span class="pre">GREATER_EQUAL</span></span><span class="w"> </span><span class="nv"><span class="pre">&lt;string1&gt;</span></span><span class="w"> </span><span class="nv"><span class="pre">&lt;string2&gt;</span></span><span class="w"> </span><span class="nv"><span class="pre">&lt;output_variable&gt;</span></span><span class="nf"><span class="pre">)</span></span></span><a class="headerlink" href="#compare-greater-equal" title="Permalink to this definition">¶</a></dt>
<dd><p>Compare the strings and store true or false in the <code class="docutils literal notranslate"><span class="pre">&lt;output_variable&gt;</span></code>.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.7: </span>Added the <code class="docutils literal notranslate"><span class="pre">LESS_EQUAL</span></code> and <code class="docutils literal notranslate"><span class="pre">GREATER_EQUAL</span></code> options.</p>
</div>
</dd></dl>

</section>
<section id="hashing">
<span id="supported-hash-algorithms"></span><h2>Hashing<a class="headerlink" href="#hashing" title="Permalink to this heading">¶</a></h2>
<dl class="cmake signature">
<dt class="sig sig-object cmake" id="hash">
<span class="code cmake highlight sig-name descname"><span class="nf"><span class="pre">string(</span></span><span class="nv"><span class="pre">&lt;HASH&gt;</span></span><span class="w"> </span><span class="nv"><span class="pre">&lt;output_variable&gt;</span></span><span class="w"> </span><span class="nv"><span class="pre">&lt;input&gt;</span></span><span class="nf"><span class="pre">)</span></span></span><a class="headerlink" href="#hash" title="Permalink to this definition">¶</a></dt>
<dd><p>Compute a cryptographic hash of the <code class="docutils literal notranslate"><span class="pre">&lt;input&gt;</span></code> string.
The supported <code class="docutils literal notranslate"><span class="pre">&lt;HASH&gt;</span></code> algorithm names are:</p>
<dl class="simple">
<dt><code class="docutils literal notranslate"><span class="pre">MD5</span></code></dt><dd><p>Message-Digest Algorithm 5, RFC 1321.</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">SHA1</span></code></dt><dd><p>US Secure Hash Algorithm 1, RFC 3174.</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">SHA224</span></code></dt><dd><p>US Secure Hash Algorithms, RFC 4634.</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">SHA256</span></code></dt><dd><p>US Secure Hash Algorithms, RFC 4634.</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">SHA384</span></code></dt><dd><p>US Secure Hash Algorithms, RFC 4634.</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">SHA512</span></code></dt><dd><p>US Secure Hash Algorithms, RFC 4634.</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">SHA3_224</span></code></dt><dd><p>Keccak SHA-3.</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">SHA3_256</span></code></dt><dd><p>Keccak SHA-3.</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">SHA3_384</span></code></dt><dd><p>Keccak SHA-3.</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">SHA3_512</span></code></dt><dd><p>Keccak SHA-3.</p>
</dd>
</dl>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.8: </span>Added the <code class="docutils literal notranslate"><span class="pre">SHA3_*</span></code> hash algorithms.</p>
</div>
</dd></dl>

</section>
<section id="generation">
<h2>Generation<a class="headerlink" href="#generation" title="Permalink to this heading">¶</a></h2>
<dl class="cmake signature">
<dt class="sig sig-object cmake" id="ascii">
<span class="code cmake highlight sig-name descname"><span class="nf"><span class="pre">string(</span></span><span class="no"><span class="pre">ASCII</span></span><span class="w"> </span><span class="nv"><span class="pre">&lt;number&gt;</span></span><span class="w"> </span><span class="p"><span class="pre">[</span></span><span class="nv"><span class="pre">&lt;number&gt;</span></span><span class="nbsp"> </span><span class="p"><span class="pre">...]</span></span><span class="w"> </span><span class="nv"><span class="pre">&lt;output_variable&gt;</span></span><span class="nf"><span class="pre">)</span></span></span><a class="headerlink" href="#ascii" title="Permalink to this definition">¶</a></dt>
<dd><p>Convert all numbers into corresponding ASCII characters.</p>
</dd></dl>

<dl class="cmake signature">
<dt class="sig sig-object cmake" id="hex">
<span class="code cmake highlight sig-name descname"><span class="nf"><span class="pre">string(</span></span><span class="no"><span class="pre">HEX</span></span><span class="w"> </span><span class="nv"><span class="pre">&lt;string&gt;</span></span><span class="w"> </span><span class="nv"><span class="pre">&lt;output_variable&gt;</span></span><span class="nf"><span class="pre">)</span></span></span><a class="headerlink" href="#hex" title="Permalink to this definition">¶</a></dt>
<dd><div class="versionadded">
<p><span class="versionmodified added">New in version 3.18.</span></p>
</div>
<p>Convert each byte in the input <code class="docutils literal notranslate"><span class="pre">&lt;string&gt;</span></code> to its hexadecimal representation
and store the concatenated hex digits in the <code class="docutils literal notranslate"><span class="pre">&lt;output_variable&gt;</span></code>.
Letters in the output (<code class="docutils literal notranslate"><span class="pre">a</span></code> through <code class="docutils literal notranslate"><span class="pre">f</span></code>) are in lowercase.</p>
</dd></dl>

<dl class="cmake signature">
<dt class="sig sig-object cmake" id="configure">
<span class="code cmake highlight sig-name descname"><span class="nf"><span class="pre">string(</span></span><span class="no"><span class="pre">CONFIGURE</span></span><span class="w"> </span><span class="nv"><span class="pre">&lt;string&gt;</span></span><span class="w"> </span><span class="nv"><span class="pre">&lt;output_variable&gt;</span></span><span class="w"> </span><span class="p"><span class="pre">[</span></span><span class="no"><span class="pre">&#64;ONLY</span></span><span class="p"><span class="pre">]</span></span><span class="w"> </span><span class="p"><span class="pre">[</span></span><span class="no"><span class="pre">ESCAPE_QUOTES</span></span><span class="p"><span class="pre">]</span></span><span class="nf"><span class="pre">)</span></span></span><a class="headerlink" href="#configure" title="Permalink to this definition">¶</a></dt>
<dd><p>Transform a <code class="docutils literal notranslate"><span class="pre">&lt;string&gt;</span></code> like <span class="target" id="index-0-command:configure_file"></span><a class="reference internal" href="configure_file.html#command:configure_file" title="configure_file"><code class="xref cmake cmake-command docutils literal notranslate"><span class="pre">configure_file()</span></code></a> transforms a file.</p>
</dd></dl>

<dl class="cmake signature">
<dt class="sig sig-object cmake" id="make-c-identifier">
<span class="code cmake highlight sig-name descname"><span class="nf"><span class="pre">string(</span></span><span class="no"><span class="pre">MAKE_C_IDENTIFIER</span></span><span class="w"> </span><span class="nv"><span class="pre">&lt;string&gt;</span></span><span class="w"> </span><span class="nv"><span class="pre">&lt;output_variable&gt;</span></span><span class="nf"><span class="pre">)</span></span></span><a class="headerlink" href="#make-c-identifier" title="Permalink to this definition">¶</a></dt>
<dd><p>Convert each non-alphanumeric character in the input <code class="docutils literal notranslate"><span class="pre">&lt;string&gt;</span></code> to an
underscore and store the result in the <code class="docutils literal notranslate"><span class="pre">&lt;output_variable&gt;</span></code>.  If the first
character of the <code class="docutils literal notranslate"><span class="pre">&lt;string&gt;</span></code> is a digit, an underscore will also be
prepended to the result.</p>
</dd></dl>

<dl class="cmake signature">
<dt class="sig sig-object cmake" id="random">
<span class="code cmake highlight sig-name descname"><span class="nf"><span class="pre">string(</span></span><span class="no"><span class="pre">RANDOM</span></span><span class="w"> </span><span class="p"><span class="pre">[</span></span><span class="no"><span class="pre">LENGTH</span></span><span class="nbsp"> </span><span class="nv"><span class="pre">&lt;length&gt;</span></span><span class="p"><span class="pre">]</span></span><span class="w"> </span><span class="p"><span class="pre">[</span></span><span class="no"><span class="pre">ALPHABET</span></span><span class="nbsp"> </span><span class="nv"><span class="pre">&lt;alphabet&gt;</span></span><span class="p"><span class="pre">]</span></span><span class="w"> </span><span class="p"><span class="pre">[</span></span><span class="no"><span class="pre">RANDOM_SEED</span></span><span class="nbsp"> </span><span class="nv"><span class="pre">&lt;seed&gt;</span></span><span class="p"><span class="pre">]</span></span><span class="w"> </span><span class="nv"><span class="pre">&lt;output_variable&gt;</span></span><span class="nf"><span class="pre">)</span></span></span><a class="headerlink" href="#random" title="Permalink to this definition">¶</a></dt>
<dd><p>Return a random string of given <code class="docutils literal notranslate"><span class="pre">&lt;length&gt;</span></code> consisting of
characters from the given <code class="docutils literal notranslate"><span class="pre">&lt;alphabet&gt;</span></code>.  Default length is 5 characters
and default alphabet is all numbers and upper and lower case letters.
If an integer <code class="docutils literal notranslate"><span class="pre">RANDOM_SEED</span></code> is given, its value will be used to seed the
random number generator.</p>
</dd></dl>

<dl class="cmake signature">
<dt class="sig sig-object cmake" id="timestamp">
<span class="code cmake highlight sig-name descname"><span class="nf"><span class="pre">string(</span></span><span class="no"><span class="pre">TIMESTAMP</span></span><span class="w"> </span><span class="nv"><span class="pre">&lt;output_variable&gt;</span></span><span class="w"> </span><span class="p"><span class="pre">[</span></span><span class="nv"><span class="pre">&lt;format_string&gt;</span></span><span class="p"><span class="pre">]</span></span><span class="w"> </span><span class="p"><span class="pre">[</span></span><span class="no"><span class="pre">UTC</span></span><span class="p"><span class="pre">]</span></span><span class="nf"><span class="pre">)</span></span></span><a class="headerlink" href="#timestamp" title="Permalink to this definition">¶</a></dt>
<dd><p>Write a string representation of the current date
and/or time to the <code class="docutils literal notranslate"><span class="pre">&lt;output_variable&gt;</span></code>.</p>
<p>If the command is unable to obtain a timestamp, the <code class="docutils literal notranslate"><span class="pre">&lt;output_variable&gt;</span></code>
will be set to the empty string <code class="docutils literal notranslate"><span class="pre">&quot;&quot;</span></code>.</p>
<p>The optional <code class="docutils literal notranslate"><span class="pre">UTC</span></code> flag requests the current date/time representation to
be in Coordinated Universal Time (UTC) rather than local time.</p>
<p>The optional <code class="docutils literal notranslate"><span class="pre">&lt;format_string&gt;</span></code> may contain the following format
specifiers:</p>
<dl>
<dt><code class="docutils literal notranslate"><span class="pre">%%</span></code></dt><dd><div class="versionadded">
<p><span class="versionmodified added">New in version 3.8.</span></p>
</div>
<p>A literal percent sign (%).</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">%d</span></code></dt><dd><p>The day of the current month (01-31).</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">%H</span></code></dt><dd><p>The hour on a 24-hour clock (00-23).</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">%I</span></code></dt><dd><p>The hour on a 12-hour clock (01-12).</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">%j</span></code></dt><dd><p>The day of the current year (001-366).</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">%m</span></code></dt><dd><p>The month of the current year (01-12).</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">%b</span></code></dt><dd><div class="versionadded">
<p><span class="versionmodified added">New in version 3.7.</span></p>
</div>
<p>Abbreviated month name (e.g. Oct).</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">%B</span></code></dt><dd><div class="versionadded">
<p><span class="versionmodified added">New in version 3.10.</span></p>
</div>
<p>Full month name (e.g. October).</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">%M</span></code></dt><dd><p>The minute of the current hour (00-59).</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">%s</span></code></dt><dd><div class="versionadded">
<p><span class="versionmodified added">New in version 3.6.</span></p>
</div>
<p>Seconds since midnight (UTC) 1-Jan-1970 (UNIX time).</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">%S</span></code></dt><dd><p>The second of the current minute.  60 represents a leap second. (00-60)</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">%f</span></code></dt><dd><div class="versionadded">
<p><span class="versionmodified added">New in version 3.23.</span></p>
</div>
<p>The microsecond of the current second (000000-999999).</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">%U</span></code></dt><dd><p>The week number of the current year (00-53).</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">%V</span></code></dt><dd><div class="versionadded">
<p><span class="versionmodified added">New in version 3.22.</span></p>
</div>
<p>The ISO 8601 week number of the current year (01-53).</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">%w</span></code></dt><dd><p>The day of the current week. 0 is Sunday. (0-6)</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">%a</span></code></dt><dd><div class="versionadded">
<p><span class="versionmodified added">New in version 3.7.</span></p>
</div>
<p>Abbreviated weekday name (e.g. Fri).</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">%A</span></code></dt><dd><div class="versionadded">
<p><span class="versionmodified added">New in version 3.10.</span></p>
</div>
<p>Full weekday name (e.g. Friday).</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">%y</span></code></dt><dd><p>The last two digits of the current year (00-99).</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">%Y</span></code></dt><dd><p>The current year.</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">%z</span></code></dt><dd><div class="versionadded">
<p><span class="versionmodified added">New in version 3.26.</span></p>
</div>
<p>The offset of the time zone from UTC, in hours and minutes,
with format <code class="docutils literal notranslate"><span class="pre">+hhmm</span></code> or <code class="docutils literal notranslate"><span class="pre">-hhmm</span></code>.</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">%Z</span></code></dt><dd><div class="versionadded">
<p><span class="versionmodified added">New in version 3.26.</span></p>
</div>
<p>The time zone name.</p>
</dd>
</dl>
<p>Unknown format specifiers will be ignored and copied to the output
as-is.</p>
<p>If no explicit <code class="docutils literal notranslate"><span class="pre">&lt;format_string&gt;</span></code> is given, it will default to:</p>
<div class="highlight-none notranslate"><div class="highlight"><pre><span></span>%Y-%m-%dT%H:%M:%S    for local time.
%Y-%m-%dT%H:%M:%SZ   for UTC.
</pre></div>
</div>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.8: </span>If the <code class="docutils literal notranslate"><span class="pre">SOURCE_DATE_EPOCH</span></code> environment variable is set,
its value will be used instead of the current time.
See <a class="reference external" href="https://reproducible-builds.org/specs/source-date-epoch/">https://reproducible-builds.org/specs/source-date-epoch/</a> for details.</p>
</div>
</dd></dl>

<dl class="cmake signature">
<dt class="sig sig-object cmake" id="uuid">
<span class="code cmake highlight sig-name descname"><span class="nf"><span class="pre">string(</span></span><span class="no"><span class="pre">UUID</span></span><span class="w"> </span><span class="nv"><span class="pre">&lt;output_variable&gt;</span></span><span class="w"> </span><span class="no"><span class="pre">NAMESPACE</span></span><span class="w"> </span><span class="nv"><span class="pre">&lt;namespace&gt;</span></span><span class="w"> </span><span class="no"><span class="pre">NAME</span></span><span class="w"> </span><span class="nv"><span class="pre">&lt;name&gt;</span></span><span class="w"> </span><span class="no"><span class="pre">TYPE</span></span><span class="w"> </span><span class="o"><span class="pre">&lt;</span></span><span class="no"><span class="pre">MD5</span></span><span class="p"><span class="pre">|</span></span><span class="no"><span class="pre">SHA1</span></span><span class="o"><span class="pre">&gt;</span></span><span class="w"> </span><span class="p"><span class="pre">[</span></span><span class="no"><span class="pre">UPPER</span></span><span class="p"><span class="pre">]</span></span><span class="nf"><span class="pre">)</span></span></span><a class="headerlink" href="#uuid" title="Permalink to this definition">¶</a></dt>
<dd><div class="versionadded">
<p><span class="versionmodified added">New in version 3.1.</span></p>
</div>
<p>Create a universally unique identifier (aka GUID) as per RFC4122
based on the hash of the combined values of <code class="docutils literal notranslate"><span class="pre">&lt;namespace&gt;</span></code>
(which itself has to be a valid UUID) and <code class="docutils literal notranslate"><span class="pre">&lt;name&gt;</span></code>.
The hash algorithm can be either <code class="docutils literal notranslate"><span class="pre">MD5</span></code> (Version 3 UUID) or
<code class="docutils literal notranslate"><span class="pre">SHA1</span></code> (Version 5 UUID).
A UUID has the format <code class="docutils literal notranslate"><span class="pre">xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx</span></code>
where each <code class="docutils literal notranslate"><span class="pre">x</span></code> represents a lower case hexadecimal character.
Where required, an uppercase representation can be requested
with the optional <code class="docutils literal notranslate"><span class="pre">UPPER</span></code> flag.</p>
</dd></dl>

</section>
<section id="json">
<span id="id2"></span><h2>JSON<a class="headerlink" href="#json" title="Permalink to this heading">¶</a></h2>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.19.</span></p>
</div>
<p>Functionality for querying a JSON string.</p>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>In each of the following JSON-related subcommands, if the optional
<code class="docutils literal notranslate"><span class="pre">ERROR_VARIABLE</span></code> argument is given, errors will be reported in
<code class="docutils literal notranslate"><span class="pre">&lt;error-variable&gt;</span></code> and the <code class="docutils literal notranslate"><span class="pre">&lt;out-var&gt;</span></code> will be set to
<code class="docutils literal notranslate"><span class="pre">&lt;member|index&gt;-[&lt;member|index&gt;...]-NOTFOUND</span></code> with the path elements
up to the point where the error occurred, or just <code class="docutils literal notranslate"><span class="pre">NOTFOUND</span></code> if there
is no relevant path.  If an error occurs but the <code class="docutils literal notranslate"><span class="pre">ERROR_VARIABLE</span></code>
option is not present, a fatal error message is generated.  If no error
occurs, the <code class="docutils literal notranslate"><span class="pre">&lt;error-variable&gt;</span></code> will be set to <code class="docutils literal notranslate"><span class="pre">NOTFOUND</span></code>.</p>
</div>
<dl class="cmake signature">
<dt class="sig sig-object cmake" id="json-get">
<span class="code cmake highlight sig-name descname"><span class="nf"><span class="pre">string(</span></span><span class="no"><span class="pre">JSON</span></span><span class="w"> </span><span class="nv"><span class="pre">&lt;out-var&gt;</span></span><span class="w"> </span><span class="p"><span class="pre">[</span></span><span class="no"><span class="pre">ERROR_VARIABLE</span></span><span class="nbsp"> </span><span class="nv"><span class="pre">&lt;error-variable&gt;</span></span><span class="p"><span class="pre">]</span></span><span class="w"> </span><span class="no"><span class="pre">GET</span></span><span class="w"> </span><span class="nv"><span class="pre">&lt;json-string&gt;</span></span><span class="w"> </span><span class="o"><span class="pre">&lt;</span></span><span class="nb"><span class="pre">member</span></span><span class="p"><span class="pre">|</span></span><span class="nb"><span class="pre">index</span></span><span class="o"><span class="pre">&gt;</span></span><span class="w"> </span><span class="p"><span class="pre">[</span></span><span class="o"><span class="pre">&lt;</span></span><span class="nb"><span class="pre">member</span></span><span class="p"><span class="pre">|</span></span><span class="nb"><span class="pre">index</span></span><span class="o"><span class="pre">&gt;</span></span><span class="nbsp"> </span><span class="p"><span class="pre">...]</span></span><span class="nf"><span class="pre">)</span></span></span><a class="headerlink" href="#json-get" title="Permalink to this definition">¶</a></dt>
<dd><p>Get an element from <code class="docutils literal notranslate"><span class="pre">&lt;json-string&gt;</span></code> at the location given
by the list of <code class="docutils literal notranslate"><span class="pre">&lt;member|index&gt;</span></code> arguments.
Array and object elements will be returned as a JSON string.
Boolean elements will be returned as <code class="docutils literal notranslate"><span class="pre">ON</span></code> or <code class="docutils literal notranslate"><span class="pre">OFF</span></code>.
Null elements will be returned as an empty string.
Number and string types will be returned as strings.</p>
</dd></dl>

<dl class="cmake signature">
<dt class="sig sig-object cmake" id="json-type">
<span class="code cmake highlight sig-name descname"><span class="nf"><span class="pre">string(</span></span><span class="no"><span class="pre">JSON</span></span><span class="w"> </span><span class="nv"><span class="pre">&lt;out-var&gt;</span></span><span class="w"> </span><span class="p"><span class="pre">[</span></span><span class="no"><span class="pre">ERROR_VARIABLE</span></span><span class="nbsp"> </span><span class="nv"><span class="pre">&lt;error-variable&gt;</span></span><span class="p"><span class="pre">]</span></span><span class="w"> </span><span class="no"><span class="pre">TYPE</span></span><span class="w"> </span><span class="nv"><span class="pre">&lt;json-string&gt;</span></span><span class="w"> </span><span class="o"><span class="pre">&lt;</span></span><span class="nb"><span class="pre">member</span></span><span class="p"><span class="pre">|</span></span><span class="nb"><span class="pre">index</span></span><span class="o"><span class="pre">&gt;</span></span><span class="w"> </span><span class="p"><span class="pre">[</span></span><span class="o"><span class="pre">&lt;</span></span><span class="nb"><span class="pre">member</span></span><span class="p"><span class="pre">|</span></span><span class="nb"><span class="pre">index</span></span><span class="o"><span class="pre">&gt;</span></span><span class="nbsp"> </span><span class="p"><span class="pre">...]</span></span><span class="nf"><span class="pre">)</span></span></span><a class="headerlink" href="#json-type" title="Permalink to this definition">¶</a></dt>
<dd><p>Get the type of an element in <code class="docutils literal notranslate"><span class="pre">&lt;json-string&gt;</span></code> at the location
given by the list of <code class="docutils literal notranslate"><span class="pre">&lt;member|index&gt;</span></code> arguments. The <code class="docutils literal notranslate"><span class="pre">&lt;out-var&gt;</span></code>
will be set to one of <code class="docutils literal notranslate"><span class="pre">NULL</span></code>, <code class="docutils literal notranslate"><span class="pre">NUMBER</span></code>, <code class="docutils literal notranslate"><span class="pre">STRING</span></code>, <code class="docutils literal notranslate"><span class="pre">BOOLEAN</span></code>,
<code class="docutils literal notranslate"><span class="pre">ARRAY</span></code>, or <code class="docutils literal notranslate"><span class="pre">OBJECT</span></code>.</p>
</dd></dl>

<dl class="cmake signature">
<dt class="sig sig-object cmake" id="json-member">
<span class="code cmake highlight sig-name descname"><span class="nf"><span class="pre">string(</span></span><span class="no"><span class="pre">JSON</span></span><span class="w"> </span><span class="nv"><span class="pre">&lt;out-var&gt;</span></span><span class="w"> </span><span class="p"><span class="pre">[</span></span><span class="no"><span class="pre">ERROR_VARIABLE</span></span><span class="nbsp"> </span><span class="nv"><span class="pre">&lt;error-var&gt;</span></span><span class="p"><span class="pre">]</span></span><span class="w"> </span><span class="no"><span class="pre">MEMBER</span></span><span class="w"> </span><span class="nv"><span class="pre">&lt;json-string&gt;</span></span><span class="w"> </span><span class="p"><span class="pre">[</span></span><span class="o"><span class="pre">&lt;</span></span><span class="nb"><span class="pre">member</span></span><span class="p"><span class="pre">|</span></span><span class="nb"><span class="pre">index</span></span><span class="o"><span class="pre">&gt;</span></span><span class="nbsp"> </span><span class="p"><span class="pre">...]</span></span><span class="w"> </span><span class="nv"><span class="pre">&lt;index&gt;</span></span><span class="nf"><span class="pre">)</span></span></span><a class="headerlink" href="#json-member" title="Permalink to this definition">¶</a></dt>
<dd><p>Get the name of the <code class="docutils literal notranslate"><span class="pre">&lt;index&gt;</span></code>-th member in <code class="docutils literal notranslate"><span class="pre">&lt;json-string&gt;</span></code>
at the location given by the list of <code class="docutils literal notranslate"><span class="pre">&lt;member|index&gt;</span></code> arguments.
Requires an element of object type.</p>
</dd></dl>

<dl class="cmake signature">
<dt class="sig sig-object cmake" id="json-length">
<span class="code cmake highlight sig-name descname"><span class="nf"><span class="pre">string(</span></span><span class="no"><span class="pre">JSON</span></span><span class="w"> </span><span class="nv"><span class="pre">&lt;out-var&gt;</span></span><span class="w"> </span><span class="p"><span class="pre">[</span></span><span class="no"><span class="pre">ERROR_VARIABLE</span></span><span class="nbsp"> </span><span class="nv"><span class="pre">&lt;error-variable&gt;</span></span><span class="p"><span class="pre">]</span></span><span class="w"> </span><span class="no"><span class="pre">LENGTH</span></span><span class="w"> </span><span class="nv"><span class="pre">&lt;json-string&gt;</span></span><span class="w"> </span><span class="p"><span class="pre">[</span></span><span class="o"><span class="pre">&lt;</span></span><span class="nb"><span class="pre">member</span></span><span class="p"><span class="pre">|</span></span><span class="nb"><span class="pre">index</span></span><span class="o"><span class="pre">&gt;</span></span><span class="nbsp"> </span><span class="p"><span class="pre">...]</span></span><span class="nf"><span class="pre">)</span></span></span><a class="headerlink" href="#json-length" title="Permalink to this definition">¶</a></dt>
<dd><p>Get the length of an element in <code class="docutils literal notranslate"><span class="pre">&lt;json-string&gt;</span></code> at the location
given by the list of <code class="docutils literal notranslate"><span class="pre">&lt;member|index&gt;</span></code> arguments.
Requires an element of array or object type.</p>
</dd></dl>

<dl class="cmake signature">
<dt class="sig sig-object cmake" id="json-remove">
<span class="code cmake highlight sig-name descname"><span class="nf"><span class="pre">string(</span></span><span class="no"><span class="pre">JSON</span></span><span class="w"> </span><span class="nv"><span class="pre">&lt;out-var&gt;</span></span><span class="w"> </span><span class="p"><span class="pre">[</span></span><span class="no"><span class="pre">ERROR_VARIABLE</span></span><span class="nbsp"> </span><span class="nv"><span class="pre">&lt;error-variable&gt;</span></span><span class="p"><span class="pre">]</span></span><span class="w"> </span><span class="no"><span class="pre">REMOVE</span></span><span class="w"> </span><span class="nv"><span class="pre">&lt;json-string&gt;</span></span><span class="w"> </span><span class="o"><span class="pre">&lt;</span></span><span class="nb"><span class="pre">member</span></span><span class="p"><span class="pre">|</span></span><span class="nb"><span class="pre">index</span></span><span class="o"><span class="pre">&gt;</span></span><span class="w"> </span><span class="p"><span class="pre">[</span></span><span class="o"><span class="pre">&lt;</span></span><span class="nb"><span class="pre">member</span></span><span class="p"><span class="pre">|</span></span><span class="nb"><span class="pre">index</span></span><span class="o"><span class="pre">&gt;</span></span><span class="nbsp"> </span><span class="p"><span class="pre">...]</span></span><span class="nf"><span class="pre">)</span></span></span><a class="headerlink" href="#json-remove" title="Permalink to this definition">¶</a></dt>
<dd><p>Remove an element from <code class="docutils literal notranslate"><span class="pre">&lt;json-string&gt;</span></code> at the location
given by the list of <code class="docutils literal notranslate"><span class="pre">&lt;member|index&gt;</span></code> arguments. The JSON string
without the removed element will be stored in <code class="docutils literal notranslate"><span class="pre">&lt;out-var&gt;</span></code>.</p>
</dd></dl>

<dl class="cmake signature">
<dt class="sig sig-object cmake" id="json-set">
<span class="code cmake highlight sig-name descname"><span class="nf"><span class="pre">string(</span></span><span class="no"><span class="pre">JSON</span></span><span class="w"> </span><span class="nv"><span class="pre">&lt;out-var&gt;</span></span><span class="w"> </span><span class="p"><span class="pre">[</span></span><span class="no"><span class="pre">ERROR_VARIABLE</span></span><span class="nbsp"> </span><span class="nv"><span class="pre">&lt;error-variable&gt;</span></span><span class="p"><span class="pre">]</span></span><span class="w"> </span><span class="no"><span class="pre">SET</span></span><span class="w"> </span><span class="nv"><span class="pre">&lt;json-string&gt;</span></span><span class="w"> </span><span class="o"><span class="pre">&lt;</span></span><span class="nb"><span class="pre">member</span></span><span class="p"><span class="pre">|</span></span><span class="nb"><span class="pre">index</span></span><span class="o"><span class="pre">&gt;</span></span><span class="w"> </span><span class="p"><span class="pre">[</span></span><span class="o"><span class="pre">&lt;</span></span><span class="nb"><span class="pre">member</span></span><span class="p"><span class="pre">|</span></span><span class="nb"><span class="pre">index</span></span><span class="o"><span class="pre">&gt;</span></span><span class="nbsp"> </span><span class="p"><span class="pre">...]</span></span><span class="w"> </span><span class="nv"><span class="pre">&lt;value&gt;</span></span><span class="nf"><span class="pre">)</span></span></span><a class="headerlink" href="#json-set" title="Permalink to this definition">¶</a></dt>
<dd><p>Set an element in <code class="docutils literal notranslate"><span class="pre">&lt;json-string&gt;</span></code> at the location
given by the list of <code class="docutils literal notranslate"><span class="pre">&lt;member|index&gt;</span></code> arguments to <code class="docutils literal notranslate"><span class="pre">&lt;value&gt;</span></code>.
The contents of <code class="docutils literal notranslate"><span class="pre">&lt;value&gt;</span></code> should be valid JSON.
If <code class="docutils literal notranslate"><span class="pre">&lt;json-string&gt;</span></code> is an array, <code class="docutils literal notranslate"><span class="pre">&lt;value&gt;</span></code> can be appended to the end of
the array by using a number greater or equal to the array length as the
<code class="docutils literal notranslate"><span class="pre">&lt;member|index&gt;</span></code> argument.</p>
</dd></dl>

<dl class="cmake signature">
<dt class="sig sig-object cmake" id="json-equal">
<span class="code cmake highlight sig-name descname"><span class="nf"><span class="pre">string(</span></span><span class="no"><span class="pre">JSON</span></span><span class="w"> </span><span class="nv"><span class="pre">&lt;out-var&gt;</span></span><span class="w"> </span><span class="p"><span class="pre">[</span></span><span class="no"><span class="pre">ERROR_VARIABLE</span></span><span class="nbsp"> </span><span class="nv"><span class="pre">&lt;error-var&gt;</span></span><span class="p"><span class="pre">]</span></span><span class="w"> </span><span class="no"><span class="pre">EQUAL</span></span><span class="w"> </span><span class="nv"><span class="pre">&lt;json-string1&gt;</span></span><span class="w"> </span><span class="nv"><span class="pre">&lt;json-string2&gt;</span></span><span class="nf"><span class="pre">)</span></span></span><a class="headerlink" href="#json-equal" title="Permalink to this definition">¶</a></dt>
<dd><p>Compare the two JSON objects given by <code class="docutils literal notranslate"><span class="pre">&lt;json-string1&gt;</span></code>
and <code class="docutils literal notranslate"><span class="pre">&lt;json-string2&gt;</span></code> for equality.  The contents of <code class="docutils literal notranslate"><span class="pre">&lt;json-string1&gt;</span></code>
and <code class="docutils literal notranslate"><span class="pre">&lt;json-string2&gt;</span></code> should be valid JSON.  The <code class="docutils literal notranslate"><span class="pre">&lt;out-var&gt;</span></code>
will be set to a true value if the JSON objects are considered equal,
or a false value otherwise.</p>
</dd></dl>

</section>
</section>


            <div class="clearer"></div>
          </div>
        </div>
      </div>
      <div class="sphinxsidebar" role="navigation" aria-label="main navigation">
        <div class="sphinxsidebarwrapper">
  <div>
    <h3><a href="../index.html">Table of Contents</a></h3>
    <ul>
<li><a class="reference internal" href="#">string</a><ul>
<li><a class="reference internal" href="#synopsis">Synopsis</a></li>
<li><a class="reference internal" href="#search-and-replace">Search and Replace</a><ul>
<li><a class="reference internal" href="#search-and-replace-with-plain-strings">Search and Replace With Plain Strings</a></li>
<li><a class="reference internal" href="#search-and-replace-with-regular-expressions">Search and Replace With Regular Expressions</a></li>
<li><a class="reference internal" href="#regex-specification">Regex Specification</a></li>
</ul>
</li>
<li><a class="reference internal" href="#manipulation">Manipulation</a></li>
<li><a class="reference internal" href="#comparison">Comparison</a></li>
<li><a class="reference internal" href="#hashing">Hashing</a></li>
<li><a class="reference internal" href="#generation">Generation</a></li>
<li><a class="reference internal" href="#json">JSON</a></li>
</ul>
</li>
</ul>

  </div>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="site_name.html"
                          title="previous chapter">site_name</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="unset.html"
                          title="next chapter">unset</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../_sources/command/string.rst.txt"
            rel="nofollow">Show Source</a></li>
    </ul>
   </div>
<div id="searchbox" style="display: none" role="search">
  <h3 id="searchlabel">Quick search</h3>
    <div class="searchformwrapper">
    <form class="search" action="../search.html" method="get">
      <input type="text" name="q" aria-labelledby="searchlabel" autocomplete="off" autocorrect="off" autocapitalize="off" spellcheck="false"/>
      <input type="submit" value="Go" />
    </form>
    </div>
</div>
<script>document.getElementById('searchbox').style.display = "block"</script>
        </div>
      </div>
      <div class="clearer"></div>
    </div>
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             >index</a></li>
        <li class="right" >
          <a href="unset.html" title="unset"
             >next</a> |</li>
        <li class="right" >
          <a href="site_name.html" title="site_name"
             >previous</a> |</li>
  <li>
    <img src="../_static/cmake-logo-16.png" alt=""
         style="vertical-align: middle; margin-top: -2px" />
  </li>
  <li>
    <a href="https://cmake.org/">CMake</a> &#187;
  </li>
  <li>
    <a href="../index.html">3.28.1 Documentation</a> &#187;
  </li>

          <li class="nav-item nav-item-1"><a href="../manual/cmake-commands.7.html" >cmake-commands(7)</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href="">string</a></li> 
      </ul>
    </div>

    <div class="footer" role="contentinfo">
        &#169; Copyright 2000-2023 Kitware, Inc. and Contributors.
      Created using <a href="https://www.sphinx-doc.org/">Sphinx</a> 6.2.1.
    </div>
  </body>
</html>