
<!DOCTYPE html>

<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="generator" content="Docutils 0.19: https://docutils.sourceforge.io/" />

  <title>link_directories &mdash; CMake 3.28.1 Documentation</title>

    <link rel="stylesheet" type="text/css" href="../_static/pygments.css" />
    <link rel="stylesheet" type="text/css" href="../_static/cmake.css" />
    
    <script data-url_root="../" id="documentation_options" src="../_static/documentation_options.js"></script>
    <script src="../_static/doctools.js"></script>
    <script src="../_static/sphinx_highlight.js"></script>
    
    <link rel="icon" href="../_static/cmake-favicon.ico"/>
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="next" title="link_libraries" href="link_libraries.html" />
    <link rel="prev" title="install" href="install.html" />
 

  </head><body>

    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             accesskey="I">index</a></li>
        <li class="right" >
          <a href="link_libraries.html" title="link_libraries"
             accesskey="N">next</a> |</li>
        <li class="right" >
          <a href="install.html" title="install"
             accesskey="P">previous</a> |</li>
  <li>
    <img src="../_static/cmake-logo-16.png" alt=""
         style="vertical-align: middle; margin-top: -2px" />
  </li>
  <li>
    <a href="https://cmake.org/">CMake</a> &#187;
  </li>
  <li>
    <a href="../index.html">3.28.1 Documentation</a> &#187;
  </li>

          <li class="nav-item nav-item-1"><a href="../manual/cmake-commands.7.html" accesskey="U">cmake-commands(7)</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href="">link_directories</a></li> 
      </ul>
    </div>  

    <div class="document">
      <div class="documentwrapper">
        <div class="bodywrapper">
          <div class="body" role="main">
            
  <section id="link-directories">
<span id="command:link_directories"></span><h1>link_directories<a class="headerlink" href="#link-directories" title="Permalink to this heading">¶</a></h1>
<p>Add directories in which the linker will look for libraries.</p>
<div class="highlight-cmake notranslate"><div class="highlight"><pre><span></span><span class="nf">link_directories(</span><span class="p">[</span><span class="no">AFTER</span><span class="p">|</span><span class="no">BEFORE</span><span class="p">]</span><span class="w"> </span><span class="nb">directory1</span><span class="w"> </span><span class="p">[</span><span class="nb">directory2</span><span class="w"> </span><span class="p">...]</span><span class="nf">)</span>
</pre></div>
</div>
<p>Adds the paths in which the linker should search for libraries.
Relative paths given to this command are interpreted as relative to
the current source directory, see <span class="target" id="index-0-policy:CMP0015"></span><a class="reference internal" href="../policy/CMP0015.html#policy:CMP0015" title="CMP0015"><code class="xref cmake cmake-policy docutils literal notranslate"><span class="pre">CMP0015</span></code></a>.</p>
<p>The command will apply only to targets created after it is called.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.13: </span>The directories are added to the <span class="target" id="index-0-prop_dir:LINK_DIRECTORIES"></span><a class="reference internal" href="../prop_dir/LINK_DIRECTORIES.html#prop_dir:LINK_DIRECTORIES" title="LINK_DIRECTORIES"><code class="xref cmake cmake-prop_dir docutils literal notranslate"><span class="pre">LINK_DIRECTORIES</span></code></a> directory
property for the current <code class="docutils literal notranslate"><span class="pre">CMakeLists.txt</span></code> file, converting relative
paths to absolute as needed.  See the <span class="target" id="index-0-manual:cmake-buildsystem(7)"></span><a class="reference internal" href="../manual/cmake-buildsystem.7.html#manual:cmake-buildsystem(7)" title="cmake-buildsystem(7)"><code class="xref cmake cmake-manual docutils literal notranslate"><span class="pre">cmake-buildsystem(7)</span></code></a>
manual for more on defining buildsystem properties.</p>
</div>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.13: </span>By default the directories specified are appended onto the current list of
directories.  This default behavior can be changed by setting
<span class="target" id="index-0-variable:CMAKE_LINK_DIRECTORIES_BEFORE"></span><a class="reference internal" href="../variable/CMAKE_LINK_DIRECTORIES_BEFORE.html#variable:CMAKE_LINK_DIRECTORIES_BEFORE" title="CMAKE_LINK_DIRECTORIES_BEFORE"><code class="xref cmake cmake-variable docutils literal notranslate"><span class="pre">CMAKE_LINK_DIRECTORIES_BEFORE</span></code></a> to <code class="docutils literal notranslate"><span class="pre">ON</span></code>.  By using
<code class="docutils literal notranslate"><span class="pre">AFTER</span></code> or <code class="docutils literal notranslate"><span class="pre">BEFORE</span></code> explicitly, you can select between appending and
prepending, independent of the default.</p>
</div>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.13: </span>Arguments to <code class="docutils literal notranslate"><span class="pre">link_directories</span></code> may use &quot;generator expressions&quot; with
the syntax &quot;$&lt;...&gt;&quot;.  See the <span class="target" id="index-0-manual:cmake-generator-expressions(7)"></span><a class="reference internal" href="../manual/cmake-generator-expressions.7.html#manual:cmake-generator-expressions(7)" title="cmake-generator-expressions(7)"><code class="xref cmake cmake-manual docutils literal notranslate"><span class="pre">cmake-generator-expressions(7)</span></code></a>
manual for available expressions.</p>
</div>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>This command is rarely necessary and should be avoided where there are
other choices.  Prefer to pass full absolute paths to libraries where
possible, since this ensures the correct library will always be linked.
The <span class="target" id="index-0-command:find_library"></span><a class="reference internal" href="find_library.html#command:find_library" title="find_library"><code class="xref cmake cmake-command docutils literal notranslate"><span class="pre">find_library()</span></code></a> command provides the full path, which can
generally be used directly in calls to <span class="target" id="index-0-command:target_link_libraries"></span><a class="reference internal" href="target_link_libraries.html#command:target_link_libraries" title="target_link_libraries"><code class="xref cmake cmake-command docutils literal notranslate"><span class="pre">target_link_libraries()</span></code></a>.
Situations where a library search path may be needed include:</p>
<ul class="simple">
<li><p>Project generators like Xcode where the user can switch target
architecture at build time, but a full path to a library cannot
be used because it only provides one architecture (i.e. it is not
a universal binary).</p></li>
<li><p>Libraries may themselves have other private library dependencies
that expect to be found via <code class="docutils literal notranslate"><span class="pre">RPATH</span></code> mechanisms, but some linkers
are not able to fully decode those paths (e.g. due to the presence
of things like <code class="docutils literal notranslate"><span class="pre">$ORIGIN</span></code>).</p></li>
</ul>
<p>If a library search path must be provided, prefer to localize the effect
where possible by using the <span class="target" id="index-0-command:target_link_directories"></span><a class="reference internal" href="target_link_directories.html#command:target_link_directories" title="target_link_directories"><code class="xref cmake cmake-command docutils literal notranslate"><span class="pre">target_link_directories()</span></code></a> command
rather than <code class="docutils literal notranslate"><span class="pre">link_directories()</span></code>.  The target-specific command can also
control how the search directories propagate to other dependent targets.</p>
</div>
<section id="see-also">
<h2>See Also<a class="headerlink" href="#see-also" title="Permalink to this heading">¶</a></h2>
<ul class="simple">
<li><p><span class="target" id="index-1-command:target_link_directories"></span><a class="reference internal" href="target_link_directories.html#command:target_link_directories" title="target_link_directories"><code class="xref cmake cmake-command docutils literal notranslate"><span class="pre">target_link_directories()</span></code></a></p></li>
<li><p><span class="target" id="index-1-command:target_link_libraries"></span><a class="reference internal" href="target_link_libraries.html#command:target_link_libraries" title="target_link_libraries"><code class="xref cmake cmake-command docutils literal notranslate"><span class="pre">target_link_libraries()</span></code></a></p></li>
</ul>
</section>
</section>


            <div class="clearer"></div>
          </div>
        </div>
      </div>
      <div class="sphinxsidebar" role="navigation" aria-label="main navigation">
        <div class="sphinxsidebarwrapper">
  <div>
    <h3><a href="../index.html">Table of Contents</a></h3>
    <ul>
<li><a class="reference internal" href="#">link_directories</a><ul>
<li><a class="reference internal" href="#see-also">See Also</a></li>
</ul>
</li>
</ul>

  </div>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="install.html"
                          title="previous chapter">install</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="link_libraries.html"
                          title="next chapter">link_libraries</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../_sources/command/link_directories.rst.txt"
            rel="nofollow">Show Source</a></li>
    </ul>
   </div>
<div id="searchbox" style="display: none" role="search">
  <h3 id="searchlabel">Quick search</h3>
    <div class="searchformwrapper">
    <form class="search" action="../search.html" method="get">
      <input type="text" name="q" aria-labelledby="searchlabel" autocomplete="off" autocorrect="off" autocapitalize="off" spellcheck="false"/>
      <input type="submit" value="Go" />
    </form>
    </div>
</div>
<script>document.getElementById('searchbox').style.display = "block"</script>
        </div>
      </div>
      <div class="clearer"></div>
    </div>
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             >index</a></li>
        <li class="right" >
          <a href="link_libraries.html" title="link_libraries"
             >next</a> |</li>
        <li class="right" >
          <a href="install.html" title="install"
             >previous</a> |</li>
  <li>
    <img src="../_static/cmake-logo-16.png" alt=""
         style="vertical-align: middle; margin-top: -2px" />
  </li>
  <li>
    <a href="https://cmake.org/">CMake</a> &#187;
  </li>
  <li>
    <a href="../index.html">3.28.1 Documentation</a> &#187;
  </li>

          <li class="nav-item nav-item-1"><a href="../manual/cmake-commands.7.html" >cmake-commands(7)</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href="">link_directories</a></li> 
      </ul>
    </div>

    <div class="footer" role="contentinfo">
        &#169; Copyright 2000-2023 Kitware, Inc. and Contributors.
      Created using <a href="https://www.sphinx-doc.org/">Sphinx</a> 6.2.1.
    </div>
  </body>
</html>