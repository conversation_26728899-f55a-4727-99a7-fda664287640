
<!DOCTYPE html>

<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="generator" content="Docutils 0.19: https://docutils.sourceforge.io/" />

  <title>get_filename_component &mdash; CMake 3.28.1 Documentation</title>

    <link rel="stylesheet" type="text/css" href="../_static/pygments.css" />
    <link rel="stylesheet" type="text/css" href="../_static/cmake.css" />
    
    <script data-url_root="../" id="documentation_options" src="../_static/documentation_options.js"></script>
    <script src="../_static/doctools.js"></script>
    <script src="../_static/sphinx_highlight.js"></script>
    
    <link rel="icon" href="../_static/cmake-favicon.ico"/>
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="next" title="get_property" href="get_property.html" />
    <link rel="prev" title="get_directory_property" href="get_directory_property.html" />
 

  </head><body>

    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             accesskey="I">index</a></li>
        <li class="right" >
          <a href="get_property.html" title="get_property"
             accesskey="N">next</a> |</li>
        <li class="right" >
          <a href="get_directory_property.html" title="get_directory_property"
             accesskey="P">previous</a> |</li>
  <li>
    <img src="../_static/cmake-logo-16.png" alt=""
         style="vertical-align: middle; margin-top: -2px" />
  </li>
  <li>
    <a href="https://cmake.org/">CMake</a> &#187;
  </li>
  <li>
    <a href="../index.html">3.28.1 Documentation</a> &#187;
  </li>

          <li class="nav-item nav-item-1"><a href="../manual/cmake-commands.7.html" accesskey="U">cmake-commands(7)</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href="">get_filename_component</a></li> 
      </ul>
    </div>  

    <div class="document">
      <div class="documentwrapper">
        <div class="bodywrapper">
          <div class="body" role="main">
            
  <section id="get-filename-component">
<span id="command:get_filename_component"></span><h1>get_filename_component<a class="headerlink" href="#get-filename-component" title="Permalink to this heading">¶</a></h1>
<p>Get a specific component of a full filename.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.20: </span>This command has been superseded by the <span class="target" id="index-0-command:cmake_path"></span><a class="reference internal" href="cmake_path.html#command:cmake_path" title="cmake_path"><code class="xref cmake cmake-command docutils literal notranslate"><span class="pre">cmake_path()</span></code></a> command, except
for <code class="docutils literal notranslate"><span class="pre">REALPATH</span></code>, which is now offered by <span class="target" id="index-0-command:file"></span><a class="reference internal" href="file.html#real-path" title="file(real_path)"><code class="xref cmake cmake-command docutils literal notranslate"><span class="pre">file(REAL_PATH)</span></code></a>, and
<code class="docutils literal notranslate"><span class="pre">PROGRAM</span></code>, now available in <span class="target" id="index-0-command:separate_arguments"></span><a class="reference internal" href="separate_arguments.html#command:separate_arguments" title="separate_arguments(program)"><code class="xref cmake cmake-command docutils literal notranslate"><span class="pre">separate_arguments(PROGRAM)</span></code></a>.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.24: </span>The undocumented feature offering the capability to query the <code class="docutils literal notranslate"><span class="pre">Windows</span></code>
registry is superseded by
<a class="reference internal" href="cmake_host_system_information.html#query-windows-registry"><span class="std std-ref">cmake_host_system_information(QUERY WINDOWS_REGISTRY)</span></a>
command.</p>
</div>
<div class="highlight-cmake notranslate"><div class="highlight"><pre><span></span><span class="nf">get_filename_component(</span><span class="nv">&lt;var&gt;</span><span class="w"> </span><span class="nv">&lt;FileName&gt;</span><span class="w"> </span><span class="nv">&lt;mode&gt;</span><span class="w"> </span><span class="p">[</span><span class="no">CACHE</span><span class="p">]</span><span class="nf">)</span>
</pre></div>
</div>
<p>Sets <code class="docutils literal notranslate"><span class="pre">&lt;var&gt;</span></code> to a component of <code class="docutils literal notranslate"><span class="pre">&lt;FileName&gt;</span></code>, where <code class="docutils literal notranslate"><span class="pre">&lt;mode&gt;</span></code> is one of:</p>
<div class="highlight-none notranslate"><div class="highlight"><pre><span></span>DIRECTORY = Directory without file name
NAME      = File name without directory
EXT       = File name longest extension (.b.c from d/a.b.c)
NAME_WE   = File name with neither the directory nor the longest extension
LAST_EXT  = File name last extension (.c from d/a.b.c)
NAME_WLE  = File name with neither the directory nor the last extension
PATH      = Legacy alias for DIRECTORY (use for CMake &lt;= 2.8.11)
</pre></div>
</div>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.14: </span>Added the <code class="docutils literal notranslate"><span class="pre">LAST_EXT</span></code> and <code class="docutils literal notranslate"><span class="pre">NAME_WLE</span></code> modes.</p>
</div>
<p>Paths are returned with forward slashes and have no trailing slashes.
If the optional <code class="docutils literal notranslate"><span class="pre">CACHE</span></code> argument is specified, the result variable is
added to the cache.</p>
<div class="highlight-cmake notranslate"><div class="highlight"><pre><span></span><span class="nf">get_filename_component(</span><span class="nv">&lt;var&gt;</span><span class="w"> </span><span class="nv">&lt;FileName&gt;</span><span class="w"> </span><span class="nv">&lt;mode&gt;</span><span class="w"> </span><span class="p">[</span><span class="no">BASE_DIR</span><span class="w"> </span><span class="nv">&lt;dir&gt;</span><span class="p">]</span><span class="w"> </span><span class="p">[</span><span class="no">CACHE</span><span class="p">]</span><span class="nf">)</span>
</pre></div>
</div>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.4.</span></p>
</div>
<p>Sets <code class="docutils literal notranslate"><span class="pre">&lt;var&gt;</span></code> to the absolute path of <code class="docutils literal notranslate"><span class="pre">&lt;FileName&gt;</span></code>, where <code class="docutils literal notranslate"><span class="pre">&lt;mode&gt;</span></code> is one
of:</p>
<div class="highlight-none notranslate"><div class="highlight"><pre><span></span>ABSOLUTE  = Full path to file
REALPATH  = Full path to existing file with symlinks resolved
</pre></div>
</div>
<p>If the provided <code class="docutils literal notranslate"><span class="pre">&lt;FileName&gt;</span></code> is a relative path, it is evaluated relative
to the given base directory <code class="docutils literal notranslate"><span class="pre">&lt;dir&gt;</span></code>.  If no base directory is
provided, the default base directory will be
<span class="target" id="index-0-variable:CMAKE_CURRENT_SOURCE_DIR"></span><a class="reference internal" href="../variable/CMAKE_CURRENT_SOURCE_DIR.html#variable:CMAKE_CURRENT_SOURCE_DIR" title="CMAKE_CURRENT_SOURCE_DIR"><code class="xref cmake cmake-variable docutils literal notranslate"><span class="pre">CMAKE_CURRENT_SOURCE_DIR</span></code></a>.</p>
<p>Paths are returned with forward slashes and have no trailing slashes.  If the
optional <code class="docutils literal notranslate"><span class="pre">CACHE</span></code> argument is specified, the result variable is added to the
cache.</p>
<div class="highlight-cmake notranslate"><div class="highlight"><pre><span></span><span class="nf">get_filename_component(</span><span class="nv">&lt;var&gt;</span><span class="w"> </span><span class="nv">&lt;FileName&gt;</span><span class="w"> </span><span class="no">PROGRAM</span><span class="w"> </span><span class="p">[</span><span class="no">PROGRAM_ARGS</span><span class="w"> </span><span class="nv">&lt;arg_var&gt;</span><span class="p">]</span><span class="w"> </span><span class="p">[</span><span class="no">CACHE</span><span class="p">]</span><span class="nf">)</span>
</pre></div>
</div>
<p>The program in <code class="docutils literal notranslate"><span class="pre">&lt;FileName&gt;</span></code> will be found in the system search path or
left as a full path.  If <code class="docutils literal notranslate"><span class="pre">PROGRAM_ARGS</span></code> is present with <code class="docutils literal notranslate"><span class="pre">PROGRAM</span></code>, then
any command-line arguments present in the <code class="docutils literal notranslate"><span class="pre">&lt;FileName&gt;</span></code> string are split
from the program name and stored in <code class="docutils literal notranslate"><span class="pre">&lt;arg_var&gt;</span></code>.  This is used to
separate a program name from its arguments in a command line string.</p>
<section id="see-also">
<h2>See Also<a class="headerlink" href="#see-also" title="Permalink to this heading">¶</a></h2>
<ul class="simple">
<li><p><span class="target" id="index-1-command:cmake_path"></span><a class="reference internal" href="cmake_path.html#command:cmake_path" title="cmake_path"><code class="xref cmake cmake-command docutils literal notranslate"><span class="pre">cmake_path()</span></code></a></p></li>
</ul>
</section>
</section>


            <div class="clearer"></div>
          </div>
        </div>
      </div>
      <div class="sphinxsidebar" role="navigation" aria-label="main navigation">
        <div class="sphinxsidebarwrapper">
  <div>
    <h3><a href="../index.html">Table of Contents</a></h3>
    <ul>
<li><a class="reference internal" href="#">get_filename_component</a><ul>
<li><a class="reference internal" href="#see-also">See Also</a></li>
</ul>
</li>
</ul>

  </div>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="get_directory_property.html"
                          title="previous chapter">get_directory_property</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="get_property.html"
                          title="next chapter">get_property</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../_sources/command/get_filename_component.rst.txt"
            rel="nofollow">Show Source</a></li>
    </ul>
   </div>
<div id="searchbox" style="display: none" role="search">
  <h3 id="searchlabel">Quick search</h3>
    <div class="searchformwrapper">
    <form class="search" action="../search.html" method="get">
      <input type="text" name="q" aria-labelledby="searchlabel" autocomplete="off" autocorrect="off" autocapitalize="off" spellcheck="false"/>
      <input type="submit" value="Go" />
    </form>
    </div>
</div>
<script>document.getElementById('searchbox').style.display = "block"</script>
        </div>
      </div>
      <div class="clearer"></div>
    </div>
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             >index</a></li>
        <li class="right" >
          <a href="get_property.html" title="get_property"
             >next</a> |</li>
        <li class="right" >
          <a href="get_directory_property.html" title="get_directory_property"
             >previous</a> |</li>
  <li>
    <img src="../_static/cmake-logo-16.png" alt=""
         style="vertical-align: middle; margin-top: -2px" />
  </li>
  <li>
    <a href="https://cmake.org/">CMake</a> &#187;
  </li>
  <li>
    <a href="../index.html">3.28.1 Documentation</a> &#187;
  </li>

          <li class="nav-item nav-item-1"><a href="../manual/cmake-commands.7.html" >cmake-commands(7)</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href="">get_filename_component</a></li> 
      </ul>
    </div>

    <div class="footer" role="contentinfo">
        &#169; Copyright 2000-2023 Kitware, Inc. and Contributors.
      Created using <a href="https://www.sphinx-doc.org/">Sphinx</a> 6.2.1.
    </div>
  </body>
</html>