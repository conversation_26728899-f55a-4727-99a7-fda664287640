
<!DOCTYPE html>

<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="generator" content="Docutils 0.19: https://docutils.sourceforge.io/" />

  <title>ProcessorCount &mdash; CMake 3.28.1 Documentation</title>

    <link rel="stylesheet" type="text/css" href="../_static/pygments.css" />
    <link rel="stylesheet" type="text/css" href="../_static/cmake.css" />
    
    <script data-url_root="../" id="documentation_options" src="../_static/documentation_options.js"></script>
    <script src="../_static/doctools.js"></script>
    <script src="../_static/sphinx_highlight.js"></script>
    
    <link rel="icon" href="../_static/cmake-favicon.ico"/>
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="next" title="SelectLibraryConfigurations" href="SelectLibraryConfigurations.html" />
    <link rel="prev" title="InstallRequiredSystemLibraries" href="InstallRequiredSystemLibraries.html" />
 

  </head><body>

    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             accesskey="I">index</a></li>
        <li class="right" >
          <a href="SelectLibraryConfigurations.html" title="SelectLibraryConfigurations"
             accesskey="N">next</a> |</li>
        <li class="right" >
          <a href="InstallRequiredSystemLibraries.html" title="InstallRequiredSystemLibraries"
             accesskey="P">previous</a> |</li>
  <li>
    <img src="../_static/cmake-logo-16.png" alt=""
         style="vertical-align: middle; margin-top: -2px" />
  </li>
  <li>
    <a href="https://cmake.org/">CMake</a> &#187;
  </li>
  <li>
    <a href="../index.html">3.28.1 Documentation</a> &#187;
  </li>

          <li class="nav-item nav-item-1"><a href="../manual/cmake-modules.7.html" accesskey="U">cmake-modules(7)</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href="">ProcessorCount</a></li> 
      </ul>
    </div>  

    <div class="document">
      <div class="documentwrapper">
        <div class="bodywrapper">
          <div class="body" role="main">
            
  <section id="processorcount">
<span id="module:ProcessorCount"></span><h1>ProcessorCount<a class="headerlink" href="#processorcount" title="Permalink to this heading">¶</a></h1>
<p>ProcessorCount(var)</p>
<p>Determine the number of processors/cores and save value in ${var}</p>
<p>Sets the variable named ${var} to the number of physical cores
available on the machine if the information can be determined.
Otherwise it is set to 0.  Currently this functionality is implemented
for AIX, cygwin, FreeBSD, HPUX, Linux, macOS, QNX, Sun and
Windows.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.15: </span>On Linux, returns the container CPU count instead of the host CPU count.</p>
</div>
<p>This function is guaranteed to return a positive integer (&gt;=1) if it
succeeds.  It returns 0 if there's a problem determining the processor
count.</p>
<p>More generally accurate physical CPU count can be obtained via
<span class="target" id="index-0-command:cmake_host_system_information"></span><a class="reference internal" href="../command/cmake_host_system_information.html#command:cmake_host_system_information" title="cmake_host_system_information"><code class="xref cmake cmake-command docutils literal notranslate"><span class="pre">cmake_host_system_information()</span></code></a>:</p>
<div class="highlight-cmake notranslate"><div class="highlight"><pre><span></span><span class="nf">cmake_host_system_information(</span><span class="no">RESULT</span><span class="w"> </span><span class="no">N</span>
<span class="w">                              </span><span class="no">QUERY</span><span class="w"> </span><span class="no">NUMBER_OF_PHYSICAL_CORES</span><span class="nf">)</span>
</pre></div>
</div>
<p>Example use, in a ctest -S dashboard script:</p>
<div class="highlight-cmake notranslate"><div class="highlight"><pre><span></span><span class="nf">include(</span><span class="nb">ProcessorCount</span><span class="nf">)</span>
<span class="nf">ProcessorCount(</span><span class="no">N</span><span class="nf">)</span>
<span class="nf">if(</span><span class="no">NOT</span><span class="w"> </span><span class="no">N</span><span class="w"> </span><span class="no">EQUAL</span><span class="w"> </span><span class="m">0</span><span class="nf">)</span>
<span class="w">  </span><span class="nf">set(</span><span class="no">CTEST_BUILD_FLAGS</span><span class="w"> </span><span class="p">-</span><span class="nb">j</span><span class="o">${</span><span class="nt">N</span><span class="o">}</span><span class="nf">)</span>
<span class="w">  </span><span class="nf">set(</span><span class="nb">ctest_test_args</span><span class="w"> </span><span class="o">${</span><span class="nt">ctest_test_args</span><span class="o">}</span><span class="w"> </span><span class="no">PARALLEL_LEVEL</span><span class="w"> </span><span class="o">${</span><span class="nt">N</span><span class="o">}</span><span class="nf">)</span>
<span class="nf">endif()</span>
</pre></div>
</div>
<p>This function is intended to offer an approximation of the value of
the number of compute cores available on the current machine, such
that you may use that value for parallel building and parallel
testing.  It is meant to help utilize as much of the machine as seems
reasonable.  Of course, knowledge of what else might be running on the
machine simultaneously should be used when deciding whether to request
a machine's full capacity all for yourself.</p>
</section>


            <div class="clearer"></div>
          </div>
        </div>
      </div>
      <div class="sphinxsidebar" role="navigation" aria-label="main navigation">
        <div class="sphinxsidebarwrapper">
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="InstallRequiredSystemLibraries.html"
                          title="previous chapter">InstallRequiredSystemLibraries</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="SelectLibraryConfigurations.html"
                          title="next chapter">SelectLibraryConfigurations</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../_sources/module/ProcessorCount.rst.txt"
            rel="nofollow">Show Source</a></li>
    </ul>
   </div>
<div id="searchbox" style="display: none" role="search">
  <h3 id="searchlabel">Quick search</h3>
    <div class="searchformwrapper">
    <form class="search" action="../search.html" method="get">
      <input type="text" name="q" aria-labelledby="searchlabel" autocomplete="off" autocorrect="off" autocapitalize="off" spellcheck="false"/>
      <input type="submit" value="Go" />
    </form>
    </div>
</div>
<script>document.getElementById('searchbox').style.display = "block"</script>
        </div>
      </div>
      <div class="clearer"></div>
    </div>
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             >index</a></li>
        <li class="right" >
          <a href="SelectLibraryConfigurations.html" title="SelectLibraryConfigurations"
             >next</a> |</li>
        <li class="right" >
          <a href="InstallRequiredSystemLibraries.html" title="InstallRequiredSystemLibraries"
             >previous</a> |</li>
  <li>
    <img src="../_static/cmake-logo-16.png" alt=""
         style="vertical-align: middle; margin-top: -2px" />
  </li>
  <li>
    <a href="https://cmake.org/">CMake</a> &#187;
  </li>
  <li>
    <a href="../index.html">3.28.1 Documentation</a> &#187;
  </li>

          <li class="nav-item nav-item-1"><a href="../manual/cmake-modules.7.html" >cmake-modules(7)</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href="">ProcessorCount</a></li> 
      </ul>
    </div>

    <div class="footer" role="contentinfo">
        &#169; Copyright 2000-2023 Kitware, Inc. and Contributors.
      Created using <a href="https://www.sphinx-doc.org/">Sphinx</a> 6.2.1.
    </div>
  </body>
</html>