
<!DOCTYPE html>

<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="generator" content="Docutils 0.19: https://docutils.sourceforge.io/" />

  <title>cmake-policies(7) &mdash; CMake 3.28.1 Documentation</title>

    <link rel="stylesheet" type="text/css" href="../_static/pygments.css" />
    <link rel="stylesheet" type="text/css" href="../_static/cmake.css" />
    
    <script data-url_root="../" id="documentation_options" src="../_static/documentation_options.js"></script>
    <script src="../_static/doctools.js"></script>
    <script src="../_static/sphinx_highlight.js"></script>
    
    <link rel="icon" href="../_static/cmake-favicon.ico"/>
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="next" title="CMP0155" href="../policy/CMP0155.html" />
    <link rel="prev" title="cmake-packages(7)" href="cmake-packages.7.html" />
 

  </head><body>

    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             accesskey="I">index</a></li>
        <li class="right" >
          <a href="../policy/CMP0155.html" title="CMP0155"
             accesskey="N">next</a> |</li>
        <li class="right" >
          <a href="cmake-packages.7.html" title="cmake-packages(7)"
             accesskey="P">previous</a> |</li>
  <li>
    <img src="../_static/cmake-logo-16.png" alt=""
         style="vertical-align: middle; margin-top: -2px" />
  </li>
  <li>
    <a href="https://cmake.org/">CMake</a> &#187;
  </li>
  <li>
    <a href="../index.html">3.28.1 Documentation</a> &#187;
  </li>

        <li class="nav-item nav-item-this"><a href="">cmake-policies(7)</a></li> 
      </ul>
    </div>  

    <div class="document">
      <div class="documentwrapper">
        <div class="bodywrapper">
          <div class="body" role="main">
            
  <span class="target" id="manual:cmake-policies(7)"></span><section id="cmake-policies-7">
<h1><a class="toc-backref" href="#id1" role="doc-backlink">cmake-policies(7)</a><a class="headerlink" href="#cmake-policies-7" title="Permalink to this heading">¶</a></h1>
<nav class="contents" id="contents">
<p class="topic-title">Contents</p>
<ul class="simple">
<li><p><a class="reference internal" href="#cmake-policies-7" id="id1">cmake-policies(7)</a></p>
<ul>
<li><p><a class="reference internal" href="#introduction" id="id2">Introduction</a></p></li>
<li><p><a class="reference internal" href="#policies-introduced-by-cmake-3-28" id="id3">Policies Introduced by CMake 3.28</a></p></li>
<li><p><a class="reference internal" href="#policies-introduced-by-cmake-3-27" id="id4">Policies Introduced by CMake 3.27</a></p></li>
<li><p><a class="reference internal" href="#policies-introduced-by-cmake-3-26" id="id5">Policies Introduced by CMake 3.26</a></p></li>
<li><p><a class="reference internal" href="#policies-introduced-by-cmake-3-25" id="id6">Policies Introduced by CMake 3.25</a></p></li>
<li><p><a class="reference internal" href="#policies-introduced-by-cmake-3-24" id="id7">Policies Introduced by CMake 3.24</a></p></li>
<li><p><a class="reference internal" href="#policies-introduced-by-cmake-3-23" id="id8">Policies Introduced by CMake 3.23</a></p></li>
<li><p><a class="reference internal" href="#policies-introduced-by-cmake-3-22" id="id9">Policies Introduced by CMake 3.22</a></p></li>
<li><p><a class="reference internal" href="#policies-introduced-by-cmake-3-21" id="id10">Policies Introduced by CMake 3.21</a></p></li>
<li><p><a class="reference internal" href="#policies-introduced-by-cmake-3-20" id="id11">Policies Introduced by CMake 3.20</a></p></li>
<li><p><a class="reference internal" href="#policies-introduced-by-cmake-3-19" id="id12">Policies Introduced by CMake 3.19</a></p></li>
<li><p><a class="reference internal" href="#policies-introduced-by-cmake-3-18" id="id13">Policies Introduced by CMake 3.18</a></p></li>
<li><p><a class="reference internal" href="#policies-introduced-by-cmake-3-17" id="id14">Policies Introduced by CMake 3.17</a></p></li>
<li><p><a class="reference internal" href="#policies-introduced-by-cmake-3-16" id="id15">Policies Introduced by CMake 3.16</a></p></li>
<li><p><a class="reference internal" href="#policies-introduced-by-cmake-3-15" id="id16">Policies Introduced by CMake 3.15</a></p></li>
<li><p><a class="reference internal" href="#policies-introduced-by-cmake-3-14" id="id17">Policies Introduced by CMake 3.14</a></p></li>
<li><p><a class="reference internal" href="#policies-introduced-by-cmake-3-13" id="id18">Policies Introduced by CMake 3.13</a></p></li>
<li><p><a class="reference internal" href="#policies-introduced-by-cmake-3-12" id="id19">Policies Introduced by CMake 3.12</a></p></li>
<li><p><a class="reference internal" href="#policies-introduced-by-cmake-3-11" id="id20">Policies Introduced by CMake 3.11</a></p></li>
<li><p><a class="reference internal" href="#policies-introduced-by-cmake-3-10" id="id21">Policies Introduced by CMake 3.10</a></p></li>
<li><p><a class="reference internal" href="#policies-introduced-by-cmake-3-9" id="id22">Policies Introduced by CMake 3.9</a></p></li>
<li><p><a class="reference internal" href="#policies-introduced-by-cmake-3-8" id="id23">Policies Introduced by CMake 3.8</a></p></li>
<li><p><a class="reference internal" href="#policies-introduced-by-cmake-3-7" id="id24">Policies Introduced by CMake 3.7</a></p></li>
<li><p><a class="reference internal" href="#policies-introduced-by-cmake-3-4" id="id25">Policies Introduced by CMake 3.4</a></p></li>
<li><p><a class="reference internal" href="#policies-introduced-by-cmake-3-3" id="id26">Policies Introduced by CMake 3.3</a></p></li>
<li><p><a class="reference internal" href="#policies-introduced-by-cmake-3-2" id="id27">Policies Introduced by CMake 3.2</a></p></li>
<li><p><a class="reference internal" href="#policies-introduced-by-cmake-3-1" id="id28">Policies Introduced by CMake 3.1</a></p></li>
<li><p><a class="reference internal" href="#policies-introduced-by-cmake-3-0" id="id29">Policies Introduced by CMake 3.0</a></p></li>
<li><p><a class="reference internal" href="#policies-introduced-by-cmake-2-8" id="id30">Policies Introduced by CMake 2.8</a></p></li>
<li><p><a class="reference internal" href="#policies-introduced-by-cmake-2-6" id="id31">Policies Introduced by CMake 2.6</a></p></li>
</ul>
</li>
</ul>
</nav>
<section id="introduction">
<h2><a class="toc-backref" href="#id2" role="doc-backlink">Introduction</a><a class="headerlink" href="#introduction" title="Permalink to this heading">¶</a></h2>
<p>Policies in CMake are used to preserve backward compatible behavior
across multiple releases.  When a new policy is introduced, newer CMake
versions will begin to warn about the backward compatible behavior.  It
is possible to disable the warning by explicitly requesting the OLD, or
backward compatible behavior using the <span class="target" id="index-0-command:cmake_policy"></span><a class="reference internal" href="../command/cmake_policy.html#command:cmake_policy" title="cmake_policy"><code class="xref cmake cmake-command docutils literal notranslate"><span class="pre">cmake_policy()</span></code></a> command.
It is also possible to request <code class="docutils literal notranslate"><span class="pre">NEW</span></code>, or non-backward compatible behavior
for a policy, also avoiding the warning.  Each policy can also be set to
either <code class="docutils literal notranslate"><span class="pre">NEW</span></code> or <code class="docutils literal notranslate"><span class="pre">OLD</span></code> behavior explicitly on the command line with the
<span class="target" id="index-0-variable:CMAKE_POLICY_DEFAULT_CMP&lt;NNNN&gt;"></span><a class="reference internal" href="../variable/CMAKE_POLICY_DEFAULT_CMPNNNN.html#variable:CMAKE_POLICY_DEFAULT_CMP&lt;NNNN&gt;" title="CMAKE_POLICY_DEFAULT_CMP&lt;NNNN&gt;"><code class="xref cmake cmake-variable docutils literal notranslate"><span class="pre">CMAKE_POLICY_DEFAULT_CMP&lt;NNNN&gt;</span></code></a> variable.</p>
<p>A policy is a deprecation mechanism and not a reliable feature toggle.
A policy should almost never be set to <code class="docutils literal notranslate"><span class="pre">OLD</span></code>, except to silence warnings
in an otherwise frozen or stable codebase, or temporarily as part of a
larger migration path. The <code class="docutils literal notranslate"><span class="pre">OLD</span></code> behavior of each policy is undesirable
and will be replaced with an error condition in a future release.</p>
<p>The <span class="target" id="index-0-command:cmake_minimum_required"></span><a class="reference internal" href="../command/cmake_minimum_required.html#command:cmake_minimum_required" title="cmake_minimum_required"><code class="xref cmake cmake-command docutils literal notranslate"><span class="pre">cmake_minimum_required()</span></code></a> command does more than report an
error if a too-old version of CMake is used to build a project.  It
also sets all policies introduced in that CMake version or earlier to
<code class="docutils literal notranslate"><span class="pre">NEW</span></code> behavior.  To manage policies without increasing the minimum required
CMake version, the <span class="target" id="index-0-command:if"></span><a class="reference internal" href="../command/if.html#policy" title="if(policy)"><code class="xref cmake cmake-command docutils literal notranslate"><span class="pre">if(POLICY)</span></code></a> command may be used:</p>
<div class="highlight-cmake notranslate"><div class="highlight"><pre><span></span><span class="nf">if(</span><span class="no">POLICY</span><span class="w"> </span><span class="no">CMP0990</span><span class="nf">)</span>
<span class="w">  </span><span class="nf">cmake_policy(</span><span class="no">SET</span><span class="w"> </span><span class="no">CMP0990</span><span class="w"> </span><span class="no">NEW</span><span class="nf">)</span>
<span class="nf">endif()</span>
</pre></div>
</div>
<p>This has the effect of using the <code class="docutils literal notranslate"><span class="pre">NEW</span></code> behavior with newer CMake releases which
users may be using and not issuing a compatibility warning.</p>
<p>The setting of a policy is confined in some cases to not propagate to the
parent scope.  For example, if the files read by the <span class="target" id="index-0-command:include"></span><a class="reference internal" href="../command/include.html#command:include" title="include"><code class="xref cmake cmake-command docutils literal notranslate"><span class="pre">include()</span></code></a> command
or the <span class="target" id="index-0-command:find_package"></span><a class="reference internal" href="../command/find_package.html#command:find_package" title="find_package"><code class="xref cmake cmake-command docutils literal notranslate"><span class="pre">find_package()</span></code></a> command contain a use of <span class="target" id="index-1-command:cmake_policy"></span><a class="reference internal" href="../command/cmake_policy.html#command:cmake_policy" title="cmake_policy"><code class="xref cmake cmake-command docutils literal notranslate"><span class="pre">cmake_policy()</span></code></a>,
that policy setting will not affect the caller by default.  Both commands accept
an optional <code class="docutils literal notranslate"><span class="pre">NO_POLICY_SCOPE</span></code> keyword to control this behavior.</p>
<p>The <span class="target" id="index-0-variable:CMAKE_MINIMUM_REQUIRED_VERSION"></span><a class="reference internal" href="../variable/CMAKE_MINIMUM_REQUIRED_VERSION.html#variable:CMAKE_MINIMUM_REQUIRED_VERSION" title="CMAKE_MINIMUM_REQUIRED_VERSION"><code class="xref cmake cmake-variable docutils literal notranslate"><span class="pre">CMAKE_MINIMUM_REQUIRED_VERSION</span></code></a> variable may also be used
to determine whether to report an error on use of deprecated macros or
functions.</p>
</section>
<section id="policies-introduced-by-cmake-3-28">
<h2><a class="toc-backref" href="#id3" role="doc-backlink">Policies Introduced by CMake 3.28</a><a class="headerlink" href="#policies-introduced-by-cmake-3-28" title="Permalink to this heading">¶</a></h2>
<div class="toctree-wrapper compound">
<ul>
<li class="toctree-l1"><a class="reference internal" href="../policy/CMP0155.html">CMP0155: C++ sources in targets with at least C++20 are scanned for imports when supported.</a></li>
<li class="toctree-l1"><a class="reference internal" href="../policy/CMP0154.html">CMP0154: Generated files are private by default in targets using file sets.</a></li>
<li class="toctree-l1"><a class="reference internal" href="../policy/CMP0153.html">CMP0153: The exec_program command should not be called.</a></li>
<li class="toctree-l1"><a class="reference internal" href="../policy/CMP0152.html">CMP0152: file(REAL_PATH) resolves symlinks before collapsing ../ components.</a></li>
</ul>
</div>
</section>
<section id="policies-introduced-by-cmake-3-27">
<h2><a class="toc-backref" href="#id4" role="doc-backlink">Policies Introduced by CMake 3.27</a><a class="headerlink" href="#policies-introduced-by-cmake-3-27" title="Permalink to this heading">¶</a></h2>
<div class="toctree-wrapper compound">
<ul>
<li class="toctree-l1"><a class="reference internal" href="../policy/CMP0151.html">CMP0151: AUTOMOC include directory is a system include directory by default.</a></li>
<li class="toctree-l1"><a class="reference internal" href="../policy/CMP0150.html">CMP0150: ExternalProject_Add and FetchContent_Declare treat relative git repository paths as being relative to parent project's remote.</a></li>
<li class="toctree-l1"><a class="reference internal" href="../policy/CMP0149.html">CMP0149: Visual Studio generators select latest Windows SDK by default.</a></li>
<li class="toctree-l1"><a class="reference internal" href="../policy/CMP0148.html">CMP0148: The FindPythonInterp and FindPythonLibs modules are removed.</a></li>
<li class="toctree-l1"><a class="reference internal" href="../policy/CMP0147.html">CMP0147: Visual Studio generators build custom commands in parallel.</a></li>
<li class="toctree-l1"><a class="reference internal" href="../policy/CMP0146.html">CMP0146: The FindCUDA module is removed.</a></li>
<li class="toctree-l1"><a class="reference internal" href="../policy/CMP0145.html">CMP0145: The Dart and FindDart modules are removed.</a></li>
<li class="toctree-l1"><a class="reference internal" href="../policy/CMP0144.html">CMP0144: find_package uses upper-case PACKAGENAME_ROOT variables.</a></li>
</ul>
</div>
</section>
<section id="policies-introduced-by-cmake-3-26">
<h2><a class="toc-backref" href="#id5" role="doc-backlink">Policies Introduced by CMake 3.26</a><a class="headerlink" href="#policies-introduced-by-cmake-3-26" title="Permalink to this heading">¶</a></h2>
<div class="toctree-wrapper compound">
<ul>
<li class="toctree-l1"><a class="reference internal" href="../policy/CMP0143.html">CMP0143: USE_FOLDERS global property is treated as ON by default.</a></li>
</ul>
</div>
</section>
<section id="policies-introduced-by-cmake-3-25">
<h2><a class="toc-backref" href="#id6" role="doc-backlink">Policies Introduced by CMake 3.25</a><a class="headerlink" href="#policies-introduced-by-cmake-3-25" title="Permalink to this heading">¶</a></h2>
<div class="toctree-wrapper compound">
<ul>
<li class="toctree-l1"><a class="reference internal" href="../policy/CMP0142.html">CMP0142: The Xcode generator does not append per-config suffixes to library search paths.</a></li>
<li class="toctree-l1"><a class="reference internal" href="../policy/CMP0141.html">CMP0141: MSVC debug information format flags are selected by an abstraction.</a></li>
<li class="toctree-l1"><a class="reference internal" href="../policy/CMP0140.html">CMP0140: The return() command checks its arguments.</a></li>
</ul>
</div>
</section>
<section id="policies-introduced-by-cmake-3-24">
<h2><a class="toc-backref" href="#id7" role="doc-backlink">Policies Introduced by CMake 3.24</a><a class="headerlink" href="#policies-introduced-by-cmake-3-24" title="Permalink to this heading">¶</a></h2>
<div class="toctree-wrapper compound">
<ul>
<li class="toctree-l1"><a class="reference internal" href="../policy/CMP0139.html">CMP0139: The if() command supports path comparisons using PATH_EQUAL operator.</a></li>
<li class="toctree-l1"><a class="reference internal" href="../policy/CMP0138.html">CMP0138: CheckIPOSupported uses flags from calling project.</a></li>
<li class="toctree-l1"><a class="reference internal" href="../policy/CMP0137.html">CMP0137: try_compile() passes platform variables in project mode.</a></li>
<li class="toctree-l1"><a class="reference internal" href="../policy/CMP0136.html">CMP0136: Watcom runtime library flags are selected by an abstraction.</a></li>
<li class="toctree-l1"><a class="reference internal" href="../policy/CMP0135.html">CMP0135: ExternalProject ignores timestamps in archives by default for the URL download method.</a></li>
<li class="toctree-l1"><a class="reference internal" href="../policy/CMP0134.html">CMP0134: Fallback to &quot;HOST&quot; Windows registry view when &quot;TARGET&quot; view is not usable.</a></li>
<li class="toctree-l1"><a class="reference internal" href="../policy/CMP0133.html">CMP0133: The CPack module disables SLA by default in the CPack DragNDrop Generator.</a></li>
<li class="toctree-l1"><a class="reference internal" href="../policy/CMP0132.html">CMP0132: Do not set compiler environment variables on first run.</a></li>
<li class="toctree-l1"><a class="reference internal" href="../policy/CMP0131.html">CMP0131: LINK_LIBRARIES supports the LINK_ONLY generator expression.</a></li>
<li class="toctree-l1"><a class="reference internal" href="../policy/CMP0130.html">CMP0130: while() diagnoses condition evaluation errors.</a></li>
</ul>
</div>
</section>
<section id="policies-introduced-by-cmake-3-23">
<h2><a class="toc-backref" href="#id8" role="doc-backlink">Policies Introduced by CMake 3.23</a><a class="headerlink" href="#policies-introduced-by-cmake-3-23" title="Permalink to this heading">¶</a></h2>
<div class="toctree-wrapper compound">
<ul>
<li class="toctree-l1"><a class="reference internal" href="../policy/CMP0129.html">CMP0129: Compiler id for MCST LCC compilers is now LCC, not GNU.</a></li>
</ul>
</div>
</section>
<section id="policies-introduced-by-cmake-3-22">
<h2><a class="toc-backref" href="#id9" role="doc-backlink">Policies Introduced by CMake 3.22</a><a class="headerlink" href="#policies-introduced-by-cmake-3-22" title="Permalink to this heading">¶</a></h2>
<div class="toctree-wrapper compound">
<ul>
<li class="toctree-l1"><a class="reference internal" href="../policy/CMP0128.html">CMP0128: Selection of language standard and extension flags improved.</a></li>
<li class="toctree-l1"><a class="reference internal" href="../policy/CMP0127.html">CMP0127: cmake_dependent_option() supports full Condition Syntax.</a></li>
</ul>
</div>
</section>
<section id="policies-introduced-by-cmake-3-21">
<h2><a class="toc-backref" href="#id10" role="doc-backlink">Policies Introduced by CMake 3.21</a><a class="headerlink" href="#policies-introduced-by-cmake-3-21" title="Permalink to this heading">¶</a></h2>
<div class="toctree-wrapper compound">
<ul>
<li class="toctree-l1"><a class="reference internal" href="../policy/CMP0126.html">CMP0126: set(CACHE) does not remove a normal variable of the same name.</a></li>
<li class="toctree-l1"><a class="reference internal" href="../policy/CMP0125.html">CMP0125: find_(path|file|library|program) have consistent behavior for cache variables.</a></li>
<li class="toctree-l1"><a class="reference internal" href="../policy/CMP0124.html">CMP0124: foreach() loop variables are only available in the loop scope.</a></li>
<li class="toctree-l1"><a class="reference internal" href="../policy/CMP0123.html">CMP0123: ARMClang cpu/arch compile and link flags must be set explicitly.</a></li>
<li class="toctree-l1"><a class="reference internal" href="../policy/CMP0122.html">CMP0122: UseSWIG use standard library name conventions for csharp language.</a></li>
<li class="toctree-l1"><a class="reference internal" href="../policy/CMP0121.html">CMP0121: The list command detects invalid indices.</a></li>
</ul>
</div>
</section>
<section id="policies-introduced-by-cmake-3-20">
<h2><a class="toc-backref" href="#id11" role="doc-backlink">Policies Introduced by CMake 3.20</a><a class="headerlink" href="#policies-introduced-by-cmake-3-20" title="Permalink to this heading">¶</a></h2>
<div class="toctree-wrapper compound">
<ul>
<li class="toctree-l1"><a class="reference internal" href="../policy/CMP0120.html">CMP0120: The WriteCompilerDetectionHeader module is removed.</a></li>
<li class="toctree-l1"><a class="reference internal" href="../policy/CMP0119.html">CMP0119: LANGUAGE source file property explicitly compiles as language.</a></li>
<li class="toctree-l1"><a class="reference internal" href="../policy/CMP0118.html">CMP0118: The GENERATED source file property is now visible in all directories.</a></li>
<li class="toctree-l1"><a class="reference internal" href="../policy/CMP0117.html">CMP0117: MSVC RTTI flag /GR is not added to CMAKE_CXX_FLAGS by default.</a></li>
<li class="toctree-l1"><a class="reference internal" href="../policy/CMP0116.html">CMP0116: Ninja generators transform DEPFILEs from add_custom_command().</a></li>
<li class="toctree-l1"><a class="reference internal" href="../policy/CMP0115.html">CMP0115: Source file extensions must be explicit.</a></li>
</ul>
</div>
</section>
<section id="policies-introduced-by-cmake-3-19">
<h2><a class="toc-backref" href="#id12" role="doc-backlink">Policies Introduced by CMake 3.19</a><a class="headerlink" href="#policies-introduced-by-cmake-3-19" title="Permalink to this heading">¶</a></h2>
<div class="toctree-wrapper compound">
<ul>
<li class="toctree-l1"><a class="reference internal" href="../policy/CMP0114.html">CMP0114: ExternalProject step targets fully adopt their steps.</a></li>
<li class="toctree-l1"><a class="reference internal" href="../policy/CMP0113.html">CMP0113: Makefile generators do not repeat custom commands from target dependencies.</a></li>
<li class="toctree-l1"><a class="reference internal" href="../policy/CMP0112.html">CMP0112: Target file component generator expressions do not add target dependencies.</a></li>
<li class="toctree-l1"><a class="reference internal" href="../policy/CMP0111.html">CMP0111: An imported target missing its location property fails during generation.</a></li>
<li class="toctree-l1"><a class="reference internal" href="../policy/CMP0110.html">CMP0110: add_test() supports arbitrary characters in test names.</a></li>
<li class="toctree-l1"><a class="reference internal" href="../policy/CMP0109.html">CMP0109: find_program() requires permission to execute but not to read.</a></li>
</ul>
</div>
</section>
<section id="policies-introduced-by-cmake-3-18">
<h2><a class="toc-backref" href="#id13" role="doc-backlink">Policies Introduced by CMake 3.18</a><a class="headerlink" href="#policies-introduced-by-cmake-3-18" title="Permalink to this heading">¶</a></h2>
<div class="toctree-wrapper compound">
<ul>
<li class="toctree-l1"><a class="reference internal" href="../policy/CMP0108.html">CMP0108: A target cannot link to itself through an alias.</a></li>
<li class="toctree-l1"><a class="reference internal" href="../policy/CMP0107.html">CMP0107: An ALIAS target cannot overwrite another target.</a></li>
<li class="toctree-l1"><a class="reference internal" href="../policy/CMP0106.html">CMP0106: The Documentation module is removed.</a></li>
<li class="toctree-l1"><a class="reference internal" href="../policy/CMP0105.html">CMP0105: Device link step uses the link options.</a></li>
<li class="toctree-l1"><a class="reference internal" href="../policy/CMP0104.html">CMP0104: CMAKE_CUDA_ARCHITECTURES now detected for NVCC, empty CUDA_ARCHITECTURES not allowed.</a></li>
<li class="toctree-l1"><a class="reference internal" href="../policy/CMP0103.html">CMP0103: Multiple export() with same FILE without APPEND is not allowed.</a></li>
</ul>
</div>
</section>
<section id="policies-introduced-by-cmake-3-17">
<h2><a class="toc-backref" href="#id14" role="doc-backlink">Policies Introduced by CMake 3.17</a><a class="headerlink" href="#policies-introduced-by-cmake-3-17" title="Permalink to this heading">¶</a></h2>
<div class="toctree-wrapper compound">
<ul>
<li class="toctree-l1"><a class="reference internal" href="../policy/CMP0102.html">CMP0102: mark_as_advanced() does nothing if a cache entry does not exist.</a></li>
<li class="toctree-l1"><a class="reference internal" href="../policy/CMP0101.html">CMP0101: target_compile_options honors BEFORE keyword in all scopes.</a></li>
<li class="toctree-l1"><a class="reference internal" href="../policy/CMP0100.html">CMP0100: Let AUTOMOC and AUTOUIC process .hh header files.</a></li>
<li class="toctree-l1"><a class="reference internal" href="../policy/CMP0099.html">CMP0099: Link properties are transitive over private dependency on static libraries.</a></li>
<li class="toctree-l1"><a class="reference internal" href="../policy/CMP0098.html">CMP0098: FindFLEX runs flex in CMAKE_CURRENT_BINARY_DIR when executing.</a></li>
</ul>
</div>
</section>
<section id="policies-introduced-by-cmake-3-16">
<h2><a class="toc-backref" href="#id15" role="doc-backlink">Policies Introduced by CMake 3.16</a><a class="headerlink" href="#policies-introduced-by-cmake-3-16" title="Permalink to this heading">¶</a></h2>
<div class="toctree-wrapper compound">
<ul>
<li class="toctree-l1"><a class="reference internal" href="../policy/CMP0097.html">CMP0097: ExternalProject_Add with GIT_SUBMODULES &quot;&quot; initializes no submodules.</a></li>
<li class="toctree-l1"><a class="reference internal" href="../policy/CMP0096.html">CMP0096: project() preserves leading zeros in version components.</a></li>
<li class="toctree-l1"><a class="reference internal" href="../policy/CMP0095.html">CMP0095: RPATH entries are properly escaped in the intermediary CMake install script.</a></li>
</ul>
</div>
</section>
<section id="policies-introduced-by-cmake-3-15">
<h2><a class="toc-backref" href="#id16" role="doc-backlink">Policies Introduced by CMake 3.15</a><a class="headerlink" href="#policies-introduced-by-cmake-3-15" title="Permalink to this heading">¶</a></h2>
<div class="toctree-wrapper compound">
<ul>
<li class="toctree-l1"><a class="reference internal" href="../policy/CMP0094.html">CMP0094: FindPython3, FindPython2 and FindPython use LOCATION for lookup strategy.</a></li>
<li class="toctree-l1"><a class="reference internal" href="../policy/CMP0093.html">CMP0093: FindBoost reports Boost_VERSION in x.y.z format.</a></li>
<li class="toctree-l1"><a class="reference internal" href="../policy/CMP0092.html">CMP0092: MSVC warning flags are not in CMAKE_{C,CXX}_FLAGS by default.</a></li>
<li class="toctree-l1"><a class="reference internal" href="../policy/CMP0091.html">CMP0091: MSVC runtime library flags are selected by an abstraction.</a></li>
<li class="toctree-l1"><a class="reference internal" href="../policy/CMP0090.html">CMP0090: export(PACKAGE) does not populate package registry by default.</a></li>
<li class="toctree-l1"><a class="reference internal" href="../policy/CMP0089.html">CMP0089: Compiler id for IBM Clang-based XL compilers is now XLClang.</a></li>
</ul>
</div>
</section>
<section id="policies-introduced-by-cmake-3-14">
<h2><a class="toc-backref" href="#id17" role="doc-backlink">Policies Introduced by CMake 3.14</a><a class="headerlink" href="#policies-introduced-by-cmake-3-14" title="Permalink to this heading">¶</a></h2>
<div class="toctree-wrapper compound">
<ul>
<li class="toctree-l1"><a class="reference internal" href="../policy/CMP0088.html">CMP0088: FindBISON runs bison in CMAKE_CURRENT_BINARY_DIR when executing.</a></li>
<li class="toctree-l1"><a class="reference internal" href="../policy/CMP0087.html">CMP0087: install(SCRIPT | CODE) supports generator expressions.</a></li>
<li class="toctree-l1"><a class="reference internal" href="../policy/CMP0086.html">CMP0086: UseSWIG honors SWIG_MODULE_NAME via -module flag.</a></li>
<li class="toctree-l1"><a class="reference internal" href="../policy/CMP0085.html">CMP0085: IN_LIST generator expression handles empty list items.</a></li>
<li class="toctree-l1"><a class="reference internal" href="../policy/CMP0084.html">CMP0084: The FindQt module does not exist for find_package().</a></li>
<li class="toctree-l1"><a class="reference internal" href="../policy/CMP0083.html">CMP0083: Add PIE options when linking executable.</a></li>
<li class="toctree-l1"><a class="reference internal" href="../policy/CMP0082.html">CMP0082: Install rules from add_subdirectory() are interleaved with those in caller.</a></li>
</ul>
</div>
</section>
<section id="policies-introduced-by-cmake-3-13">
<h2><a class="toc-backref" href="#id18" role="doc-backlink">Policies Introduced by CMake 3.13</a><a class="headerlink" href="#policies-introduced-by-cmake-3-13" title="Permalink to this heading">¶</a></h2>
<div class="toctree-wrapper compound">
<ul>
<li class="toctree-l1"><a class="reference internal" href="../policy/CMP0081.html">CMP0081: Relative paths not allowed in LINK_DIRECTORIES target property.</a></li>
<li class="toctree-l1"><a class="reference internal" href="../policy/CMP0080.html">CMP0080: BundleUtilities cannot be included at configure time.</a></li>
<li class="toctree-l1"><a class="reference internal" href="../policy/CMP0079.html">CMP0079: target_link_libraries allows use with targets in other directories.</a></li>
<li class="toctree-l1"><a class="reference internal" href="../policy/CMP0078.html">CMP0078: UseSWIG generates standard target names.</a></li>
<li class="toctree-l1"><a class="reference internal" href="../policy/CMP0077.html">CMP0077: option() honors normal variables.</a></li>
<li class="toctree-l1"><a class="reference internal" href="../policy/CMP0076.html">CMP0076: target_sources() command converts relative paths to absolute.</a></li>
</ul>
</div>
</section>
<section id="policies-introduced-by-cmake-3-12">
<h2><a class="toc-backref" href="#id19" role="doc-backlink">Policies Introduced by CMake 3.12</a><a class="headerlink" href="#policies-introduced-by-cmake-3-12" title="Permalink to this heading">¶</a></h2>
<div class="toctree-wrapper compound">
<ul>
<li class="toctree-l1"><a class="reference internal" href="../policy/CMP0075.html">CMP0075: Include file check macros honor CMAKE_REQUIRED_LIBRARIES.</a></li>
<li class="toctree-l1"><a class="reference internal" href="../policy/CMP0074.html">CMP0074: find_package uses PackageName_ROOT variables.</a></li>
<li class="toctree-l1"><a class="reference internal" href="../policy/CMP0073.html">CMP0073: Do not produce legacy _LIB_DEPENDS cache entries.</a></li>
</ul>
</div>
</section>
<section id="policies-introduced-by-cmake-3-11">
<h2><a class="toc-backref" href="#id20" role="doc-backlink">Policies Introduced by CMake 3.11</a><a class="headerlink" href="#policies-introduced-by-cmake-3-11" title="Permalink to this heading">¶</a></h2>
<div class="toctree-wrapper compound">
<ul>
<li class="toctree-l1"><a class="reference internal" href="../policy/CMP0072.html">CMP0072: FindOpenGL prefers GLVND by default when available.</a></li>
</ul>
</div>
</section>
<section id="policies-introduced-by-cmake-3-10">
<h2><a class="toc-backref" href="#id21" role="doc-backlink">Policies Introduced by CMake 3.10</a><a class="headerlink" href="#policies-introduced-by-cmake-3-10" title="Permalink to this heading">¶</a></h2>
<div class="toctree-wrapper compound">
<ul>
<li class="toctree-l1"><a class="reference internal" href="../policy/CMP0071.html">CMP0071: Let AUTOMOC and AUTOUIC process GENERATED files.</a></li>
<li class="toctree-l1"><a class="reference internal" href="../policy/CMP0070.html">CMP0070: Define file(GENERATE) behavior for relative paths.</a></li>
</ul>
</div>
</section>
<section id="policies-introduced-by-cmake-3-9">
<h2><a class="toc-backref" href="#id22" role="doc-backlink">Policies Introduced by CMake 3.9</a><a class="headerlink" href="#policies-introduced-by-cmake-3-9" title="Permalink to this heading">¶</a></h2>
<div class="toctree-wrapper compound">
<ul>
<li class="toctree-l1"><a class="reference internal" href="../policy/CMP0069.html">CMP0069: INTERPROCEDURAL_OPTIMIZATION is enforced when enabled.</a></li>
<li class="toctree-l1"><a class="reference internal" href="../policy/CMP0068.html">CMP0068: RPATH settings on macOS do not affect install_name.</a></li>
</ul>
</div>
</section>
<section id="policies-introduced-by-cmake-3-8">
<h2><a class="toc-backref" href="#id23" role="doc-backlink">Policies Introduced by CMake 3.8</a><a class="headerlink" href="#policies-introduced-by-cmake-3-8" title="Permalink to this heading">¶</a></h2>
<div class="toctree-wrapper compound">
<ul>
<li class="toctree-l1"><a class="reference internal" href="../policy/CMP0067.html">CMP0067: Honor language standard in try_compile() source-file signature.</a></li>
</ul>
</div>
</section>
<section id="policies-introduced-by-cmake-3-7">
<h2><a class="toc-backref" href="#id24" role="doc-backlink">Policies Introduced by CMake 3.7</a><a class="headerlink" href="#policies-introduced-by-cmake-3-7" title="Permalink to this heading">¶</a></h2>
<div class="toctree-wrapper compound">
<ul>
<li class="toctree-l1"><a class="reference internal" href="../policy/CMP0066.html">CMP0066: Honor per-config flags in try_compile() source-file signature.</a></li>
</ul>
</div>
</section>
<section id="policies-introduced-by-cmake-3-4">
<h2><a class="toc-backref" href="#id25" role="doc-backlink">Policies Introduced by CMake 3.4</a><a class="headerlink" href="#policies-introduced-by-cmake-3-4" title="Permalink to this heading">¶</a></h2>
<div class="toctree-wrapper compound">
<ul>
<li class="toctree-l1"><a class="reference internal" href="../policy/CMP0065.html">CMP0065: Do not add flags to export symbols from executables without the ENABLE_EXPORTS target property.</a></li>
<li class="toctree-l1"><a class="reference internal" href="../policy/CMP0064.html">CMP0064: Support new TEST if() operator.</a></li>
</ul>
</div>
</section>
<section id="policies-introduced-by-cmake-3-3">
<h2><a class="toc-backref" href="#id26" role="doc-backlink">Policies Introduced by CMake 3.3</a><a class="headerlink" href="#policies-introduced-by-cmake-3-3" title="Permalink to this heading">¶</a></h2>
<div class="toctree-wrapper compound">
<ul>
<li class="toctree-l1"><a class="reference internal" href="../policy/CMP0063.html">CMP0063: Honor visibility properties for all target types.</a></li>
<li class="toctree-l1"><a class="reference internal" href="../policy/CMP0062.html">CMP0062: Disallow install() of export() result.</a></li>
<li class="toctree-l1"><a class="reference internal" href="../policy/CMP0061.html">CMP0061: CTest does not by default tell make to ignore errors (-i).</a></li>
<li class="toctree-l1"><a class="reference internal" href="../policy/CMP0060.html">CMP0060: Link libraries by full path even in implicit directories.</a></li>
<li class="toctree-l1"><a class="reference internal" href="../policy/CMP0059.html">CMP0059: Do not treat DEFINITIONS as a built-in directory property.</a></li>
<li class="toctree-l1"><a class="reference internal" href="../policy/CMP0058.html">CMP0058: Ninja requires custom command byproducts to be explicit.</a></li>
<li class="toctree-l1"><a class="reference internal" href="../policy/CMP0057.html">CMP0057: Support new IN_LIST if() operator.</a></li>
</ul>
</div>
</section>
<section id="policies-introduced-by-cmake-3-2">
<h2><a class="toc-backref" href="#id27" role="doc-backlink">Policies Introduced by CMake 3.2</a><a class="headerlink" href="#policies-introduced-by-cmake-3-2" title="Permalink to this heading">¶</a></h2>
<div class="toctree-wrapper compound">
<ul>
<li class="toctree-l1"><a class="reference internal" href="../policy/CMP0056.html">CMP0056: Honor link flags in try_compile() source-file signature.</a></li>
<li class="toctree-l1"><a class="reference internal" href="../policy/CMP0055.html">CMP0055: Strict checking for break() command.</a></li>
</ul>
</div>
</section>
<section id="policies-introduced-by-cmake-3-1">
<h2><a class="toc-backref" href="#id28" role="doc-backlink">Policies Introduced by CMake 3.1</a><a class="headerlink" href="#policies-introduced-by-cmake-3-1" title="Permalink to this heading">¶</a></h2>
<div class="toctree-wrapper compound">
<ul>
<li class="toctree-l1"><a class="reference internal" href="../policy/CMP0054.html">CMP0054: Only interpret if() arguments as variables or keywords when unquoted.</a></li>
<li class="toctree-l1"><a class="reference internal" href="../policy/CMP0053.html">CMP0053: Simplify variable reference and escape sequence evaluation.</a></li>
<li class="toctree-l1"><a class="reference internal" href="../policy/CMP0052.html">CMP0052: Reject source and build dirs in installed INTERFACE_INCLUDE_DIRECTORIES.</a></li>
<li class="toctree-l1"><a class="reference internal" href="../policy/CMP0051.html">CMP0051: List TARGET_OBJECTS in SOURCES target property.</a></li>
</ul>
</div>
</section>
<section id="policies-introduced-by-cmake-3-0">
<h2><a class="toc-backref" href="#id29" role="doc-backlink">Policies Introduced by CMake 3.0</a><a class="headerlink" href="#policies-introduced-by-cmake-3-0" title="Permalink to this heading">¶</a></h2>
<div class="toctree-wrapper compound">
<ul>
<li class="toctree-l1"><a class="reference internal" href="../policy/CMP0050.html">CMP0050: Disallow add_custom_command SOURCE signatures.</a></li>
<li class="toctree-l1"><a class="reference internal" href="../policy/CMP0049.html">CMP0049: Do not expand variables in target source entries.</a></li>
<li class="toctree-l1"><a class="reference internal" href="../policy/CMP0048.html">CMP0048: project() command manages VERSION variables.</a></li>
<li class="toctree-l1"><a class="reference internal" href="../policy/CMP0047.html">CMP0047: Use QCC compiler id for the qcc drivers on QNX.</a></li>
<li class="toctree-l1"><a class="reference internal" href="../policy/CMP0046.html">CMP0046: Error on non-existent dependency in add_dependencies.</a></li>
<li class="toctree-l1"><a class="reference internal" href="../policy/CMP0045.html">CMP0045: Error on non-existent target in get_target_property.</a></li>
<li class="toctree-l1"><a class="reference internal" href="../policy/CMP0044.html">CMP0044: Case sensitive Lang_COMPILER_ID generator expressions.</a></li>
<li class="toctree-l1"><a class="reference internal" href="../policy/CMP0043.html">CMP0043: Ignore COMPILE_DEFINITIONS_Config properties.</a></li>
<li class="toctree-l1"><a class="reference internal" href="../policy/CMP0042.html">CMP0042: MACOSX_RPATH is enabled by default.</a></li>
<li class="toctree-l1"><a class="reference internal" href="../policy/CMP0041.html">CMP0041: Error on relative include with generator expression.</a></li>
<li class="toctree-l1"><a class="reference internal" href="../policy/CMP0040.html">CMP0040: The target in the TARGET signature of add_custom_command() must exist.</a></li>
<li class="toctree-l1"><a class="reference internal" href="../policy/CMP0039.html">CMP0039: Utility targets may not have link dependencies.</a></li>
<li class="toctree-l1"><a class="reference internal" href="../policy/CMP0038.html">CMP0038: Targets may not link directly to themselves.</a></li>
<li class="toctree-l1"><a class="reference internal" href="../policy/CMP0037.html">CMP0037: Target names should not be reserved and should match a validity pattern.</a></li>
<li class="toctree-l1"><a class="reference internal" href="../policy/CMP0036.html">CMP0036: The build_name command should not be called.</a></li>
<li class="toctree-l1"><a class="reference internal" href="../policy/CMP0035.html">CMP0035: The variable_requires command should not be called.</a></li>
<li class="toctree-l1"><a class="reference internal" href="../policy/CMP0034.html">CMP0034: The utility_source command should not be called.</a></li>
<li class="toctree-l1"><a class="reference internal" href="../policy/CMP0033.html">CMP0033: The export_library_dependencies command should not be called.</a></li>
<li class="toctree-l1"><a class="reference internal" href="../policy/CMP0032.html">CMP0032: The output_required_files command should not be called.</a></li>
<li class="toctree-l1"><a class="reference internal" href="../policy/CMP0031.html">CMP0031: The load_command command should not be called.</a></li>
<li class="toctree-l1"><a class="reference internal" href="../policy/CMP0030.html">CMP0030: The use_mangled_mesa command should not be called.</a></li>
<li class="toctree-l1"><a class="reference internal" href="../policy/CMP0029.html">CMP0029: The subdir_depends command should not be called.</a></li>
<li class="toctree-l1"><a class="reference internal" href="../policy/CMP0028.html">CMP0028: Double colon in target name means ALIAS or IMPORTED target.</a></li>
<li class="toctree-l1"><a class="reference internal" href="../policy/CMP0027.html">CMP0027: Conditionally linked imported targets with missing include directories.</a></li>
<li class="toctree-l1"><a class="reference internal" href="../policy/CMP0026.html">CMP0026: Disallow use of the LOCATION target property.</a></li>
<li class="toctree-l1"><a class="reference internal" href="../policy/CMP0025.html">CMP0025: Compiler id for Apple Clang is now AppleClang.</a></li>
<li class="toctree-l1"><a class="reference internal" href="../policy/CMP0024.html">CMP0024: Disallow include export result.</a></li>
</ul>
</div>
</section>
<section id="policies-introduced-by-cmake-2-8">
<h2><a class="toc-backref" href="#id30" role="doc-backlink">Policies Introduced by CMake 2.8</a><a class="headerlink" href="#policies-introduced-by-cmake-2-8" title="Permalink to this heading">¶</a></h2>
<div class="toctree-wrapper compound">
<ul>
<li class="toctree-l1"><a class="reference internal" href="../policy/CMP0023.html">CMP0023: Plain and keyword target_link_libraries signatures cannot be mixed.</a></li>
<li class="toctree-l1"><a class="reference internal" href="../policy/CMP0022.html">CMP0022: INTERFACE_LINK_LIBRARIES defines the link interface.</a></li>
<li class="toctree-l1"><a class="reference internal" href="../policy/CMP0021.html">CMP0021: Fatal error on relative paths in INCLUDE_DIRECTORIES target property.</a></li>
<li class="toctree-l1"><a class="reference internal" href="../policy/CMP0020.html">CMP0020: Automatically link Qt executables to qtmain target on Windows.</a></li>
<li class="toctree-l1"><a class="reference internal" href="../policy/CMP0019.html">CMP0019: Do not re-expand variables in include and link information.</a></li>
<li class="toctree-l1"><a class="reference internal" href="../policy/CMP0018.html">CMP0018: Ignore CMAKE_SHARED_LIBRARY_Lang_FLAGS variable.</a></li>
<li class="toctree-l1"><a class="reference internal" href="../policy/CMP0017.html">CMP0017: Prefer files from the CMake module directory when including from there.</a></li>
<li class="toctree-l1"><a class="reference internal" href="../policy/CMP0016.html">CMP0016: target_link_libraries() reports error if its only argument is not a target.</a></li>
<li class="toctree-l1"><a class="reference internal" href="../policy/CMP0015.html">CMP0015: link_directories() treats paths relative to the source dir.</a></li>
<li class="toctree-l1"><a class="reference internal" href="../policy/CMP0014.html">CMP0014: Input directories must have CMakeLists.txt.</a></li>
<li class="toctree-l1"><a class="reference internal" href="../policy/CMP0013.html">CMP0013: Duplicate binary directories are not allowed.</a></li>
<li class="toctree-l1"><a class="reference internal" href="../policy/CMP0012.html">CMP0012: if() recognizes numbers and boolean constants.</a></li>
</ul>
</div>
</section>
<section id="policies-introduced-by-cmake-2-6">
<h2><a class="toc-backref" href="#id31" role="doc-backlink">Policies Introduced by CMake 2.6</a><a class="headerlink" href="#policies-introduced-by-cmake-2-6" title="Permalink to this heading">¶</a></h2>
<div class="toctree-wrapper compound">
<ul>
<li class="toctree-l1"><a class="reference internal" href="../policy/CMP0011.html">CMP0011: Included scripts do automatic cmake_policy PUSH and POP.</a></li>
<li class="toctree-l1"><a class="reference internal" href="../policy/CMP0010.html">CMP0010: Bad variable reference syntax is an error.</a></li>
<li class="toctree-l1"><a class="reference internal" href="../policy/CMP0009.html">CMP0009: FILE GLOB_RECURSE calls should not follow symlinks by default.</a></li>
<li class="toctree-l1"><a class="reference internal" href="../policy/CMP0008.html">CMP0008: Libraries linked by full-path must have a valid library file name.</a></li>
<li class="toctree-l1"><a class="reference internal" href="../policy/CMP0007.html">CMP0007: list command no longer ignores empty elements.</a></li>
<li class="toctree-l1"><a class="reference internal" href="../policy/CMP0006.html">CMP0006: Installing MACOSX_BUNDLE targets requires a BUNDLE DESTINATION.</a></li>
<li class="toctree-l1"><a class="reference internal" href="../policy/CMP0005.html">CMP0005: Preprocessor definition values are now escaped automatically.</a></li>
<li class="toctree-l1"><a class="reference internal" href="../policy/CMP0004.html">CMP0004: Libraries linked may not have leading or trailing whitespace.</a></li>
<li class="toctree-l1"><a class="reference internal" href="../policy/CMP0003.html">CMP0003: Libraries linked via full path no longer produce linker search paths.</a></li>
<li class="toctree-l1"><a class="reference internal" href="../policy/CMP0002.html">CMP0002: Logical target names must be globally unique.</a></li>
<li class="toctree-l1"><a class="reference internal" href="../policy/CMP0001.html">CMP0001: CMAKE_BACKWARDS_COMPATIBILITY should no longer be used.</a></li>
<li class="toctree-l1"><a class="reference internal" href="../policy/CMP0000.html">CMP0000: A minimum required CMake version must be specified.</a></li>
</ul>
</div>
</section>
</section>


            <div class="clearer"></div>
          </div>
        </div>
      </div>
      <div class="sphinxsidebar" role="navigation" aria-label="main navigation">
        <div class="sphinxsidebarwrapper">
  <div>
    <h3><a href="../index.html">Table of Contents</a></h3>
    <ul>
<li><a class="reference internal" href="#">cmake-policies(7)</a><ul>
<li><a class="reference internal" href="#introduction">Introduction</a></li>
<li><a class="reference internal" href="#policies-introduced-by-cmake-3-28">Policies Introduced by CMake 3.28</a></li>
<li><a class="reference internal" href="#policies-introduced-by-cmake-3-27">Policies Introduced by CMake 3.27</a></li>
<li><a class="reference internal" href="#policies-introduced-by-cmake-3-26">Policies Introduced by CMake 3.26</a></li>
<li><a class="reference internal" href="#policies-introduced-by-cmake-3-25">Policies Introduced by CMake 3.25</a></li>
<li><a class="reference internal" href="#policies-introduced-by-cmake-3-24">Policies Introduced by CMake 3.24</a></li>
<li><a class="reference internal" href="#policies-introduced-by-cmake-3-23">Policies Introduced by CMake 3.23</a></li>
<li><a class="reference internal" href="#policies-introduced-by-cmake-3-22">Policies Introduced by CMake 3.22</a></li>
<li><a class="reference internal" href="#policies-introduced-by-cmake-3-21">Policies Introduced by CMake 3.21</a></li>
<li><a class="reference internal" href="#policies-introduced-by-cmake-3-20">Policies Introduced by CMake 3.20</a></li>
<li><a class="reference internal" href="#policies-introduced-by-cmake-3-19">Policies Introduced by CMake 3.19</a></li>
<li><a class="reference internal" href="#policies-introduced-by-cmake-3-18">Policies Introduced by CMake 3.18</a></li>
<li><a class="reference internal" href="#policies-introduced-by-cmake-3-17">Policies Introduced by CMake 3.17</a></li>
<li><a class="reference internal" href="#policies-introduced-by-cmake-3-16">Policies Introduced by CMake 3.16</a></li>
<li><a class="reference internal" href="#policies-introduced-by-cmake-3-15">Policies Introduced by CMake 3.15</a></li>
<li><a class="reference internal" href="#policies-introduced-by-cmake-3-14">Policies Introduced by CMake 3.14</a></li>
<li><a class="reference internal" href="#policies-introduced-by-cmake-3-13">Policies Introduced by CMake 3.13</a></li>
<li><a class="reference internal" href="#policies-introduced-by-cmake-3-12">Policies Introduced by CMake 3.12</a></li>
<li><a class="reference internal" href="#policies-introduced-by-cmake-3-11">Policies Introduced by CMake 3.11</a></li>
<li><a class="reference internal" href="#policies-introduced-by-cmake-3-10">Policies Introduced by CMake 3.10</a></li>
<li><a class="reference internal" href="#policies-introduced-by-cmake-3-9">Policies Introduced by CMake 3.9</a></li>
<li><a class="reference internal" href="#policies-introduced-by-cmake-3-8">Policies Introduced by CMake 3.8</a></li>
<li><a class="reference internal" href="#policies-introduced-by-cmake-3-7">Policies Introduced by CMake 3.7</a></li>
<li><a class="reference internal" href="#policies-introduced-by-cmake-3-4">Policies Introduced by CMake 3.4</a></li>
<li><a class="reference internal" href="#policies-introduced-by-cmake-3-3">Policies Introduced by CMake 3.3</a></li>
<li><a class="reference internal" href="#policies-introduced-by-cmake-3-2">Policies Introduced by CMake 3.2</a></li>
<li><a class="reference internal" href="#policies-introduced-by-cmake-3-1">Policies Introduced by CMake 3.1</a></li>
<li><a class="reference internal" href="#policies-introduced-by-cmake-3-0">Policies Introduced by CMake 3.0</a></li>
<li><a class="reference internal" href="#policies-introduced-by-cmake-2-8">Policies Introduced by CMake 2.8</a></li>
<li><a class="reference internal" href="#policies-introduced-by-cmake-2-6">Policies Introduced by CMake 2.6</a></li>
</ul>
</li>
</ul>

  </div>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="cmake-packages.7.html"
                          title="previous chapter">cmake-packages(7)</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="../policy/CMP0155.html"
                          title="next chapter">CMP0155</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../_sources/manual/cmake-policies.7.rst.txt"
            rel="nofollow">Show Source</a></li>
    </ul>
   </div>
<div id="searchbox" style="display: none" role="search">
  <h3 id="searchlabel">Quick search</h3>
    <div class="searchformwrapper">
    <form class="search" action="../search.html" method="get">
      <input type="text" name="q" aria-labelledby="searchlabel" autocomplete="off" autocorrect="off" autocapitalize="off" spellcheck="false"/>
      <input type="submit" value="Go" />
    </form>
    </div>
</div>
<script>document.getElementById('searchbox').style.display = "block"</script>
        </div>
      </div>
      <div class="clearer"></div>
    </div>
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             >index</a></li>
        <li class="right" >
          <a href="../policy/CMP0155.html" title="CMP0155"
             >next</a> |</li>
        <li class="right" >
          <a href="cmake-packages.7.html" title="cmake-packages(7)"
             >previous</a> |</li>
  <li>
    <img src="../_static/cmake-logo-16.png" alt=""
         style="vertical-align: middle; margin-top: -2px" />
  </li>
  <li>
    <a href="https://cmake.org/">CMake</a> &#187;
  </li>
  <li>
    <a href="../index.html">3.28.1 Documentation</a> &#187;
  </li>

        <li class="nav-item nav-item-this"><a href="">cmake-policies(7)</a></li> 
      </ul>
    </div>

    <div class="footer" role="contentinfo">
        &#169; Copyright 2000-2023 Kitware, Inc. and Contributors.
      Created using <a href="https://www.sphinx-doc.org/">Sphinx</a> 6.2.1.
    </div>
  </body>
</html>