{"python.defaultInterpreter": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\python.exe", "python.terminal.activateEnvironment": false, "python.linting.enabled": true, "python.linting.pylintEnabled": false, "python.linting.flake8Enabled": true, "python.formatting.provider": "black", "python.analysis.typeCheckingMode": "basic", "python.analysis.autoImportCompletions": true, "files.associations": {"*.py": "python"}, "terminal.integrated.env.windows": {"CUDA_PATH": "C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v12.6", "CUDA_PATH_V12_6": "C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v12.6", "PATH": "${env:PATH};C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v12.6\\bin;C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v12.6\\libnvvp"}}