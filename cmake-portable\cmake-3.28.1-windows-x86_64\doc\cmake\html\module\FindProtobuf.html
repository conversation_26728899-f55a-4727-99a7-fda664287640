
<!DOCTYPE html>

<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="generator" content="Docutils 0.19: https://docutils.sourceforge.io/" />

  <title>FindProtobuf &mdash; CMake 3.28.1 Documentation</title>

    <link rel="stylesheet" type="text/css" href="../_static/pygments.css" />
    <link rel="stylesheet" type="text/css" href="../_static/cmake.css" />
    
    <script data-url_root="../" id="documentation_options" src="../_static/documentation_options.js"></script>
    <script src="../_static/doctools.js"></script>
    <script src="../_static/sphinx_highlight.js"></script>
    
    <link rel="icon" href="../_static/cmake-favicon.ico"/>
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="next" title="FindPython" href="FindPython.html" />
    <link rel="prev" title="FindProducer" href="FindProducer.html" />
 

  </head><body>

    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             accesskey="I">index</a></li>
        <li class="right" >
          <a href="FindPython.html" title="FindPython"
             accesskey="N">next</a> |</li>
        <li class="right" >
          <a href="FindProducer.html" title="FindProducer"
             accesskey="P">previous</a> |</li>
  <li>
    <img src="../_static/cmake-logo-16.png" alt=""
         style="vertical-align: middle; margin-top: -2px" />
  </li>
  <li>
    <a href="https://cmake.org/">CMake</a> &#187;
  </li>
  <li>
    <a href="../index.html">3.28.1 Documentation</a> &#187;
  </li>

          <li class="nav-item nav-item-1"><a href="../manual/cmake-modules.7.html" accesskey="U">cmake-modules(7)</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href="">FindProtobuf</a></li> 
      </ul>
    </div>  

    <div class="document">
      <div class="documentwrapper">
        <div class="bodywrapper">
          <div class="body" role="main">
            
  <section id="findprotobuf">
<span id="module:FindProtobuf"></span><h1>FindProtobuf<a class="headerlink" href="#findprotobuf" title="Permalink to this heading">¶</a></h1>
<p>Locate and configure the Google Protocol Buffers library.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.6: </span>Support for <span class="target" id="index-0-command:find_package"></span><a class="reference internal" href="../command/find_package.html#command:find_package" title="find_package"><code class="xref cmake cmake-command docutils literal notranslate"><span class="pre">find_package()</span></code></a> version checks.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.6: </span>All input and output variables use the <code class="docutils literal notranslate"><span class="pre">Protobuf_</span></code> prefix.
Variables with <code class="docutils literal notranslate"><span class="pre">PROTOBUF_</span></code> prefix are still supported for compatibility.</p>
</div>
<p>The following variables can be set and are optional:</p>
<dl>
<dt><code class="docutils literal notranslate"><span class="pre">Protobuf_SRC_ROOT_FOLDER</span></code></dt><dd><p>When compiling with MSVC, if this cache variable is set
the protobuf-default VS project build locations
(vsprojects/Debug and vsprojects/Release
or vsprojects/x64/Debug and vsprojects/x64/Release)
will be searched for libraries and binaries.</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">Protobuf_IMPORT_DIRS</span></code></dt><dd><p>List of additional directories to be searched for
imported .proto files.</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">Protobuf_DEBUG</span></code></dt><dd><div class="versionadded">
<p><span class="versionmodified added">New in version 3.6.</span></p>
</div>
<p>Show debug messages.</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">Protobuf_USE_STATIC_LIBS</span></code></dt><dd><div class="versionadded">
<p><span class="versionmodified added">New in version 3.9.</span></p>
</div>
<p>Set to ON to force the use of the static libraries.
Default is OFF.</p>
</dd>
</dl>
<p>Defines the following variables:</p>
<dl>
<dt><code class="docutils literal notranslate"><span class="pre">Protobuf_FOUND</span></code></dt><dd><p>Found the Google Protocol Buffers library
(libprotobuf &amp; header files)</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">Protobuf_VERSION</span></code></dt><dd><div class="versionadded">
<p><span class="versionmodified added">New in version 3.6.</span></p>
</div>
<p>Version of package found.</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">Protobuf_INCLUDE_DIRS</span></code></dt><dd><p>Include directories for Google Protocol Buffers</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">Protobuf_LIBRARIES</span></code></dt><dd><p>The protobuf libraries</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">Protobuf_PROTOC_LIBRARIES</span></code></dt><dd><p>The protoc libraries</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">Protobuf_LITE_LIBRARIES</span></code></dt><dd><p>The protobuf-lite libraries</p>
</dd>
</dl>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.9: </span>The following <span class="target" id="index-0-prop_tgt:IMPORTED"></span><a class="reference internal" href="../prop_tgt/IMPORTED.html#prop_tgt:IMPORTED" title="IMPORTED"><code class="xref cmake cmake-prop_tgt docutils literal notranslate"><span class="pre">IMPORTED</span></code></a> targets are also defined:</p>
</div>
<dl>
<dt><code class="docutils literal notranslate"><span class="pre">protobuf::libprotobuf</span></code></dt><dd><p>The protobuf library.</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">protobuf::libprotobuf-lite</span></code></dt><dd><p>The protobuf lite library.</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">protobuf::libprotoc</span></code></dt><dd><p>The protoc library.</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">protobuf::protoc</span></code></dt><dd><div class="versionadded">
<p><span class="versionmodified added">New in version 3.10: </span>The protoc compiler.</p>
</div>
</dd>
</dl>
<p>The following cache variables are also available to set or use:</p>
<dl class="simple">
<dt><code class="docutils literal notranslate"><span class="pre">Protobuf_LIBRARY</span></code></dt><dd><p>The protobuf library</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">Protobuf_PROTOC_LIBRARY</span></code></dt><dd><p>The protoc library</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">Protobuf_INCLUDE_DIR</span></code></dt><dd><p>The include directory for protocol buffers</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">Protobuf_PROTOC_EXECUTABLE</span></code></dt><dd><p>The protoc compiler</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">Protobuf_LIBRARY_DEBUG</span></code></dt><dd><p>The protobuf library (debug)</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">Protobuf_PROTOC_LIBRARY_DEBUG</span></code></dt><dd><p>The protoc library (debug)</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">Protobuf_LITE_LIBRARY</span></code></dt><dd><p>The protobuf lite library</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">Protobuf_LITE_LIBRARY_DEBUG</span></code></dt><dd><p>The protobuf lite library (debug)</p>
</dd>
</dl>
<p>Example:</p>
<div class="highlight-cmake notranslate"><div class="highlight"><pre><span></span><span class="nf">find_package(</span><span class="nb">Protobuf</span><span class="w"> </span><span class="no">REQUIRED</span><span class="nf">)</span>
<span class="nf">include_directories(</span><span class="o">${</span><span class="nt">Protobuf_INCLUDE_DIRS</span><span class="o">}</span><span class="nf">)</span>
<span class="nf">include_directories(</span><span class="o">${</span><span class="nt">CMAKE_CURRENT_BINARY_DIR</span><span class="o">}</span><span class="nf">)</span>
<span class="nf">protobuf_generate_cpp(</span><span class="no">PROTO_SRCS</span><span class="w"> </span><span class="no">PROTO_HDRS</span><span class="w"> </span><span class="nb">foo.proto</span><span class="nf">)</span>
<span class="nf">protobuf_generate_cpp(</span><span class="no">PROTO_SRCS</span><span class="w"> </span><span class="no">PROTO_HDRS</span><span class="w"> </span><span class="no">EXPORT_MACRO</span><span class="w"> </span><span class="no">DLL_EXPORT</span><span class="w"> </span><span class="nb">foo.proto</span><span class="nf">)</span>
<span class="nf">protobuf_generate_cpp(</span><span class="no">PROTO_SRCS</span><span class="w"> </span><span class="no">PROTO_HDRS</span><span class="w"> </span><span class="no">DESCRIPTORS</span><span class="w"> </span><span class="no">PROTO_DESCS</span><span class="w"> </span><span class="nb">foo.proto</span><span class="nf">)</span>
<span class="nf">protobuf_generate_python(</span><span class="no">PROTO_PY</span><span class="w"> </span><span class="nb">foo.proto</span><span class="nf">)</span>
<span class="nf">add_executable(</span><span class="nb">bar</span><span class="w"> </span><span class="nb">bar.cc</span><span class="w"> </span><span class="o">${</span><span class="nt">PROTO_SRCS</span><span class="o">}</span><span class="w"> </span><span class="o">${</span><span class="nt">PROTO_HDRS</span><span class="o">}</span><span class="nf">)</span>
<span class="nf">target_link_libraries(</span><span class="nb">bar</span><span class="w"> </span><span class="o">${</span><span class="nt">Protobuf_LIBRARIES</span><span class="o">}</span><span class="nf">)</span>
</pre></div>
</div>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>The <code class="docutils literal notranslate"><span class="pre">protobuf_generate_cpp</span></code> and <code class="docutils literal notranslate"><span class="pre">protobuf_generate_python</span></code>
functions and <span class="target" id="index-0-command:add_executable"></span><a class="reference internal" href="../command/add_executable.html#command:add_executable" title="add_executable"><code class="xref cmake cmake-command docutils literal notranslate"><span class="pre">add_executable()</span></code></a> or <span class="target" id="index-0-command:add_library"></span><a class="reference internal" href="../command/add_library.html#command:add_library" title="add_library"><code class="xref cmake cmake-command docutils literal notranslate"><span class="pre">add_library()</span></code></a>
calls only work properly within the same directory.</p>
</div>
<dl class="cmake command">
<dt class="sig sig-object cmake" id="command:protobuf_generate_cpp">
<span class="sig-name descname"><span class="pre">protobuf_generate_cpp</span></span><a class="headerlink" href="#command:protobuf_generate_cpp" title="Permalink to this definition">¶</a></dt>
<dd><p>Add custom commands to process <code class="docutils literal notranslate"><span class="pre">.proto</span></code> files to C++:</p>
<div class="highlight-none notranslate"><div class="highlight"><pre><span></span>protobuf_generate_cpp (&lt;SRCS&gt; &lt;HDRS&gt;
    [DESCRIPTORS &lt;DESC&gt;] [EXPORT_MACRO &lt;MACRO&gt;] [&lt;ARGN&gt;...])
</pre></div>
</div>
<dl>
<dt><code class="docutils literal notranslate"><span class="pre">SRCS</span></code></dt><dd><p>Variable to define with autogenerated source files</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">HDRS</span></code></dt><dd><p>Variable to define with autogenerated header files</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">DESCRIPTORS</span></code></dt><dd><div class="versionadded">
<p><span class="versionmodified added">New in version 3.10: </span>Variable to define with autogenerated descriptor files, if requested.</p>
</div>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">EXPORT_MACRO</span></code></dt><dd><p>is a macro which should expand to <code class="docutils literal notranslate"><span class="pre">__declspec(dllexport)</span></code> or
<code class="docutils literal notranslate"><span class="pre">__declspec(dllimport)</span></code> depending on what is being compiled.</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">ARGN</span></code></dt><dd><p><code class="docutils literal notranslate"><span class="pre">.proto</span></code> files</p>
</dd>
</dl>
</dd></dl>

<dl class="cmake command">
<dt class="sig sig-object cmake" id="command:protobuf_generate_python">
<span class="sig-name descname"><span class="pre">protobuf_generate_python</span></span><a class="headerlink" href="#command:protobuf_generate_python" title="Permalink to this definition">¶</a></dt>
<dd><div class="versionadded">
<p><span class="versionmodified added">New in version 3.4.</span></p>
</div>
<p>Add custom commands to process <code class="docutils literal notranslate"><span class="pre">.proto</span></code> files to Python:</p>
<div class="highlight-none notranslate"><div class="highlight"><pre><span></span>protobuf_generate_python (&lt;PY&gt; [&lt;ARGN&gt;...])
</pre></div>
</div>
<dl class="simple">
<dt><code class="docutils literal notranslate"><span class="pre">PY</span></code></dt><dd><p>Variable to define with autogenerated Python files</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">ARGN</span></code></dt><dd><p><code class="docutils literal notranslate"><span class="pre">.proto</span></code> files</p>
</dd>
</dl>
</dd></dl>

<dl class="cmake command">
<dt class="sig sig-object cmake" id="command:protobuf_generate">
<span class="sig-name descname"><span class="pre">protobuf_generate</span></span><a class="headerlink" href="#command:protobuf_generate" title="Permalink to this definition">¶</a></dt>
<dd><div class="versionadded">
<p><span class="versionmodified added">New in version 3.13.</span></p>
</div>
<p>Automatically generate source files from <code class="docutils literal notranslate"><span class="pre">.proto</span></code> schema files at build time:</p>
<div class="highlight-none notranslate"><div class="highlight"><pre><span></span>protobuf_generate (
    TARGET &lt;target&gt;
    [LANGUAGE &lt;lang&gt;]
    [OUT_VAR &lt;out_var&gt;]
    [EXPORT_MACRO &lt;macro&gt;]
    [PROTOC_OUT_DIR &lt;dir&gt;]
    [PLUGIN &lt;plugin&gt;]
    [PLUGIN_OPTIONS &lt;plugin_options&gt;]
    [DEPENDENCIES &lt;depends]
    [PROTOS &lt;protobuf_files&gt;]
    [IMPORT_DIRS &lt;dirs&gt;]
    [GENERATE_EXTENSIONS &lt;extensions&gt;]
    [PROTOC_OPTIONS &lt;protoc_options&gt;]
    [APPEND_PATH])
</pre></div>
</div>
<dl>
<dt><code class="docutils literal notranslate"><span class="pre">APPEND_PATH</span></code></dt><dd><p>A flag that causes the base path of all proto schema files to be added to
<code class="docutils literal notranslate"><span class="pre">IMPORT_DIRS</span></code>.</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">LANGUAGE</span></code></dt><dd><p>A single value: cpp or python. Determines what kind of source files are
being generated. Defaults to cpp.</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">OUT_VAR</span></code></dt><dd><p>Name of a CMake variable that will be filled with the paths to the generated
source files.</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">EXPORT_MACRO</span></code></dt><dd><p>Name of a macro that is applied to all generated Protobuf message classes
and extern variables. It can, for example, be used to declare DLL exports.</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">PROTOC_OUT_DIR</span></code></dt><dd><p>Output directory of generated source files. Defaults to <code class="docutils literal notranslate"><span class="pre">CMAKE_CURRENT_BINARY_DIR</span></code>.</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">PLUGIN</span></code></dt><dd><div class="versionadded">
<p><span class="versionmodified added">New in version 3.21.</span></p>
</div>
<p>An optional plugin executable. This could, for example, be the path to
<code class="docutils literal notranslate"><span class="pre">grpc_cpp_plugin</span></code>.</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">PLUGIN_OPTIONS</span></code></dt><dd><div class="versionadded">
<p><span class="versionmodified added">New in version 3.28.</span></p>
</div>
<p>Additional options provided to the plugin, such as <code class="docutils literal notranslate"><span class="pre">generate_mock_code=true</span></code>
for the gRPC cpp plugin.</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">DEPENDENCIES</span></code></dt><dd><div class="versionadded">
<p><span class="versionmodified added">New in version 3.28.</span></p>
</div>
<p>Arguments forwarded to the <code class="docutils literal notranslate"><span class="pre">DEPENDS</span></code> of the underlying <code class="docutils literal notranslate"><span class="pre">add_custom_command</span></code>
invocation.</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">TARGET</span></code></dt><dd><p>CMake target that will have the generated files added as sources.</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">PROTOS</span></code></dt><dd><p>List of proto schema files. If omitted, then every source file ending in <em>proto</em> of <code class="docutils literal notranslate"><span class="pre">TARGET</span></code> will be used.</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">IMPORT_DIRS</span></code></dt><dd><p>A common parent directory for the schema files. For example, if the schema file is
<code class="docutils literal notranslate"><span class="pre">proto/helloworld/helloworld.proto</span></code> and the import directory <code class="docutils literal notranslate"><span class="pre">proto/</span></code> then the
generated files are <code class="docutils literal notranslate"><span class="pre">${PROTOC_OUT_DIR}/helloworld/helloworld.pb.h</span></code> and
<code class="docutils literal notranslate"><span class="pre">${PROTOC_OUT_DIR}/helloworld/helloworld.pb.cc</span></code>.</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">GENERATE_EXTENSIONS</span></code></dt><dd><p>If LANGUAGE is omitted then this must be set to the extensions that protoc generates.</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">PROTOC_OPTIONS</span></code></dt><dd><div class="versionadded">
<p><span class="versionmodified added">New in version 3.28.</span></p>
</div>
<p>Additional arguments that are forwarded to protoc.</p>
</dd>
</dl>
<p>Example:</p>
<div class="highlight-none notranslate"><div class="highlight"><pre><span></span>find_package(gRPC CONFIG REQUIRED)
find_package(Protobuf REQUIRED)
add_library(ProtoTest Test.proto)
target_link_libraries(ProtoTest PUBLIC gRPC::grpc++)
protobuf_generate(TARGET ProtoTest)
protobuf_generate(
    TARGET ProtoTest
    LANGUAGE grpc
    PLUGIN protoc-gen-grpc=$&lt;TARGET_FILE:gRPC::grpc_cpp_plugin&gt;
    PLUGIN_OPTIONS generate_mock_code=true
    GENERATE_EXTENSIONS .grpc.pb.h .grpc.pb.cc)
</pre></div>
</div>
</dd></dl>

</section>


            <div class="clearer"></div>
          </div>
        </div>
      </div>
      <div class="sphinxsidebar" role="navigation" aria-label="main navigation">
        <div class="sphinxsidebarwrapper">
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="FindProducer.html"
                          title="previous chapter">FindProducer</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="FindPython.html"
                          title="next chapter">FindPython</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../_sources/module/FindProtobuf.rst.txt"
            rel="nofollow">Show Source</a></li>
    </ul>
   </div>
<div id="searchbox" style="display: none" role="search">
  <h3 id="searchlabel">Quick search</h3>
    <div class="searchformwrapper">
    <form class="search" action="../search.html" method="get">
      <input type="text" name="q" aria-labelledby="searchlabel" autocomplete="off" autocorrect="off" autocapitalize="off" spellcheck="false"/>
      <input type="submit" value="Go" />
    </form>
    </div>
</div>
<script>document.getElementById('searchbox').style.display = "block"</script>
        </div>
      </div>
      <div class="clearer"></div>
    </div>
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             >index</a></li>
        <li class="right" >
          <a href="FindPython.html" title="FindPython"
             >next</a> |</li>
        <li class="right" >
          <a href="FindProducer.html" title="FindProducer"
             >previous</a> |</li>
  <li>
    <img src="../_static/cmake-logo-16.png" alt=""
         style="vertical-align: middle; margin-top: -2px" />
  </li>
  <li>
    <a href="https://cmake.org/">CMake</a> &#187;
  </li>
  <li>
    <a href="../index.html">3.28.1 Documentation</a> &#187;
  </li>

          <li class="nav-item nav-item-1"><a href="../manual/cmake-modules.7.html" >cmake-modules(7)</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href="">FindProtobuf</a></li> 
      </ul>
    </div>

    <div class="footer" role="contentinfo">
        &#169; Copyright 2000-2023 Kitware, Inc. and Contributors.
      Created using <a href="https://www.sphinx-doc.org/">Sphinx</a> 6.2.1.
    </div>
  </body>
</html>