
<!DOCTYPE html>

<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="generator" content="Docutils 0.19: https://docutils.sourceforge.io/" />

  <title>cmake_path &mdash; CMake 3.28.1 Documentation</title>

    <link rel="stylesheet" type="text/css" href="../_static/pygments.css" />
    <link rel="stylesheet" type="text/css" href="../_static/cmake.css" />
    
    <script data-url_root="../" id="documentation_options" src="../_static/documentation_options.js"></script>
    <script src="../_static/doctools.js"></script>
    <script src="../_static/sphinx_highlight.js"></script>
    
    <link rel="icon" href="../_static/cmake-favicon.ico"/>
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="next" title="cmake_policy" href="cmake_policy.html" />
    <link rel="prev" title="cmake_parse_arguments" href="cmake_parse_arguments.html" />
 

  </head><body>

    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             accesskey="I">index</a></li>
        <li class="right" >
          <a href="cmake_policy.html" title="cmake_policy"
             accesskey="N">next</a> |</li>
        <li class="right" >
          <a href="cmake_parse_arguments.html" title="cmake_parse_arguments"
             accesskey="P">previous</a> |</li>
  <li>
    <img src="../_static/cmake-logo-16.png" alt=""
         style="vertical-align: middle; margin-top: -2px" />
  </li>
  <li>
    <a href="https://cmake.org/">CMake</a> &#187;
  </li>
  <li>
    <a href="../index.html">3.28.1 Documentation</a> &#187;
  </li>

          <li class="nav-item nav-item-1"><a href="../manual/cmake-commands.7.html" accesskey="U">cmake-commands(7)</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href="">cmake_path</a></li> 
      </ul>
    </div>  

    <div class="document">
      <div class="documentwrapper">
        <div class="bodywrapper">
          <div class="body" role="main">
            
  <section id="cmake-path">
<span id="command:cmake_path"></span><h1>cmake_path<a class="headerlink" href="#cmake-path" title="Permalink to this heading">¶</a></h1>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.20.</span></p>
</div>
<p>This command is for the manipulation of paths.  Only syntactic aspects of
paths are handled, there is no interaction of any kind with any underlying
file system.  The path may represent a non-existing path or even one that
is not allowed to exist on the current file system or platform.
For operations that do interact with the filesystem, see the <span class="target" id="index-0-command:file"></span><a class="reference internal" href="file.html#command:file" title="file"><code class="xref cmake cmake-command docutils literal notranslate"><span class="pre">file()</span></code></a>
command.</p>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>The <code class="docutils literal notranslate"><span class="pre">cmake_path</span></code> command handles paths in the format of the build system
(i.e. the host platform), not the target system.  When cross-compiling,
if the path contains elements that are not representable on the host
platform (e.g. a drive letter when the host is not Windows), the results
will be unpredictable.</p>
</div>
<section id="synopsis">
<h2>Synopsis<a class="headerlink" href="#synopsis" title="Permalink to this heading">¶</a></h2>
<pre class="literal-block"><a class="reference internal" href="#conventions">Conventions</a>

<a class="reference internal" href="#path-structure-and-terminology">Path Structure And Terminology</a>

<a class="reference internal" href="#normalization">Normalization</a>

<a class="reference internal" href="#decomposition">Decomposition</a>
  cmake_path(<a class="reference internal" href="#get">GET</a> &lt;path-var&gt; <a class="reference internal" href="#get-root-name"><span class="std std-ref">ROOT_NAME</span></a> &lt;out-var&gt;)
  cmake_path(<a class="reference internal" href="#get">GET</a> &lt;path-var&gt; <a class="reference internal" href="#get-root-directory"><span class="std std-ref">ROOT_DIRECTORY</span></a> &lt;out-var&gt;)
  cmake_path(<a class="reference internal" href="#get">GET</a> &lt;path-var&gt; <a class="reference internal" href="#get-root-path"><span class="std std-ref">ROOT_PATH</span></a> &lt;out-var&gt;)
  cmake_path(<a class="reference internal" href="#get">GET</a> &lt;path-var&gt; <a class="reference internal" href="#get-filename"><span class="std std-ref">FILENAME</span></a> &lt;out-var&gt;)
  cmake_path(<a class="reference internal" href="#get">GET</a> &lt;path-var&gt; <a class="reference internal" href="#get-extension"><span class="std std-ref">EXTENSION</span></a> [LAST_ONLY] &lt;out-var&gt;)
  cmake_path(<a class="reference internal" href="#get">GET</a> &lt;path-var&gt; <a class="reference internal" href="#get-stem"><span class="std std-ref">STEM</span></a> [LAST_ONLY] &lt;out-var&gt;)
  cmake_path(<a class="reference internal" href="#get">GET</a> &lt;path-var&gt; <a class="reference internal" href="#get-relative-part"><span class="std std-ref">RELATIVE_PART</span></a> &lt;out-var&gt;)
  cmake_path(<a class="reference internal" href="#get">GET</a> &lt;path-var&gt; <a class="reference internal" href="#get-parent-path"><span class="std std-ref">PARENT_PATH</span></a> &lt;out-var&gt;)

<a class="reference internal" href="#query">Query</a>
  cmake_path(<a class="reference internal" href="#has-root-name">HAS_ROOT_NAME</a> &lt;path-var&gt; &lt;out-var&gt;)
  cmake_path(<a class="reference internal" href="#has-root-directory">HAS_ROOT_DIRECTORY</a> &lt;path-var&gt; &lt;out-var&gt;)
  cmake_path(<a class="reference internal" href="#has-root-path">HAS_ROOT_PATH</a> &lt;path-var&gt; &lt;out-var&gt;)
  cmake_path(<a class="reference internal" href="#has-filename">HAS_FILENAME</a> &lt;path-var&gt; &lt;out-var&gt;)
  cmake_path(<a class="reference internal" href="#has-extension">HAS_EXTENSION</a> &lt;path-var&gt; &lt;out-var&gt;)
  cmake_path(<a class="reference internal" href="#has-stem">HAS_STEM</a> &lt;path-var&gt; &lt;out-var&gt;)
  cmake_path(<a class="reference internal" href="#has-relative-part">HAS_RELATIVE_PART</a> &lt;path-var&gt; &lt;out-var&gt;)
  cmake_path(<a class="reference internal" href="#has-parent-path">HAS_PARENT_PATH</a> &lt;path-var&gt; &lt;out-var&gt;)
  cmake_path(<a class="reference internal" href="#is-absolute">IS_ABSOLUTE</a> &lt;path-var&gt; &lt;out-var&gt;)
  cmake_path(<a class="reference internal" href="#is-relative">IS_RELATIVE</a> &lt;path-var&gt; &lt;out-var&gt;)
  cmake_path(<a class="reference internal" href="#is-prefix">IS_PREFIX</a> &lt;path-var&gt; &lt;input&gt; [NORMALIZE] &lt;out-var&gt;)
  cmake_path(<a class="reference internal" href="#compare">COMPARE</a> &lt;input1&gt; &lt;OP&gt; &lt;input2&gt; &lt;out-var&gt;)

<a class="reference internal" href="#modification">Modification</a>
  cmake_path(<a class="reference internal" href="#cmake-path-set"><span class="std std-ref">SET</span></a> &lt;path-var&gt; [NORMALIZE] &lt;input&gt;)
  cmake_path(<a class="reference internal" href="#append">APPEND</a> &lt;path-var&gt; [&lt;input&gt;...] [OUTPUT_VARIABLE &lt;out-var&gt;])
  cmake_path(<a class="reference internal" href="#append-string">APPEND_STRING</a> &lt;path-var&gt; [&lt;input&gt;...] [OUTPUT_VARIABLE &lt;out-var&gt;])
  cmake_path(<a class="reference internal" href="#remove-filename">REMOVE_FILENAME</a> &lt;path-var&gt; [OUTPUT_VARIABLE &lt;out-var&gt;])
  cmake_path(<a class="reference internal" href="#replace-filename">REPLACE_FILENAME</a> &lt;path-var&gt; &lt;input&gt; [OUTPUT_VARIABLE &lt;out-var&gt;])
  cmake_path(<a class="reference internal" href="#remove-extension">REMOVE_EXTENSION</a> &lt;path-var&gt; [LAST_ONLY] [OUTPUT_VARIABLE &lt;out-var&gt;])
  cmake_path(<a class="reference internal" href="#replace-extension">REPLACE_EXTENSION</a> &lt;path-var&gt; [LAST_ONLY] &lt;input&gt; [OUTPUT_VARIABLE &lt;out-var&gt;])

<a class="reference internal" href="#generation">Generation</a>
  cmake_path(<a class="reference internal" href="#normal-path">NORMAL_PATH</a> &lt;path-var&gt; [OUTPUT_VARIABLE &lt;out-var&gt;])
  cmake_path(<a class="reference internal" href="#relative-path">RELATIVE_PATH</a> &lt;path-var&gt; [BASE_DIRECTORY &lt;input&gt;] [OUTPUT_VARIABLE &lt;out-var&gt;])
  cmake_path(<a class="reference internal" href="#absolute-path">ABSOLUTE_PATH</a> &lt;path-var&gt; [BASE_DIRECTORY &lt;input&gt;] [NORMALIZE] [OUTPUT_VARIABLE &lt;out-var&gt;])

<a class="reference internal" href="#native-conversion">Native Conversion</a>
  cmake_path(<a class="reference internal" href="#native-path">NATIVE_PATH</a> &lt;path-var&gt; [NORMALIZE] &lt;out-var&gt;)
  cmake_path(<a class="reference internal" href="#convert">CONVERT</a> &lt;input&gt; <a class="reference internal" href="#to-cmake-path-list">TO_CMAKE_PATH_LIST</a> &lt;out-var&gt; [NORMALIZE])
  cmake_path(<a class="reference internal" href="#convert">CONVERT</a> &lt;input&gt; <a class="reference internal" href="#to-native-path-list">TO_NATIVE_PATH_LIST</a> &lt;out-var&gt; [NORMALIZE])

<a class="reference internal" href="#hashing">Hashing</a>
  cmake_path(<a class="reference internal" href="#hash">HASH</a> &lt;path-var&gt; &lt;out-var&gt;)</pre>
</section>
<section id="conventions">
<h2>Conventions<a class="headerlink" href="#conventions" title="Permalink to this heading">¶</a></h2>
<p>The following conventions are used in this command's documentation:</p>
<dl class="simple">
<dt><code class="docutils literal notranslate"><span class="pre">&lt;path-var&gt;</span></code></dt><dd><p>Always the name of a variable.  For commands that expect a <code class="docutils literal notranslate"><span class="pre">&lt;path-var&gt;</span></code>
as input, the variable must exist and it is expected to hold a single path.</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">&lt;input&gt;</span></code></dt><dd><p>A string literal which may contain a path, path fragment, or multiple paths
with a special separator depending on the command.  See the description of
each command to see how this is interpreted.</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">&lt;input&gt;...</span></code></dt><dd><p>Zero or more string literal arguments.</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">&lt;out-var&gt;</span></code></dt><dd><p>The name of a variable into which the result of a command will be written.</p>
</dd>
</dl>
</section>
<section id="path-structure-and-terminology">
<span id="id1"></span><h2>Path Structure And Terminology<a class="headerlink" href="#path-structure-and-terminology" title="Permalink to this heading">¶</a></h2>
<p>A path has the following structure (all components are optional, with some
constraints):</p>
<div class="highlight-none notranslate"><div class="highlight"><pre><span></span>root-name root-directory-separator (item-name directory-separator)* filename
</pre></div>
</div>
<dl>
<dt><code class="docutils literal notranslate"><span class="pre">root-name</span></code></dt><dd><p>Identifies the root on a filesystem with multiple roots (such as <code class="docutils literal notranslate"><span class="pre">&quot;C:&quot;</span></code>
or <code class="docutils literal notranslate"><span class="pre">&quot;//myserver&quot;</span></code>). It is optional.</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">root-directory-separator</span></code></dt><dd><p>A directory separator that, if present, indicates that this path is
absolute.  If it is missing and the first element other than the
<code class="docutils literal notranslate"><span class="pre">root-name</span></code> is an <code class="docutils literal notranslate"><span class="pre">item-name</span></code>, then the path is relative.</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">item-name</span></code></dt><dd><p>A sequence of characters that aren't directory separators.  This name may
identify a file, a hard link, a symbolic link, or a directory.  Two special
cases are recognized:</p>
<blockquote>
<div><ul class="simple">
<li><p>The item name consisting of a single dot character <code class="docutils literal notranslate"><span class="pre">.</span></code> is a
directory name that refers to the current directory.</p></li>
<li><p>The item name consisting of two dot characters <code class="docutils literal notranslate"><span class="pre">..</span></code> is a
directory name that refers to the parent directory.</p></li>
</ul>
</div></blockquote>
<p>The <code class="docutils literal notranslate"><span class="pre">(...)*</span></code> pattern shown above is to indicate that there can be zero
or more item names, with multiple items separated by a
<code class="docutils literal notranslate"><span class="pre">directory-separator</span></code>.  The <code class="docutils literal notranslate"><span class="pre">()*</span></code> characters are not part of the path.</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">directory-separator</span></code></dt><dd><p>The only recognized directory separator is a forward slash character <code class="docutils literal notranslate"><span class="pre">/</span></code>.
If this character is repeated, it is treated as a single directory
separator.  In other words, <code class="docutils literal notranslate"><span class="pre">/usr///////lib</span></code> is the same as <code class="docutils literal notranslate"><span class="pre">/usr/lib</span></code>.</p>
</dd>
</dl>
<span id="extension-def"></span><span id="filename-def"></span><dl id="stem-def">
<dt><code class="docutils literal notranslate"><span class="pre">filename</span></code></dt><dd><p>A path has a <code class="docutils literal notranslate"><span class="pre">filename</span></code> if it does not end with a <code class="docutils literal notranslate"><span class="pre">directory-separator</span></code>.
The <code class="docutils literal notranslate"><span class="pre">filename</span></code> is effectively the last <code class="docutils literal notranslate"><span class="pre">item-name</span></code> of the path, so it
can also be a hard link, symbolic link or a directory.</p>
<p>A <code class="docutils literal notranslate"><span class="pre">filename</span></code> can have an <em>extension</em>.  By default, the extension is
defined as the sub-string beginning at the left-most period (including
the period) and until the end of the <code class="docutils literal notranslate"><span class="pre">filename</span></code>.  In commands that
accept a <code class="docutils literal notranslate"><span class="pre">LAST_ONLY</span></code> keyword, <code class="docutils literal notranslate"><span class="pre">LAST_ONLY</span></code> changes the interpretation
to the sub-string beginning at the right-most period.</p>
<p>The following exceptions apply to the above interpretation:</p>
<blockquote>
<div><ul class="simple">
<li><p>If the first character in the <code class="docutils literal notranslate"><span class="pre">filename</span></code> is a period, that period is
ignored (i.e. a <code class="docutils literal notranslate"><span class="pre">filename</span></code> like <code class="docutils literal notranslate"><span class="pre">&quot;.profile&quot;</span></code> is treated as having
no extension).</p></li>
<li><p>If the <code class="docutils literal notranslate"><span class="pre">filename</span></code> is either <code class="docutils literal notranslate"><span class="pre">.</span></code> or <code class="docutils literal notranslate"><span class="pre">..</span></code>, it has no extension.</p></li>
</ul>
</div></blockquote>
<p>The <em>stem</em> is the part of the <code class="docutils literal notranslate"><span class="pre">filename</span></code> before the extension.</p>
</dd>
</dl>
<p>Some commands refer to a <code class="docutils literal notranslate"><span class="pre">root-path</span></code>.  This is the concatenation of
<code class="docutils literal notranslate"><span class="pre">root-name</span></code> and <code class="docutils literal notranslate"><span class="pre">root-directory-separator</span></code>, either or both of which can
be empty.  A <code class="docutils literal notranslate"><span class="pre">relative-part</span></code> refers to the full path with any <code class="docutils literal notranslate"><span class="pre">root-path</span></code>
removed.</p>
</section>
<section id="creating-a-path-variable">
<h2>Creating A Path Variable<a class="headerlink" href="#creating-a-path-variable" title="Permalink to this heading">¶</a></h2>
<p>While a path can be created with care using an ordinary <span class="target" id="index-0-command:set"></span><a class="reference internal" href="set.html#command:set" title="set"><code class="xref cmake cmake-command docutils literal notranslate"><span class="pre">set()</span></code></a>
command, it is recommended to use <a class="reference internal" href="#cmake-path-set"><span class="std std-ref">cmake_path(SET)</span></a>
instead, as it automatically converts the path to the required form where
required.  The <a class="reference internal" href="#append"><span class="std std-ref">cmake_path(APPEND)</span></a> subcommand may
be another suitable alternative where a path needs to be constructed by
joining fragments.  The following example compares the three methods for
constructing the same path:</p>
<div class="highlight-cmake notranslate"><div class="highlight"><pre><span></span><span class="nf">set(</span><span class="nb">path1</span><span class="w"> </span><span class="s">&quot;${CMAKE_CURRENT_SOURCE_DIR}/data&quot;</span><span class="nf">)</span>

<span class="nf">cmake_path(</span><span class="no">SET</span><span class="w"> </span><span class="nb">path2</span><span class="w"> </span><span class="s">&quot;${CMAKE_CURRENT_SOURCE_DIR}/data&quot;</span><span class="nf">)</span>

<span class="nf">cmake_path(</span><span class="no">APPEND</span><span class="w"> </span><span class="nb">path3</span><span class="w"> </span><span class="s">&quot;${CMAKE_CURRENT_SOURCE_DIR}&quot;</span><span class="w"> </span><span class="s">&quot;data&quot;</span><span class="nf">)</span>
</pre></div>
</div>
<p><a class="reference internal" href="#modification">Modification</a> and <a class="reference internal" href="#generation">Generation</a> sub-commands can either store the result
in-place, or in a separate variable named after an <code class="docutils literal notranslate"><span class="pre">OUTPUT_VARIABLE</span></code>
keyword.  All other sub-commands store the result in a mandatory <code class="docutils literal notranslate"><span class="pre">&lt;out-var&gt;</span></code>
variable.</p>
</section>
<section id="normalization">
<span id="id2"></span><h2>Normalization<a class="headerlink" href="#normalization" title="Permalink to this heading">¶</a></h2>
<p>Some sub-commands support <em>normalizing</em> a path.  The algorithm used to
normalize a path is as follows:</p>
<ol class="arabic simple">
<li><p>If the path is empty, stop (the normalized form of an empty path is
also an empty path).</p></li>
<li><p>Replace each <code class="docutils literal notranslate"><span class="pre">directory-separator</span></code>, which may consist of multiple
separators, with a single <code class="docutils literal notranslate"><span class="pre">/</span></code> (<code class="docutils literal notranslate"><span class="pre">/a///b</span>&#160; <span class="pre">--&gt;</span> <span class="pre">/a/b</span></code>).</p></li>
<li><p>Remove each solitary period (<code class="docutils literal notranslate"><span class="pre">.</span></code>) and any immediately following
<code class="docutils literal notranslate"><span class="pre">directory-separator</span></code> (<code class="docutils literal notranslate"><span class="pre">/a/./b/.</span> <span class="pre">--&gt;</span> <span class="pre">/a/b</span></code>).</p></li>
<li><p>Remove each <code class="docutils literal notranslate"><span class="pre">item-name</span></code> (other than <code class="docutils literal notranslate"><span class="pre">..</span></code>) that is immediately
followed by a <code class="docutils literal notranslate"><span class="pre">directory-separator</span></code> and a <code class="docutils literal notranslate"><span class="pre">..</span></code>, along with any
immediately following <code class="docutils literal notranslate"><span class="pre">directory-separator</span></code> (<code class="docutils literal notranslate"><span class="pre">/a/b/../c</span> <span class="pre">--&gt;</span> <span class="pre">a/c</span></code>).</p></li>
<li><p>If there is a <code class="docutils literal notranslate"><span class="pre">root-directory</span></code>, remove any <code class="docutils literal notranslate"><span class="pre">..</span></code> and any
<code class="docutils literal notranslate"><span class="pre">directory-separators</span></code> immediately following them.  The parent of the
root directory is treated as still the root directory (<code class="docutils literal notranslate"><span class="pre">/../a</span> <span class="pre">--&gt;</span> <span class="pre">/a</span></code>).</p></li>
<li><p>If the last <code class="docutils literal notranslate"><span class="pre">item-name</span></code> is <code class="docutils literal notranslate"><span class="pre">..</span></code>, remove any trailing
<code class="docutils literal notranslate"><span class="pre">directory-separator</span></code> (<code class="docutils literal notranslate"><span class="pre">../</span> <span class="pre">--&gt;</span> <span class="pre">..</span></code>).</p></li>
<li><p>If the path is empty by this stage, add a <code class="docutils literal notranslate"><span class="pre">dot</span></code> (normal form of <code class="docutils literal notranslate"><span class="pre">./</span></code>
is <code class="docutils literal notranslate"><span class="pre">.</span></code>).</p></li>
</ol>
</section>
<section id="decomposition">
<span id="path-decomposition"></span><h2>Decomposition<a class="headerlink" href="#decomposition" title="Permalink to this heading">¶</a></h2>
<p id="get-parent-path"><span id="get-relative-part"></span><span id="get-stem"></span><span id="get-extension"></span><span id="get-filename"></span><span id="get-root-path"></span><span id="get-root-directory"></span><span id="get-root-name"></span><span id="get"></span>The following forms of the <code class="docutils literal notranslate"><span class="pre">GET</span></code> subcommand each retrieve a different
component or group of components from a path.  See
<a class="reference internal" href="#path-structure-and-terminology">Path Structure And Terminology</a> for the meaning of each path component.</p>
<div class="highlight-cmake notranslate"><div class="highlight"><pre><span></span><span class="nf">cmake_path(</span><span class="no">GET</span><span class="w"> </span><span class="nv">&lt;path-var&gt;</span><span class="w"> </span><span class="no">ROOT_NAME</span><span class="w"> </span><span class="nv">&lt;out-var&gt;</span><span class="nf">)</span>
<span class="nf">cmake_path(</span><span class="no">GET</span><span class="w"> </span><span class="nv">&lt;path-var&gt;</span><span class="w"> </span><span class="no">ROOT_DIRECTORY</span><span class="w"> </span><span class="nv">&lt;out-var&gt;</span><span class="nf">)</span>
<span class="nf">cmake_path(</span><span class="no">GET</span><span class="w"> </span><span class="nv">&lt;path-var&gt;</span><span class="w"> </span><span class="no">ROOT_PATH</span><span class="w"> </span><span class="nv">&lt;out-var&gt;</span><span class="nf">)</span>
<span class="nf">cmake_path(</span><span class="no">GET</span><span class="w"> </span><span class="nv">&lt;path-var&gt;</span><span class="w"> </span><span class="no">FILENAME</span><span class="w"> </span><span class="nv">&lt;out-var&gt;</span><span class="nf">)</span>
<span class="nf">cmake_path(</span><span class="no">GET</span><span class="w"> </span><span class="nv">&lt;path-var&gt;</span><span class="w"> </span><span class="no">EXTENSION</span><span class="w"> </span><span class="p">[</span><span class="no">LAST_ONLY</span><span class="p">]</span><span class="w"> </span><span class="nv">&lt;out-var&gt;</span><span class="nf">)</span>
<span class="nf">cmake_path(</span><span class="no">GET</span><span class="w"> </span><span class="nv">&lt;path-var&gt;</span><span class="w"> </span><span class="no">STEM</span><span class="w"> </span><span class="p">[</span><span class="no">LAST_ONLY</span><span class="p">]</span><span class="w"> </span><span class="nv">&lt;out-var&gt;</span><span class="nf">)</span>
<span class="nf">cmake_path(</span><span class="no">GET</span><span class="w"> </span><span class="nv">&lt;path-var&gt;</span><span class="w"> </span><span class="no">RELATIVE_PART</span><span class="w"> </span><span class="nv">&lt;out-var&gt;</span><span class="nf">)</span>
<span class="nf">cmake_path(</span><span class="no">GET</span><span class="w"> </span><span class="nv">&lt;path-var&gt;</span><span class="w"> </span><span class="no">PARENT_PATH</span><span class="w"> </span><span class="nv">&lt;out-var&gt;</span><span class="nf">)</span>
</pre></div>
</div>
<p>If a requested component is not present in the path, an empty string will be
stored in <code class="docutils literal notranslate"><span class="pre">&lt;out-var&gt;</span></code>.  For example, only Windows systems have the concept
of a <code class="docutils literal notranslate"><span class="pre">root-name</span></code>, so when the host machine is non-Windows, the <code class="docutils literal notranslate"><span class="pre">ROOT_NAME</span></code>
subcommand will always return an empty string.</p>
<p>For <code class="docutils literal notranslate"><span class="pre">PARENT_PATH</span></code>, if the <a class="reference internal" href="#has-relative-part">HAS_RELATIVE_PART</a> subcommand returns false,
the result is a copy of <code class="docutils literal notranslate"><span class="pre">&lt;path-var&gt;</span></code>.  Note that this implies that a root
directory is considered to have a parent, with that parent being itself.
Where <a class="reference internal" href="#has-relative-part">HAS_RELATIVE_PART</a> returns true, the result will essentially be
<code class="docutils literal notranslate"><span class="pre">&lt;path-var&gt;</span></code> with one less element.</p>
<section id="root-examples">
<h3>Root examples<a class="headerlink" href="#root-examples" title="Permalink to this heading">¶</a></h3>
<div class="highlight-cmake notranslate"><div class="highlight"><pre><span></span><span class="nf">set(</span><span class="nb">path</span><span class="w"> </span><span class="s">&quot;c:/a&quot;</span><span class="nf">)</span>

<span class="nf">cmake_path(</span><span class="no">GET</span><span class="w"> </span><span class="nb">path</span><span class="w"> </span><span class="no">ROOT_NAME</span><span class="w"> </span><span class="nb">rootName</span><span class="nf">)</span>
<span class="nf">cmake_path(</span><span class="no">GET</span><span class="w"> </span><span class="nb">path</span><span class="w"> </span><span class="no">ROOT_DIRECTORY</span><span class="w"> </span><span class="nb">rootDir</span><span class="nf">)</span>
<span class="nf">cmake_path(</span><span class="no">GET</span><span class="w"> </span><span class="nb">path</span><span class="w"> </span><span class="no">ROOT_PATH</span><span class="w"> </span><span class="nb">rootPath</span><span class="nf">)</span>

<span class="nf">message(</span><span class="s">&quot;Root name is \&quot;${rootName}\&quot;&quot;</span><span class="nf">)</span>
<span class="nf">message(</span><span class="s">&quot;Root directory is \&quot;${rootDir}\&quot;&quot;</span><span class="nf">)</span>
<span class="nf">message(</span><span class="s">&quot;Root path is \&quot;${rootPath}\&quot;&quot;</span><span class="nf">)</span>
</pre></div>
</div>
<div class="highlight-none notranslate"><div class="highlight"><pre><span></span>Root name is &quot;c:&quot;
Root directory is &quot;/&quot;
Root path is &quot;c:/&quot;
</pre></div>
</div>
</section>
<section id="filename-examples">
<h3>Filename examples<a class="headerlink" href="#filename-examples" title="Permalink to this heading">¶</a></h3>
<div class="highlight-cmake notranslate"><div class="highlight"><pre><span></span><span class="nf">set(</span><span class="nb">path</span><span class="w"> </span><span class="s">&quot;/a/b&quot;</span><span class="nf">)</span>
<span class="nf">cmake_path(</span><span class="no">GET</span><span class="w"> </span><span class="nb">path</span><span class="w"> </span><span class="no">FILENAME</span><span class="w"> </span><span class="nb">filename</span><span class="nf">)</span>
<span class="nf">message(</span><span class="s">&quot;First filename is \&quot;${filename}\&quot;&quot;</span><span class="nf">)</span>

<span class="c"># Trailing slash means filename is empty</span>
<span class="nf">set(</span><span class="nb">path</span><span class="w"> </span><span class="s">&quot;/a/b/&quot;</span><span class="nf">)</span>
<span class="nf">cmake_path(</span><span class="no">GET</span><span class="w"> </span><span class="nb">path</span><span class="w"> </span><span class="no">FILENAME</span><span class="w"> </span><span class="nb">filename</span><span class="nf">)</span>
<span class="nf">message(</span><span class="s">&quot;Second filename is \&quot;${filename}\&quot;&quot;</span><span class="nf">)</span>
</pre></div>
</div>
<div class="highlight-none notranslate"><div class="highlight"><pre><span></span>First filename is &quot;b&quot;
Second filename is &quot;&quot;
</pre></div>
</div>
</section>
<section id="extension-and-stem-examples">
<h3>Extension and stem examples<a class="headerlink" href="#extension-and-stem-examples" title="Permalink to this heading">¶</a></h3>
<div class="highlight-cmake notranslate"><div class="highlight"><pre><span></span><span class="nf">set(</span><span class="nb">path</span><span class="w"> </span><span class="s">&quot;name.ext1.ext2&quot;</span><span class="nf">)</span>

<span class="nf">cmake_path(</span><span class="no">GET</span><span class="w"> </span><span class="nb">path</span><span class="w"> </span><span class="no">EXTENSION</span><span class="w"> </span><span class="nb">fullExt</span><span class="nf">)</span>
<span class="nf">cmake_path(</span><span class="no">GET</span><span class="w"> </span><span class="nb">path</span><span class="w"> </span><span class="no">STEM</span><span class="w"> </span><span class="nb">fullStem</span><span class="nf">)</span>
<span class="nf">message(</span><span class="s">&quot;Full extension is \&quot;${fullExt}\&quot;&quot;</span><span class="nf">)</span>
<span class="nf">message(</span><span class="s">&quot;Full stem is \&quot;${fullStem}\&quot;&quot;</span><span class="nf">)</span>

<span class="c"># Effect of LAST_ONLY</span>
<span class="nf">cmake_path(</span><span class="no">GET</span><span class="w"> </span><span class="nb">path</span><span class="w"> </span><span class="no">EXTENSION</span><span class="w"> </span><span class="no">LAST_ONLY</span><span class="w"> </span><span class="nb">lastExt</span><span class="nf">)</span>
<span class="nf">cmake_path(</span><span class="no">GET</span><span class="w"> </span><span class="nb">path</span><span class="w"> </span><span class="no">STEM</span><span class="w"> </span><span class="no">LAST_ONLY</span><span class="w"> </span><span class="nb">lastStem</span><span class="nf">)</span>
<span class="nf">message(</span><span class="s">&quot;Last extension is \&quot;${lastExt}\&quot;&quot;</span><span class="nf">)</span>
<span class="nf">message(</span><span class="s">&quot;Last stem is \&quot;${lastStem}\&quot;&quot;</span><span class="nf">)</span>

<span class="c"># Special cases</span>
<span class="nf">set(</span><span class="nb">dotPath</span><span class="w"> </span><span class="s">&quot;/a/.&quot;</span><span class="nf">)</span>
<span class="nf">set(</span><span class="nb">dotDotPath</span><span class="w"> </span><span class="s">&quot;/a/..&quot;</span><span class="nf">)</span>
<span class="nf">set(</span><span class="nb">someMorePath</span><span class="w"> </span><span class="s">&quot;/a/.some.more&quot;</span><span class="nf">)</span>
<span class="nf">cmake_path(</span><span class="no">GET</span><span class="w"> </span><span class="nb">dotPath</span><span class="w"> </span><span class="no">EXTENSION</span><span class="w"> </span><span class="nb">dotExt</span><span class="nf">)</span>
<span class="nf">cmake_path(</span><span class="no">GET</span><span class="w"> </span><span class="nb">dotPath</span><span class="w"> </span><span class="no">STEM</span><span class="w"> </span><span class="nb">dotStem</span><span class="nf">)</span>
<span class="nf">cmake_path(</span><span class="no">GET</span><span class="w"> </span><span class="nb">dotDotPath</span><span class="w"> </span><span class="no">EXTENSION</span><span class="w"> </span><span class="nb">dotDotExt</span><span class="nf">)</span>
<span class="nf">cmake_path(</span><span class="no">GET</span><span class="w"> </span><span class="nb">dotDotPath</span><span class="w"> </span><span class="no">STEM</span><span class="w"> </span><span class="nb">dotDotStem</span><span class="nf">)</span>
<span class="nf">cmake_path(</span><span class="no">GET</span><span class="w"> </span><span class="nb">dotMorePath</span><span class="w"> </span><span class="no">EXTENSION</span><span class="w"> </span><span class="nb">someMoreExt</span><span class="nf">)</span>
<span class="nf">cmake_path(</span><span class="no">GET</span><span class="w"> </span><span class="nb">dotMorePath</span><span class="w"> </span><span class="no">STEM</span><span class="w"> </span><span class="nb">someMoreStem</span><span class="nf">)</span>
<span class="nf">message(</span><span class="s">&quot;Dot extension is \&quot;${dotExt}\&quot;&quot;</span><span class="nf">)</span>
<span class="nf">message(</span><span class="s">&quot;Dot stem is \&quot;${dotStem}\&quot;&quot;</span><span class="nf">)</span>
<span class="nf">message(</span><span class="s">&quot;Dot-dot extension is \&quot;${dotDotExt}\&quot;&quot;</span><span class="nf">)</span>
<span class="nf">message(</span><span class="s">&quot;Dot-dot stem is \&quot;${dotDotStem}\&quot;&quot;</span><span class="nf">)</span>
<span class="nf">message(</span><span class="s">&quot;.some.more extension is \&quot;${someMoreExt}\&quot;&quot;</span><span class="nf">)</span>
<span class="nf">message(</span><span class="s">&quot;.some.more stem is \&quot;${someMoreStem}\&quot;&quot;</span><span class="nf">)</span>
</pre></div>
</div>
<div class="highlight-none notranslate"><div class="highlight"><pre><span></span>Full extension is &quot;.ext1.ext2&quot;
Full stem is &quot;name&quot;
Last extension is &quot;.ext2&quot;
Last stem is &quot;name.ext1&quot;
Dot extension is &quot;&quot;
Dot stem is &quot;.&quot;
Dot-dot extension is &quot;&quot;
Dot-dot stem is &quot;..&quot;
.some.more extension is &quot;.more&quot;
.some.more stem is &quot;.some&quot;
</pre></div>
</div>
</section>
<section id="relative-part-examples">
<h3>Relative part examples<a class="headerlink" href="#relative-part-examples" title="Permalink to this heading">¶</a></h3>
<div class="highlight-cmake notranslate"><div class="highlight"><pre><span></span><span class="nf">set(</span><span class="nb">path</span><span class="w"> </span><span class="s">&quot;c:/a/b&quot;</span><span class="nf">)</span>
<span class="nf">cmake_path(</span><span class="no">GET</span><span class="w"> </span><span class="nb">path</span><span class="w"> </span><span class="no">RELATIVE_PART</span><span class="w"> </span><span class="nb">result</span><span class="nf">)</span>
<span class="nf">message(</span><span class="s">&quot;Relative part is \&quot;${result}\&quot;&quot;</span><span class="nf">)</span>

<span class="nf">set(</span><span class="nb">path</span><span class="w"> </span><span class="s">&quot;c/d&quot;</span><span class="nf">)</span>
<span class="nf">cmake_path(</span><span class="no">GET</span><span class="w"> </span><span class="nb">path</span><span class="w"> </span><span class="no">RELATIVE_PART</span><span class="w"> </span><span class="nb">result</span><span class="nf">)</span>
<span class="nf">message(</span><span class="s">&quot;Relative part is \&quot;${result}\&quot;&quot;</span><span class="nf">)</span>

<span class="nf">set(</span><span class="nb">path</span><span class="w"> </span><span class="s">&quot;/&quot;</span><span class="nf">)</span>
<span class="nf">cmake_path(</span><span class="no">GET</span><span class="w"> </span><span class="nb">path</span><span class="w"> </span><span class="no">RELATIVE_PART</span><span class="w"> </span><span class="nb">result</span><span class="nf">)</span>
<span class="nf">message(</span><span class="s">&quot;Relative part is \&quot;${result}\&quot;&quot;</span><span class="nf">)</span>
</pre></div>
</div>
<div class="highlight-none notranslate"><div class="highlight"><pre><span></span>Relative part is &quot;a/b&quot;
Relative part is &quot;c/d&quot;
Relative part is &quot;&quot;
</pre></div>
</div>
</section>
<section id="path-traversal-examples">
<h3>Path traversal examples<a class="headerlink" href="#path-traversal-examples" title="Permalink to this heading">¶</a></h3>
<div class="highlight-cmake notranslate"><div class="highlight"><pre><span></span><span class="nf">set(</span><span class="nb">path</span><span class="w"> </span><span class="s">&quot;c:/a/b&quot;</span><span class="nf">)</span>
<span class="nf">cmake_path(</span><span class="no">GET</span><span class="w"> </span><span class="nb">path</span><span class="w"> </span><span class="no">PARENT_PATH</span><span class="w"> </span><span class="nb">result</span><span class="nf">)</span>
<span class="nf">message(</span><span class="s">&quot;Parent path is \&quot;${result}\&quot;&quot;</span><span class="nf">)</span>

<span class="nf">set(</span><span class="nb">path</span><span class="w"> </span><span class="s">&quot;c:/&quot;</span><span class="nf">)</span>
<span class="nf">cmake_path(</span><span class="no">GET</span><span class="w"> </span><span class="nb">path</span><span class="w"> </span><span class="no">PARENT_PATH</span><span class="w"> </span><span class="nb">result</span><span class="nf">)</span>
<span class="nf">message(</span><span class="s">&quot;Parent path is \&quot;${result}\&quot;&quot;</span><span class="nf">)</span>
</pre></div>
</div>
<div class="highlight-none notranslate"><div class="highlight"><pre><span></span>Parent path is &quot;c:/a&quot;
Parent path is &quot;c:/&quot;
</pre></div>
</div>
</section>
</section>
<section id="query">
<span id="path-query"></span><h2>Query<a class="headerlink" href="#query" title="Permalink to this heading">¶</a></h2>
<p>Each of the <code class="docutils literal notranslate"><span class="pre">GET</span></code> subcommands has a corresponding <code class="docutils literal notranslate"><span class="pre">HAS_...</span></code>
subcommand which can be used to discover whether a particular path
component is present.  See <a class="reference internal" href="#path-structure-and-terminology">Path Structure And Terminology</a> for the
meaning of each path component.</p>
<div class="highlight-cmake notranslate" id="has-parent-path"><span id="has-relative-part"></span><span id="has-stem"></span><span id="has-extension"></span><span id="has-filename"></span><span id="has-root-path"></span><span id="has-root-directory"></span><span id="has-root-name"></span><div class="highlight"><pre><span></span><span class="nf">cmake_path(</span><span class="no">HAS_ROOT_NAME</span><span class="w"> </span><span class="nv">&lt;path-var&gt;</span><span class="w"> </span><span class="nv">&lt;out-var&gt;</span><span class="nf">)</span>
<span class="nf">cmake_path(</span><span class="no">HAS_ROOT_DIRECTORY</span><span class="w"> </span><span class="nv">&lt;path-var&gt;</span><span class="w"> </span><span class="nv">&lt;out-var&gt;</span><span class="nf">)</span>
<span class="nf">cmake_path(</span><span class="no">HAS_ROOT_PATH</span><span class="w"> </span><span class="nv">&lt;path-var&gt;</span><span class="w"> </span><span class="nv">&lt;out-var&gt;</span><span class="nf">)</span>
<span class="nf">cmake_path(</span><span class="no">HAS_FILENAME</span><span class="w"> </span><span class="nv">&lt;path-var&gt;</span><span class="w"> </span><span class="nv">&lt;out-var&gt;</span><span class="nf">)</span>
<span class="nf">cmake_path(</span><span class="no">HAS_EXTENSION</span><span class="w"> </span><span class="nv">&lt;path-var&gt;</span><span class="w"> </span><span class="nv">&lt;out-var&gt;</span><span class="nf">)</span>
<span class="nf">cmake_path(</span><span class="no">HAS_STEM</span><span class="w"> </span><span class="nv">&lt;path-var&gt;</span><span class="w"> </span><span class="nv">&lt;out-var&gt;</span><span class="nf">)</span>
<span class="nf">cmake_path(</span><span class="no">HAS_RELATIVE_PART</span><span class="w"> </span><span class="nv">&lt;path-var&gt;</span><span class="w"> </span><span class="nv">&lt;out-var&gt;</span><span class="nf">)</span>
<span class="nf">cmake_path(</span><span class="no">HAS_PARENT_PATH</span><span class="w"> </span><span class="nv">&lt;path-var&gt;</span><span class="w"> </span><span class="nv">&lt;out-var&gt;</span><span class="nf">)</span>
</pre></div>
</div>
<p>Each of the above follows the predictable pattern of setting <code class="docutils literal notranslate"><span class="pre">&lt;out-var&gt;</span></code>
to true if the path has the associated component, or false otherwise.
Note the following special cases:</p>
<ul class="simple">
<li><p>For <code class="docutils literal notranslate"><span class="pre">HAS_ROOT_PATH</span></code>, a true result will only be returned if at least one
of <code class="docutils literal notranslate"><span class="pre">root-name</span></code> or <code class="docutils literal notranslate"><span class="pre">root-directory</span></code> is non-empty.</p></li>
<li><p>For <code class="docutils literal notranslate"><span class="pre">HAS_PARENT_PATH</span></code>, the root directory is also considered to have a
parent, which will be itself.  The result is true except if the path
consists of just a <a class="reference internal" href="#filename-def"><span class="std std-ref">filename</span></a>.</p></li>
</ul>
<div class="highlight-cmake notranslate" id="is-absolute"><div class="highlight"><pre><span></span><span class="nf">cmake_path(</span><span class="no">IS_ABSOLUTE</span><span class="w"> </span><span class="nv">&lt;path-var&gt;</span><span class="w"> </span><span class="nv">&lt;out-var&gt;</span><span class="nf">)</span>
</pre></div>
</div>
<p>Sets <code class="docutils literal notranslate"><span class="pre">&lt;out-var&gt;</span></code> to true if <code class="docutils literal notranslate"><span class="pre">&lt;path-var&gt;</span></code> is absolute.  An absolute path
is a path that unambiguously identifies the location of a file without
reference to an additional starting location.  On Windows, this means the
path must have both a <code class="docutils literal notranslate"><span class="pre">root-name</span></code> and a <code class="docutils literal notranslate"><span class="pre">root-directory-separator</span></code> to be
considered absolute.  On other platforms, just a <code class="docutils literal notranslate"><span class="pre">root-directory-separator</span></code>
is sufficient.  Note that this means on Windows, <code class="docutils literal notranslate"><span class="pre">IS_ABSOLUTE</span></code> can be
false while <code class="docutils literal notranslate"><span class="pre">HAS_ROOT_DIRECTORY</span></code> can be true.</p>
<div class="highlight-cmake notranslate" id="is-relative"><div class="highlight"><pre><span></span><span class="nf">cmake_path(</span><span class="no">IS_RELATIVE</span><span class="w"> </span><span class="nv">&lt;path-var&gt;</span><span class="w"> </span><span class="nv">&lt;out-var&gt;</span><span class="nf">)</span>
</pre></div>
</div>
<p>This will store the opposite of <code class="docutils literal notranslate"><span class="pre">IS_ABSOLUTE</span></code> in <code class="docutils literal notranslate"><span class="pre">&lt;out-var&gt;</span></code>.</p>
<div class="highlight-cmake notranslate" id="is-prefix"><div class="highlight"><pre><span></span><span class="nf">cmake_path(</span><span class="no">IS_PREFIX</span><span class="w"> </span><span class="nv">&lt;path-var&gt;</span><span class="w"> </span><span class="nv">&lt;input&gt;</span><span class="w"> </span><span class="p">[</span><span class="no">NORMALIZE</span><span class="p">]</span><span class="w"> </span><span class="nv">&lt;out-var&gt;</span><span class="nf">)</span>
</pre></div>
</div>
<p>Checks if <code class="docutils literal notranslate"><span class="pre">&lt;path-var&gt;</span></code> is the prefix of <code class="docutils literal notranslate"><span class="pre">&lt;input&gt;</span></code>.</p>
<p>When the <code class="docutils literal notranslate"><span class="pre">NORMALIZE</span></code> option is specified, <code class="docutils literal notranslate"><span class="pre">&lt;path-var&gt;</span></code> and <code class="docutils literal notranslate"><span class="pre">&lt;input&gt;</span></code>
are <a class="reference internal" href="#normalization"><span class="std std-ref">normalized</span></a> before the check.</p>
<div class="highlight-cmake notranslate"><div class="highlight"><pre><span></span><span class="nf">set(</span><span class="nb">path</span><span class="w"> </span><span class="s">&quot;/a/b/c&quot;</span><span class="nf">)</span>
<span class="nf">cmake_path(</span><span class="no">IS_PREFIX</span><span class="w"> </span><span class="nb">path</span><span class="w"> </span><span class="s">&quot;/a/b/c/d&quot;</span><span class="w"> </span><span class="nb">result</span><span class="nf">)</span><span class="w"> </span><span class="c"># result = true</span>
<span class="nf">cmake_path(</span><span class="no">IS_PREFIX</span><span class="w"> </span><span class="nb">path</span><span class="w"> </span><span class="s">&quot;/a/b&quot;</span><span class="w"> </span><span class="nb">result</span><span class="nf">)</span><span class="w">     </span><span class="c"># result = false</span>
<span class="nf">cmake_path(</span><span class="no">IS_PREFIX</span><span class="w"> </span><span class="nb">path</span><span class="w"> </span><span class="s">&quot;/x/y/z&quot;</span><span class="w"> </span><span class="nb">result</span><span class="nf">)</span><span class="w">   </span><span class="c"># result = false</span>

<span class="nf">set(</span><span class="nb">path</span><span class="w"> </span><span class="s">&quot;/a/b&quot;</span><span class="nf">)</span>
<span class="nf">cmake_path(</span><span class="no">IS_PREFIX</span><span class="w"> </span><span class="nb">path</span><span class="w"> </span><span class="s">&quot;/a/c/../b&quot;</span><span class="w"> </span><span class="no">NORMALIZE</span><span class="w"> </span><span class="nb">result</span><span class="nf">)</span><span class="w">   </span><span class="c"># result = true</span>
</pre></div>
</div>
<div class="highlight-cmake notranslate" id="compare"><span id="path-compare"></span><div class="highlight"><pre><span></span><span class="nf">cmake_path(</span><span class="no">COMPARE</span><span class="w"> </span><span class="nv">&lt;input1&gt;</span><span class="w"> </span><span class="no">EQUAL</span><span class="w"> </span><span class="nv">&lt;input2&gt;</span><span class="w"> </span><span class="nv">&lt;out-var&gt;</span><span class="nf">)</span>
<span class="nf">cmake_path(</span><span class="no">COMPARE</span><span class="w"> </span><span class="nv">&lt;input1&gt;</span><span class="w"> </span><span class="no">NOT_EQUAL</span><span class="w"> </span><span class="nv">&lt;input2&gt;</span><span class="w"> </span><span class="nv">&lt;out-var&gt;</span><span class="nf">)</span>
</pre></div>
</div>
<p>Compares the lexical representations of two paths provided as string literals.
No normalization is performed on either path, except multiple consecutive
directory separators are effectively collapsed into a single separator.
Equality is determined according to the following pseudo-code logic:</p>
<div class="highlight-none notranslate"><div class="highlight"><pre><span></span>if(NOT &lt;input1&gt;.root_name() STREQUAL &lt;input2&gt;.root_name())
  return FALSE

if(&lt;input1&gt;.has_root_directory() XOR &lt;input2&gt;.has_root_directory())
  return FALSE

Return FALSE if a relative portion of &lt;input1&gt; is not lexicographically
equal to the relative portion of &lt;input2&gt;. This comparison is performed path
component-wise. If all of the components compare equal, then return TRUE.
</pre></div>
</div>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>Unlike most other <code class="docutils literal notranslate"><span class="pre">cmake_path()</span></code> subcommands, the <code class="docutils literal notranslate"><span class="pre">COMPARE</span></code> subcommand
takes literal strings as input, not the names of variables.</p>
</div>
</section>
<section id="modification">
<span id="path-modification"></span><h2>Modification<a class="headerlink" href="#modification" title="Permalink to this heading">¶</a></h2>
<div class="highlight-cmake notranslate" id="cmake-path-set"><div class="highlight"><pre><span></span><span class="nf">cmake_path(</span><span class="no">SET</span><span class="w"> </span><span class="nv">&lt;path-var&gt;</span><span class="w"> </span><span class="p">[</span><span class="no">NORMALIZE</span><span class="p">]</span><span class="w"> </span><span class="nv">&lt;input&gt;</span><span class="nf">)</span>
</pre></div>
</div>
<p>Assign the <code class="docutils literal notranslate"><span class="pre">&lt;input&gt;</span></code> path to <code class="docutils literal notranslate"><span class="pre">&lt;path-var&gt;</span></code>.  If <code class="docutils literal notranslate"><span class="pre">&lt;input&gt;</span></code> is a native
path, it is converted into a cmake-style path with forward-slashes
(<code class="docutils literal notranslate"><span class="pre">/</span></code>). On Windows, the long filename marker is taken into account.</p>
<p>When the <code class="docutils literal notranslate"><span class="pre">NORMALIZE</span></code> option is specified, the path is <a class="reference internal" href="#normalization"><span class="std std-ref">normalized</span></a> after the conversion.</p>
<p>For example:</p>
<div class="highlight-cmake notranslate"><div class="highlight"><pre><span></span><span class="nf">set(</span><span class="nb">native_path</span><span class="w"> </span><span class="s">&quot;c:\\a\\b/..\\c&quot;</span><span class="nf">)</span>
<span class="nf">cmake_path(</span><span class="no">SET</span><span class="w"> </span><span class="nb">path</span><span class="w"> </span><span class="s">&quot;${native_path}&quot;</span><span class="nf">)</span>
<span class="nf">message(</span><span class="s">&quot;CMake path is \&quot;${path}\&quot;&quot;</span><span class="nf">)</span>

<span class="nf">cmake_path(</span><span class="no">SET</span><span class="w"> </span><span class="nb">path</span><span class="w"> </span><span class="no">NORMALIZE</span><span class="w"> </span><span class="s">&quot;${native_path}&quot;</span><span class="nf">)</span>
<span class="nf">message(</span><span class="s">&quot;Normalized CMake path is \&quot;${path}\&quot;&quot;</span><span class="nf">)</span>
</pre></div>
</div>
<p>Output:</p>
<div class="highlight-none notranslate"><div class="highlight"><pre><span></span>CMake path is &quot;c:/a/b/../c&quot;
Normalized CMake path is &quot;c:/a/c&quot;
</pre></div>
</div>
<div class="highlight-cmake notranslate" id="append"><div class="highlight"><pre><span></span><span class="nf">cmake_path(</span><span class="no">APPEND</span><span class="w"> </span><span class="nv">&lt;path-var&gt;</span><span class="w"> </span><span class="p">[</span><span class="nv">&lt;input&gt;...</span><span class="p">]</span><span class="w"> </span><span class="p">[</span><span class="no">OUTPUT_VARIABLE</span><span class="w"> </span><span class="nv">&lt;out-var&gt;</span><span class="p">]</span><span class="nf">)</span>
</pre></div>
</div>
<p>Append all the <code class="docutils literal notranslate"><span class="pre">&lt;input&gt;</span></code> arguments to the <code class="docutils literal notranslate"><span class="pre">&lt;path-var&gt;</span></code> using <code class="docutils literal notranslate"><span class="pre">/</span></code> as
the <code class="docutils literal notranslate"><span class="pre">directory-separator</span></code>.  Depending on the <code class="docutils literal notranslate"><span class="pre">&lt;input&gt;</span></code>, the previous
contents of <code class="docutils literal notranslate"><span class="pre">&lt;path-var&gt;</span></code> may be discarded.  For each <code class="docutils literal notranslate"><span class="pre">&lt;input&gt;</span></code> argument,
the following algorithm (pseudo-code) applies:</p>
<div class="highlight-none notranslate"><div class="highlight"><pre><span></span># &lt;path&gt; is the contents of &lt;path-var&gt;

if(&lt;input&gt;.is_absolute() OR
   (&lt;input&gt;.has_root_name() AND
    NOT &lt;input&gt;.root_name() STREQUAL &lt;path&gt;.root_name()))
  replace &lt;path&gt; with &lt;input&gt;
  return()
endif()

if(&lt;input&gt;.has_root_directory())
  remove any root-directory and the entire relative path from &lt;path&gt;
elseif(&lt;path&gt;.has_filename() OR
       (NOT &lt;path-var&gt;.has_root_directory() OR &lt;path&gt;.is_absolute()))
  append directory-separator to &lt;path&gt;
endif()

append &lt;input&gt; omitting any root-name to &lt;path&gt;
</pre></div>
</div>
<div class="highlight-cmake notranslate" id="append-string"><div class="highlight"><pre><span></span><span class="nf">cmake_path(</span><span class="no">APPEND_STRING</span><span class="w"> </span><span class="nv">&lt;path-var&gt;</span><span class="w"> </span><span class="p">[</span><span class="nv">&lt;input&gt;...</span><span class="p">]</span><span class="w"> </span><span class="p">[</span><span class="no">OUTPUT_VARIABLE</span><span class="w"> </span><span class="nv">&lt;out-var&gt;</span><span class="p">]</span><span class="nf">)</span>
</pre></div>
</div>
<p>Append all the <code class="docutils literal notranslate"><span class="pre">&lt;input&gt;</span></code> arguments to the <code class="docutils literal notranslate"><span class="pre">&lt;path-var&gt;</span></code> without adding any
<code class="docutils literal notranslate"><span class="pre">directory-separator</span></code>.</p>
<div class="highlight-cmake notranslate" id="remove-filename"><div class="highlight"><pre><span></span><span class="nf">cmake_path(</span><span class="no">REMOVE_FILENAME</span><span class="w"> </span><span class="nv">&lt;path-var&gt;</span><span class="w"> </span><span class="p">[</span><span class="no">OUTPUT_VARIABLE</span><span class="w"> </span><span class="nv">&lt;out-var&gt;</span><span class="p">]</span><span class="nf">)</span>
</pre></div>
</div>
<p>Removes the <a class="reference internal" href="#filename-def"><span class="std std-ref">filename</span></a> component (as returned by
<a class="reference internal" href="#get-filename"><span class="std std-ref">GET ... FILENAME</span></a>) from <code class="docutils literal notranslate"><span class="pre">&lt;path-var&gt;</span></code>.  After removal,
any trailing <code class="docutils literal notranslate"><span class="pre">directory-separator</span></code> is left alone, if present.</p>
<p>If <code class="docutils literal notranslate"><span class="pre">OUTPUT_VARIABLE</span></code> is not given, then after this function returns,
<a class="reference internal" href="#has-filename">HAS_FILENAME</a> returns false for <code class="docutils literal notranslate"><span class="pre">&lt;path-var&gt;</span></code>.</p>
<p>For example:</p>
<div class="highlight-cmake notranslate"><div class="highlight"><pre><span></span><span class="nf">set(</span><span class="nb">path</span><span class="w"> </span><span class="s">&quot;/a/b&quot;</span><span class="nf">)</span>
<span class="nf">cmake_path(</span><span class="no">REMOVE_FILENAME</span><span class="w"> </span><span class="nb">path</span><span class="nf">)</span>
<span class="nf">message(</span><span class="s">&quot;First path is \&quot;${path}\&quot;&quot;</span><span class="nf">)</span>

<span class="c"># filename is now already empty, the following removes nothing</span>
<span class="nf">cmake_path(</span><span class="no">REMOVE_FILENAME</span><span class="w"> </span><span class="nb">path</span><span class="nf">)</span>
<span class="nf">message(</span><span class="s">&quot;Second path is \&quot;${result}\&quot;&quot;</span><span class="nf">)</span>
</pre></div>
</div>
<p>Output:</p>
<div class="highlight-none notranslate"><div class="highlight"><pre><span></span>First path is &quot;/a/&quot;
Second path is &quot;/a/&quot;
</pre></div>
</div>
<div class="highlight-cmake notranslate" id="replace-filename"><div class="highlight"><pre><span></span><span class="nf">cmake_path(</span><span class="no">REPLACE_FILENAME</span><span class="w"> </span><span class="nv">&lt;path-var&gt;</span><span class="w"> </span><span class="nv">&lt;input&gt;</span><span class="w"> </span><span class="p">[</span><span class="no">OUTPUT_VARIABLE</span><span class="w"> </span><span class="nv">&lt;out-var&gt;</span><span class="p">]</span><span class="nf">)</span>
</pre></div>
</div>
<p>Replaces the <a class="reference internal" href="#filename-def"><span class="std std-ref">filename</span></a> component from <code class="docutils literal notranslate"><span class="pre">&lt;path-var&gt;</span></code>
with <code class="docutils literal notranslate"><span class="pre">&lt;input&gt;</span></code>.  If <code class="docutils literal notranslate"><span class="pre">&lt;path-var&gt;</span></code> has no filename component (i.e.
<a class="reference internal" href="#has-filename">HAS_FILENAME</a> returns false), the path is unchanged.  The operation is
equivalent to the following:</p>
<div class="highlight-cmake notranslate"><div class="highlight"><pre><span></span><span class="nf">cmake_path(</span><span class="no">HAS_FILENAME</span><span class="w"> </span><span class="nb">path</span><span class="w"> </span><span class="nb">has_filename</span><span class="nf">)</span>
<span class="nf">if(</span><span class="nb">has_filename</span><span class="nf">)</span>
<span class="w">  </span><span class="nf">cmake_path(</span><span class="no">REMOVE_FILENAME</span><span class="w"> </span><span class="nb">path</span><span class="nf">)</span>
<span class="w">  </span><span class="nf">cmake_path(</span><span class="no">APPEND</span><span class="w"> </span><span class="nb">path</span><span class="w"> </span><span class="nb">input</span><span class="nf">)</span><span class="p">;</span>
<span class="nf">endif()</span>
</pre></div>
</div>
<div class="highlight-cmake notranslate" id="remove-extension"><div class="highlight"><pre><span></span><span class="nf">cmake_path(</span><span class="no">REMOVE_EXTENSION</span><span class="w"> </span><span class="nv">&lt;path-var&gt;</span><span class="w"> </span><span class="p">[</span><span class="no">LAST_ONLY</span><span class="p">]</span>
<span class="w">                                       </span><span class="p">[</span><span class="no">OUTPUT_VARIABLE</span><span class="w"> </span><span class="nv">&lt;out-var&gt;</span><span class="p">]</span><span class="nf">)</span>
</pre></div>
</div>
<p>Removes the <a class="reference internal" href="#extension-def"><span class="std std-ref">extension</span></a>, if any, from <code class="docutils literal notranslate"><span class="pre">&lt;path-var&gt;</span></code>.</p>
<div class="highlight-cmake notranslate" id="replace-extension"><div class="highlight"><pre><span></span><span class="nf">cmake_path(</span><span class="no">REPLACE_EXTENSION</span><span class="w"> </span><span class="nv">&lt;path-var&gt;</span><span class="w"> </span><span class="p">[</span><span class="no">LAST_ONLY</span><span class="p">]</span><span class="w"> </span><span class="nv">&lt;input&gt;</span>
<span class="w">                             </span><span class="p">[</span><span class="no">OUTPUT_VARIABLE</span><span class="w"> </span><span class="nv">&lt;out-var&gt;</span><span class="p">]</span><span class="nf">)</span>
</pre></div>
</div>
<p>Replaces the <a class="reference internal" href="#extension-def"><span class="std std-ref">extension</span></a> with <code class="docutils literal notranslate"><span class="pre">&lt;input&gt;</span></code>.  Its effect
is equivalent to the following:</p>
<div class="highlight-cmake notranslate"><div class="highlight"><pre><span></span><span class="nf">cmake_path(</span><span class="no">REMOVE_EXTENSION</span><span class="w"> </span><span class="nb">path</span><span class="nf">)</span>
<span class="nf">if(</span><span class="no">NOT</span><span class="w"> </span><span class="s">&quot;input&quot;</span><span class="w"> </span><span class="no">MATCHES</span><span class="w"> </span><span class="s">&quot;^\\.&quot;</span><span class="nf">)</span>
<span class="w">  </span><span class="nf">cmake_path(</span><span class="no">APPEND_STRING</span><span class="w"> </span><span class="nb">path</span><span class="w"> </span><span class="s">&quot;.&quot;</span><span class="nf">)</span>
<span class="nf">endif()</span>
<span class="nf">cmake_path(</span><span class="no">APPEND_STRING</span><span class="w"> </span><span class="nb">path</span><span class="w"> </span><span class="s">&quot;input&quot;</span><span class="nf">)</span>
</pre></div>
</div>
</section>
<section id="generation">
<span id="path-generation"></span><h2>Generation<a class="headerlink" href="#generation" title="Permalink to this heading">¶</a></h2>
<div class="highlight-cmake notranslate" id="normal-path"><div class="highlight"><pre><span></span><span class="nf">cmake_path(</span><span class="no">NORMAL_PATH</span><span class="w"> </span><span class="nv">&lt;path-var&gt;</span><span class="w"> </span><span class="p">[</span><span class="no">OUTPUT_VARIABLE</span><span class="w"> </span><span class="nv">&lt;out-var&gt;</span><span class="p">]</span><span class="nf">)</span>
</pre></div>
</div>
<p>Normalize <code class="docutils literal notranslate"><span class="pre">&lt;path-var&gt;</span></code> according the steps described in <a class="reference internal" href="#normalization"><span class="std std-ref">Normalization</span></a>.</p>
<div class="highlight-cmake notranslate" id="relative-path"><span id="cmake-path-relative-path"></span><div class="highlight"><pre><span></span><span class="nf">cmake_path(</span><span class="no">RELATIVE_PATH</span><span class="w"> </span><span class="nv">&lt;path-var&gt;</span><span class="w"> </span><span class="p">[</span><span class="no">BASE_DIRECTORY</span><span class="w"> </span><span class="nv">&lt;input&gt;</span><span class="p">]</span>
<span class="w">                                    </span><span class="p">[</span><span class="no">OUTPUT_VARIABLE</span><span class="w"> </span><span class="nv">&lt;out-var&gt;</span><span class="p">]</span><span class="nf">)</span>
</pre></div>
</div>
<p>Modifies <code class="docutils literal notranslate"><span class="pre">&lt;path-var&gt;</span></code> to make it relative to the <code class="docutils literal notranslate"><span class="pre">BASE_DIRECTORY</span></code> argument.
If <code class="docutils literal notranslate"><span class="pre">BASE_DIRECTORY</span></code> is not specified, the default base directory will be
<span class="target" id="index-0-variable:CMAKE_CURRENT_SOURCE_DIR"></span><a class="reference internal" href="../variable/CMAKE_CURRENT_SOURCE_DIR.html#variable:CMAKE_CURRENT_SOURCE_DIR" title="CMAKE_CURRENT_SOURCE_DIR"><code class="xref cmake cmake-variable docutils literal notranslate"><span class="pre">CMAKE_CURRENT_SOURCE_DIR</span></code></a>.</p>
<p>For reference, the algorithm used to compute the relative path is the same
as that used by C++
<a class="reference external" href="https://en.cppreference.com/w/cpp/filesystem/path/lexically_normal">std::filesystem::path::lexically_relative</a>.</p>
<div class="highlight-cmake notranslate" id="absolute-path"><div class="highlight"><pre><span></span><span class="nf">cmake_path(</span><span class="no">ABSOLUTE_PATH</span><span class="w"> </span><span class="nv">&lt;path-var&gt;</span><span class="w"> </span><span class="p">[</span><span class="no">BASE_DIRECTORY</span><span class="w"> </span><span class="nv">&lt;input&gt;</span><span class="p">]</span><span class="w"> </span><span class="p">[</span><span class="no">NORMALIZE</span><span class="p">]</span>
<span class="w">                                    </span><span class="p">[</span><span class="no">OUTPUT_VARIABLE</span><span class="w"> </span><span class="nv">&lt;out-var&gt;</span><span class="p">]</span><span class="nf">)</span>
</pre></div>
</div>
<p>If <code class="docutils literal notranslate"><span class="pre">&lt;path-var&gt;</span></code> is a relative path (<a class="reference internal" href="#is-relative">IS_RELATIVE</a> is true), it is evaluated
relative to the given base directory specified by <code class="docutils literal notranslate"><span class="pre">BASE_DIRECTORY</span></code> option.
If <code class="docutils literal notranslate"><span class="pre">BASE_DIRECTORY</span></code> is not specified, the default base directory will be
<span class="target" id="index-1-variable:CMAKE_CURRENT_SOURCE_DIR"></span><a class="reference internal" href="../variable/CMAKE_CURRENT_SOURCE_DIR.html#variable:CMAKE_CURRENT_SOURCE_DIR" title="CMAKE_CURRENT_SOURCE_DIR"><code class="xref cmake cmake-variable docutils literal notranslate"><span class="pre">CMAKE_CURRENT_SOURCE_DIR</span></code></a>.</p>
<p>When the <code class="docutils literal notranslate"><span class="pre">NORMALIZE</span></code> option is specified, the path is <a class="reference internal" href="#normalization"><span class="std std-ref">normalized</span></a> after the path computation.</p>
<p>Because <code class="docutils literal notranslate"><span class="pre">cmake_path()</span></code> does not access the filesystem, symbolic links are
not resolved and any leading tilde is not expanded.  To compute a real path
with symbolic links resolved and leading tildes expanded, use the
<span class="target" id="index-1-command:file"></span><a class="reference internal" href="file.html#real-path" title="file(real_path)"><code class="xref cmake cmake-command docutils literal notranslate"><span class="pre">file(REAL_PATH)</span></code></a> command instead.</p>
</section>
<section id="native-conversion">
<h2>Native Conversion<a class="headerlink" href="#native-conversion" title="Permalink to this heading">¶</a></h2>
<p>For commands in this section, <em>native</em> refers to the host platform, not the
target platform when cross-compiling.</p>
<div class="highlight-cmake notranslate" id="native-path"><span id="cmake-path-native-path"></span><div class="highlight"><pre><span></span><span class="nf">cmake_path(</span><span class="no">NATIVE_PATH</span><span class="w"> </span><span class="nv">&lt;path-var&gt;</span><span class="w"> </span><span class="p">[</span><span class="no">NORMALIZE</span><span class="p">]</span><span class="w"> </span><span class="nv">&lt;out-var&gt;</span><span class="nf">)</span>
</pre></div>
</div>
<p>Converts a cmake-style <code class="docutils literal notranslate"><span class="pre">&lt;path-var&gt;</span></code> into a native path with
platform-specific slashes (<code class="docutils literal notranslate"><span class="pre">\</span></code> on Windows hosts and <code class="docutils literal notranslate"><span class="pre">/</span></code> elsewhere).</p>
<p>When the <code class="docutils literal notranslate"><span class="pre">NORMALIZE</span></code> option is specified, the path is <a class="reference internal" href="#normalization"><span class="std std-ref">normalized</span></a> before the conversion.</p>
<div class="highlight-cmake notranslate" id="to-cmake-path-list"><span id="cmake-path-to-cmake-path-list"></span><span id="convert"></span><div class="highlight"><pre><span></span><span class="nf">cmake_path(</span><span class="no">CONVERT</span><span class="w"> </span><span class="nv">&lt;input&gt;</span><span class="w"> </span><span class="no">TO_CMAKE_PATH_LIST</span><span class="w"> </span><span class="nv">&lt;out-var&gt;</span><span class="w"> </span><span class="p">[</span><span class="no">NORMALIZE</span><span class="p">]</span><span class="nf">)</span>
</pre></div>
</div>
<p>Converts a native <code class="docutils literal notranslate"><span class="pre">&lt;input&gt;</span></code> path into a cmake-style path with forward
slashes (<code class="docutils literal notranslate"><span class="pre">/</span></code>).  On Windows hosts, the long filename marker is taken into
account.  The input can be a single path or a system search path like
<code class="docutils literal notranslate"><span class="pre">$ENV{PATH}</span></code>.  A search path will be converted to a cmake-style list
separated by <code class="docutils literal notranslate"><span class="pre">;</span></code> characters (on non-Windows platforms, this essentially
means <code class="docutils literal notranslate"><span class="pre">:</span></code> separators are replaced with <code class="docutils literal notranslate"><span class="pre">;</span></code>).  The result of the
conversion is stored in the <code class="docutils literal notranslate"><span class="pre">&lt;out-var&gt;</span></code> variable.</p>
<p>When the <code class="docutils literal notranslate"><span class="pre">NORMALIZE</span></code> option is specified, the path is <a class="reference internal" href="#normalization"><span class="std std-ref">normalized</span></a> before the conversion.</p>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>Unlike most other <code class="docutils literal notranslate"><span class="pre">cmake_path()</span></code> subcommands, the <code class="docutils literal notranslate"><span class="pre">CONVERT</span></code> subcommand
takes a literal string as input, not the name of a variable.</p>
</div>
<div class="highlight-cmake notranslate" id="to-native-path-list"><span id="cmake-path-to-native-path-list"></span><div class="highlight"><pre><span></span><span class="nf">cmake_path(</span><span class="no">CONVERT</span><span class="w"> </span><span class="nv">&lt;input&gt;</span><span class="w"> </span><span class="no">TO_NATIVE_PATH_LIST</span><span class="w"> </span><span class="nv">&lt;out-var&gt;</span><span class="w"> </span><span class="p">[</span><span class="no">NORMALIZE</span><span class="p">]</span><span class="nf">)</span>
</pre></div>
</div>
<p>Converts a cmake-style <code class="docutils literal notranslate"><span class="pre">&lt;input&gt;</span></code> path into a native path with
platform-specific slashes (<code class="docutils literal notranslate"><span class="pre">\</span></code> on Windows hosts and <code class="docutils literal notranslate"><span class="pre">/</span></code> elsewhere).
The input can be a single path or a cmake-style list.  A list will be
converted into a native search path (<code class="docutils literal notranslate"><span class="pre">;</span></code>-separated on Windows,
<code class="docutils literal notranslate"><span class="pre">:</span></code>-separated on other platforms).  The result of the conversion is
stored in the <code class="docutils literal notranslate"><span class="pre">&lt;out-var&gt;</span></code> variable.</p>
<p>When the <code class="docutils literal notranslate"><span class="pre">NORMALIZE</span></code> option is specified, the path is <a class="reference internal" href="#normalization"><span class="std std-ref">normalized</span></a> before the conversion.</p>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>Unlike most other <code class="docutils literal notranslate"><span class="pre">cmake_path()</span></code> subcommands, the <code class="docutils literal notranslate"><span class="pre">CONVERT</span></code> subcommand
takes a literal string as input, not the name of a variable.</p>
</div>
<p>For example:</p>
<div class="highlight-cmake notranslate"><div class="highlight"><pre><span></span><span class="nf">set(</span><span class="nb">paths</span><span class="w"> </span><span class="s">&quot;/a/b/c&quot;</span><span class="w"> </span><span class="s">&quot;/x/y/z&quot;</span><span class="nf">)</span>
<span class="nf">cmake_path(</span><span class="no">CONVERT</span><span class="w"> </span><span class="s">&quot;${paths}&quot;</span><span class="w"> </span><span class="no">TO_NATIVE_PATH_LIST</span><span class="w"> </span><span class="nb">native_paths</span><span class="nf">)</span>
<span class="nf">message(</span><span class="s">&quot;Native path list is \&quot;${native_paths}\&quot;&quot;</span><span class="nf">)</span>
</pre></div>
</div>
<p>Output on Windows:</p>
<div class="highlight-none notranslate"><div class="highlight"><pre><span></span>Native path list is &quot;\a\b\c;\x\y\z&quot;
</pre></div>
</div>
<p>Output on all other platforms:</p>
<div class="highlight-none notranslate"><div class="highlight"><pre><span></span>Native path list is &quot;/a/b/c:/x/y/z&quot;
</pre></div>
</div>
</section>
<section id="hashing">
<h2>Hashing<a class="headerlink" href="#hashing" title="Permalink to this heading">¶</a></h2>
<div class="highlight-cmake notranslate" id="hash"><div class="highlight"><pre><span></span><span class="nf">cmake_path(</span><span class="no">HASH</span><span class="w"> </span><span class="nv">&lt;path-var&gt;</span><span class="w"> </span><span class="nv">&lt;out-var&gt;</span><span class="nf">)</span>
</pre></div>
</div>
<p>Compute a hash value of <code class="docutils literal notranslate"><span class="pre">&lt;path-var&gt;</span></code> such that for two paths <code class="docutils literal notranslate"><span class="pre">p1</span></code> and
<code class="docutils literal notranslate"><span class="pre">p2</span></code> that compare equal (<a class="reference internal" href="string.html#compare"><span class="std std-ref">COMPARE ... EQUAL</span></a>), the hash
value of <code class="docutils literal notranslate"><span class="pre">p1</span></code> is equal to the hash value of <code class="docutils literal notranslate"><span class="pre">p2</span></code>.  The path is always
<a class="reference internal" href="#normalization"><span class="std std-ref">normalized</span></a> before the hash is computed.</p>
</section>
</section>


            <div class="clearer"></div>
          </div>
        </div>
      </div>
      <div class="sphinxsidebar" role="navigation" aria-label="main navigation">
        <div class="sphinxsidebarwrapper">
  <div>
    <h3><a href="../index.html">Table of Contents</a></h3>
    <ul>
<li><a class="reference internal" href="#">cmake_path</a><ul>
<li><a class="reference internal" href="#synopsis">Synopsis</a></li>
<li><a class="reference internal" href="#conventions">Conventions</a></li>
<li><a class="reference internal" href="#path-structure-and-terminology">Path Structure And Terminology</a></li>
<li><a class="reference internal" href="#creating-a-path-variable">Creating A Path Variable</a></li>
<li><a class="reference internal" href="#normalization">Normalization</a></li>
<li><a class="reference internal" href="#decomposition">Decomposition</a><ul>
<li><a class="reference internal" href="#root-examples">Root examples</a></li>
<li><a class="reference internal" href="#filename-examples">Filename examples</a></li>
<li><a class="reference internal" href="#extension-and-stem-examples">Extension and stem examples</a></li>
<li><a class="reference internal" href="#relative-part-examples">Relative part examples</a></li>
<li><a class="reference internal" href="#path-traversal-examples">Path traversal examples</a></li>
</ul>
</li>
<li><a class="reference internal" href="#query">Query</a></li>
<li><a class="reference internal" href="#modification">Modification</a></li>
<li><a class="reference internal" href="#generation">Generation</a></li>
<li><a class="reference internal" href="#native-conversion">Native Conversion</a></li>
<li><a class="reference internal" href="#hashing">Hashing</a></li>
</ul>
</li>
</ul>

  </div>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="cmake_parse_arguments.html"
                          title="previous chapter">cmake_parse_arguments</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="cmake_policy.html"
                          title="next chapter">cmake_policy</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../_sources/command/cmake_path.rst.txt"
            rel="nofollow">Show Source</a></li>
    </ul>
   </div>
<div id="searchbox" style="display: none" role="search">
  <h3 id="searchlabel">Quick search</h3>
    <div class="searchformwrapper">
    <form class="search" action="../search.html" method="get">
      <input type="text" name="q" aria-labelledby="searchlabel" autocomplete="off" autocorrect="off" autocapitalize="off" spellcheck="false"/>
      <input type="submit" value="Go" />
    </form>
    </div>
</div>
<script>document.getElementById('searchbox').style.display = "block"</script>
        </div>
      </div>
      <div class="clearer"></div>
    </div>
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             >index</a></li>
        <li class="right" >
          <a href="cmake_policy.html" title="cmake_policy"
             >next</a> |</li>
        <li class="right" >
          <a href="cmake_parse_arguments.html" title="cmake_parse_arguments"
             >previous</a> |</li>
  <li>
    <img src="../_static/cmake-logo-16.png" alt=""
         style="vertical-align: middle; margin-top: -2px" />
  </li>
  <li>
    <a href="https://cmake.org/">CMake</a> &#187;
  </li>
  <li>
    <a href="../index.html">3.28.1 Documentation</a> &#187;
  </li>

          <li class="nav-item nav-item-1"><a href="../manual/cmake-commands.7.html" >cmake-commands(7)</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href="">cmake_path</a></li> 
      </ul>
    </div>

    <div class="footer" role="contentinfo">
        &#169; Copyright 2000-2023 Kitware, Inc. and Contributors.
      Created using <a href="https://www.sphinx-doc.org/">Sphinx</a> 6.2.1.
    </div>
  </body>
</html>