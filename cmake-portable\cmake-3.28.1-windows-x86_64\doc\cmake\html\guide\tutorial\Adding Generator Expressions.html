
<!DOCTYPE html>

<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="generator" content="Docutils 0.19: https://docutils.sourceforge.io/" />

  <title>Step 4: Adding Generator Expressions &mdash; CMake 3.28.1 Documentation</title>

    <link rel="stylesheet" type="text/css" href="../../_static/pygments.css" />
    <link rel="stylesheet" type="text/css" href="../../_static/cmake.css" />
    
    <script data-url_root="../../" id="documentation_options" src="../../_static/documentation_options.js"></script>
    <script src="../../_static/doctools.js"></script>
    <script src="../../_static/sphinx_highlight.js"></script>
    
    <link rel="icon" href="../../_static/cmake-favicon.ico"/>
    <link rel="index" title="Index" href="../../genindex.html" />
    <link rel="search" title="Search" href="../../search.html" />
    <link rel="next" title="Step 5: Installing and Testing" href="Installing%20and%20Testing.html" />
    <link rel="prev" title="Step 3: Adding Usage Requirements for a Library" href="Adding%20Usage%20Requirements%20for%20a%20Library.html" />
 

  </head><body>

    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../../genindex.html" title="General Index"
             accesskey="I">index</a></li>
        <li class="right" >
          <a href="Installing%20and%20Testing.html" title="Step 5: Installing and Testing"
             accesskey="N">next</a> |</li>
        <li class="right" >
          <a href="Adding%20Usage%20Requirements%20for%20a%20Library.html" title="Step 3: Adding Usage Requirements for a Library"
             accesskey="P">previous</a> |</li>
  <li>
    <img src="../../_static/cmake-logo-16.png" alt=""
         style="vertical-align: middle; margin-top: -2px" />
  </li>
  <li>
    <a href="https://cmake.org/">CMake</a> &#187;
  </li>
  <li>
    <a href="../../index.html">3.28.1 Documentation</a> &#187;
  </li>

          <li class="nav-item nav-item-1"><a href="index.html" accesskey="U">CMake Tutorial</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href="">Step 4: Adding Generator Expressions</a></li> 
      </ul>
    </div>  

    <div class="document">
      <div class="documentwrapper">
        <div class="bodywrapper">
          <div class="body" role="main">
            
  <section id="step-4-adding-generator-expressions">
<span id="guide:tutorial/Adding Generator Expressions"></span><h1>Step 4: Adding Generator Expressions<a class="headerlink" href="#step-4-adding-generator-expressions" title="Permalink to this heading">¶</a></h1>
<p><span class="target" id="index-0-manual:cmake-generator-expressions(7)"></span><a class="reference internal" href="../../manual/cmake-generator-expressions.7.html#manual:cmake-generator-expressions(7)" title="cmake-generator-expressions(7)"><code class="xref cmake cmake-manual docutils literal notranslate"><span class="pre">Generator</span> <span class="pre">expressions</span></code></a> are evaluated
during build system generation to produce information specific to each build
configuration.</p>
<p><span class="target" id="index-1-manual:cmake-generator-expressions(7)"></span><a class="reference internal" href="../../manual/cmake-generator-expressions.7.html#manual:cmake-generator-expressions(7)" title="cmake-generator-expressions(7)"><code class="xref cmake cmake-manual docutils literal notranslate"><span class="pre">Generator</span> <span class="pre">expressions</span></code></a> are allowed in
the context of many target properties, such as <span class="target" id="index-0-prop_tgt:LINK_LIBRARIES"></span><a class="reference internal" href="../../prop_tgt/LINK_LIBRARIES.html#prop_tgt:LINK_LIBRARIES" title="LINK_LIBRARIES"><code class="xref cmake cmake-prop_tgt docutils literal notranslate"><span class="pre">LINK_LIBRARIES</span></code></a>,
<span class="target" id="index-0-prop_tgt:INCLUDE_DIRECTORIES"></span><a class="reference internal" href="../../prop_tgt/INCLUDE_DIRECTORIES.html#prop_tgt:INCLUDE_DIRECTORIES" title="INCLUDE_DIRECTORIES"><code class="xref cmake cmake-prop_tgt docutils literal notranslate"><span class="pre">INCLUDE_DIRECTORIES</span></code></a>, <span class="target" id="index-0-prop_tgt:COMPILE_DEFINITIONS"></span><a class="reference internal" href="../../prop_tgt/COMPILE_DEFINITIONS.html#prop_tgt:COMPILE_DEFINITIONS" title="COMPILE_DEFINITIONS"><code class="xref cmake cmake-prop_tgt docutils literal notranslate"><span class="pre">COMPILE_DEFINITIONS</span></code></a> and others.
They may also be used when using commands to populate those properties, such as
<span class="target" id="index-0-command:target_link_libraries"></span><a class="reference internal" href="../../command/target_link_libraries.html#command:target_link_libraries" title="target_link_libraries"><code class="xref cmake cmake-command docutils literal notranslate"><span class="pre">target_link_libraries()</span></code></a>, <span class="target" id="index-0-command:target_include_directories"></span><a class="reference internal" href="../../command/target_include_directories.html#command:target_include_directories" title="target_include_directories"><code class="xref cmake cmake-command docutils literal notranslate"><span class="pre">target_include_directories()</span></code></a>,
<span class="target" id="index-0-command:target_compile_definitions"></span><a class="reference internal" href="../../command/target_compile_definitions.html#command:target_compile_definitions" title="target_compile_definitions"><code class="xref cmake cmake-command docutils literal notranslate"><span class="pre">target_compile_definitions()</span></code></a> and others.</p>
<p><span class="target" id="index-2-manual:cmake-generator-expressions(7)"></span><a class="reference internal" href="../../manual/cmake-generator-expressions.7.html#manual:cmake-generator-expressions(7)" title="cmake-generator-expressions(7)"><code class="xref cmake cmake-manual docutils literal notranslate"><span class="pre">Generator</span> <span class="pre">expressions</span></code></a>  may be used
to enable conditional linking, conditional definitions used when compiling,
conditional include directories and more. The conditions may be based on the
build configuration, target properties, platform information or any other
queryable information.</p>
<p>There are different types of
<span class="target" id="index-3-manual:cmake-generator-expressions(7)"></span><a class="reference internal" href="../../manual/cmake-generator-expressions.7.html#manual:cmake-generator-expressions(7)" title="cmake-generator-expressions(7)"><code class="xref cmake cmake-manual docutils literal notranslate"><span class="pre">generator</span> <span class="pre">expressions</span></code></a> including
Logical, Informational, and Output expressions.</p>
<p>Logical expressions are used to create conditional output. The basic
expressions are the <code class="docutils literal notranslate"><span class="pre">0</span></code> and <code class="docutils literal notranslate"><span class="pre">1</span></code> expressions. A <code class="docutils literal notranslate"><span class="pre">$&lt;0:...&gt;</span></code> results in the
empty string, and <code class="docutils literal notranslate"><span class="pre">&lt;1:...&gt;</span></code> results in the content of <code class="docutils literal notranslate"><span class="pre">...</span></code>.  They can also
be nested.</p>
<section id="exercise-1-adding-compiler-warning-flags-with-generator-expressions">
<h2>Exercise 1 - Adding Compiler Warning Flags with Generator Expressions<a class="headerlink" href="#exercise-1-adding-compiler-warning-flags-with-generator-expressions" title="Permalink to this heading">¶</a></h2>
<p>A common usage of
<span class="target" id="index-4-manual:cmake-generator-expressions(7)"></span><a class="reference internal" href="../../manual/cmake-generator-expressions.7.html#manual:cmake-generator-expressions(7)" title="cmake-generator-expressions(7)"><code class="xref cmake cmake-manual docutils literal notranslate"><span class="pre">generator</span> <span class="pre">expressions</span></code></a> is to
conditionally add compiler flags, such as those for language levels or
warnings. A nice pattern is to associate this information to an <code class="docutils literal notranslate"><span class="pre">INTERFACE</span></code>
target allowing this information to propagate.</p>
<section id="goal">
<h3>Goal<a class="headerlink" href="#goal" title="Permalink to this heading">¶</a></h3>
<p>Add compiler warning flags when building but not for installed versions.</p>
</section>
<section id="helpful-resources">
<h3>Helpful Resources<a class="headerlink" href="#helpful-resources" title="Permalink to this heading">¶</a></h3>
<ul class="simple">
<li><p><span class="target" id="index-5-manual:cmake-generator-expressions(7)"></span><a class="reference internal" href="../../manual/cmake-generator-expressions.7.html#manual:cmake-generator-expressions(7)" title="cmake-generator-expressions(7)"><code class="xref cmake cmake-manual docutils literal notranslate"><span class="pre">cmake-generator-expressions(7)</span></code></a></p></li>
<li><p><span class="target" id="index-0-command:cmake_minimum_required"></span><a class="reference internal" href="../../command/cmake_minimum_required.html#command:cmake_minimum_required" title="cmake_minimum_required"><code class="xref cmake cmake-command docutils literal notranslate"><span class="pre">cmake_minimum_required()</span></code></a></p></li>
<li><p><span class="target" id="index-0-command:set"></span><a class="reference internal" href="../../command/set.html#command:set" title="set"><code class="xref cmake cmake-command docutils literal notranslate"><span class="pre">set()</span></code></a></p></li>
<li><p><span class="target" id="index-0-command:target_compile_options"></span><a class="reference internal" href="../../command/target_compile_options.html#command:target_compile_options" title="target_compile_options"><code class="xref cmake cmake-command docutils literal notranslate"><span class="pre">target_compile_options()</span></code></a></p></li>
</ul>
</section>
<section id="files-to-edit">
<h3>Files to Edit<a class="headerlink" href="#files-to-edit" title="Permalink to this heading">¶</a></h3>
<ul class="simple">
<li><p><code class="docutils literal notranslate"><span class="pre">CMakeLists.txt</span></code></p></li>
</ul>
</section>
<section id="getting-started">
<h3>Getting Started<a class="headerlink" href="#getting-started" title="Permalink to this heading">¶</a></h3>
<p>Open the file <code class="docutils literal notranslate"><span class="pre">Step4/CMakeLists.txt</span></code> and complete <code class="docutils literal notranslate"><span class="pre">TODO</span> <span class="pre">1</span></code> through
<code class="docutils literal notranslate"><span class="pre">TODO</span> <span class="pre">4</span></code>.</p>
<p>First, in the top level <code class="docutils literal notranslate"><span class="pre">CMakeLists.txt</span></code> file, we need to set the
<span class="target" id="index-1-command:cmake_minimum_required"></span><a class="reference internal" href="../../command/cmake_minimum_required.html#command:cmake_minimum_required" title="cmake_minimum_required"><code class="xref cmake cmake-command docutils literal notranslate"><span class="pre">cmake_minimum_required()</span></code></a> to <code class="docutils literal notranslate"><span class="pre">3.15</span></code>. In this exercise we are going
to use a generator expression which was introduced in CMake 3.15.</p>
<p>Next we add the desired compiler warning flags that we want for our project.
As warning flags vary based on the compiler, we use the
<code class="docutils literal notranslate"><span class="pre">COMPILE_LANG_AND_ID</span></code> generator expression to control which flags to apply
given a language and a set of compiler ids.</p>
</section>
<section id="build-and-run">
<h3>Build and Run<a class="headerlink" href="#build-and-run" title="Permalink to this heading">¶</a></h3>
<p>Make a new directory called <code class="docutils literal notranslate"><span class="pre">Step4_build</span></code>, run the <span class="target" id="index-0-manual:cmake(1)"></span><a class="reference internal" href="../../manual/cmake.1.html#manual:cmake(1)" title="cmake(1)"><code class="xref cmake cmake-manual docutils literal notranslate"><span class="pre">cmake</span></code></a>
executable or the <span class="target" id="index-0-manual:cmake-gui(1)"></span><a class="reference internal" href="../../manual/cmake-gui.1.html#manual:cmake-gui(1)" title="cmake-gui(1)"><code class="xref cmake cmake-manual docutils literal notranslate"><span class="pre">cmake-gui</span></code></a> to configure the project
and then build it with your chosen build tool or by using <code class="docutils literal notranslate"><span class="pre">cmake</span> <span class="pre">--build</span> <span class="pre">.</span></code>
from the build directory.</p>
<div class="highlight-console notranslate"><div class="highlight"><pre><span></span><span class="go">mkdir Step4_build</span>
<span class="go">cd Step4_build</span>
<span class="go">cmake ../Step4</span>
<span class="go">cmake --build .</span>
</pre></div>
</div>
</section>
<section id="solution">
<h3>Solution<a class="headerlink" href="#solution" title="Permalink to this heading">¶</a></h3>
<p>Update the <span class="target" id="index-2-command:cmake_minimum_required"></span><a class="reference internal" href="../../command/cmake_minimum_required.html#command:cmake_minimum_required" title="cmake_minimum_required"><code class="xref cmake cmake-command docutils literal notranslate"><span class="pre">cmake_minimum_required()</span></code></a> to require at least CMake
version <code class="docutils literal notranslate"><span class="pre">3.15</span></code>:</p>
<details><summary>TODO 1: Click to show/hide answer</summary><div class="literal-block-wrapper docutils container" id="mathfunctions-cmakelists-txt-minimum-required-step4">
<div class="code-block-caption"><span class="caption-text">TODO 1: CMakeLists.txt</span><a class="headerlink" href="#mathfunctions-cmakelists-txt-minimum-required-step4" title="Permalink to this code">¶</a></div>
<div class="highlight-cmake notranslate"><div class="highlight"><pre><span></span><span class="nf">cmake_minimum_required(</span><span class="no">VERSION</span><span class="w"> </span><span class="m">3.15</span><span class="nf">)</span>
</pre></div>
</div>
</div>
</details><p>Next we determine which compiler our system is currently using to build
since warning flags vary based on the compiler we use. This is done with
the <code class="docutils literal notranslate"><span class="pre">COMPILE_LANG_AND_ID</span></code> generator expression. We set the result in the
variables <code class="docutils literal notranslate"><span class="pre">gcc_like_cxx</span></code> and <code class="docutils literal notranslate"><span class="pre">msvc_cxx</span></code> as follows:</p>
<details><summary>TODO 2: Click to show/hide answer</summary><div class="literal-block-wrapper docutils container" id="cmakelists-txt-compile-lang-and-id">
<div class="code-block-caption"><span class="caption-text">TODO 2: CMakeLists.txt</span><a class="headerlink" href="#cmakelists-txt-compile-lang-and-id" title="Permalink to this code">¶</a></div>
<div class="highlight-cmake notranslate"><div class="highlight"><pre><span></span><span class="nf">set(</span><span class="nb">gcc_like_cxx</span><span class="w"> </span><span class="s">&quot;$&lt;COMPILE_LANG_AND_ID:CXX,ARMClang,AppleClang,Clang,GNU,LCC&gt;&quot;</span><span class="nf">)</span>
<span class="nf">set(</span><span class="nb">msvc_cxx</span><span class="w"> </span><span class="s">&quot;$&lt;COMPILE_LANG_AND_ID:CXX,MSVC&gt;&quot;</span><span class="nf">)</span>
</pre></div>
</div>
</div>
</details><p>Next we add the desired compiler warning flags that we want for our project.
Using our variables <code class="docutils literal notranslate"><span class="pre">gcc_like_cxx</span></code> and <code class="docutils literal notranslate"><span class="pre">msvc_cxx</span></code>, we can use another
generator expression to apply the respective flags only when the variables are
true. We use <span class="target" id="index-1-command:target_compile_options"></span><a class="reference internal" href="../../command/target_compile_options.html#command:target_compile_options" title="target_compile_options"><code class="xref cmake cmake-command docutils literal notranslate"><span class="pre">target_compile_options()</span></code></a> to apply these flags to our
interface library.</p>
<details><summary>TODO 3: Click to show/hide answer</summary><div class="literal-block-wrapper docutils container" id="cmakelists-txt-compile-flags">
<div class="code-block-caption"><span class="caption-text">TODO 3: CMakeLists.txt</span><a class="headerlink" href="#cmakelists-txt-compile-flags" title="Permalink to this code">¶</a></div>
<div class="highlight-cmake notranslate"><div class="highlight"><pre><span></span><span class="nf">target_compile_options(</span><span class="nb">tutorial_compiler_flags</span><span class="w"> </span><span class="no">INTERFACE</span>
<span class="w">  </span><span class="s">&quot;$&lt;${gcc_like_cxx}:-Wall;-Wextra;-Wshadow;-Wformat=2;-Wunused&gt;&quot;</span>
<span class="w">  </span><span class="s">&quot;$&lt;${msvc_cxx}:-W3&gt;&quot;</span>
<span class="nf">)</span>
</pre></div>
</div>
</div>
</details><p>Lastly, we only want these warning flags to be used during builds. Consumers
of our installed project should not inherit our warning flags. To specify
this, we wrap our flags from TODO 3 in a generator expression using the
<code class="docutils literal notranslate"><span class="pre">BUILD_INTERFACE</span></code> condition. The resulting full code looks like the following:</p>
<details><summary>TODO 4: Click to show/hide answer</summary><div class="literal-block-wrapper docutils container" id="cmakelists-txt-target-compile-options-genex">
<div class="code-block-caption"><span class="caption-text">TODO 4: CMakeLists.txt</span><a class="headerlink" href="#cmakelists-txt-target-compile-options-genex" title="Permalink to this code">¶</a></div>
<div class="highlight-cmake notranslate"><div class="highlight"><pre><span></span><span class="nf">target_compile_options(</span><span class="nb">tutorial_compiler_flags</span><span class="w"> </span><span class="no">INTERFACE</span>
<span class="w">  </span><span class="s">&quot;$&lt;${gcc_like_cxx}:$&lt;BUILD_INTERFACE:-Wall;-Wextra;-Wshadow;-Wformat=2;-Wunused&gt;&gt;&quot;</span>
<span class="w">  </span><span class="s">&quot;$&lt;${msvc_cxx}:$&lt;BUILD_INTERFACE:-W3&gt;&gt;&quot;</span>
<span class="nf">)</span>
</pre></div>
</div>
</div>
</details></section>
</section>
</section>


            <div class="clearer"></div>
          </div>
        </div>
      </div>
      <div class="sphinxsidebar" role="navigation" aria-label="main navigation">
        <div class="sphinxsidebarwrapper">
  <div>
    <h3><a href="../../index.html">Table of Contents</a></h3>
    <ul>
<li><a class="reference internal" href="#">Step 4: Adding Generator Expressions</a><ul>
<li><a class="reference internal" href="#exercise-1-adding-compiler-warning-flags-with-generator-expressions">Exercise 1 - Adding Compiler Warning Flags with Generator Expressions</a><ul>
<li><a class="reference internal" href="#goal">Goal</a></li>
<li><a class="reference internal" href="#helpful-resources">Helpful Resources</a></li>
<li><a class="reference internal" href="#files-to-edit">Files to Edit</a></li>
<li><a class="reference internal" href="#getting-started">Getting Started</a></li>
<li><a class="reference internal" href="#build-and-run">Build and Run</a></li>
<li><a class="reference internal" href="#solution">Solution</a></li>
</ul>
</li>
</ul>
</li>
</ul>

  </div>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="Adding%20Usage%20Requirements%20for%20a%20Library.html"
                          title="previous chapter">Step 3: Adding Usage Requirements for a Library</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="Installing%20and%20Testing.html"
                          title="next chapter">Step 5: Installing and Testing</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../../_sources/guide/tutorial/Adding Generator Expressions.rst.txt"
            rel="nofollow">Show Source</a></li>
    </ul>
   </div>
<div id="searchbox" style="display: none" role="search">
  <h3 id="searchlabel">Quick search</h3>
    <div class="searchformwrapper">
    <form class="search" action="../../search.html" method="get">
      <input type="text" name="q" aria-labelledby="searchlabel" autocomplete="off" autocorrect="off" autocapitalize="off" spellcheck="false"/>
      <input type="submit" value="Go" />
    </form>
    </div>
</div>
<script>document.getElementById('searchbox').style.display = "block"</script>
        </div>
      </div>
      <div class="clearer"></div>
    </div>
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../../genindex.html" title="General Index"
             >index</a></li>
        <li class="right" >
          <a href="Installing%20and%20Testing.html" title="Step 5: Installing and Testing"
             >next</a> |</li>
        <li class="right" >
          <a href="Adding%20Usage%20Requirements%20for%20a%20Library.html" title="Step 3: Adding Usage Requirements for a Library"
             >previous</a> |</li>
  <li>
    <img src="../../_static/cmake-logo-16.png" alt=""
         style="vertical-align: middle; margin-top: -2px" />
  </li>
  <li>
    <a href="https://cmake.org/">CMake</a> &#187;
  </li>
  <li>
    <a href="../../index.html">3.28.1 Documentation</a> &#187;
  </li>

          <li class="nav-item nav-item-1"><a href="index.html" >CMake Tutorial</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href="">Step 4: Adding Generator Expressions</a></li> 
      </ul>
    </div>

    <div class="footer" role="contentinfo">
        &#169; Copyright 2000-2023 Kitware, Inc. and Contributors.
      Created using <a href="https://www.sphinx-doc.org/">Sphinx</a> 6.2.1.
    </div>
  </body>
</html>