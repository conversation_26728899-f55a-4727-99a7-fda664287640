
<!DOCTYPE html>

<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="generator" content="Docutils 0.19: https://docutils.sourceforge.io/" />

  <title>exec_program &mdash; CMake 3.28.1 Documentation</title>

    <link rel="stylesheet" type="text/css" href="../_static/pygments.css" />
    <link rel="stylesheet" type="text/css" href="../_static/cmake.css" />
    
    <script data-url_root="../" id="documentation_options" src="../_static/documentation_options.js"></script>
    <script src="../_static/doctools.js"></script>
    <script src="../_static/sphinx_highlight.js"></script>
    
    <link rel="icon" href="../_static/cmake-favicon.ico"/>
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="next" title="export_library_dependencies" href="export_library_dependencies.html" />
    <link rel="prev" title="build_name" href="build_name.html" />
 

  </head><body>

    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             accesskey="I">index</a></li>
        <li class="right" >
          <a href="export_library_dependencies.html" title="export_library_dependencies"
             accesskey="N">next</a> |</li>
        <li class="right" >
          <a href="build_name.html" title="build_name"
             accesskey="P">previous</a> |</li>
  <li>
    <img src="../_static/cmake-logo-16.png" alt=""
         style="vertical-align: middle; margin-top: -2px" />
  </li>
  <li>
    <a href="https://cmake.org/">CMake</a> &#187;
  </li>
  <li>
    <a href="../index.html">3.28.1 Documentation</a> &#187;
  </li>

          <li class="nav-item nav-item-1"><a href="../manual/cmake-commands.7.html" accesskey="U">cmake-commands(7)</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href="">exec_program</a></li> 
      </ul>
    </div>  

    <div class="document">
      <div class="documentwrapper">
        <div class="bodywrapper">
          <div class="body" role="main">
            
  <section id="exec-program">
<span id="command:exec_program"></span><h1>exec_program<a class="headerlink" href="#exec-program" title="Permalink to this heading">¶</a></h1>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.28: </span>This command is available only if policy <span class="target" id="index-0-policy:CMP0153"></span><a class="reference internal" href="../policy/CMP0153.html#policy:CMP0153" title="CMP0153"><code class="xref cmake cmake-policy docutils literal notranslate"><span class="pre">CMP0153</span></code></a> is not set to <code class="docutils literal notranslate"><span class="pre">NEW</span></code>.
Port projects to the <span class="target" id="index-0-command:execute_process"></span><a class="reference internal" href="execute_process.html#command:execute_process" title="execute_process"><code class="xref cmake cmake-command docutils literal notranslate"><span class="pre">execute_process()</span></code></a> command.</p>
</div>
<div class="deprecated">
<p><span class="versionmodified deprecated">Deprecated since version 3.0: </span>Use the <span class="target" id="index-1-command:execute_process"></span><a class="reference internal" href="execute_process.html#command:execute_process" title="execute_process"><code class="xref cmake cmake-command docutils literal notranslate"><span class="pre">execute_process()</span></code></a> command instead.</p>
</div>
<p>Run an executable program during the processing of the CMakeList.txt
file.</p>
<div class="highlight-cmake notranslate"><div class="highlight"><pre><span></span><span class="nf">exec_program(</span><span class="nb">Executable</span><span class="w"> </span><span class="p">[</span><span class="nb">directory</span><span class="w"> </span><span class="nb">in</span><span class="w"> </span><span class="nb">which</span><span class="w"> </span><span class="nb">to</span><span class="w"> </span><span class="nb">run</span><span class="p">]</span>
<span class="w">             </span><span class="p">[</span><span class="no">ARGS</span><span class="w"> </span><span class="nv">&lt;arguments to executable&gt;</span><span class="p">]</span>
<span class="w">             </span><span class="p">[</span><span class="no">OUTPUT_VARIABLE</span><span class="w"> </span><span class="nv">&lt;var&gt;</span><span class="p">]</span>
<span class="w">             </span><span class="p">[</span><span class="no">RETURN_VALUE</span><span class="w"> </span><span class="nv">&lt;var&gt;</span><span class="p">]</span><span class="nf">)</span>
</pre></div>
</div>
<p>The executable is run in the optionally specified directory.  The
executable can include arguments if it is double quoted, but it is
better to use the optional <code class="docutils literal notranslate"><span class="pre">ARGS</span></code> argument to specify arguments to the
program.  This is because cmake will then be able to escape spaces in
the executable path.  An optional argument <code class="docutils literal notranslate"><span class="pre">OUTPUT_VARIABLE</span></code> specifies a
variable in which to store the output.  To capture the return value of
the execution, provide a <code class="docutils literal notranslate"><span class="pre">RETURN_VALUE</span></code>.  If <code class="docutils literal notranslate"><span class="pre">OUTPUT_VARIABLE</span></code> is
specified, then no output will go to the stdout/stderr of the console
running cmake.</p>
</section>


            <div class="clearer"></div>
          </div>
        </div>
      </div>
      <div class="sphinxsidebar" role="navigation" aria-label="main navigation">
        <div class="sphinxsidebarwrapper">
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="build_name.html"
                          title="previous chapter">build_name</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="export_library_dependencies.html"
                          title="next chapter">export_library_dependencies</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../_sources/command/exec_program.rst.txt"
            rel="nofollow">Show Source</a></li>
    </ul>
   </div>
<div id="searchbox" style="display: none" role="search">
  <h3 id="searchlabel">Quick search</h3>
    <div class="searchformwrapper">
    <form class="search" action="../search.html" method="get">
      <input type="text" name="q" aria-labelledby="searchlabel" autocomplete="off" autocorrect="off" autocapitalize="off" spellcheck="false"/>
      <input type="submit" value="Go" />
    </form>
    </div>
</div>
<script>document.getElementById('searchbox').style.display = "block"</script>
        </div>
      </div>
      <div class="clearer"></div>
    </div>
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             >index</a></li>
        <li class="right" >
          <a href="export_library_dependencies.html" title="export_library_dependencies"
             >next</a> |</li>
        <li class="right" >
          <a href="build_name.html" title="build_name"
             >previous</a> |</li>
  <li>
    <img src="../_static/cmake-logo-16.png" alt=""
         style="vertical-align: middle; margin-top: -2px" />
  </li>
  <li>
    <a href="https://cmake.org/">CMake</a> &#187;
  </li>
  <li>
    <a href="../index.html">3.28.1 Documentation</a> &#187;
  </li>

          <li class="nav-item nav-item-1"><a href="../manual/cmake-commands.7.html" >cmake-commands(7)</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href="">exec_program</a></li> 
      </ul>
    </div>

    <div class="footer" role="contentinfo">
        &#169; Copyright 2000-2023 Kitware, Inc. and Contributors.
      Created using <a href="https://www.sphinx-doc.org/">Sphinx</a> 6.2.1.
    </div>
  </body>
</html>