
<!DOCTYPE html>

<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="generator" content="Docutils 0.19: https://docutils.sourceforge.io/" />

  <title>FindVulkan &mdash; CMake 3.28.1 Documentation</title>

    <link rel="stylesheet" type="text/css" href="../_static/pygments.css" />
    <link rel="stylesheet" type="text/css" href="../_static/cmake.css" />
    
    <script data-url_root="../" id="documentation_options" src="../_static/documentation_options.js"></script>
    <script src="../_static/doctools.js"></script>
    <script src="../_static/sphinx_highlight.js"></script>
    
    <link rel="icon" href="../_static/cmake-favicon.ico"/>
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="next" title="FindWget" href="FindWget.html" />
    <link rel="prev" title="FindTIFF" href="FindTIFF.html" />
 

  </head><body>

    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             accesskey="I">index</a></li>
        <li class="right" >
          <a href="FindWget.html" title="FindWget"
             accesskey="N">next</a> |</li>
        <li class="right" >
          <a href="FindTIFF.html" title="FindTIFF"
             accesskey="P">previous</a> |</li>
  <li>
    <img src="../_static/cmake-logo-16.png" alt=""
         style="vertical-align: middle; margin-top: -2px" />
  </li>
  <li>
    <a href="https://cmake.org/">CMake</a> &#187;
  </li>
  <li>
    <a href="../index.html">3.28.1 Documentation</a> &#187;
  </li>

          <li class="nav-item nav-item-1"><a href="../manual/cmake-modules.7.html" accesskey="U">cmake-modules(7)</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href="">FindVulkan</a></li> 
      </ul>
    </div>  

    <div class="document">
      <div class="documentwrapper">
        <div class="bodywrapper">
          <div class="body" role="main">
            
  <section id="findvulkan">
<span id="module:FindVulkan"></span><h1>FindVulkan<a class="headerlink" href="#findvulkan" title="Permalink to this heading">¶</a></h1>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.7.</span></p>
</div>
<p>Find Vulkan, which is a low-overhead, cross-platform 3D graphics
and computing API.</p>
<section id="optional-components">
<h2>Optional COMPONENTS<a class="headerlink" href="#optional-components" title="Permalink to this heading">¶</a></h2>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.24.</span></p>
</div>
<p>This module respects several optional COMPONENTS.
There are corresponding imported targets for each of these.</p>
<dl>
<dt><code class="docutils literal notranslate"><span class="pre">glslc</span></code></dt><dd><p>The SPIR-V compiler.</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">glslangValidator</span></code></dt><dd><p>The <code class="docutils literal notranslate"><span class="pre">glslangValidator</span></code> tool.</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">glslang</span></code></dt><dd><p>The SPIR-V generator library.</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">shaderc_combined</span></code></dt><dd><p>The static library for Vulkan shader compilation.</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">SPIRV-Tools</span></code></dt><dd><p>Tools to process SPIR-V modules.</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">MoltenVK</span></code></dt><dd><p>On macOS, an additional component <code class="docutils literal notranslate"><span class="pre">MoltenVK</span></code> is available.</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">dxc</span></code></dt><dd><div class="versionadded">
<p><span class="versionmodified added">New in version 3.25.</span></p>
</div>
<p>The DirectX Shader Compiler.</p>
</dd>
</dl>
<p>The <code class="docutils literal notranslate"><span class="pre">glslc</span></code> and <code class="docutils literal notranslate"><span class="pre">glslangValidator</span></code> components are provided even
if not explicitly requested (for backward compatibility).</p>
</section>
<section id="imported-targets">
<h2>IMPORTED Targets<a class="headerlink" href="#imported-targets" title="Permalink to this heading">¶</a></h2>
<p>This module defines <span class="target" id="index-0-prop_tgt:IMPORTED"></span><a class="reference internal" href="../prop_tgt/IMPORTED.html#prop_tgt:IMPORTED" title="IMPORTED"><code class="xref cmake cmake-prop_tgt docutils literal notranslate"><span class="pre">IMPORTED</span></code></a> targets if Vulkan has been found:</p>
<dl>
<dt><code class="docutils literal notranslate"><span class="pre">Vulkan::Vulkan</span></code></dt><dd><p>The main Vulkan library.</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">Vulkan::glslc</span></code></dt><dd><div class="versionadded">
<p><span class="versionmodified added">New in version 3.19.</span></p>
</div>
<p>The GLSLC SPIR-V compiler, if it has been found.</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">Vulkan::Headers</span></code></dt><dd><div class="versionadded">
<p><span class="versionmodified added">New in version 3.21.</span></p>
</div>
<p>Provides just Vulkan headers include paths, if found.  No library is
included in this target.  This can be useful for applications that
load Vulkan library dynamically.</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">Vulkan::glslangValidator</span></code></dt><dd><div class="versionadded">
<p><span class="versionmodified added">New in version 3.21.</span></p>
</div>
<p>The glslangValidator tool, if found.  It is used to compile GLSL and
HLSL shaders into SPIR-V.</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">Vulkan::glslang</span></code></dt><dd><div class="versionadded">
<p><span class="versionmodified added">New in version 3.24.</span></p>
</div>
<p>Defined if SDK has the Khronos-reference front-end shader parser and SPIR-V
generator library (glslang).</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">Vulkan::shaderc_combined</span></code></dt><dd><div class="versionadded">
<p><span class="versionmodified added">New in version 3.24.</span></p>
</div>
<p>Defined if SDK has the Google static library for Vulkan shader compilation
(shaderc_combined).</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">Vulkan::SPIRV-Tools</span></code></dt><dd><div class="versionadded">
<p><span class="versionmodified added">New in version 3.24.</span></p>
</div>
<p>Defined if SDK has the Khronos library to process SPIR-V modules
(SPIRV-Tools).</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">Vulkan::MoltenVK</span></code></dt><dd><div class="versionadded">
<p><span class="versionmodified added">New in version 3.24.</span></p>
</div>
<p>Defined if SDK has the Khronos library which implement a subset of Vulkan API
over Apple Metal graphics framework. (MoltenVK).</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">Vulkan::volk</span></code></dt><dd><div class="versionadded">
<p><span class="versionmodified added">New in version 3.25.</span></p>
</div>
<p>Defined if SDK has the Vulkan meta-loader (volk).</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">Vulkan::dxc_lib</span></code></dt><dd><div class="versionadded">
<p><span class="versionmodified added">New in version 3.25.</span></p>
</div>
<p>Defined if SDK has the DirectX shader compiler library.</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">Vulkan::dxc_exe</span></code></dt><dd><div class="versionadded">
<p><span class="versionmodified added">New in version 3.25.</span></p>
</div>
<p>Defined if SDK has the DirectX shader compiler CLI tool.</p>
</dd>
</dl>
</section>
<section id="result-variables">
<h2>Result Variables<a class="headerlink" href="#result-variables" title="Permalink to this heading">¶</a></h2>
<p>This module defines the following variables:</p>
<dl>
<dt><code class="docutils literal notranslate"><span class="pre">Vulkan_FOUND</span></code></dt><dd><p>set to true if Vulkan was found</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">Vulkan_INCLUDE_DIRS</span></code></dt><dd><p>include directories for Vulkan</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">Vulkan_LIBRARIES</span></code></dt><dd><p>link against this library to use Vulkan</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">Vulkan_VERSION</span></code></dt><dd><div class="versionadded">
<p><span class="versionmodified added">New in version 3.23.</span></p>
</div>
<p>value from <code class="docutils literal notranslate"><span class="pre">vulkan/vulkan_core.h</span></code></p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">Vulkan_glslc_FOUND</span></code></dt><dd><div class="versionadded">
<p><span class="versionmodified added">New in version 3.24.</span></p>
</div>
<p>True, if the SDK has the glslc executable.</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">Vulkan_glslangValidator_FOUND</span></code></dt><dd><div class="versionadded">
<p><span class="versionmodified added">New in version 3.24.</span></p>
</div>
<p>True, if the SDK has the glslangValidator executable.</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">Vulkan_glslang_FOUND</span></code></dt><dd><div class="versionadded">
<p><span class="versionmodified added">New in version 3.24.</span></p>
</div>
<p>True, if the SDK has the glslang library.</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">Vulkan_shaderc_combined_FOUND</span></code></dt><dd><div class="versionadded">
<p><span class="versionmodified added">New in version 3.24.</span></p>
</div>
<p>True, if the SDK has the shaderc_combined library.</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">Vulkan_SPIRV-Tools_FOUND</span></code></dt><dd><div class="versionadded">
<p><span class="versionmodified added">New in version 3.24.</span></p>
</div>
<p>True, if the SDK has the SPIRV-Tools library.</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">Vulkan_MoltenVK_FOUND</span></code></dt><dd><div class="versionadded">
<p><span class="versionmodified added">New in version 3.24.</span></p>
</div>
<p>True, if the SDK has the MoltenVK library.</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">Vulkan_volk_FOUND</span></code></dt><dd><div class="versionadded">
<p><span class="versionmodified added">New in version 3.25.</span></p>
</div>
<p>True, if the SDK has the volk library.</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">Vulkan_dxc_lib_FOUND</span></code></dt><dd><div class="versionadded">
<p><span class="versionmodified added">New in version 3.25.</span></p>
</div>
<p>True, if the SDK has the DirectX shader compiler library.</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">Vulkan_dxc_exe_FOUND</span></code></dt><dd><div class="versionadded">
<p><span class="versionmodified added">New in version 3.25.</span></p>
</div>
<p>True, if the SDK has the DirectX shader compiler CLI tool.</p>
</dd>
</dl>
<p>The module will also defines these cache variables:</p>
<dl>
<dt><code class="docutils literal notranslate"><span class="pre">Vulkan_INCLUDE_DIR</span></code></dt><dd><p>the Vulkan include directory</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">Vulkan_LIBRARY</span></code></dt><dd><p>the path to the Vulkan library</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">Vulkan_GLSLC_EXECUTABLE</span></code></dt><dd><p>the path to the GLSL SPIR-V compiler</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">Vulkan_GLSLANG_VALIDATOR_EXECUTABLE</span></code></dt><dd><p>the path to the glslangValidator tool</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">Vulkan_glslang_LIBRARY</span></code></dt><dd><div class="versionadded">
<p><span class="versionmodified added">New in version 3.24.</span></p>
</div>
<p>Path to the glslang library.</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">Vulkan_shaderc_combined_LIBRARY</span></code></dt><dd><div class="versionadded">
<p><span class="versionmodified added">New in version 3.24.</span></p>
</div>
<p>Path to the shaderc_combined library.</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">Vulkan_SPIRV-Tools_LIBRARY</span></code></dt><dd><div class="versionadded">
<p><span class="versionmodified added">New in version 3.24.</span></p>
</div>
<p>Path to the SPIRV-Tools library.</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">Vulkan_MoltenVK_LIBRARY</span></code></dt><dd><div class="versionadded">
<p><span class="versionmodified added">New in version 3.24.</span></p>
</div>
<p>Path to the MoltenVK library.</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">Vulkan_volk_LIBRARY</span></code></dt><dd><div class="versionadded">
<p><span class="versionmodified added">New in version 3.25.</span></p>
</div>
<p>Path to the volk library.</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">Vulkan_dxc_LIBRARY</span></code></dt><dd><div class="versionadded">
<p><span class="versionmodified added">New in version 3.25.</span></p>
</div>
<p>Path to the DirectX shader compiler library.</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">Vulkan_dxc_EXECUTABLE</span></code></dt><dd><div class="versionadded">
<p><span class="versionmodified added">New in version 3.25.</span></p>
</div>
<p>Path to the DirectX shader compiler CLI tool.</p>
</dd>
</dl>
</section>
<section id="hints">
<h2>Hints<a class="headerlink" href="#hints" title="Permalink to this heading">¶</a></h2>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.18.</span></p>
</div>
<p>The <code class="docutils literal notranslate"><span class="pre">VULKAN_SDK</span></code> environment variable optionally specifies the
location of the Vulkan SDK root directory for the given
architecture. It is typically set by sourcing the toplevel
<code class="docutils literal notranslate"><span class="pre">setup-env.sh</span></code> script of the Vulkan SDK directory into the shell
environment.</p>
</section>
</section>


            <div class="clearer"></div>
          </div>
        </div>
      </div>
      <div class="sphinxsidebar" role="navigation" aria-label="main navigation">
        <div class="sphinxsidebarwrapper">
  <div>
    <h3><a href="../index.html">Table of Contents</a></h3>
    <ul>
<li><a class="reference internal" href="#">FindVulkan</a><ul>
<li><a class="reference internal" href="#optional-components">Optional COMPONENTS</a></li>
<li><a class="reference internal" href="#imported-targets">IMPORTED Targets</a></li>
<li><a class="reference internal" href="#result-variables">Result Variables</a></li>
<li><a class="reference internal" href="#hints">Hints</a></li>
</ul>
</li>
</ul>

  </div>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="FindTIFF.html"
                          title="previous chapter">FindTIFF</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="FindWget.html"
                          title="next chapter">FindWget</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../_sources/module/FindVulkan.rst.txt"
            rel="nofollow">Show Source</a></li>
    </ul>
   </div>
<div id="searchbox" style="display: none" role="search">
  <h3 id="searchlabel">Quick search</h3>
    <div class="searchformwrapper">
    <form class="search" action="../search.html" method="get">
      <input type="text" name="q" aria-labelledby="searchlabel" autocomplete="off" autocorrect="off" autocapitalize="off" spellcheck="false"/>
      <input type="submit" value="Go" />
    </form>
    </div>
</div>
<script>document.getElementById('searchbox').style.display = "block"</script>
        </div>
      </div>
      <div class="clearer"></div>
    </div>
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             >index</a></li>
        <li class="right" >
          <a href="FindWget.html" title="FindWget"
             >next</a> |</li>
        <li class="right" >
          <a href="FindTIFF.html" title="FindTIFF"
             >previous</a> |</li>
  <li>
    <img src="../_static/cmake-logo-16.png" alt=""
         style="vertical-align: middle; margin-top: -2px" />
  </li>
  <li>
    <a href="https://cmake.org/">CMake</a> &#187;
  </li>
  <li>
    <a href="../index.html">3.28.1 Documentation</a> &#187;
  </li>

          <li class="nav-item nav-item-1"><a href="../manual/cmake-modules.7.html" >cmake-modules(7)</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href="">FindVulkan</a></li> 
      </ul>
    </div>

    <div class="footer" role="contentinfo">
        &#169; Copyright 2000-2023 Kitware, Inc. and Contributors.
      Created using <a href="https://www.sphinx-doc.org/">Sphinx</a> 6.2.1.
    </div>
  </body>
</html>