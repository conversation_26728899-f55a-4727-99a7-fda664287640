
<!DOCTYPE html>

<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="generator" content="Docutils 0.19: https://docutils.sourceforge.io/" />

  <title>Green Hills MULTI &mdash; CMake 3.28.1 Documentation</title>

    <link rel="stylesheet" type="text/css" href="../_static/pygments.css" />
    <link rel="stylesheet" type="text/css" href="../_static/cmake.css" />
    
    <script data-url_root="../" id="documentation_options" src="../_static/documentation_options.js"></script>
    <script src="../_static/doctools.js"></script>
    <script src="../_static/sphinx_highlight.js"></script>
    
    <link rel="icon" href="../_static/cmake-favicon.ico"/>
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="next" title="Xcode" href="Xcode.html" />
    <link rel="prev" title="Visual Studio 17 2022" href="Visual%20Studio%2017%202022.html" />
 

  </head><body>

    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             accesskey="I">index</a></li>
        <li class="right" >
          <a href="Xcode.html" title="Xcode"
             accesskey="N">next</a> |</li>
        <li class="right" >
          <a href="Visual%20Studio%2017%202022.html" title="Visual Studio 17 2022"
             accesskey="P">previous</a> |</li>
  <li>
    <img src="../_static/cmake-logo-16.png" alt=""
         style="vertical-align: middle; margin-top: -2px" />
  </li>
  <li>
    <a href="https://cmake.org/">CMake</a> &#187;
  </li>
  <li>
    <a href="../index.html">3.28.1 Documentation</a> &#187;
  </li>

          <li class="nav-item nav-item-1"><a href="../manual/cmake-generators.7.html" accesskey="U">cmake-generators(7)</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href="">Green Hills MULTI</a></li> 
      </ul>
    </div>  

    <div class="document">
      <div class="documentwrapper">
        <div class="bodywrapper">
          <div class="body" role="main">
            
  <section id="green-hills-multi">
<span id="generator:Green Hills MULTI"></span><h1>Green Hills MULTI<a class="headerlink" href="#green-hills-multi" title="Permalink to this heading">¶</a></h1>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.3.</span></p>
</div>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.15: </span>Linux support.</p>
</div>
<p>Generates Green Hills MULTI project files (experimental, work-in-progress).</p>
<blockquote>
<div><p>The buildsystem has predetermined build-configuration settings that can be controlled
via the <span class="target" id="index-0-variable:CMAKE_BUILD_TYPE"></span><a class="reference internal" href="../variable/CMAKE_BUILD_TYPE.html#variable:CMAKE_BUILD_TYPE" title="CMAKE_BUILD_TYPE"><code class="xref cmake cmake-variable docutils literal notranslate"><span class="pre">CMAKE_BUILD_TYPE</span></code></a> variable.</p>
</div></blockquote>
<section id="platform-selection">
<h2>Platform Selection<a class="headerlink" href="#platform-selection" title="Permalink to this heading">¶</a></h2>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.13.</span></p>
</div>
<p>The variable <code class="docutils literal notranslate"><span class="pre">GHS_PRIMARY_TARGET</span></code> can be used to select the target platform.</p>
<blockquote>
<div><div class="line-block">
<div class="line">Sets <code class="docutils literal notranslate"><span class="pre">primaryTarget</span></code> entry in project file.</div>
</div>
</div></blockquote>
<p>For example:</p>
<ul class="simple">
<li><p><code class="docutils literal notranslate"><span class="pre">cmake</span> <span class="pre">-G</span> <span class="pre">&quot;Green</span> <span class="pre">Hills</span> <span class="pre">MULTI&quot;</span> <span class="pre">-D</span> <span class="pre">GHS_PRIMARY_TARGET=ppc_integrity.tgt</span></code></p></li>
</ul>
<p>Otherwise the <code class="docutils literal notranslate"><span class="pre">primaryTarget</span></code> will be composed from the values of <span class="target" id="index-0-variable:CMAKE_GENERATOR_PLATFORM"></span><a class="reference internal" href="../variable/CMAKE_GENERATOR_PLATFORM.html#variable:CMAKE_GENERATOR_PLATFORM" title="CMAKE_GENERATOR_PLATFORM"><code class="xref cmake cmake-variable docutils literal notranslate"><span class="pre">CMAKE_GENERATOR_PLATFORM</span></code></a>
and <code class="docutils literal notranslate"><span class="pre">GHS_TARGET_PLATFORM</span></code>. Defaulting to the value of <code class="docutils literal notranslate"><span class="pre">arm_integrity.tgt</span></code></p>
<ul>
<li><p>The <span class="target" id="index-1-variable:CMAKE_GENERATOR_PLATFORM"></span><a class="reference internal" href="../variable/CMAKE_GENERATOR_PLATFORM.html#variable:CMAKE_GENERATOR_PLATFORM" title="CMAKE_GENERATOR_PLATFORM"><code class="xref cmake cmake-variable docutils literal notranslate"><span class="pre">CMAKE_GENERATOR_PLATFORM</span></code></a> variable may be set, perhaps
via the <a class="reference internal" href="../manual/cmake.1.html#cmdoption-cmake-A"><code class="xref std std-option docutils literal notranslate"><span class="pre">cmake</span> <span class="pre">-A</span></code></a> option.</p>
<div class="line-block">
<div class="line">Typical values of <code class="docutils literal notranslate"><span class="pre">arm</span></code>, <code class="docutils literal notranslate"><span class="pre">ppc</span></code>, <code class="docutils literal notranslate"><span class="pre">86</span></code>, etcetera, are used.</div>
</div>
</li>
<li><p>The variable <code class="docutils literal notranslate"><span class="pre">GHS_TARGET_PLATFORM</span></code> may be set, perhaps via the <a class="reference internal" href="../manual/cmake.1.html#cmdoption-cmake-D"><code class="xref std std-option docutils literal notranslate"><span class="pre">cmake</span> <span class="pre">-D</span></code></a>
option.</p>
<div class="line-block">
<div class="line">Defaults to <code class="docutils literal notranslate"><span class="pre">integrity</span></code>.</div>
<div class="line">Usual values are <code class="docutils literal notranslate"><span class="pre">integrity</span></code>, <code class="docutils literal notranslate"><span class="pre">threadx</span></code>, <code class="docutils literal notranslate"><span class="pre">uvelosity</span></code>, <code class="docutils literal notranslate"><span class="pre">velosity</span></code>,
<code class="docutils literal notranslate"><span class="pre">vxworks</span></code>, <code class="docutils literal notranslate"><span class="pre">standalone</span></code>.</div>
</div>
</li>
</ul>
<p>For example:</p>
<ul class="simple">
<li><p><code class="docutils literal notranslate"><span class="pre">cmake</span> <span class="pre">-G</span> <span class="pre">&quot;Green</span> <span class="pre">Hills</span> <span class="pre">MULTI&quot;</span></code> for <code class="docutils literal notranslate"><span class="pre">arm_integrity.tgt</span></code>.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">cmake</span> <span class="pre">-G</span> <span class="pre">&quot;Green</span> <span class="pre">Hills</span> <span class="pre">MULTI&quot;</span> <span class="pre">-A</span> <span class="pre">86</span></code> for <code class="docutils literal notranslate"><span class="pre">86_integrity.tgt</span></code>.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">cmake</span> <span class="pre">-G</span> <span class="pre">&quot;Green</span> <span class="pre">Hills</span> <span class="pre">MULTI&quot;</span> <span class="pre">-D</span> <span class="pre">GHS_TARGET_PLATFORM=standalone</span></code> for <code class="docutils literal notranslate"><span class="pre">arm_standalone.tgt</span></code>.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">cmake</span> <span class="pre">-G</span> <span class="pre">&quot;Green</span> <span class="pre">Hills</span> <span class="pre">MULTI&quot;</span> <span class="pre">-A</span> <span class="pre">ppc</span> <span class="pre">-D</span> <span class="pre">GHS_TARGET_PLATFORM=standalone</span></code> for <code class="docutils literal notranslate"><span class="pre">ppc_standalone.tgt</span></code>.</p></li>
</ul>
</section>
<section id="toolset-selection">
<h2>Toolset Selection<a class="headerlink" href="#toolset-selection" title="Permalink to this heading">¶</a></h2>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.13.</span></p>
</div>
<p>The generator searches for the latest compiler or can be given a location to use.
<code class="docutils literal notranslate"><span class="pre">GHS_TOOLSET_ROOT</span></code> is the directory that is checked for the latest compiler.</p>
<ul>
<li><p>The <span class="target" id="index-0-variable:CMAKE_GENERATOR_TOOLSET"></span><a class="reference internal" href="../variable/CMAKE_GENERATOR_TOOLSET.html#variable:CMAKE_GENERATOR_TOOLSET" title="CMAKE_GENERATOR_TOOLSET"><code class="xref cmake cmake-variable docutils literal notranslate"><span class="pre">CMAKE_GENERATOR_TOOLSET</span></code></a> option may be set, perhaps
via the <a class="reference internal" href="../manual/cmake.1.html#cmdoption-cmake-T"><code class="xref std std-option docutils literal notranslate"><span class="pre">cmake</span> <span class="pre">-T</span></code></a> option, to specify the location of the toolset.
Both absolute and relative paths are valid. Paths are relative to <code class="docutils literal notranslate"><span class="pre">GHS_TOOLSET_ROOT</span></code>.</p></li>
<li><p>The variable <code class="docutils literal notranslate"><span class="pre">GHS_TOOLSET_ROOT</span></code> may be set, perhaps via the <a class="reference internal" href="../manual/cmake.1.html#cmdoption-cmake-D"><code class="xref std std-option docutils literal notranslate"><span class="pre">cmake</span> <span class="pre">-D</span></code></a>
option.</p>
<div class="line-block">
<div class="line">Root path for toolset searches and relative paths.</div>
<div class="line">Defaults to <code class="docutils literal notranslate"><span class="pre">C:/ghs</span></code> in Windows or <code class="docutils literal notranslate"><span class="pre">/usr/ghs</span></code> in Linux.</div>
</div>
</li>
</ul>
<p>For example, setting a specific compiler:</p>
<ul class="simple">
<li><p><code class="docutils literal notranslate"><span class="pre">cmake</span> <span class="pre">-G</span> <span class="pre">&quot;Green</span> <span class="pre">Hills</span> <span class="pre">MULTI&quot;</span> <span class="pre">-T</span> <span class="pre">comp_201754</span></code> for <code class="docutils literal notranslate"><span class="pre">/usr/ghs/comp_201754</span></code>.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">cmake</span> <span class="pre">-G</span> <span class="pre">&quot;Green</span> <span class="pre">Hills</span> <span class="pre">MULTI&quot;</span> <span class="pre">-T</span> <span class="pre">comp_201754</span> <span class="pre">-D</span> <span class="pre">GHS_TOOLSET_ROOT=/opt/ghs</span></code> for <code class="docutils literal notranslate"><span class="pre">/opt/ghs/comp_201754</span></code>.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">cmake</span> <span class="pre">-G</span> <span class="pre">&quot;Green</span> <span class="pre">Hills</span> <span class="pre">MULTI&quot;</span> <span class="pre">-T</span> <span class="pre">/usr/ghs/comp_201554</span></code></p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">cmake</span> <span class="pre">-G</span> <span class="pre">&quot;Green</span> <span class="pre">Hills</span> <span class="pre">MULTI&quot;</span> <span class="pre">-T</span> <span class="pre">C:/ghs/comp_201754</span></code></p></li>
</ul>
<p>For example, searching for latest compiler:</p>
<ul class="simple">
<li><p><code class="docutils literal notranslate"><span class="pre">cmake</span> <span class="pre">-G</span> <span class="pre">&quot;Green</span> <span class="pre">Hills</span> <span class="pre">MULTI&quot;</span></code> for searching <code class="docutils literal notranslate"><span class="pre">/usr/ghs</span></code>.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">cmake</span> <span class="pre">-G</span> <span class="pre">&quot;Green</span> <span class="pre">Hills</span> <span class="pre">MULTI</span> <span class="pre">-D</span> <span class="pre">GHS_TOOLSET_ROOT=/opt/ghs&quot;</span></code> for searching <code class="docutils literal notranslate"><span class="pre">/opt/ghs</span></code>.</p></li>
</ul>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>The <span class="target" id="index-1-variable:CMAKE_GENERATOR_TOOLSET"></span><a class="reference internal" href="../variable/CMAKE_GENERATOR_TOOLSET.html#variable:CMAKE_GENERATOR_TOOLSET" title="CMAKE_GENERATOR_TOOLSET"><code class="xref cmake cmake-variable docutils literal notranslate"><span class="pre">CMAKE_GENERATOR_TOOLSET</span></code></a> should use CMake style paths.</p>
</div>
</section>
<section id="os-and-bsp-selection">
<h2>OS and BSP Selection<a class="headerlink" href="#os-and-bsp-selection" title="Permalink to this heading">¶</a></h2>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.3.</span></p>
</div>
<p>Certain target platforms, like Integrity, require an OS.  The RTOS directory path
can be explicitly set using <code class="docutils literal notranslate"><span class="pre">GHS_OS_DIR</span></code>.  Otherwise <code class="docutils literal notranslate"><span class="pre">GHS_OS_ROOT</span></code> will be
searched for the latest Integrity RTOS.</p>
<p>If the target platform, like Integrity, requires a BSP name then it can be set via
the <code class="docutils literal notranslate"><span class="pre">GHS_BSP_NAME</span></code> variable.</p>
<ul>
<li><p><code class="docutils literal notranslate"><span class="pre">GHS_OS_DIR</span></code> and <code class="docutils literal notranslate"><span class="pre">GHS_OS_DIR_OPTION</span></code></p>
<div class="line-block">
<div class="line">Sets <code class="docutils literal notranslate"><span class="pre">-os_dir</span></code> entry in project file.</div>
</div>
<div class="line-block">
<div class="line"><code class="docutils literal notranslate"><span class="pre">GHS_OS_DIR_OPTION</span></code> default value is <code class="docutils literal notranslate"><span class="pre">-os_dir</span></code>.</div>
</div>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.15: </span>The <code class="docutils literal notranslate"><span class="pre">GHS_OS_DIR_OPTION</span></code> variable.</p>
</div>
<p>For example:</p>
<ul class="simple">
<li><p><code class="docutils literal notranslate"><span class="pre">cmake</span> <span class="pre">-G</span> <span class="pre">&quot;Green</span> <span class="pre">Hills</span> <span class="pre">MULTI&quot;</span> <span class="pre">-D</span> <span class="pre">GHS_OS_DIR=/usr/ghs/int1144</span></code></p></li>
</ul>
</li>
<li><p><code class="docutils literal notranslate"><span class="pre">GHS_OS_ROOT</span></code></p>
<div class="line-block">
<div class="line">Root path for RTOS searches.</div>
<div class="line">Defaults to <code class="docutils literal notranslate"><span class="pre">C:/ghs</span></code> in Windows or <code class="docutils literal notranslate"><span class="pre">/usr/ghs</span></code> in Linux.</div>
</div>
<p>For example:</p>
<ul class="simple">
<li><p><code class="docutils literal notranslate"><span class="pre">cmake</span> <span class="pre">-G</span> <span class="pre">&quot;Green</span> <span class="pre">Hills</span> <span class="pre">MULTI&quot;</span> <span class="pre">-D</span> <span class="pre">GHS_OS_ROOT=/opt/ghs</span></code></p></li>
</ul>
</li>
<li><p><code class="docutils literal notranslate"><span class="pre">GHS_BSP_NAME</span></code></p>
<div class="line-block">
<div class="line">Sets <code class="docutils literal notranslate"><span class="pre">-bsp</span></code> entry in project file.</div>
<div class="line">Defaults to <code class="docutils literal notranslate"><span class="pre">sim&lt;arch&gt;</span></code> for <code class="docutils literal notranslate"><span class="pre">integrity</span></code> platforms.</div>
</div>
<p>For example:</p>
<ul class="simple">
<li><p><code class="docutils literal notranslate"><span class="pre">cmake</span> <span class="pre">-G</span> <span class="pre">&quot;Green</span> <span class="pre">Hills</span> <span class="pre">MULTI&quot;</span></code> for <code class="docutils literal notranslate"><span class="pre">simarm</span></code> on <code class="docutils literal notranslate"><span class="pre">arm_integrity.tgt</span></code>.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">cmake</span> <span class="pre">-G</span> <span class="pre">&quot;Green</span> <span class="pre">Hills</span> <span class="pre">MULTI&quot;</span> <span class="pre">-A</span> <span class="pre">86</span></code> for <code class="docutils literal notranslate"><span class="pre">sim86</span></code> on <code class="docutils literal notranslate"><span class="pre">86_integrity.tgt</span></code>.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">cmake</span> <span class="pre">-G</span> <span class="pre">&quot;Green</span> <span class="pre">Hills</span> <span class="pre">MULTI&quot;</span> <span class="pre">-A</span> <span class="pre">ppc</span> <span class="pre">-D</span> <span class="pre">GHS_BSP_NAME=sim800</span></code> for <code class="docutils literal notranslate"><span class="pre">sim800</span></code>
on <code class="docutils literal notranslate"><span class="pre">ppc_integrity.tgt</span></code>.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">cmake</span> <span class="pre">-G</span> <span class="pre">&quot;Green</span> <span class="pre">Hills</span> <span class="pre">MULTI&quot;</span> <span class="pre">-D</span> <span class="pre">GHS_PRIMARY_TARGET=ppc_integrity.tgt</span> <span class="pre">-D</span> <span class="pre">GHS_BSP_NAME=fsl-t1040</span></code>
for <code class="docutils literal notranslate"><span class="pre">fsl-t1040</span></code> on <code class="docutils literal notranslate"><span class="pre">ppc_integrity.tgt</span></code>.</p></li>
</ul>
</li>
</ul>
</section>
<section id="target-properties">
<h2>Target Properties<a class="headerlink" href="#target-properties" title="Permalink to this heading">¶</a></h2>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.14.</span></p>
</div>
<p>The following properties are available:</p>
<ul class="simple">
<li><p><span class="target" id="index-0-prop_tgt:GHS_INTEGRITY_APP"></span><a class="reference internal" href="../prop_tgt/GHS_INTEGRITY_APP.html#prop_tgt:GHS_INTEGRITY_APP" title="GHS_INTEGRITY_APP"><code class="xref cmake cmake-prop_tgt docutils literal notranslate"><span class="pre">GHS_INTEGRITY_APP</span></code></a></p></li>
<li><p><span class="target" id="index-0-prop_tgt:GHS_NO_SOURCE_GROUP_FILE"></span><a class="reference internal" href="../prop_tgt/GHS_NO_SOURCE_GROUP_FILE.html#prop_tgt:GHS_NO_SOURCE_GROUP_FILE" title="GHS_NO_SOURCE_GROUP_FILE"><code class="xref cmake cmake-prop_tgt docutils literal notranslate"><span class="pre">GHS_NO_SOURCE_GROUP_FILE</span></code></a></p></li>
</ul>
</section>
<section id="multi-project-variables">
<h2>MULTI Project Variables<a class="headerlink" href="#multi-project-variables" title="Permalink to this heading">¶</a></h2>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.3.</span></p>
</div>
<p>Adding a Customization file and macros are available through the use of the following
variables:</p>
<ul class="simple">
<li><p><code class="docutils literal notranslate"><span class="pre">GHS_CUSTOMIZATION</span></code> - CMake path name to Customization File.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">GHS_GPJ_MACROS</span></code> - CMake list of Macros.</p></li>
</ul>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>This generator is deemed experimental as of CMake 3.28.1
and is still a work in progress.  Future versions of CMake
may make breaking changes as the generator matures.</p>
</div>
</section>
</section>


            <div class="clearer"></div>
          </div>
        </div>
      </div>
      <div class="sphinxsidebar" role="navigation" aria-label="main navigation">
        <div class="sphinxsidebarwrapper">
  <div>
    <h3><a href="../index.html">Table of Contents</a></h3>
    <ul>
<li><a class="reference internal" href="#">Green Hills MULTI</a><ul>
<li><a class="reference internal" href="#platform-selection">Platform Selection</a></li>
<li><a class="reference internal" href="#toolset-selection">Toolset Selection</a></li>
<li><a class="reference internal" href="#os-and-bsp-selection">OS and BSP Selection</a></li>
<li><a class="reference internal" href="#target-properties">Target Properties</a></li>
<li><a class="reference internal" href="#multi-project-variables">MULTI Project Variables</a></li>
</ul>
</li>
</ul>

  </div>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="Visual%20Studio%2017%202022.html"
                          title="previous chapter">Visual Studio 17 2022</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="Xcode.html"
                          title="next chapter">Xcode</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../_sources/generator/Green Hills MULTI.rst.txt"
            rel="nofollow">Show Source</a></li>
    </ul>
   </div>
<div id="searchbox" style="display: none" role="search">
  <h3 id="searchlabel">Quick search</h3>
    <div class="searchformwrapper">
    <form class="search" action="../search.html" method="get">
      <input type="text" name="q" aria-labelledby="searchlabel" autocomplete="off" autocorrect="off" autocapitalize="off" spellcheck="false"/>
      <input type="submit" value="Go" />
    </form>
    </div>
</div>
<script>document.getElementById('searchbox').style.display = "block"</script>
        </div>
      </div>
      <div class="clearer"></div>
    </div>
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             >index</a></li>
        <li class="right" >
          <a href="Xcode.html" title="Xcode"
             >next</a> |</li>
        <li class="right" >
          <a href="Visual%20Studio%2017%202022.html" title="Visual Studio 17 2022"
             >previous</a> |</li>
  <li>
    <img src="../_static/cmake-logo-16.png" alt=""
         style="vertical-align: middle; margin-top: -2px" />
  </li>
  <li>
    <a href="https://cmake.org/">CMake</a> &#187;
  </li>
  <li>
    <a href="../index.html">3.28.1 Documentation</a> &#187;
  </li>

          <li class="nav-item nav-item-1"><a href="../manual/cmake-generators.7.html" >cmake-generators(7)</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href="">Green Hills MULTI</a></li> 
      </ul>
    </div>

    <div class="footer" role="contentinfo">
        &#169; Copyright 2000-2023 Kitware, Inc. and Contributors.
      Created using <a href="https://www.sphinx-doc.org/">Sphinx</a> 6.2.1.
    </div>
  </body>
</html>