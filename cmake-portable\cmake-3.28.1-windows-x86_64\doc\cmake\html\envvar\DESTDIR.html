
<!DOCTYPE html>

<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="generator" content="Docutils 0.19: https://docutils.sourceforge.io/" />

  <title>DESTDIR &mdash; CMake 3.28.1 Documentation</title>

    <link rel="stylesheet" type="text/css" href="../_static/pygments.css" />
    <link rel="stylesheet" type="text/css" href="../_static/cmake.css" />
    
    <script data-url_root="../" id="documentation_options" src="../_static/documentation_options.js"></script>
    <script src="../_static/doctools.js"></script>
    <script src="../_static/sphinx_highlight.js"></script>
    
    <link rel="icon" href="../_static/cmake-favicon.ico"/>
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="next" title="LDFLAGS" href="LDFLAGS.html" />
    <link rel="prev" title="CMAKE_TOOLCHAIN_FILE" href="CMAKE_TOOLCHAIN_FILE.html" />
 

  </head><body>

    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             accesskey="I">index</a></li>
        <li class="right" >
          <a href="LDFLAGS.html" title="LDFLAGS"
             accesskey="N">next</a> |</li>
        <li class="right" >
          <a href="CMAKE_TOOLCHAIN_FILE.html" title="CMAKE_TOOLCHAIN_FILE"
             accesskey="P">previous</a> |</li>
  <li>
    <img src="../_static/cmake-logo-16.png" alt=""
         style="vertical-align: middle; margin-top: -2px" />
  </li>
  <li>
    <a href="https://cmake.org/">CMake</a> &#187;
  </li>
  <li>
    <a href="../index.html">3.28.1 Documentation</a> &#187;
  </li>

          <li class="nav-item nav-item-1"><a href="../manual/cmake-env-variables.7.html" accesskey="U">cmake-env-variables(7)</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href="">DESTDIR</a></li> 
      </ul>
    </div>  

    <div class="document">
      <div class="documentwrapper">
        <div class="bodywrapper">
          <div class="body" role="main">
            
  <section id="destdir">
<span id="envvar:DESTDIR"></span><h1>DESTDIR<a class="headerlink" href="#destdir" title="Permalink to this heading">¶</a></h1>
<p>This is a CMake <a class="reference internal" href="../manual/cmake-language.7.html#cmake-language-environment-variables"><span class="std std-ref">Environment Variable</span></a>. Its initial value is taken from
the calling process environment.</p>
<p>On UNIX one can use the <code class="docutils literal notranslate"><span class="pre">DESTDIR</span></code> mechanism in order to relocate the
whole installation.  <code class="docutils literal notranslate"><span class="pre">DESTDIR</span></code> means DESTination DIRectory.  It is
commonly used by packagers to install software in a staging directory.</p>
<p>For example, running</p>
<div class="highlight-shell notranslate"><div class="highlight"><pre><span></span>make<span class="w"> </span><span class="nv">DESTDIR</span><span class="o">=</span>/package/stage<span class="w"> </span>install
</pre></div>
</div>
<p>will install the software using the installation prefix, e.g. <code class="docutils literal notranslate"><span class="pre">/usr/local</span></code>,
prepended with the <code class="docutils literal notranslate"><span class="pre">DESTDIR</span></code> value which gives <code class="docutils literal notranslate"><span class="pre">/package/stage/usr/local</span></code>.
The packaging tool may then construct the package from the content of the
<code class="docutils literal notranslate"><span class="pre">/package/stage</span></code> directory.</p>
<p>See the <span class="target" id="index-0-variable:CMAKE_INSTALL_PREFIX"></span><a class="reference internal" href="../variable/CMAKE_INSTALL_PREFIX.html#variable:CMAKE_INSTALL_PREFIX" title="CMAKE_INSTALL_PREFIX"><code class="xref cmake cmake-variable docutils literal notranslate"><span class="pre">CMAKE_INSTALL_PREFIX</span></code></a> variable to control the
installation prefix when configuring a build tree.  Or, when using
the <span class="target" id="index-0-manual:cmake(1)"></span><a class="reference internal" href="../manual/cmake.1.html#manual:cmake(1)" title="cmake(1)"><code class="xref cmake cmake-manual docutils literal notranslate"><span class="pre">cmake(1)</span></code></a> command-line tool's <a class="reference internal" href="../manual/cmake.1.html#cmdoption-cmake-install"><code class="xref std std-option docutils literal notranslate"><span class="pre">--install</span></code></a>
mode, one may specify a different prefix using the
<a class="reference internal" href="../manual/cmake.1.html#cmdoption-cmake--install-0"><code class="xref std std-option docutils literal notranslate"><span class="pre">--prefix</span></code></a> option.</p>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p><code class="docutils literal notranslate"><span class="pre">DESTDIR</span></code> may not be used on Windows because installation
prefix usually contains a drive letter like in <code class="docutils literal notranslate"><span class="pre">C:/Program</span> <span class="pre">Files</span></code>
which cannot be prepended with some other prefix.</p>
</div>
</section>


            <div class="clearer"></div>
          </div>
        </div>
      </div>
      <div class="sphinxsidebar" role="navigation" aria-label="main navigation">
        <div class="sphinxsidebarwrapper">
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="CMAKE_TOOLCHAIN_FILE.html"
                          title="previous chapter">CMAKE_TOOLCHAIN_FILE</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="LDFLAGS.html"
                          title="next chapter">LDFLAGS</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../_sources/envvar/DESTDIR.rst.txt"
            rel="nofollow">Show Source</a></li>
    </ul>
   </div>
<div id="searchbox" style="display: none" role="search">
  <h3 id="searchlabel">Quick search</h3>
    <div class="searchformwrapper">
    <form class="search" action="../search.html" method="get">
      <input type="text" name="q" aria-labelledby="searchlabel" autocomplete="off" autocorrect="off" autocapitalize="off" spellcheck="false"/>
      <input type="submit" value="Go" />
    </form>
    </div>
</div>
<script>document.getElementById('searchbox').style.display = "block"</script>
        </div>
      </div>
      <div class="clearer"></div>
    </div>
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             >index</a></li>
        <li class="right" >
          <a href="LDFLAGS.html" title="LDFLAGS"
             >next</a> |</li>
        <li class="right" >
          <a href="CMAKE_TOOLCHAIN_FILE.html" title="CMAKE_TOOLCHAIN_FILE"
             >previous</a> |</li>
  <li>
    <img src="../_static/cmake-logo-16.png" alt=""
         style="vertical-align: middle; margin-top: -2px" />
  </li>
  <li>
    <a href="https://cmake.org/">CMake</a> &#187;
  </li>
  <li>
    <a href="../index.html">3.28.1 Documentation</a> &#187;
  </li>

          <li class="nav-item nav-item-1"><a href="../manual/cmake-env-variables.7.html" >cmake-env-variables(7)</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href="">DESTDIR</a></li> 
      </ul>
    </div>

    <div class="footer" role="contentinfo">
        &#169; Copyright 2000-2023 Kitware, Inc. and Contributors.
      Created using <a href="https://www.sphinx-doc.org/">Sphinx</a> 6.2.1.
    </div>
  </body>
</html>