
<!DOCTYPE html>

<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="generator" content="Docutils 0.19: https://docutils.sourceforge.io/" />

  <title>CPack FreeBSD Generator &mdash; CMake 3.28.1 Documentation</title>

    <link rel="stylesheet" type="text/css" href="../_static/pygments.css" />
    <link rel="stylesheet" type="text/css" href="../_static/cmake.css" />
    
    <script data-url_root="../" id="documentation_options" src="../_static/documentation_options.js"></script>
    <script src="../_static/doctools.js"></script>
    <script src="../_static/sphinx_highlight.js"></script>
    
    <link rel="icon" href="../_static/cmake-favicon.ico"/>
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="next" title="CPack Inno Setup Generator" href="innosetup.html" />
    <link rel="prev" title="CPack External Generator" href="external.html" />
 

  </head><body>

    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             accesskey="I">index</a></li>
        <li class="right" >
          <a href="innosetup.html" title="CPack Inno Setup Generator"
             accesskey="N">next</a> |</li>
        <li class="right" >
          <a href="external.html" title="CPack External Generator"
             accesskey="P">previous</a> |</li>
  <li>
    <img src="../_static/cmake-logo-16.png" alt=""
         style="vertical-align: middle; margin-top: -2px" />
  </li>
  <li>
    <a href="https://cmake.org/">CMake</a> &#187;
  </li>
  <li>
    <a href="../index.html">3.28.1 Documentation</a> &#187;
  </li>

          <li class="nav-item nav-item-1"><a href="../manual/cpack-generators.7.html" accesskey="U">cpack-generators(7)</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href="">CPack FreeBSD Generator</a></li> 
      </ul>
    </div>  

    <div class="document">
      <div class="documentwrapper">
        <div class="bodywrapper">
          <div class="body" role="main">
            
  <section id="cpack-freebsd-generator">
<span id="cpack_gen:CPack FreeBSD Generator"></span><h1>CPack FreeBSD Generator<a class="headerlink" href="#cpack-freebsd-generator" title="Permalink to this heading">¶</a></h1>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.10.</span></p>
</div>
<p>The built in (binary) CPack FreeBSD (pkg) generator (Unix only)</p>
<section id="variables-affecting-the-cpack-freebsd-pkg-generator">
<h2>Variables affecting the CPack FreeBSD (pkg) generator<a class="headerlink" href="#variables-affecting-the-cpack-freebsd-pkg-generator" title="Permalink to this heading">¶</a></h2>
<ul>
<li><div class="versionadded">
<p><span class="versionmodified added">New in version 3.18: </span><span class="target" id="index-0-variable:CPACK_ARCHIVE_THREADS"></span><a class="reference internal" href="archive.html#variable:CPACK_ARCHIVE_THREADS" title="CPACK_ARCHIVE_THREADS"><code class="xref cmake cmake-variable docutils literal notranslate"><span class="pre">CPACK_ARCHIVE_THREADS</span></code></a></p>
</div>
</li>
</ul>
</section>
<section id="variables-specific-to-cpack-freebsd-pkg-generator">
<h2>Variables specific to CPack FreeBSD (pkg) generator<a class="headerlink" href="#variables-specific-to-cpack-freebsd-pkg-generator" title="Permalink to this heading">¶</a></h2>
<p>The CPack FreeBSD generator may be used to create pkg(8) packages -- these may
be used on FreeBSD, DragonflyBSD, NetBSD, OpenBSD, but also on Linux or OSX,
depending on the installed package-management tools -- using <span class="target" id="index-0-module:CPack"></span><a class="reference internal" href="../module/CPack.html#module:CPack" title="CPack"><code class="xref cmake cmake-module docutils literal notranslate"><span class="pre">CPack</span></code></a>.</p>
<p>The CPack FreeBSD generator is a <span class="target" id="index-1-module:CPack"></span><a class="reference internal" href="../module/CPack.html#module:CPack" title="CPack"><code class="xref cmake cmake-module docutils literal notranslate"><span class="pre">CPack</span></code></a> generator and uses the
<code class="xref cmake cmake-variable docutils literal notranslate"><span class="pre">CPACK_XXX</span></code> variables used by <span class="target" id="index-2-module:CPack"></span><a class="reference internal" href="../module/CPack.html#module:CPack" title="CPack"><code class="xref cmake cmake-module docutils literal notranslate"><span class="pre">CPack</span></code></a>. It tries to reuse packaging
information that may already be specified for Debian packages for the
<span class="target" id="index-0-cpack_gen:CPack DEB Generator"></span><a class="reference internal" href="deb.html#cpack_gen:CPack DEB Generator" title="CPack DEB Generator"><code class="xref cmake cmake-cpack_gen docutils literal notranslate"><span class="pre">CPack</span> <span class="pre">DEB</span> <span class="pre">Generator</span></code></a>. It also tries to reuse RPM packaging
information when Debian does not specify.</p>
<p>The CPack FreeBSD generator should work on any host with libpkg installed. The
packages it produces are specific to the host architecture and ABI.</p>
<p>The CPack FreeBSD generator sets package-metadata through
<code class="xref cmake cmake-variable docutils literal notranslate"><span class="pre">CPACK_FREEBSD_XXX</span></code> variables. The CPack FreeBSD generator, unlike the
CPack Deb generator, does not specially support componentized packages; a
single package is created from all the software artifacts created through
CMake.</p>
<p>All of the variables can be set specifically for FreeBSD packaging in
the CPackConfig file or in CMakeLists.txt, but most of them have defaults
that use general settings (e.g. <span class="target" id="index-0-variable:CMAKE_PROJECT_NAME"></span><a class="reference internal" href="../variable/CMAKE_PROJECT_NAME.html#variable:CMAKE_PROJECT_NAME" title="CMAKE_PROJECT_NAME"><code class="xref cmake cmake-variable docutils literal notranslate"><span class="pre">CMAKE_PROJECT_NAME</span></code></a>) or Debian-specific
variables when those make sense (e.g. the homepage of an upstream project
is usually unchanged by the flavor of packaging). When there is no Debian
information to fall back on, but the RPM packaging has it, fall back to
the RPM information (e.g. package license).</p>
<dl class="cmake variable">
<dt class="sig sig-object cmake" id="variable:CPACK_FREEBSD_PACKAGE_NAME">
<span class="sig-name descname"><span class="pre">CPACK_FREEBSD_PACKAGE_NAME</span></span><a class="headerlink" href="#variable:CPACK_FREEBSD_PACKAGE_NAME" title="Permalink to this definition">¶</a></dt>
<dd><p>Sets the package name (in the package manifest, but also affects the
output filename).</p>
<dl class="field-list simple">
<dt class="field-odd">Mandatory<span class="colon">:</span></dt>
<dd class="field-odd"><p>Yes</p>
</dd>
<dt class="field-even">Default<span class="colon">:</span></dt>
<dd class="field-even"><ul class="simple">
<li><p><span class="target" id="index-0-variable:CPACK_PACKAGE_NAME"></span><a class="reference internal" href="../module/CPack.html#variable:CPACK_PACKAGE_NAME" title="CPACK_PACKAGE_NAME"><code class="xref cmake cmake-variable docutils literal notranslate"><span class="pre">CPACK_PACKAGE_NAME</span></code></a> (this is always set by CPack itself,
based on CMAKE_PROJECT_NAME).</p></li>
</ul>
</dd>
</dl>
</dd></dl>

<dl class="cmake variable">
<dt class="sig sig-object cmake" id="variable:CPACK_FREEBSD_PACKAGE_COMMENT">
<span class="sig-name descname"><span class="pre">CPACK_FREEBSD_PACKAGE_COMMENT</span></span><a class="headerlink" href="#variable:CPACK_FREEBSD_PACKAGE_COMMENT" title="Permalink to this definition">¶</a></dt>
<dd><p>Sets the package comment. This is the short description displayed by
pkg(8) in standard &quot;pkg info&quot; output.</p>
<dl class="field-list simple">
<dt class="field-odd">Mandatory<span class="colon">:</span></dt>
<dd class="field-odd"><p>Yes</p>
</dd>
<dt class="field-even">Default<span class="colon">:</span></dt>
<dd class="field-even"><ul class="simple">
<li><p><span class="target" id="index-0-variable:CPACK_PACKAGE_DESCRIPTION_SUMMARY"></span><a class="reference internal" href="../module/CPack.html#variable:CPACK_PACKAGE_DESCRIPTION_SUMMARY" title="CPACK_PACKAGE_DESCRIPTION_SUMMARY"><code class="xref cmake cmake-variable docutils literal notranslate"><span class="pre">CPACK_PACKAGE_DESCRIPTION_SUMMARY</span></code></a> (this is always set
by CPack itself, if nothing else sets it explicitly).</p></li>
</ul>
</dd>
</dl>
</dd></dl>

<dl class="cmake variable">
<dt class="sig sig-object cmake" id="variable:CPACK_FREEBSD_PACKAGE_DESCRIPTION">
<span class="sig-name descname"><span class="pre">CPACK_FREEBSD_PACKAGE_DESCRIPTION</span></span><a class="headerlink" href="#variable:CPACK_FREEBSD_PACKAGE_DESCRIPTION" title="Permalink to this definition">¶</a></dt>
<dd><p>Sets the package description. This is the long description of the package,
given by &quot;pkg info&quot; with a specific package as argument.</p>
<dl class="field-list simple">
<dt class="field-odd">Mandatory<span class="colon">:</span></dt>
<dd class="field-odd"><p>Yes</p>
</dd>
<dt class="field-even">Default<span class="colon">:</span></dt>
<dd class="field-even"><ul class="simple">
<li><p><span class="target" id="index-0-variable:CPACK_DEBIAN_PACKAGE_DESCRIPTION"></span><a class="reference internal" href="deb.html#variable:CPACK_DEBIAN_PACKAGE_DESCRIPTION" title="CPACK_DEBIAN_PACKAGE_DESCRIPTION"><code class="xref cmake cmake-variable docutils literal notranslate"><span class="pre">CPACK_DEBIAN_PACKAGE_DESCRIPTION</span></code></a> (this may be set already
for Debian packaging, so it is used as a fallback).</p></li>
<li><p><span class="target" id="index-1-variable:CPACK_PACKAGE_DESCRIPTION_SUMMARY"></span><a class="reference internal" href="../module/CPack.html#variable:CPACK_PACKAGE_DESCRIPTION_SUMMARY" title="CPACK_PACKAGE_DESCRIPTION_SUMMARY"><code class="xref cmake cmake-variable docutils literal notranslate"><span class="pre">CPACK_PACKAGE_DESCRIPTION_SUMMARY</span></code></a> (this is always set
by CPack itself, if nothing else sets it explicitly).</p></li>
<li><p><span class="target" id="index-0-variable:PROJECT_DESCRIPTION"></span><a class="reference internal" href="../variable/PROJECT_DESCRIPTION.html#variable:PROJECT_DESCRIPTION" title="PROJECT_DESCRIPTION"><code class="xref cmake cmake-variable docutils literal notranslate"><span class="pre">PROJECT_DESCRIPTION</span></code></a> (this can be set with the <code class="docutils literal notranslate"><span class="pre">DESCRIPTION</span></code>
parameter for <span class="target" id="index-0-command:project"></span><a class="reference internal" href="../command/project.html#command:project" title="project"><code class="xref cmake cmake-command docutils literal notranslate"><span class="pre">project()</span></code></a>).</p></li>
</ul>
</dd>
</dl>
</dd></dl>

<dl class="cmake variable">
<dt class="sig sig-object cmake" id="variable:CPACK_FREEBSD_PACKAGE_WWW">
<span class="sig-name descname"><span class="pre">CPACK_FREEBSD_PACKAGE_WWW</span></span><a class="headerlink" href="#variable:CPACK_FREEBSD_PACKAGE_WWW" title="Permalink to this definition">¶</a></dt>
<dd><p>The URL of the web site for this package, preferably (when applicable) the
site from which the original source can be obtained and any additional
upstream documentation or information may be found.</p>
<dl class="field-list simple">
<dt class="field-odd">Mandatory<span class="colon">:</span></dt>
<dd class="field-odd"><p>Yes</p>
</dd>
<dt class="field-even">Default<span class="colon">:</span></dt>
<dd class="field-even"><ul class="simple">
<li><p><span class="target" id="index-0-variable:CPACK_PACKAGE_HOMEPAGE_URL"></span><a class="reference internal" href="../module/CPack.html#variable:CPACK_PACKAGE_HOMEPAGE_URL" title="CPACK_PACKAGE_HOMEPAGE_URL"><code class="xref cmake cmake-variable docutils literal notranslate"><span class="pre">CPACK_PACKAGE_HOMEPAGE_URL</span></code></a>, or if that is not set,</p></li>
<li><p><span class="target" id="index-0-variable:CPACK_DEBIAN_PACKAGE_HOMEPAGE"></span><a class="reference internal" href="deb.html#variable:CPACK_DEBIAN_PACKAGE_HOMEPAGE" title="CPACK_DEBIAN_PACKAGE_HOMEPAGE"><code class="xref cmake cmake-variable docutils literal notranslate"><span class="pre">CPACK_DEBIAN_PACKAGE_HOMEPAGE</span></code></a> (this may be set already
for Debian packaging, so it is used as a fallback).</p></li>
</ul>
</dd>
</dl>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.12: </span>The <code class="xref cmake cmake-variable docutils literal notranslate"><span class="pre">CPACK_PACKAGE_HOMEPAGE_URL</span></code> variable.</p>
</div>
</dd></dl>

<dl class="cmake variable">
<dt class="sig sig-object cmake" id="variable:CPACK_FREEBSD_PACKAGE_LICENSE">
<span class="sig-name descname"><span class="pre">CPACK_FREEBSD_PACKAGE_LICENSE</span></span><a class="headerlink" href="#variable:CPACK_FREEBSD_PACKAGE_LICENSE" title="Permalink to this definition">¶</a></dt>
<dd><p>The license, or licenses, which apply to this software package. This must
be one or more license-identifiers that pkg recognizes as acceptable license
identifiers (e.g. &quot;GPLv2&quot;).</p>
<dl class="field-list simple">
<dt class="field-odd">Mandatory<span class="colon">:</span></dt>
<dd class="field-odd"><p>Yes</p>
</dd>
<dt class="field-even">Default<span class="colon">:</span></dt>
<dd class="field-even"><ul class="simple">
<li><p><span class="target" id="index-0-variable:CPACK_RPM_PACKAGE_LICENSE"></span><a class="reference internal" href="rpm.html#variable:CPACK_RPM_PACKAGE_LICENSE" title="CPACK_RPM_PACKAGE_LICENSE"><code class="xref cmake cmake-variable docutils literal notranslate"><span class="pre">CPACK_RPM_PACKAGE_LICENSE</span></code></a></p></li>
</ul>
</dd>
</dl>
</dd></dl>

<dl class="cmake variable">
<dt class="sig sig-object cmake" id="variable:CPACK_FREEBSD_PACKAGE_LICENSE_LOGIC">
<span class="sig-name descname"><span class="pre">CPACK_FREEBSD_PACKAGE_LICENSE_LOGIC</span></span><a class="headerlink" href="#variable:CPACK_FREEBSD_PACKAGE_LICENSE_LOGIC" title="Permalink to this definition">¶</a></dt>
<dd><p>This variable is only of importance if there is more than one license.
The default is &quot;single&quot;, which is only applicable to a single license.
Other acceptable values are determined by pkg -- those are &quot;dual&quot; or &quot;multi&quot; --
meaning choice (OR) or simultaneous (AND) application of the licenses.</p>
<dl class="field-list simple">
<dt class="field-odd">Mandatory<span class="colon">:</span></dt>
<dd class="field-odd"><p>No</p>
</dd>
<dt class="field-even">Default<span class="colon">:</span></dt>
<dd class="field-even"><p>single</p>
</dd>
</dl>
</dd></dl>

<dl class="cmake variable">
<dt class="sig sig-object cmake" id="variable:CPACK_FREEBSD_PACKAGE_MAINTAINER">
<span class="sig-name descname"><span class="pre">CPACK_FREEBSD_PACKAGE_MAINTAINER</span></span><a class="headerlink" href="#variable:CPACK_FREEBSD_PACKAGE_MAINTAINER" title="Permalink to this definition">¶</a></dt>
<dd><p>The FreeBSD maintainer (e.g. <code class="docutils literal notranslate"><span class="pre">kde&#64;freebsd.org</span></code>) of this package.</p>
<dl class="field-list simple">
<dt class="field-odd">Mandatory<span class="colon">:</span></dt>
<dd class="field-odd"><p>Yes</p>
</dd>
<dt class="field-even">Default<span class="colon">:</span></dt>
<dd class="field-even"><p>none</p>
</dd>
</dl>
</dd></dl>

<dl class="cmake variable">
<dt class="sig sig-object cmake" id="variable:CPACK_FREEBSD_PACKAGE_ORIGIN">
<span class="sig-name descname"><span class="pre">CPACK_FREEBSD_PACKAGE_ORIGIN</span></span><a class="headerlink" href="#variable:CPACK_FREEBSD_PACKAGE_ORIGIN" title="Permalink to this definition">¶</a></dt>
<dd><p>The origin (ports label) of this package; for packages built by CPack
outside of the ports system this is of less importance. The default
puts the package somewhere under <code class="docutils literal notranslate"><span class="pre">misc/</span></code>, as a stopgap.</p>
<dl class="field-list simple">
<dt class="field-odd">Mandatory<span class="colon">:</span></dt>
<dd class="field-odd"><p>Yes</p>
</dd>
<dt class="field-even">Default<span class="colon">:</span></dt>
<dd class="field-even"><p><code class="docutils literal notranslate"><span class="pre">misc/&lt;package</span> <span class="pre">name&gt;</span></code></p>
</dd>
</dl>
</dd></dl>

<dl class="cmake variable">
<dt class="sig sig-object cmake" id="variable:CPACK_FREEBSD_PACKAGE_CATEGORIES">
<span class="sig-name descname"><span class="pre">CPACK_FREEBSD_PACKAGE_CATEGORIES</span></span><a class="headerlink" href="#variable:CPACK_FREEBSD_PACKAGE_CATEGORIES" title="Permalink to this definition">¶</a></dt>
<dd><p>The ports categories where this package lives (if it were to be built
from ports). If none is set a single category is determined based on
the package origin.</p>
<dl class="field-list simple">
<dt class="field-odd">Mandatory<span class="colon">:</span></dt>
<dd class="field-odd"><p>Yes</p>
</dd>
<dt class="field-even">Default<span class="colon">:</span></dt>
<dd class="field-even"><p>derived from <code class="docutils literal notranslate"><span class="pre">ORIGIN</span></code></p>
</dd>
</dl>
</dd></dl>

<dl class="cmake variable">
<dt class="sig sig-object cmake" id="variable:CPACK_FREEBSD_PACKAGE_DEPS">
<span class="sig-name descname"><span class="pre">CPACK_FREEBSD_PACKAGE_DEPS</span></span><a class="headerlink" href="#variable:CPACK_FREEBSD_PACKAGE_DEPS" title="Permalink to this definition">¶</a></dt>
<dd><p>A list of package origins that should be added as package dependencies.
These are in the form <code class="docutils literal notranslate"><span class="pre">&lt;category&gt;/&lt;packagename&gt;</span></code>, e.g. <code class="docutils literal notranslate"><span class="pre">x11/libkonq</span></code>.
No version information needs to be provided (this is not included
in the manifest).</p>
<dl class="field-list simple">
<dt class="field-odd">Mandatory<span class="colon">:</span></dt>
<dd class="field-odd"><p>No</p>
</dd>
<dt class="field-even">Default<span class="colon">:</span></dt>
<dd class="field-even"><p>empty</p>
</dd>
</dl>
</dd></dl>

</section>
</section>


            <div class="clearer"></div>
          </div>
        </div>
      </div>
      <div class="sphinxsidebar" role="navigation" aria-label="main navigation">
        <div class="sphinxsidebarwrapper">
  <div>
    <h3><a href="../index.html">Table of Contents</a></h3>
    <ul>
<li><a class="reference internal" href="#">CPack FreeBSD Generator</a><ul>
<li><a class="reference internal" href="#variables-affecting-the-cpack-freebsd-pkg-generator">Variables affecting the CPack FreeBSD (pkg) generator</a></li>
<li><a class="reference internal" href="#variables-specific-to-cpack-freebsd-pkg-generator">Variables specific to CPack FreeBSD (pkg) generator</a></li>
</ul>
</li>
</ul>

  </div>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="external.html"
                          title="previous chapter">CPack External Generator</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="innosetup.html"
                          title="next chapter">CPack Inno Setup Generator</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../_sources/cpack_gen/freebsd.rst.txt"
            rel="nofollow">Show Source</a></li>
    </ul>
   </div>
<div id="searchbox" style="display: none" role="search">
  <h3 id="searchlabel">Quick search</h3>
    <div class="searchformwrapper">
    <form class="search" action="../search.html" method="get">
      <input type="text" name="q" aria-labelledby="searchlabel" autocomplete="off" autocorrect="off" autocapitalize="off" spellcheck="false"/>
      <input type="submit" value="Go" />
    </form>
    </div>
</div>
<script>document.getElementById('searchbox').style.display = "block"</script>
        </div>
      </div>
      <div class="clearer"></div>
    </div>
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             >index</a></li>
        <li class="right" >
          <a href="innosetup.html" title="CPack Inno Setup Generator"
             >next</a> |</li>
        <li class="right" >
          <a href="external.html" title="CPack External Generator"
             >previous</a> |</li>
  <li>
    <img src="../_static/cmake-logo-16.png" alt=""
         style="vertical-align: middle; margin-top: -2px" />
  </li>
  <li>
    <a href="https://cmake.org/">CMake</a> &#187;
  </li>
  <li>
    <a href="../index.html">3.28.1 Documentation</a> &#187;
  </li>

          <li class="nav-item nav-item-1"><a href="../manual/cpack-generators.7.html" >cpack-generators(7)</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href="">CPack FreeBSD Generator</a></li> 
      </ul>
    </div>

    <div class="footer" role="contentinfo">
        &#169; Copyright 2000-2023 Kitware, Inc. and Contributors.
      Created using <a href="https://www.sphinx-doc.org/">Sphinx</a> 6.2.1.
    </div>
  </body>
</html>