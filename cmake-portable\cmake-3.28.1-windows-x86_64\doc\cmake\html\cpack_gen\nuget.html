
<!DOCTYPE html>

<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="generator" content="Docutils 0.19: https://docutils.sourceforge.io/" />

  <title>CPack NuGet Generator &mdash; CMake 3.28.1 Documentation</title>

    <link rel="stylesheet" type="text/css" href="../_static/pygments.css" />
    <link rel="stylesheet" type="text/css" href="../_static/cmake.css" />
    
    <script data-url_root="../" id="documentation_options" src="../_static/documentation_options.js"></script>
    <script src="../_static/doctools.js"></script>
    <script src="../_static/sphinx_highlight.js"></script>
    
    <link rel="icon" href="../_static/cmake-favicon.ico"/>
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="next" title="CPack PackageMaker Generator" href="packagemaker.html" />
    <link rel="prev" title="CPack NSIS Generator" href="nsis.html" />
 

  </head><body>

    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             accesskey="I">index</a></li>
        <li class="right" >
          <a href="packagemaker.html" title="CPack PackageMaker Generator"
             accesskey="N">next</a> |</li>
        <li class="right" >
          <a href="nsis.html" title="CPack NSIS Generator"
             accesskey="P">previous</a> |</li>
  <li>
    <img src="../_static/cmake-logo-16.png" alt=""
         style="vertical-align: middle; margin-top: -2px" />
  </li>
  <li>
    <a href="https://cmake.org/">CMake</a> &#187;
  </li>
  <li>
    <a href="../index.html">3.28.1 Documentation</a> &#187;
  </li>

          <li class="nav-item nav-item-1"><a href="../manual/cpack-generators.7.html" accesskey="U">cpack-generators(7)</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href="">CPack NuGet Generator</a></li> 
      </ul>
    </div>  

    <div class="document">
      <div class="documentwrapper">
        <div class="bodywrapper">
          <div class="body" role="main">
            
  <section id="cpack-nuget-generator">
<span id="cpack_gen:CPack NuGet Generator"></span><h1>CPack NuGet Generator<a class="headerlink" href="#cpack-nuget-generator" title="Permalink to this heading">¶</a></h1>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.12.</span></p>
</div>
<p>When build a NuGet package there is no direct way to control an output
filename due a lack of the corresponding CLI option of NuGet, so there
is no <code class="xref cmake cmake-variable docutils literal notranslate"><span class="pre">CPACK_NUGET_PACKAGE_FILE_NAME</span></code> variable. To form the output filename
NuGet uses the package name and the version according to its built-in rules.</p>
<p>Also, be aware that including a top level directory
(<span class="target" id="index-0-variable:CPACK_INCLUDE_TOPLEVEL_DIRECTORY"></span><a class="reference internal" href="../variable/CPACK_INCLUDE_TOPLEVEL_DIRECTORY.html#variable:CPACK_INCLUDE_TOPLEVEL_DIRECTORY" title="CPACK_INCLUDE_TOPLEVEL_DIRECTORY"><code class="xref cmake cmake-variable docutils literal notranslate"><span class="pre">CPACK_INCLUDE_TOPLEVEL_DIRECTORY</span></code></a>) is ignored by this generator.</p>
<section id="variables-specific-to-cpack-nuget-generator">
<h2>Variables specific to CPack NuGet generator<a class="headerlink" href="#variables-specific-to-cpack-nuget-generator" title="Permalink to this heading">¶</a></h2>
<p>The CPack NuGet generator may be used to create NuGet packages using
<span class="target" id="index-0-module:CPack"></span><a class="reference internal" href="../module/CPack.html#module:CPack" title="CPack"><code class="xref cmake cmake-module docutils literal notranslate"><span class="pre">CPack</span></code></a>. The CPack NuGet generator is a <span class="target" id="index-1-module:CPack"></span><a class="reference internal" href="../module/CPack.html#module:CPack" title="CPack"><code class="xref cmake cmake-module docutils literal notranslate"><span class="pre">CPack</span></code></a> generator thus
it uses the <code class="xref cmake cmake-variable docutils literal notranslate"><span class="pre">CPACK_XXX</span></code> variables used by <span class="target" id="index-2-module:CPack"></span><a class="reference internal" href="../module/CPack.html#module:CPack" title="CPack"><code class="xref cmake cmake-module docutils literal notranslate"><span class="pre">CPack</span></code></a>.</p>
<p>The CPack NuGet generator has specific features which are controlled by the
specifics <code class="xref cmake cmake-variable docutils literal notranslate"><span class="pre">CPACK_NUGET_XXX</span></code> variables. In the &quot;one per group&quot; mode
(see <span class="target" id="index-0-variable:CPACK_COMPONENTS_GROUPING"></span><a class="reference internal" href="../module/CPackComponent.html#variable:CPACK_COMPONENTS_GROUPING" title="CPACK_COMPONENTS_GROUPING"><code class="xref cmake cmake-variable docutils literal notranslate"><span class="pre">CPACK_COMPONENTS_GROUPING</span></code></a>), <code class="docutils literal notranslate"><span class="pre">&lt;compName&gt;</span></code> placeholder
in the variables below would contain a group name (uppercased and turned into
a &quot;C&quot; identifier).</p>
<p>List of CPack NuGet generator specific variables:</p>
<dl class="cmake variable">
<dt class="sig sig-object cmake" id="variable:CPACK_NUGET_COMPONENT_INSTALL">
<span class="sig-name descname"><span class="pre">CPACK_NUGET_COMPONENT_INSTALL</span></span><a class="headerlink" href="#variable:CPACK_NUGET_COMPONENT_INSTALL" title="Permalink to this definition">¶</a></dt>
<dd><p>Enable component packaging for CPack NuGet generator</p>
<dl class="field-list simple">
<dt class="field-odd">Mandatory<span class="colon">:</span></dt>
<dd class="field-odd"><p>No</p>
</dd>
<dt class="field-even">Default<span class="colon">:</span></dt>
<dd class="field-even"><p><code class="docutils literal notranslate"><span class="pre">OFF</span></code></p>
</dd>
</dl>
</dd></dl>

<dl class="cmake variable">
<dt class="sig sig-object cmake" id="variable:CPACK_NUGET_PACKAGE_NAME">
<span class="sig-name descname"><span class="pre">CPACK_NUGET_PACKAGE_NAME</span></span><a class="headerlink" href="#variable:CPACK_NUGET_PACKAGE_NAME" title="Permalink to this definition">¶</a></dt>
<dt class="sig sig-object cmake" id="variable:CPACK_NUGET_&lt;compName&gt;_PACKAGE_NAME">
<span class="sig-name descname"><span class="pre">CPACK_NUGET_&lt;compName&gt;_PACKAGE_NAME</span></span><a class="headerlink" href="#variable:CPACK_NUGET_<compName>_PACKAGE_NAME" title="Permalink to this definition">¶</a></dt>
<dd><p>The NUGET package name. <code class="docutils literal notranslate"><span class="pre">CPACK_NUGET_PACKAGE_NAME</span></code> is used as the
package <code class="docutils literal notranslate"><span class="pre">id</span></code> on <a class="reference external" href="https://www.nuget.org">nuget.org</a></p>
<dl class="field-list simple">
<dt class="field-odd">Mandatory<span class="colon">:</span></dt>
<dd class="field-odd"><p>Yes</p>
</dd>
<dt class="field-even">Default<span class="colon">:</span></dt>
<dd class="field-even"><p><span class="target" id="index-0-variable:CPACK_PACKAGE_NAME"></span><a class="reference internal" href="../module/CPack.html#variable:CPACK_PACKAGE_NAME" title="CPACK_PACKAGE_NAME"><code class="xref cmake cmake-variable docutils literal notranslate"><span class="pre">CPACK_PACKAGE_NAME</span></code></a></p>
</dd>
</dl>
</dd></dl>

<dl class="cmake variable">
<dt class="sig sig-object cmake" id="variable:CPACK_NUGET_PACKAGE_VERSION">
<span class="sig-name descname"><span class="pre">CPACK_NUGET_PACKAGE_VERSION</span></span><a class="headerlink" href="#variable:CPACK_NUGET_PACKAGE_VERSION" title="Permalink to this definition">¶</a></dt>
<dt class="sig sig-object cmake" id="variable:CPACK_NUGET_&lt;compName&gt;_PACKAGE_VERSION">
<span class="sig-name descname"><span class="pre">CPACK_NUGET_&lt;compName&gt;_PACKAGE_VERSION</span></span><a class="headerlink" href="#variable:CPACK_NUGET_<compName>_PACKAGE_VERSION" title="Permalink to this definition">¶</a></dt>
<dd><p>The NuGet package version.</p>
<dl class="field-list simple">
<dt class="field-odd">Mandatory<span class="colon">:</span></dt>
<dd class="field-odd"><p>Yes</p>
</dd>
<dt class="field-even">Default<span class="colon">:</span></dt>
<dd class="field-even"><p><span class="target" id="index-0-variable:CPACK_PACKAGE_VERSION"></span><a class="reference internal" href="../module/CPack.html#variable:CPACK_PACKAGE_VERSION" title="CPACK_PACKAGE_VERSION"><code class="xref cmake cmake-variable docutils literal notranslate"><span class="pre">CPACK_PACKAGE_VERSION</span></code></a></p>
</dd>
</dl>
</dd></dl>

<dl class="cmake variable">
<dt class="sig sig-object cmake" id="variable:CPACK_NUGET_PACKAGE_DESCRIPTION">
<span class="sig-name descname"><span class="pre">CPACK_NUGET_PACKAGE_DESCRIPTION</span></span><a class="headerlink" href="#variable:CPACK_NUGET_PACKAGE_DESCRIPTION" title="Permalink to this definition">¶</a></dt>
<dt class="sig sig-object cmake" id="variable:CPACK_NUGET_&lt;compName&gt;_PACKAGE_DESCRIPTION">
<span class="sig-name descname"><span class="pre">CPACK_NUGET_&lt;compName&gt;_PACKAGE_DESCRIPTION</span></span><a class="headerlink" href="#variable:CPACK_NUGET_<compName>_PACKAGE_DESCRIPTION" title="Permalink to this definition">¶</a></dt>
<dd><p>A long description of the package for UI display.</p>
<dl class="field-list simple">
<dt class="field-odd">Mandatory<span class="colon">:</span></dt>
<dd class="field-odd"><p>Yes</p>
</dd>
<dt class="field-even">Default<span class="colon">:</span></dt>
<dd class="field-even"><ul class="simple">
<li><p><span class="target" id="index-0-variable:CPACK_COMPONENT_&lt;compName&gt;_DESCRIPTION"></span><a class="reference internal" href="../module/CPackComponent.html#variable:CPACK_COMPONENT_&lt;compName&gt;_DESCRIPTION" title="CPACK_COMPONENT_&lt;compName&gt;_DESCRIPTION"><code class="xref cmake cmake-variable docutils literal notranslate"><span class="pre">CPACK_COMPONENT_&lt;compName&gt;_DESCRIPTION</span></code></a>,</p></li>
<li><p><code class="xref cmake cmake-variable docutils literal notranslate"><span class="pre">CPACK_COMPONENT_GROUP_&lt;groupName&gt;_DESCRIPTION</span></code>,</p></li>
<li><p><span class="target" id="index-0-variable:CPACK_PACKAGE_DESCRIPTION"></span><a class="reference internal" href="../module/CPack.html#variable:CPACK_PACKAGE_DESCRIPTION" title="CPACK_PACKAGE_DESCRIPTION"><code class="xref cmake cmake-variable docutils literal notranslate"><span class="pre">CPACK_PACKAGE_DESCRIPTION</span></code></a></p></li>
</ul>
</dd>
</dl>
</dd></dl>

<dl class="cmake variable">
<dt class="sig sig-object cmake" id="variable:CPACK_NUGET_PACKAGE_AUTHORS">
<span class="sig-name descname"><span class="pre">CPACK_NUGET_PACKAGE_AUTHORS</span></span><a class="headerlink" href="#variable:CPACK_NUGET_PACKAGE_AUTHORS" title="Permalink to this definition">¶</a></dt>
<dt class="sig sig-object cmake" id="variable:CPACK_NUGET_&lt;compName&gt;_PACKAGE_AUTHORS">
<span class="sig-name descname"><span class="pre">CPACK_NUGET_&lt;compName&gt;_PACKAGE_AUTHORS</span></span><a class="headerlink" href="#variable:CPACK_NUGET_<compName>_PACKAGE_AUTHORS" title="Permalink to this definition">¶</a></dt>
<dd><p>A comma-separated list of packages authors, matching the profile names
on <a class="reference external" href="https://www.nuget.org">nuget.org</a>. These are displayed in the NuGet Gallery on
<a class="reference external" href="https://www.nuget.org">nuget.org</a> and are used to cross-reference packages by the same
authors.</p>
<dl class="field-list simple">
<dt class="field-odd">Mandatory<span class="colon">:</span></dt>
<dd class="field-odd"><p>Yes</p>
</dd>
<dt class="field-even">Default<span class="colon">:</span></dt>
<dd class="field-even"><p><span class="target" id="index-0-variable:CPACK_PACKAGE_VENDOR"></span><a class="reference internal" href="../module/CPack.html#variable:CPACK_PACKAGE_VENDOR" title="CPACK_PACKAGE_VENDOR"><code class="xref cmake cmake-variable docutils literal notranslate"><span class="pre">CPACK_PACKAGE_VENDOR</span></code></a></p>
</dd>
</dl>
</dd></dl>

<dl class="cmake variable">
<dt class="sig sig-object cmake" id="variable:CPACK_NUGET_PACKAGE_TITLE">
<span class="sig-name descname"><span class="pre">CPACK_NUGET_PACKAGE_TITLE</span></span><a class="headerlink" href="#variable:CPACK_NUGET_PACKAGE_TITLE" title="Permalink to this definition">¶</a></dt>
<dt class="sig sig-object cmake" id="variable:CPACK_NUGET_&lt;compName&gt;_PACKAGE_TITLE">
<span class="sig-name descname"><span class="pre">CPACK_NUGET_&lt;compName&gt;_PACKAGE_TITLE</span></span><a class="headerlink" href="#variable:CPACK_NUGET_<compName>_PACKAGE_TITLE" title="Permalink to this definition">¶</a></dt>
<dd><p>A human-friendly title of the package, typically used in UI displays
as on <a class="reference external" href="https://www.nuget.org">nuget.org</a> and the Package Manager in Visual Studio. If not
specified, the package ID is used.</p>
<dl class="field-list simple">
<dt class="field-odd">Mandatory<span class="colon">:</span></dt>
<dd class="field-odd"><p>No</p>
</dd>
<dt class="field-even">Default<span class="colon">:</span></dt>
<dd class="field-even"><ul class="simple">
<li><p><span class="target" id="index-0-variable:CPACK_COMPONENT_&lt;compName&gt;_DISPLAY_NAME"></span><a class="reference internal" href="../module/CPackComponent.html#variable:CPACK_COMPONENT_&lt;compName&gt;_DISPLAY_NAME" title="CPACK_COMPONENT_&lt;compName&gt;_DISPLAY_NAME"><code class="xref cmake cmake-variable docutils literal notranslate"><span class="pre">CPACK_COMPONENT_&lt;compName&gt;_DISPLAY_NAME</span></code></a>,</p></li>
<li><p><code class="xref cmake cmake-variable docutils literal notranslate"><span class="pre">CPACK_COMPONENT_GROUP_&lt;groupName&gt;_DISPLAY_NAME</span></code></p></li>
</ul>
</dd>
</dl>
</dd></dl>

<dl class="cmake variable">
<dt class="sig sig-object cmake" id="variable:CPACK_NUGET_PACKAGE_OWNERS">
<span class="sig-name descname"><span class="pre">CPACK_NUGET_PACKAGE_OWNERS</span></span><a class="headerlink" href="#variable:CPACK_NUGET_PACKAGE_OWNERS" title="Permalink to this definition">¶</a></dt>
<dt class="sig sig-object cmake" id="variable:CPACK_NUGET_&lt;compName&gt;_PACKAGE_OWNERS">
<span class="sig-name descname"><span class="pre">CPACK_NUGET_&lt;compName&gt;_PACKAGE_OWNERS</span></span><a class="headerlink" href="#variable:CPACK_NUGET_<compName>_PACKAGE_OWNERS" title="Permalink to this definition">¶</a></dt>
<dd><p>A comma-separated list of the package creators using profile names
on <a class="reference external" href="https://www.nuget.org">nuget.org</a>. This is often the same list as in authors,
and is ignored when uploading the package to <a class="reference external" href="https://www.nuget.org">nuget.org</a>.</p>
<dl class="field-list simple">
<dt class="field-odd">Mandatory<span class="colon">:</span></dt>
<dd class="field-odd"><p>No</p>
</dd>
<dt class="field-even">Default<span class="colon">:</span></dt>
<dd class="field-even"><p>None</p>
</dd>
</dl>
</dd></dl>

<dl class="cmake variable">
<dt class="sig sig-object cmake" id="variable:CPACK_NUGET_PACKAGE_HOMEPAGE_URL">
<span class="sig-name descname"><span class="pre">CPACK_NUGET_PACKAGE_HOMEPAGE_URL</span></span><a class="headerlink" href="#variable:CPACK_NUGET_PACKAGE_HOMEPAGE_URL" title="Permalink to this definition">¶</a></dt>
<dt class="sig sig-object cmake" id="variable:CPACK_NUGET_&lt;compName&gt;_PACKAGE_HOMEPAGE_URL">
<span class="sig-name descname"><span class="pre">CPACK_NUGET_&lt;compName&gt;_PACKAGE_HOMEPAGE_URL</span></span><a class="headerlink" href="#variable:CPACK_NUGET_<compName>_PACKAGE_HOMEPAGE_URL" title="Permalink to this definition">¶</a></dt>
<dd><p>An URL for the package's home page, often shown in UI displays as well
as <a class="reference external" href="https://www.nuget.org">nuget.org</a>.</p>
<dl class="field-list simple">
<dt class="field-odd">Mandatory<span class="colon">:</span></dt>
<dd class="field-odd"><p>No</p>
</dd>
<dt class="field-even">Default<span class="colon">:</span></dt>
<dd class="field-even"><p><span class="target" id="index-0-variable:CPACK_PACKAGE_HOMEPAGE_URL"></span><a class="reference internal" href="../module/CPack.html#variable:CPACK_PACKAGE_HOMEPAGE_URL" title="CPACK_PACKAGE_HOMEPAGE_URL"><code class="xref cmake cmake-variable docutils literal notranslate"><span class="pre">CPACK_PACKAGE_HOMEPAGE_URL</span></code></a></p>
</dd>
</dl>
</dd></dl>

<dl class="cmake variable">
<dt class="sig sig-object cmake" id="variable:CPACK_NUGET_PACKAGE_LICENSEURL">
<span class="sig-name descname"><span class="pre">CPACK_NUGET_PACKAGE_LICENSEURL</span></span><a class="headerlink" href="#variable:CPACK_NUGET_PACKAGE_LICENSEURL" title="Permalink to this definition">¶</a></dt>
<dt class="sig sig-object cmake" id="variable:CPACK_NUGET_&lt;compName&gt;_PACKAGE_LICENSEURL">
<span class="sig-name descname"><span class="pre">CPACK_NUGET_&lt;compName&gt;_PACKAGE_LICENSEURL</span></span><a class="headerlink" href="#variable:CPACK_NUGET_<compName>_PACKAGE_LICENSEURL" title="Permalink to this definition">¶</a></dt>
<dd><div class="deprecated">
<p><span class="versionmodified deprecated">Deprecated since version 3.20: </span>Use a local license file
(<span class="target" id="index-0-variable:CPACK_NUGET_PACKAGE_LICENSE_FILE_NAME"></span><a class="reference internal" href="#variable:CPACK_NUGET_PACKAGE_LICENSE_FILE_NAME" title="CPACK_NUGET_PACKAGE_LICENSE_FILE_NAME"><code class="xref cmake cmake-variable docutils literal notranslate"><span class="pre">CPACK_NUGET_PACKAGE_LICENSE_FILE_NAME</span></code></a>)
or a <a class="reference external" href="https://spdx.org/licenses">SPDX license identifier</a>
(<span class="target" id="index-0-variable:CPACK_NUGET_PACKAGE_LICENSE_EXPRESSION"></span><a class="reference internal" href="#variable:CPACK_NUGET_PACKAGE_LICENSE_EXPRESSION" title="CPACK_NUGET_PACKAGE_LICENSE_EXPRESSION"><code class="xref cmake cmake-variable docutils literal notranslate"><span class="pre">CPACK_NUGET_PACKAGE_LICENSE_EXPRESSION</span></code></a>) instead.</p>
</div>
<p>An URL for the package's license, often shown in UI displays as well
as on <a class="reference external" href="https://www.nuget.org">nuget.org</a>.</p>
<dl class="field-list simple">
<dt class="field-odd">Mandatory<span class="colon">:</span></dt>
<dd class="field-odd"><p>No</p>
</dd>
<dt class="field-even">Default<span class="colon">:</span></dt>
<dd class="field-even"><p>None</p>
</dd>
</dl>
</dd></dl>

<dl class="cmake variable">
<dt class="sig sig-object cmake" id="variable:CPACK_NUGET_PACKAGE_LICENSE_EXPRESSION">
<span class="sig-name descname"><span class="pre">CPACK_NUGET_PACKAGE_LICENSE_EXPRESSION</span></span><a class="headerlink" href="#variable:CPACK_NUGET_PACKAGE_LICENSE_EXPRESSION" title="Permalink to this definition">¶</a></dt>
<dt class="sig sig-object cmake" id="variable:CPACK_NUGET_&lt;compName&gt;_PACKAGE_LICENSE_EXPRESSION">
<span class="sig-name descname"><span class="pre">CPACK_NUGET_&lt;compName&gt;_PACKAGE_LICENSE_EXPRESSION</span></span><a class="headerlink" href="#variable:CPACK_NUGET_<compName>_PACKAGE_LICENSE_EXPRESSION" title="Permalink to this definition">¶</a></dt>
<dd><div class="versionadded">
<p><span class="versionmodified added">New in version 3.20.</span></p>
</div>
<p>A Software Package Data Exchange <a class="reference external" href="https://spdx.org/licenses">SPDX license identifier</a> such as
<code class="docutils literal notranslate"><span class="pre">MIT</span></code>, <code class="docutils literal notranslate"><span class="pre">BSD-3-Clause</span></code>, or <code class="docutils literal notranslate"><span class="pre">LGPL-3.0-or-later</span></code>. In the case of a
choice of licenses or more complex restrictions, compound license
expressions may be formed using boolean operators, for example
<code class="docutils literal notranslate"><span class="pre">MIT</span> <span class="pre">OR</span> <span class="pre">BSD-3-Clause</span></code>.  See the <a class="reference external" href="https://spdx.github.io/spdx-spec/v2.3/SPDX-license-expressions">SPDX specification</a> for guidance
on forming complex license expressions.</p>
<p>If <span class="target" id="index-1-variable:CPACK_NUGET_PACKAGE_LICENSE_FILE_NAME"></span><a class="reference internal" href="#variable:CPACK_NUGET_PACKAGE_LICENSE_FILE_NAME" title="CPACK_NUGET_PACKAGE_LICENSE_FILE_NAME"><code class="xref cmake cmake-variable docutils literal notranslate"><span class="pre">CPACK_NUGET_PACKAGE_LICENSE_FILE_NAME</span></code></a> is specified,
<code class="xref cmake cmake-variable docutils literal notranslate"><span class="pre">CPACK_NUGET_PACKAGE_LICENSE_EXPRESSION</span></code> is ignored.</p>
<dl class="field-list simple">
<dt class="field-odd">Mandatory<span class="colon">:</span></dt>
<dd class="field-odd"><p>No</p>
</dd>
<dt class="field-even">Default<span class="colon">:</span></dt>
<dd class="field-even"><p>None</p>
</dd>
</dl>
</dd></dl>

<dl class="cmake variable">
<dt class="sig sig-object cmake" id="variable:CPACK_NUGET_PACKAGE_LICENSE_FILE_NAME">
<span class="sig-name descname"><span class="pre">CPACK_NUGET_PACKAGE_LICENSE_FILE_NAME</span></span><a class="headerlink" href="#variable:CPACK_NUGET_PACKAGE_LICENSE_FILE_NAME" title="Permalink to this definition">¶</a></dt>
<dt class="sig sig-object cmake" id="variable:CPACK_NUGET_&lt;compName&gt;_PACKAGE_LICENSE_FILE_NAME">
<span class="sig-name descname"><span class="pre">CPACK_NUGET_&lt;compName&gt;_PACKAGE_LICENSE_FILE_NAME</span></span><a class="headerlink" href="#variable:CPACK_NUGET_<compName>_PACKAGE_LICENSE_FILE_NAME" title="Permalink to this definition">¶</a></dt>
<dd><p>The package's license file in <code class="file docutils literal notranslate"><span class="pre">.txt</span></code> or <code class="file docutils literal notranslate"><span class="pre">.md</span></code> format.</p>
<p>If <code class="xref cmake cmake-variable docutils literal notranslate"><span class="pre">CPACK_NUGET_PACKAGE_LICENSE_FILE_NAME</span></code> is specified,
<code class="xref cmake cmake-variable docutils literal notranslate"><span class="pre">CPACK_NUGET_PACKAGE_LICENSE_EXPRESSION</span></code> is ignored.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.20.</span></p>
</div>
<dl class="field-list simple">
<dt class="field-odd">Mandatory<span class="colon">:</span></dt>
<dd class="field-odd"><p>No</p>
</dd>
<dt class="field-even">Default<span class="colon">:</span></dt>
<dd class="field-even"><p>None</p>
</dd>
</dl>
</dd></dl>

<dl class="cmake variable">
<dt class="sig sig-object cmake" id="variable:CPACK_NUGET_PACKAGE_ICONURL">
<span class="sig-name descname"><span class="pre">CPACK_NUGET_PACKAGE_ICONURL</span></span><a class="headerlink" href="#variable:CPACK_NUGET_PACKAGE_ICONURL" title="Permalink to this definition">¶</a></dt>
<dt class="sig sig-object cmake" id="variable:CPACK_NUGET_&lt;compName&gt;_PACKAGE_ICONURL">
<span class="sig-name descname"><span class="pre">CPACK_NUGET_&lt;compName&gt;_PACKAGE_ICONURL</span></span><a class="headerlink" href="#variable:CPACK_NUGET_<compName>_PACKAGE_ICONURL" title="Permalink to this definition">¶</a></dt>
<dd><div class="deprecated">
<p><span class="versionmodified deprecated">Deprecated since version 3.20: </span>Use a local icon file (<span class="target" id="index-0-variable:CPACK_NUGET_PACKAGE_ICON"></span><a class="reference internal" href="#variable:CPACK_NUGET_PACKAGE_ICON" title="CPACK_NUGET_PACKAGE_ICON"><code class="xref cmake cmake-variable docutils literal notranslate"><span class="pre">CPACK_NUGET_PACKAGE_ICON</span></code></a>) instead.</p>
</div>
<p>An URL for a 64x64 image with transparency background to use as the
icon for the package in UI display.</p>
<dl class="field-list simple">
<dt class="field-odd">Mandatory<span class="colon">:</span></dt>
<dd class="field-odd"><p>No</p>
</dd>
<dt class="field-even">Default<span class="colon">:</span></dt>
<dd class="field-even"><p>None</p>
</dd>
</dl>
</dd></dl>

<dl class="cmake variable">
<dt class="sig sig-object cmake" id="variable:CPACK_NUGET_PACKAGE_REQUIRE_LICENSE_ACCEPTANCE">
<span class="sig-name descname"><span class="pre">CPACK_NUGET_PACKAGE_REQUIRE_LICENSE_ACCEPTANCE</span></span><a class="headerlink" href="#variable:CPACK_NUGET_PACKAGE_REQUIRE_LICENSE_ACCEPTANCE" title="Permalink to this definition">¶</a></dt>
<dd><p>When set to a true value, the user will be prompted to accept the license
before installing the package.</p>
<dl class="field-list simple">
<dt class="field-odd">Mandatory<span class="colon">:</span></dt>
<dd class="field-odd"><p>No</p>
</dd>
<dt class="field-even">Default<span class="colon">:</span></dt>
<dd class="field-even"><p>None</p>
</dd>
</dl>
</dd></dl>

<dl class="cmake variable">
<dt class="sig sig-object cmake" id="variable:CPACK_NUGET_PACKAGE_ICON">
<span class="sig-name descname"><span class="pre">CPACK_NUGET_PACKAGE_ICON</span></span><a class="headerlink" href="#variable:CPACK_NUGET_PACKAGE_ICON" title="Permalink to this definition">¶</a></dt>
<dt class="sig sig-object cmake" id="variable:CPACK_NUGET_&lt;compName&gt;_PACKAGE_ICON">
<span class="sig-name descname"><span class="pre">CPACK_NUGET_&lt;compName&gt;_PACKAGE_ICON</span></span><a class="headerlink" href="#variable:CPACK_NUGET_<compName>_PACKAGE_ICON" title="Permalink to this definition">¶</a></dt>
<dd><div class="versionadded">
<p><span class="versionmodified added">New in version 3.20.</span></p>
</div>
<p>The filename of a 64x64 image with transparency background to use as the
icon for the package in UI display.</p>
<dl class="field-list simple">
<dt class="field-odd">Mandatory<span class="colon">:</span></dt>
<dd class="field-odd"><p>No</p>
</dd>
<dt class="field-even">Default<span class="colon">:</span></dt>
<dd class="field-even"><p>None</p>
</dd>
</dl>
</dd></dl>

<dl class="cmake variable">
<dt class="sig sig-object cmake" id="variable:CPACK_NUGET_PACKAGE_DESCRIPTION_SUMMARY">
<span class="sig-name descname"><span class="pre">CPACK_NUGET_PACKAGE_DESCRIPTION_SUMMARY</span></span><a class="headerlink" href="#variable:CPACK_NUGET_PACKAGE_DESCRIPTION_SUMMARY" title="Permalink to this definition">¶</a></dt>
<dt class="sig sig-object cmake" id="variable:CPACK_NUGET_&lt;compName&gt;_PACKAGE_DESCRIPTION_SUMMARY">
<span class="sig-name descname"><span class="pre">CPACK_NUGET_&lt;compName&gt;_PACKAGE_DESCRIPTION_SUMMARY</span></span><a class="headerlink" href="#variable:CPACK_NUGET_<compName>_PACKAGE_DESCRIPTION_SUMMARY" title="Permalink to this definition">¶</a></dt>
<dd><p>A short description of the package for UI display. If omitted, a
truncated version of description is used.</p>
<dl class="field-list simple">
<dt class="field-odd">Mandatory<span class="colon">:</span></dt>
<dd class="field-odd"><p>No</p>
</dd>
<dt class="field-even">Default<span class="colon">:</span></dt>
<dd class="field-even"><p><span class="target" id="index-0-variable:CPACK_PACKAGE_DESCRIPTION_SUMMARY"></span><a class="reference internal" href="../module/CPack.html#variable:CPACK_PACKAGE_DESCRIPTION_SUMMARY" title="CPACK_PACKAGE_DESCRIPTION_SUMMARY"><code class="xref cmake cmake-variable docutils literal notranslate"><span class="pre">CPACK_PACKAGE_DESCRIPTION_SUMMARY</span></code></a></p>
</dd>
</dl>
</dd></dl>

<dl class="cmake variable">
<dt class="sig sig-object cmake" id="variable:CPACK_NUGET_PACKAGE_RELEASE_NOTES">
<span class="sig-name descname"><span class="pre">CPACK_NUGET_PACKAGE_RELEASE_NOTES</span></span><a class="headerlink" href="#variable:CPACK_NUGET_PACKAGE_RELEASE_NOTES" title="Permalink to this definition">¶</a></dt>
<dt class="sig sig-object cmake" id="variable:CPACK_NUGET_&lt;compName&gt;_PACKAGE_RELEASE_NOTES">
<span class="sig-name descname"><span class="pre">CPACK_NUGET_&lt;compName&gt;_PACKAGE_RELEASE_NOTES</span></span><a class="headerlink" href="#variable:CPACK_NUGET_<compName>_PACKAGE_RELEASE_NOTES" title="Permalink to this definition">¶</a></dt>
<dd><p>A description of the changes made in this release of the package,
often used in UI like the Updates tab of the Visual Studio Package
Manager in place of the package description.</p>
<dl class="field-list simple">
<dt class="field-odd">Mandatory<span class="colon">:</span></dt>
<dd class="field-odd"><p>No</p>
</dd>
<dt class="field-even">Default<span class="colon">:</span></dt>
<dd class="field-even"><p>None</p>
</dd>
</dl>
</dd></dl>

<dl class="cmake variable">
<dt class="sig sig-object cmake" id="variable:CPACK_NUGET_PACKAGE_COPYRIGHT">
<span class="sig-name descname"><span class="pre">CPACK_NUGET_PACKAGE_COPYRIGHT</span></span><a class="headerlink" href="#variable:CPACK_NUGET_PACKAGE_COPYRIGHT" title="Permalink to this definition">¶</a></dt>
<dt class="sig sig-object cmake" id="variable:CPACK_NUGET_&lt;compName&gt;_PACKAGE_COPYRIGHT">
<span class="sig-name descname"><span class="pre">CPACK_NUGET_&lt;compName&gt;_PACKAGE_COPYRIGHT</span></span><a class="headerlink" href="#variable:CPACK_NUGET_<compName>_PACKAGE_COPYRIGHT" title="Permalink to this definition">¶</a></dt>
<dd><p>Copyright details for the package.</p>
<dl class="field-list simple">
<dt class="field-odd">Mandatory<span class="colon">:</span></dt>
<dd class="field-odd"><p>No</p>
</dd>
<dt class="field-even">Default<span class="colon">:</span></dt>
<dd class="field-even"><p>None</p>
</dd>
</dl>
</dd></dl>

<dl class="cmake variable">
<dt class="sig sig-object cmake" id="variable:CPACK_NUGET_PACKAGE_LANGUAGE">
<span class="sig-name descname"><span class="pre">CPACK_NUGET_PACKAGE_LANGUAGE</span></span><a class="headerlink" href="#variable:CPACK_NUGET_PACKAGE_LANGUAGE" title="Permalink to this definition">¶</a></dt>
<dt class="sig sig-object cmake" id="variable:CPACK_NUGET_&lt;compName&gt;_PACKAGE_LANGUAGE">
<span class="sig-name descname"><span class="pre">CPACK_NUGET_&lt;compName&gt;_PACKAGE_LANGUAGE</span></span><a class="headerlink" href="#variable:CPACK_NUGET_<compName>_PACKAGE_LANGUAGE" title="Permalink to this definition">¶</a></dt>
<dd><div class="versionadded">
<p><span class="versionmodified added">New in version 3.20.</span></p>
</div>
<p>Locale specifier for the package, for example <code class="docutils literal notranslate"><span class="pre">en_CA</span></code>.</p>
<dl class="field-list simple">
<dt class="field-odd">Mandatory<span class="colon">:</span></dt>
<dd class="field-odd"><p>No</p>
</dd>
<dt class="field-even">Default<span class="colon">:</span></dt>
<dd class="field-even"><p>None</p>
</dd>
</dl>
</dd></dl>

<dl class="cmake variable">
<dt class="sig sig-object cmake" id="variable:CPACK_NUGET_PACKAGE_TAGS">
<span class="sig-name descname"><span class="pre">CPACK_NUGET_PACKAGE_TAGS</span></span><a class="headerlink" href="#variable:CPACK_NUGET_PACKAGE_TAGS" title="Permalink to this definition">¶</a></dt>
<dt class="sig sig-object cmake" id="variable:CPACK_NUGET_&lt;compName&gt;_PACKAGE_TAGS">
<span class="sig-name descname"><span class="pre">CPACK_NUGET_&lt;compName&gt;_PACKAGE_TAGS</span></span><a class="headerlink" href="#variable:CPACK_NUGET_<compName>_PACKAGE_TAGS" title="Permalink to this definition">¶</a></dt>
<dd><p>A space-delimited list of tags and keywords that describe the
package and aid discoverability of packages through search and
filtering.</p>
<dl class="field-list simple">
<dt class="field-odd">Mandatory<span class="colon">:</span></dt>
<dd class="field-odd"><p>No</p>
</dd>
<dt class="field-even">Default<span class="colon">:</span></dt>
<dd class="field-even"><p>None</p>
</dd>
</dl>
</dd></dl>

<dl class="cmake variable">
<dt class="sig sig-object cmake" id="variable:CPACK_NUGET_PACKAGE_DEPENDENCIES">
<span class="sig-name descname"><span class="pre">CPACK_NUGET_PACKAGE_DEPENDENCIES</span></span><a class="headerlink" href="#variable:CPACK_NUGET_PACKAGE_DEPENDENCIES" title="Permalink to this definition">¶</a></dt>
<dt class="sig sig-object cmake" id="variable:CPACK_NUGET_&lt;compName&gt;_PACKAGE_DEPENDENCIES">
<span class="sig-name descname"><span class="pre">CPACK_NUGET_&lt;compName&gt;_PACKAGE_DEPENDENCIES</span></span><a class="headerlink" href="#variable:CPACK_NUGET_<compName>_PACKAGE_DEPENDENCIES" title="Permalink to this definition">¶</a></dt>
<dd><p>A list of package dependencies.</p>
<dl class="field-list simple">
<dt class="field-odd">Mandatory<span class="colon">:</span></dt>
<dd class="field-odd"><p>No</p>
</dd>
<dt class="field-even">Default<span class="colon">:</span></dt>
<dd class="field-even"><p>None</p>
</dd>
</dl>
</dd></dl>

<dl class="cmake variable">
<dt class="sig sig-object cmake" id="variable:CPACK_NUGET_PACKAGE_DEPENDENCIES_&lt;dependency&gt;_VERSION">
<span class="sig-name descname"><span class="pre">CPACK_NUGET_PACKAGE_DEPENDENCIES_&lt;dependency&gt;_VERSION</span></span><a class="headerlink" href="#variable:CPACK_NUGET_PACKAGE_DEPENDENCIES_<dependency>_VERSION" title="Permalink to this definition">¶</a></dt>
<dt class="sig sig-object cmake" id="variable:CPACK_NUGET_&lt;compName&gt;_PACKAGE_DEPENDENCIES_&lt;dependency&gt;_VERSION">
<span class="sig-name descname"><span class="pre">CPACK_NUGET_&lt;compName&gt;_PACKAGE_DEPENDENCIES_&lt;dependency&gt;_VERSION</span></span><a class="headerlink" href="#variable:CPACK_NUGET_<compName>_PACKAGE_DEPENDENCIES_<dependency>_VERSION" title="Permalink to this definition">¶</a></dt>
<dd><p>A <a class="reference external" href="https://learn.microsoft.com/en-us/nuget/concepts/package-versioning#version-ranges">version specification</a> for the particular dependency, where
<code class="docutils literal notranslate"><span class="pre">&lt;dependency&gt;</span></code> is an item of the dependency list (see above)
transformed with <span class="target" id="index-0-command:string"></span><a class="reference internal" href="../command/string.html#make-c-identifier" title="string(make_c_identifier)"><code class="xref cmake cmake-command docutils literal notranslate"><span class="pre">string(MAKE_C_IDENTIFIER)</span></code></a> command.</p>
<dl class="field-list simple">
<dt class="field-odd">Mandatory<span class="colon">:</span></dt>
<dd class="field-odd"><p>No</p>
</dd>
<dt class="field-even">Default<span class="colon">:</span></dt>
<dd class="field-even"><p>None</p>
</dd>
</dl>
</dd></dl>

<dl class="cmake variable">
<dt class="sig sig-object cmake" id="variable:CPACK_NUGET_PACKAGE_DEBUG">
<span class="sig-name descname"><span class="pre">CPACK_NUGET_PACKAGE_DEBUG</span></span><a class="headerlink" href="#variable:CPACK_NUGET_PACKAGE_DEBUG" title="Permalink to this definition">¶</a></dt>
<dd><p>Enable debug messages while executing CPack NuGet generator.</p>
<dl class="field-list simple">
<dt class="field-odd">Mandatory<span class="colon">:</span></dt>
<dd class="field-odd"><p>No</p>
</dd>
<dt class="field-even">Default<span class="colon">:</span></dt>
<dd class="field-even"><p><code class="docutils literal notranslate"><span class="pre">OFF</span></code></p>
</dd>
</dl>
</dd></dl>

</section>
</section>


            <div class="clearer"></div>
          </div>
        </div>
      </div>
      <div class="sphinxsidebar" role="navigation" aria-label="main navigation">
        <div class="sphinxsidebarwrapper">
  <div>
    <h3><a href="../index.html">Table of Contents</a></h3>
    <ul>
<li><a class="reference internal" href="#">CPack NuGet Generator</a><ul>
<li><a class="reference internal" href="#variables-specific-to-cpack-nuget-generator">Variables specific to CPack NuGet generator</a></li>
</ul>
</li>
</ul>

  </div>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="nsis.html"
                          title="previous chapter">CPack NSIS Generator</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="packagemaker.html"
                          title="next chapter">CPack PackageMaker Generator</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../_sources/cpack_gen/nuget.rst.txt"
            rel="nofollow">Show Source</a></li>
    </ul>
   </div>
<div id="searchbox" style="display: none" role="search">
  <h3 id="searchlabel">Quick search</h3>
    <div class="searchformwrapper">
    <form class="search" action="../search.html" method="get">
      <input type="text" name="q" aria-labelledby="searchlabel" autocomplete="off" autocorrect="off" autocapitalize="off" spellcheck="false"/>
      <input type="submit" value="Go" />
    </form>
    </div>
</div>
<script>document.getElementById('searchbox').style.display = "block"</script>
        </div>
      </div>
      <div class="clearer"></div>
    </div>
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             >index</a></li>
        <li class="right" >
          <a href="packagemaker.html" title="CPack PackageMaker Generator"
             >next</a> |</li>
        <li class="right" >
          <a href="nsis.html" title="CPack NSIS Generator"
             >previous</a> |</li>
  <li>
    <img src="../_static/cmake-logo-16.png" alt=""
         style="vertical-align: middle; margin-top: -2px" />
  </li>
  <li>
    <a href="https://cmake.org/">CMake</a> &#187;
  </li>
  <li>
    <a href="../index.html">3.28.1 Documentation</a> &#187;
  </li>

          <li class="nav-item nav-item-1"><a href="../manual/cpack-generators.7.html" >cpack-generators(7)</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href="">CPack NuGet Generator</a></li> 
      </ul>
    </div>

    <div class="footer" role="contentinfo">
        &#169; Copyright 2000-2023 Kitware, Inc. and Contributors.
      Created using <a href="https://www.sphinx-doc.org/">Sphinx</a> 6.2.1.
    </div>
  </body>
</html>