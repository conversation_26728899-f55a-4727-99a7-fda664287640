The libarchive distribution as a whole is Copyright by <PERSON>
and is subject to the copyright notice reproduced at the bottom of
this file.

Each individual file in this distribution should have a clear
copyright/licensing statement at the beginning of the file.  If any do
not, please let me know and I will rectify it.  The following is
intended to summarize the copyright status of the individual files;
the actual statements in the files are controlling.

* Except as listed below, all C sources (including .c and .h files)
  and documentation files are subject to the copyright notice reproduced
  at the bottom of this file.

* The following source files are also subject in whole or in part to
  a 3-clause UC Regents copyright; please read the individual source
  files for details:
   libarchive/archive_read_support_filter_compress.c
   libarchive/archive_write_add_filter_compress.c
   libarchive/mtree.5

* The following source files are in the public domain:
   libarchive/archive_getdate.c

* The following source files are triple-licensed with the ability to choose
  from CC0 1.0 Universal, OpenSSL or Apache 2.0 licenses:
   libarchive/archive_blake2.h
   libarchive/archive_blake2_impl.h
   libarchive/archive_blake2s_ref.c
   libarchive/archive_blake2sp_ref.c

* The build files---including Makefiles, configure scripts,
  and auxiliary scripts used as part of the compile process---have
  widely varying licensing terms.  Please check individual files before
  distributing them to see if those restrictions apply to you.

I intend for all new source code to use the license below and hope over
time to replace code with other licenses with new implementations that
do use the license below.  The varying licensing of the build scripts
seems to be an unavoidable mess.


Copyright (c) 2003-2018 <author(s)>
All rights reserved.

Redistribution and use in source and binary forms, with or without
modification, are permitted provided that the following conditions
are met:
1. Redistributions of source code must retain the above copyright
   notice, this list of conditions and the following disclaimer
   in this position and unchanged.
2. Redistributions in binary form must reproduce the above copyright
   notice, this list of conditions and the following disclaimer in the
   documentation and/or other materials provided with the distribution.

THIS SOFTWARE IS PROVIDED BY THE AUTHOR(S) ``AS IS'' AND ANY EXPRESS OR
IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED.
IN NO EVENT SHALL THE AUTHOR(S) BE LIABLE FOR ANY DIRECT, INDIRECT,
INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT
NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE,
DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY
THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
(INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF
THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
