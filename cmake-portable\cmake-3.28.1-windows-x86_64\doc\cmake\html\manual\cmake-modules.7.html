
<!DOCTYPE html>

<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="generator" content="Docutils 0.19: https://docutils.sourceforge.io/" />

  <title>cmake-modules(7) &mdash; CMake 3.28.1 Documentation</title>

    <link rel="stylesheet" type="text/css" href="../_static/pygments.css" />
    <link rel="stylesheet" type="text/css" href="../_static/cmake.css" />
    
    <script data-url_root="../" id="documentation_options" src="../_static/documentation_options.js"></script>
    <script src="../_static/doctools.js"></script>
    <script src="../_static/sphinx_highlight.js"></script>
    
    <link rel="icon" href="../_static/cmake-favicon.ico"/>
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="next" title="AndroidTestUtilities" href="../module/AndroidTestUtilities.html" />
    <link rel="prev" title="cmake-language(7)" href="cmake-language.7.html" />
 

  </head><body>

    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             accesskey="I">index</a></li>
        <li class="right" >
          <a href="../module/AndroidTestUtilities.html" title="AndroidTestUtilities"
             accesskey="N">next</a> |</li>
        <li class="right" >
          <a href="cmake-language.7.html" title="cmake-language(7)"
             accesskey="P">previous</a> |</li>
  <li>
    <img src="../_static/cmake-logo-16.png" alt=""
         style="vertical-align: middle; margin-top: -2px" />
  </li>
  <li>
    <a href="https://cmake.org/">CMake</a> &#187;
  </li>
  <li>
    <a href="../index.html">3.28.1 Documentation</a> &#187;
  </li>

        <li class="nav-item nav-item-this"><a href="">cmake-modules(7)</a></li> 
      </ul>
    </div>  

    <div class="document">
      <div class="documentwrapper">
        <div class="bodywrapper">
          <div class="body" role="main">
            
  <span class="target" id="manual:cmake-modules(7)"></span><section id="cmake-modules-7">
<h1>cmake-modules(7)<a class="headerlink" href="#cmake-modules-7" title="Permalink to this heading">¶</a></h1>
<p>The modules listed here are part of the CMake distribution.
Projects may provide further modules; their location(s)
can be specified in the <span class="target" id="index-0-variable:CMAKE_MODULE_PATH"></span><a class="reference internal" href="../variable/CMAKE_MODULE_PATH.html#variable:CMAKE_MODULE_PATH" title="CMAKE_MODULE_PATH"><code class="xref cmake cmake-variable docutils literal notranslate"><span class="pre">CMAKE_MODULE_PATH</span></code></a> variable.</p>
<section id="utility-modules">
<h2>Utility Modules<a class="headerlink" href="#utility-modules" title="Permalink to this heading">¶</a></h2>
<p>These modules are loaded using the <span class="target" id="index-0-command:include"></span><a class="reference internal" href="../command/include.html#command:include" title="include"><code class="xref cmake cmake-command docutils literal notranslate"><span class="pre">include()</span></code></a> command.</p>
<div class="toctree-wrapper compound">
<ul>
<li class="toctree-l1"><a class="reference internal" href="../module/AndroidTestUtilities.html">AndroidTestUtilities</a></li>
<li class="toctree-l1"><a class="reference internal" href="../module/BundleUtilities.html">BundleUtilities</a></li>
<li class="toctree-l1"><a class="reference internal" href="../module/CheckCCompilerFlag.html">CheckCCompilerFlag</a></li>
<li class="toctree-l1"><a class="reference internal" href="../module/CheckCompilerFlag.html">CheckCompilerFlag</a></li>
<li class="toctree-l1"><a class="reference internal" href="../module/CheckCSourceCompiles.html">CheckCSourceCompiles</a></li>
<li class="toctree-l1"><a class="reference internal" href="../module/CheckCSourceRuns.html">CheckCSourceRuns</a></li>
<li class="toctree-l1"><a class="reference internal" href="../module/CheckCXXCompilerFlag.html">CheckCXXCompilerFlag</a></li>
<li class="toctree-l1"><a class="reference internal" href="../module/CheckCXXSourceCompiles.html">CheckCXXSourceCompiles</a></li>
<li class="toctree-l1"><a class="reference internal" href="../module/CheckCXXSourceRuns.html">CheckCXXSourceRuns</a></li>
<li class="toctree-l1"><a class="reference internal" href="../module/CheckCXXSymbolExists.html">CheckCXXSymbolExists</a></li>
<li class="toctree-l1"><a class="reference internal" href="../module/CheckFortranCompilerFlag.html">CheckFortranCompilerFlag</a></li>
<li class="toctree-l1"><a class="reference internal" href="../module/CheckFortranFunctionExists.html">CheckFortranFunctionExists</a></li>
<li class="toctree-l1"><a class="reference internal" href="../module/CheckFortranSourceCompiles.html">CheckFortranSourceCompiles</a></li>
<li class="toctree-l1"><a class="reference internal" href="../module/CheckFortranSourceRuns.html">CheckFortranSourceRuns</a></li>
<li class="toctree-l1"><a class="reference internal" href="../module/CheckFunctionExists.html">CheckFunctionExists</a></li>
<li class="toctree-l1"><a class="reference internal" href="../module/CheckIncludeFileCXX.html">CheckIncludeFileCXX</a></li>
<li class="toctree-l1"><a class="reference internal" href="../module/CheckIncludeFile.html">CheckIncludeFile</a></li>
<li class="toctree-l1"><a class="reference internal" href="../module/CheckIncludeFiles.html">CheckIncludeFiles</a></li>
<li class="toctree-l1"><a class="reference internal" href="../module/CheckIPOSupported.html">CheckIPOSupported</a></li>
<li class="toctree-l1"><a class="reference internal" href="../module/CheckLanguage.html">CheckLanguage</a></li>
<li class="toctree-l1"><a class="reference internal" href="../module/CheckLibraryExists.html">CheckLibraryExists</a></li>
<li class="toctree-l1"><a class="reference internal" href="../module/CheckLinkerFlag.html">CheckLinkerFlag</a></li>
<li class="toctree-l1"><a class="reference internal" href="../module/CheckOBJCCompilerFlag.html">CheckOBJCCompilerFlag</a></li>
<li class="toctree-l1"><a class="reference internal" href="../module/CheckOBJCSourceCompiles.html">CheckOBJCSourceCompiles</a></li>
<li class="toctree-l1"><a class="reference internal" href="../module/CheckOBJCSourceRuns.html">CheckOBJCSourceRuns</a></li>
<li class="toctree-l1"><a class="reference internal" href="../module/CheckOBJCXXCompilerFlag.html">CheckOBJCXXCompilerFlag</a></li>
<li class="toctree-l1"><a class="reference internal" href="../module/CheckOBJCXXSourceCompiles.html">CheckOBJCXXSourceCompiles</a></li>
<li class="toctree-l1"><a class="reference internal" href="../module/CheckOBJCXXSourceRuns.html">CheckOBJCXXSourceRuns</a></li>
<li class="toctree-l1"><a class="reference internal" href="../module/CheckPIESupported.html">CheckPIESupported</a></li>
<li class="toctree-l1"><a class="reference internal" href="../module/CheckPrototypeDefinition.html">CheckPrototypeDefinition</a></li>
<li class="toctree-l1"><a class="reference internal" href="../module/CheckSourceCompiles.html">CheckSourceCompiles</a></li>
<li class="toctree-l1"><a class="reference internal" href="../module/CheckSourceRuns.html">CheckSourceRuns</a></li>
<li class="toctree-l1"><a class="reference internal" href="../module/CheckStructHasMember.html">CheckStructHasMember</a></li>
<li class="toctree-l1"><a class="reference internal" href="../module/CheckSymbolExists.html">CheckSymbolExists</a></li>
<li class="toctree-l1"><a class="reference internal" href="../module/CheckTypeSize.html">CheckTypeSize</a></li>
<li class="toctree-l1"><a class="reference internal" href="../module/CheckVariableExists.html">CheckVariableExists</a></li>
<li class="toctree-l1"><a class="reference internal" href="../module/CMakeAddFortranSubdirectory.html">CMakeAddFortranSubdirectory</a></li>
<li class="toctree-l1"><a class="reference internal" href="../module/CMakeBackwardCompatibilityCXX.html">CMakeBackwardCompatibilityCXX</a></li>
<li class="toctree-l1"><a class="reference internal" href="../module/CMakeDependentOption.html">CMakeDependentOption</a></li>
<li class="toctree-l1"><a class="reference internal" href="../module/CMakeFindDependencyMacro.html">CMakeFindDependencyMacro</a></li>
<li class="toctree-l1"><a class="reference internal" href="../module/CMakeFindFrameworks.html">CMakeFindFrameworks</a></li>
<li class="toctree-l1"><a class="reference internal" href="../module/CMakeFindPackageMode.html">CMakeFindPackageMode</a></li>
<li class="toctree-l1"><a class="reference internal" href="../module/CMakeGraphVizOptions.html">CMakeGraphVizOptions</a></li>
<li class="toctree-l1"><a class="reference internal" href="../module/CMakePackageConfigHelpers.html">CMakePackageConfigHelpers</a></li>
<li class="toctree-l1"><a class="reference internal" href="../module/CMakePrintHelpers.html">CMakePrintHelpers</a></li>
<li class="toctree-l1"><a class="reference internal" href="../module/CMakePrintSystemInformation.html">CMakePrintSystemInformation</a></li>
<li class="toctree-l1"><a class="reference internal" href="../module/CMakePushCheckState.html">CMakePushCheckState</a></li>
<li class="toctree-l1"><a class="reference internal" href="../module/CMakeVerifyManifest.html">CMakeVerifyManifest</a></li>
<li class="toctree-l1"><a class="reference internal" href="../module/CPack.html">CPack</a></li>
<li class="toctree-l1"><a class="reference internal" href="../module/CPackComponent.html">CPackComponent</a></li>
<li class="toctree-l1"><a class="reference internal" href="../module/CPackIFW.html">CPackIFW</a></li>
<li class="toctree-l1"><a class="reference internal" href="../module/CPackIFWConfigureFile.html">CPackIFWConfigureFile</a></li>
<li class="toctree-l1"><a class="reference internal" href="../module/CSharpUtilities.html">CSharpUtilities</a></li>
<li class="toctree-l1"><a class="reference internal" href="../module/CTest.html">CTest</a></li>
<li class="toctree-l1"><a class="reference internal" href="../module/CTestCoverageCollectGCOV.html">CTestCoverageCollectGCOV</a></li>
<li class="toctree-l1"><a class="reference internal" href="../module/CTestScriptMode.html">CTestScriptMode</a></li>
<li class="toctree-l1"><a class="reference internal" href="../module/CTestUseLaunchers.html">CTestUseLaunchers</a></li>
<li class="toctree-l1"><a class="reference internal" href="../module/DeployQt4.html">DeployQt4</a></li>
<li class="toctree-l1"><a class="reference internal" href="../module/ExternalData.html">ExternalData</a></li>
<li class="toctree-l1"><a class="reference internal" href="../module/ExternalProject.html">ExternalProject</a></li>
<li class="toctree-l1"><a class="reference internal" href="../module/FeatureSummary.html">FeatureSummary</a></li>
<li class="toctree-l1"><a class="reference internal" href="../module/FetchContent.html">FetchContent</a></li>
<li class="toctree-l1"><a class="reference internal" href="../module/FindPackageHandleStandardArgs.html">FindPackageHandleStandardArgs</a></li>
<li class="toctree-l1"><a class="reference internal" href="../module/FindPackageMessage.html">FindPackageMessage</a></li>
<li class="toctree-l1"><a class="reference internal" href="../module/FortranCInterface.html">FortranCInterface</a></li>
<li class="toctree-l1"><a class="reference internal" href="../module/GenerateExportHeader.html">GenerateExportHeader</a></li>
<li class="toctree-l1"><a class="reference internal" href="../module/GNUInstallDirs.html">GNUInstallDirs</a></li>
<li class="toctree-l1"><a class="reference internal" href="../module/GoogleTest.html">GoogleTest</a></li>
<li class="toctree-l1"><a class="reference internal" href="../module/InstallRequiredSystemLibraries.html">InstallRequiredSystemLibraries</a></li>
<li class="toctree-l1"><a class="reference internal" href="../module/ProcessorCount.html">ProcessorCount</a></li>
<li class="toctree-l1"><a class="reference internal" href="../module/SelectLibraryConfigurations.html">SelectLibraryConfigurations</a></li>
<li class="toctree-l1"><a class="reference internal" href="../module/SquishTestScript.html">SquishTestScript</a></li>
<li class="toctree-l1"><a class="reference internal" href="../module/TestForANSIForScope.html">TestForANSIForScope</a></li>
<li class="toctree-l1"><a class="reference internal" href="../module/TestForANSIStreamHeaders.html">TestForANSIStreamHeaders</a></li>
<li class="toctree-l1"><a class="reference internal" href="../module/TestForSSTREAM.html">TestForSSTREAM</a></li>
<li class="toctree-l1"><a class="reference internal" href="../module/TestForSTDNamespace.html">TestForSTDNamespace</a></li>
<li class="toctree-l1"><a class="reference internal" href="../module/UseEcos.html">UseEcos</a></li>
<li class="toctree-l1"><a class="reference internal" href="../module/UseJava.html">UseJava</a></li>
<li class="toctree-l1"><a class="reference internal" href="../module/UseSWIG.html">UseSWIG</a></li>
<li class="toctree-l1"><a class="reference internal" href="../module/UsewxWidgets.html">UsewxWidgets</a></li>
</ul>
</div>
</section>
<section id="find-modules">
<h2>Find Modules<a class="headerlink" href="#find-modules" title="Permalink to this heading">¶</a></h2>
<p>These modules search for third-party software.
They are normally called through the <span class="target" id="index-0-command:find_package"></span><a class="reference internal" href="../command/find_package.html#command:find_package" title="find_package"><code class="xref cmake cmake-command docutils literal notranslate"><span class="pre">find_package()</span></code></a> command.</p>
<div class="toctree-wrapper compound">
<ul>
<li class="toctree-l1"><a class="reference internal" href="../module/FindALSA.html">FindALSA</a></li>
<li class="toctree-l1"><a class="reference internal" href="../module/FindArmadillo.html">FindArmadillo</a></li>
<li class="toctree-l1"><a class="reference internal" href="../module/FindASPELL.html">FindASPELL</a></li>
<li class="toctree-l1"><a class="reference internal" href="../module/FindAVIFile.html">FindAVIFile</a></li>
<li class="toctree-l1"><a class="reference internal" href="../module/FindBacktrace.html">FindBacktrace</a></li>
<li class="toctree-l1"><a class="reference internal" href="../module/FindBISON.html">FindBISON</a></li>
<li class="toctree-l1"><a class="reference internal" href="../module/FindBLAS.html">FindBLAS</a></li>
<li class="toctree-l1"><a class="reference internal" href="../module/FindBoost.html">FindBoost</a></li>
<li class="toctree-l1"><a class="reference internal" href="../module/FindBullet.html">FindBullet</a></li>
<li class="toctree-l1"><a class="reference internal" href="../module/FindBZip2.html">FindBZip2</a></li>
<li class="toctree-l1"><a class="reference internal" href="../module/FindCABLE.html">FindCABLE</a></li>
<li class="toctree-l1"><a class="reference internal" href="../module/FindCoin3D.html">FindCoin3D</a></li>
<li class="toctree-l1"><a class="reference internal" href="../module/FindCUDAToolkit.html">FindCUDAToolkit</a></li>
<li class="toctree-l1"><a class="reference internal" href="../module/FindCups.html">FindCups</a></li>
<li class="toctree-l1"><a class="reference internal" href="../module/FindCURL.html">FindCURL</a></li>
<li class="toctree-l1"><a class="reference internal" href="../module/FindCurses.html">FindCurses</a></li>
<li class="toctree-l1"><a class="reference internal" href="../module/FindCVS.html">FindCVS</a></li>
<li class="toctree-l1"><a class="reference internal" href="../module/FindCxxTest.html">FindCxxTest</a></li>
<li class="toctree-l1"><a class="reference internal" href="../module/FindCygwin.html">FindCygwin</a></li>
<li class="toctree-l1"><a class="reference internal" href="../module/FindDCMTK.html">FindDCMTK</a></li>
<li class="toctree-l1"><a class="reference internal" href="../module/FindDevIL.html">FindDevIL</a></li>
<li class="toctree-l1"><a class="reference internal" href="../module/FindDoxygen.html">FindDoxygen</a></li>
<li class="toctree-l1"><a class="reference internal" href="../module/FindEnvModules.html">FindEnvModules</a></li>
<li class="toctree-l1"><a class="reference internal" href="../module/FindEXPAT.html">FindEXPAT</a></li>
<li class="toctree-l1"><a class="reference internal" href="../module/FindFLEX.html">FindFLEX</a></li>
<li class="toctree-l1"><a class="reference internal" href="../module/FindFLTK.html">FindFLTK</a></li>
<li class="toctree-l1"><a class="reference internal" href="../module/FindFLTK2.html">FindFLTK2</a></li>
<li class="toctree-l1"><a class="reference internal" href="../module/FindFontconfig.html">FindFontconfig</a></li>
<li class="toctree-l1"><a class="reference internal" href="../module/FindFreetype.html">FindFreetype</a></li>
<li class="toctree-l1"><a class="reference internal" href="../module/FindGCCXML.html">FindGCCXML</a></li>
<li class="toctree-l1"><a class="reference internal" href="../module/FindGDAL.html">FindGDAL</a></li>
<li class="toctree-l1"><a class="reference internal" href="../module/FindGettext.html">FindGettext</a></li>
<li class="toctree-l1"><a class="reference internal" href="../module/FindGIF.html">FindGIF</a></li>
<li class="toctree-l1"><a class="reference internal" href="../module/FindGit.html">FindGit</a></li>
<li class="toctree-l1"><a class="reference internal" href="../module/FindGLEW.html">FindGLEW</a></li>
<li class="toctree-l1"><a class="reference internal" href="../module/FindGLUT.html">FindGLUT</a></li>
<li class="toctree-l1"><a class="reference internal" href="../module/FindGnuplot.html">FindGnuplot</a></li>
<li class="toctree-l1"><a class="reference internal" href="../module/FindGnuTLS.html">FindGnuTLS</a></li>
<li class="toctree-l1"><a class="reference internal" href="../module/FindGSL.html">FindGSL</a></li>
<li class="toctree-l1"><a class="reference internal" href="../module/FindGTest.html">FindGTest</a></li>
<li class="toctree-l1"><a class="reference internal" href="../module/FindGTK.html">FindGTK</a></li>
<li class="toctree-l1"><a class="reference internal" href="../module/FindGTK2.html">FindGTK2</a></li>
<li class="toctree-l1"><a class="reference internal" href="../module/FindHDF5.html">FindHDF5</a></li>
<li class="toctree-l1"><a class="reference internal" href="../module/FindHg.html">FindHg</a></li>
<li class="toctree-l1"><a class="reference internal" href="../module/FindHSPELL.html">FindHSPELL</a></li>
<li class="toctree-l1"><a class="reference internal" href="../module/FindHTMLHelp.html">FindHTMLHelp</a></li>
<li class="toctree-l1"><a class="reference internal" href="../module/FindIce.html">FindIce</a></li>
<li class="toctree-l1"><a class="reference internal" href="../module/FindIconv.html">FindIconv</a></li>
<li class="toctree-l1"><a class="reference internal" href="../module/FindIcotool.html">FindIcotool</a></li>
<li class="toctree-l1"><a class="reference internal" href="../module/FindICU.html">FindICU</a></li>
<li class="toctree-l1"><a class="reference internal" href="../module/FindImageMagick.html">FindImageMagick</a></li>
<li class="toctree-l1"><a class="reference internal" href="../module/FindIntl.html">FindIntl</a></li>
<li class="toctree-l1"><a class="reference internal" href="../module/FindJasper.html">FindJasper</a></li>
<li class="toctree-l1"><a class="reference internal" href="../module/FindJava.html">FindJava</a></li>
<li class="toctree-l1"><a class="reference internal" href="../module/FindJNI.html">FindJNI</a></li>
<li class="toctree-l1"><a class="reference internal" href="../module/FindJPEG.html">FindJPEG</a></li>
<li class="toctree-l1"><a class="reference internal" href="../module/FindKDE3.html">FindKDE3</a></li>
<li class="toctree-l1"><a class="reference internal" href="../module/FindKDE4.html">FindKDE4</a></li>
<li class="toctree-l1"><a class="reference internal" href="../module/FindLAPACK.html">FindLAPACK</a></li>
<li class="toctree-l1"><a class="reference internal" href="../module/FindLATEX.html">FindLATEX</a></li>
<li class="toctree-l1"><a class="reference internal" href="../module/FindLibArchive.html">FindLibArchive</a></li>
<li class="toctree-l1"><a class="reference internal" href="../module/FindLibinput.html">FindLibinput</a></li>
<li class="toctree-l1"><a class="reference internal" href="../module/FindLibLZMA.html">FindLibLZMA</a></li>
<li class="toctree-l1"><a class="reference internal" href="../module/FindLibXml2.html">FindLibXml2</a></li>
<li class="toctree-l1"><a class="reference internal" href="../module/FindLibXslt.html">FindLibXslt</a></li>
<li class="toctree-l1"><a class="reference internal" href="../module/FindLTTngUST.html">FindLTTngUST</a></li>
<li class="toctree-l1"><a class="reference internal" href="../module/FindLua.html">FindLua</a></li>
<li class="toctree-l1"><a class="reference internal" href="../module/FindLua50.html">FindLua50</a></li>
<li class="toctree-l1"><a class="reference internal" href="../module/FindLua51.html">FindLua51</a></li>
<li class="toctree-l1"><a class="reference internal" href="../module/FindMatlab.html">FindMatlab</a></li>
<li class="toctree-l1"><a class="reference internal" href="../module/FindMFC.html">FindMFC</a></li>
<li class="toctree-l1"><a class="reference internal" href="../module/FindMotif.html">FindMotif</a></li>
<li class="toctree-l1"><a class="reference internal" href="../module/FindMPEG.html">FindMPEG</a></li>
<li class="toctree-l1"><a class="reference internal" href="../module/FindMPEG2.html">FindMPEG2</a></li>
<li class="toctree-l1"><a class="reference internal" href="../module/FindMPI.html">FindMPI</a></li>
<li class="toctree-l1"><a class="reference internal" href="../module/FindMsys.html">FindMsys</a></li>
<li class="toctree-l1"><a class="reference internal" href="../module/FindODBC.html">FindODBC</a></li>
<li class="toctree-l1"><a class="reference internal" href="../module/FindOpenACC.html">FindOpenACC</a></li>
<li class="toctree-l1"><a class="reference internal" href="../module/FindOpenAL.html">FindOpenAL</a></li>
<li class="toctree-l1"><a class="reference internal" href="../module/FindOpenCL.html">FindOpenCL</a></li>
<li class="toctree-l1"><a class="reference internal" href="../module/FindOpenGL.html">FindOpenGL</a></li>
<li class="toctree-l1"><a class="reference internal" href="../module/FindOpenMP.html">FindOpenMP</a></li>
<li class="toctree-l1"><a class="reference internal" href="../module/FindOpenSceneGraph.html">FindOpenSceneGraph</a></li>
<li class="toctree-l1"><a class="reference internal" href="../module/FindOpenSP.html">FindOpenSP</a></li>
<li class="toctree-l1"><a class="reference internal" href="../module/FindOpenSSL.html">FindOpenSSL</a></li>
<li class="toctree-l1"><a class="reference internal" href="../module/FindOpenThreads.html">FindOpenThreads</a></li>
<li class="toctree-l1"><a class="reference internal" href="../module/Findosg.html">Findosg</a></li>
<li class="toctree-l1"><a class="reference internal" href="../module/Findosg_functions.html">Findosg_functions</a></li>
<li class="toctree-l1"><a class="reference internal" href="../module/FindosgAnimation.html">FindosgAnimation</a></li>
<li class="toctree-l1"><a class="reference internal" href="../module/FindosgDB.html">FindosgDB</a></li>
<li class="toctree-l1"><a class="reference internal" href="../module/FindosgFX.html">FindosgFX</a></li>
<li class="toctree-l1"><a class="reference internal" href="../module/FindosgGA.html">FindosgGA</a></li>
<li class="toctree-l1"><a class="reference internal" href="../module/FindosgIntrospection.html">FindosgIntrospection</a></li>
<li class="toctree-l1"><a class="reference internal" href="../module/FindosgManipulator.html">FindosgManipulator</a></li>
<li class="toctree-l1"><a class="reference internal" href="../module/FindosgParticle.html">FindosgParticle</a></li>
<li class="toctree-l1"><a class="reference internal" href="../module/FindosgPresentation.html">FindosgPresentation</a></li>
<li class="toctree-l1"><a class="reference internal" href="../module/FindosgProducer.html">FindosgProducer</a></li>
<li class="toctree-l1"><a class="reference internal" href="../module/FindosgQt.html">FindosgQt</a></li>
<li class="toctree-l1"><a class="reference internal" href="../module/FindosgShadow.html">FindosgShadow</a></li>
<li class="toctree-l1"><a class="reference internal" href="../module/FindosgSim.html">FindosgSim</a></li>
<li class="toctree-l1"><a class="reference internal" href="../module/FindosgTerrain.html">FindosgTerrain</a></li>
<li class="toctree-l1"><a class="reference internal" href="../module/FindosgText.html">FindosgText</a></li>
<li class="toctree-l1"><a class="reference internal" href="../module/FindosgUtil.html">FindosgUtil</a></li>
<li class="toctree-l1"><a class="reference internal" href="../module/FindosgViewer.html">FindosgViewer</a></li>
<li class="toctree-l1"><a class="reference internal" href="../module/FindosgVolume.html">FindosgVolume</a></li>
<li class="toctree-l1"><a class="reference internal" href="../module/FindosgWidget.html">FindosgWidget</a></li>
<li class="toctree-l1"><a class="reference internal" href="../module/FindPatch.html">FindPatch</a></li>
<li class="toctree-l1"><a class="reference internal" href="../module/FindPerl.html">FindPerl</a></li>
<li class="toctree-l1"><a class="reference internal" href="../module/FindPerlLibs.html">FindPerlLibs</a></li>
<li class="toctree-l1"><a class="reference internal" href="../module/FindPHP4.html">FindPHP4</a></li>
<li class="toctree-l1"><a class="reference internal" href="../module/FindPhysFS.html">FindPhysFS</a></li>
<li class="toctree-l1"><a class="reference internal" href="../module/FindPike.html">FindPike</a></li>
<li class="toctree-l1"><a class="reference internal" href="../module/FindPkgConfig.html">FindPkgConfig</a></li>
<li class="toctree-l1"><a class="reference internal" href="../module/FindPNG.html">FindPNG</a></li>
<li class="toctree-l1"><a class="reference internal" href="../module/FindPostgreSQL.html">FindPostgreSQL</a></li>
<li class="toctree-l1"><a class="reference internal" href="../module/FindProducer.html">FindProducer</a></li>
<li class="toctree-l1"><a class="reference internal" href="../module/FindProtobuf.html">FindProtobuf</a></li>
<li class="toctree-l1"><a class="reference internal" href="../module/FindPython.html">FindPython</a></li>
<li class="toctree-l1"><a class="reference internal" href="../module/FindPython2.html">FindPython2</a></li>
<li class="toctree-l1"><a class="reference internal" href="../module/FindPython3.html">FindPython3</a></li>
<li class="toctree-l1"><a class="reference internal" href="../module/FindQt3.html">FindQt3</a></li>
<li class="toctree-l1"><a class="reference internal" href="../module/FindQt4.html">FindQt4</a></li>
<li class="toctree-l1"><a class="reference internal" href="../module/FindQuickTime.html">FindQuickTime</a></li>
<li class="toctree-l1"><a class="reference internal" href="../module/FindRTI.html">FindRTI</a></li>
<li class="toctree-l1"><a class="reference internal" href="../module/FindRuby.html">FindRuby</a></li>
<li class="toctree-l1"><a class="reference internal" href="../module/FindSDL.html">FindSDL</a></li>
<li class="toctree-l1"><a class="reference internal" href="../module/FindSDL_image.html">FindSDL_image</a></li>
<li class="toctree-l1"><a class="reference internal" href="../module/FindSDL_gfx.html">FindSDL_gfx</a></li>
<li class="toctree-l1"><a class="reference internal" href="../module/FindSDL_mixer.html">FindSDL_mixer</a></li>
<li class="toctree-l1"><a class="reference internal" href="../module/FindSDL_net.html">FindSDL_net</a></li>
<li class="toctree-l1"><a class="reference internal" href="../module/FindSDL_sound.html">FindSDL_sound</a></li>
<li class="toctree-l1"><a class="reference internal" href="../module/FindSDL_ttf.html">FindSDL_ttf</a></li>
<li class="toctree-l1"><a class="reference internal" href="../module/FindSelfPackers.html">FindSelfPackers</a></li>
<li class="toctree-l1"><a class="reference internal" href="../module/FindSquish.html">FindSquish</a></li>
<li class="toctree-l1"><a class="reference internal" href="../module/FindSQLite3.html">FindSQLite3</a></li>
<li class="toctree-l1"><a class="reference internal" href="../module/FindSubversion.html">FindSubversion</a></li>
<li class="toctree-l1"><a class="reference internal" href="../module/FindSWIG.html">FindSWIG</a></li>
<li class="toctree-l1"><a class="reference internal" href="../module/FindTCL.html">FindTCL</a></li>
<li class="toctree-l1"><a class="reference internal" href="../module/FindTclsh.html">FindTclsh</a></li>
<li class="toctree-l1"><a class="reference internal" href="../module/FindTclStub.html">FindTclStub</a></li>
<li class="toctree-l1"><a class="reference internal" href="../module/FindThreads.html">FindThreads</a></li>
<li class="toctree-l1"><a class="reference internal" href="../module/FindTIFF.html">FindTIFF</a></li>
<li class="toctree-l1"><a class="reference internal" href="../module/FindVulkan.html">FindVulkan</a></li>
<li class="toctree-l1"><a class="reference internal" href="../module/FindWget.html">FindWget</a></li>
<li class="toctree-l1"><a class="reference internal" href="../module/FindWish.html">FindWish</a></li>
<li class="toctree-l1"><a class="reference internal" href="../module/FindwxWidgets.html">FindwxWidgets</a></li>
<li class="toctree-l1"><a class="reference internal" href="../module/FindX11.html">FindX11</a></li>
<li class="toctree-l1"><a class="reference internal" href="../module/FindXalanC.html">FindXalanC</a></li>
<li class="toctree-l1"><a class="reference internal" href="../module/FindXCTest.html">FindXCTest</a></li>
<li class="toctree-l1"><a class="reference internal" href="../module/FindXercesC.html">FindXercesC</a></li>
<li class="toctree-l1"><a class="reference internal" href="../module/FindXMLRPC.html">FindXMLRPC</a></li>
<li class="toctree-l1"><a class="reference internal" href="../module/FindZLIB.html">FindZLIB</a></li>
</ul>
</div>
</section>
<section id="deprecated-modules">
<h2>Deprecated Modules<a class="headerlink" href="#deprecated-modules" title="Permalink to this heading">¶</a></h2>
<section id="deprecated-utility-modules">
<h3>Deprecated Utility Modules<a class="headerlink" href="#deprecated-utility-modules" title="Permalink to this heading">¶</a></h3>
<div class="toctree-wrapper compound">
<ul>
<li class="toctree-l1"><a class="reference internal" href="../module/AddFileDependencies.html">AddFileDependencies</a></li>
<li class="toctree-l1"><a class="reference internal" href="../module/CMakeDetermineVSServicePack.html">CMakeDetermineVSServicePack</a></li>
<li class="toctree-l1"><a class="reference internal" href="../module/CMakeExpandImportedTargets.html">CMakeExpandImportedTargets</a></li>
<li class="toctree-l1"><a class="reference internal" href="../module/CMakeForceCompiler.html">CMakeForceCompiler</a></li>
<li class="toctree-l1"><a class="reference internal" href="../module/CMakeParseArguments.html">CMakeParseArguments</a></li>
<li class="toctree-l1"><a class="reference internal" href="../module/Dart.html">Dart</a></li>
<li class="toctree-l1"><a class="reference internal" href="../module/Documentation.html">Documentation</a></li>
<li class="toctree-l1"><a class="reference internal" href="../module/GetPrerequisites.html">GetPrerequisites</a></li>
<li class="toctree-l1"><a class="reference internal" href="../module/MacroAddFileDependencies.html">MacroAddFileDependencies</a></li>
<li class="toctree-l1"><a class="reference internal" href="../module/TestBigEndian.html">TestBigEndian</a></li>
<li class="toctree-l1"><a class="reference internal" href="../module/TestCXXAcceptsFlag.html">TestCXXAcceptsFlag</a></li>
<li class="toctree-l1"><a class="reference internal" href="../module/UseJavaClassFilelist.html">UseJavaClassFilelist</a></li>
<li class="toctree-l1"><a class="reference internal" href="../module/UseJavaSymlinks.html">UseJavaSymlinks</a></li>
<li class="toctree-l1"><a class="reference internal" href="../module/UsePkgConfig.html">UsePkgConfig</a></li>
<li class="toctree-l1"><a class="reference internal" href="../module/Use_wxWindows.html">Use_wxWindows</a></li>
<li class="toctree-l1"><a class="reference internal" href="../module/WriteBasicConfigVersionFile.html">WriteBasicConfigVersionFile</a></li>
<li class="toctree-l1"><a class="reference internal" href="../module/WriteCompilerDetectionHeader.html">WriteCompilerDetectionHeader</a></li>
</ul>
</div>
</section>
<section id="deprecated-find-modules">
<h3>Deprecated Find Modules<a class="headerlink" href="#deprecated-find-modules" title="Permalink to this heading">¶</a></h3>
<div class="toctree-wrapper compound">
<ul>
<li class="toctree-l1"><a class="reference internal" href="../module/FindCUDA.html">FindCUDA</a></li>
<li class="toctree-l1"><a class="reference internal" href="../module/FindDart.html">FindDart</a></li>
<li class="toctree-l1"><a class="reference internal" href="../module/FindITK.html">FindITK</a></li>
<li class="toctree-l1"><a class="reference internal" href="../module/FindPythonInterp.html">FindPythonInterp</a></li>
<li class="toctree-l1"><a class="reference internal" href="../module/FindPythonLibs.html">FindPythonLibs</a></li>
<li class="toctree-l1"><a class="reference internal" href="../module/FindQt.html">FindQt</a></li>
<li class="toctree-l1"><a class="reference internal" href="../module/FindUnixCommands.html">FindUnixCommands</a></li>
<li class="toctree-l1"><a class="reference internal" href="../module/FindVTK.html">FindVTK</a></li>
<li class="toctree-l1"><a class="reference internal" href="../module/FindwxWindows.html">FindwxWindows</a></li>
</ul>
</div>
</section>
<section id="legacy-cpack-modules">
<h3>Legacy CPack Modules<a class="headerlink" href="#legacy-cpack-modules" title="Permalink to this heading">¶</a></h3>
<p>These modules used to be mistakenly exposed to the user, and have been moved
out of user visibility. They are for CPack internal use, and should never be
used directly.</p>
<div class="toctree-wrapper compound">
<ul>
<li class="toctree-l1"><a class="reference internal" href="../module/CPackArchive.html">CPackArchive</a></li>
<li class="toctree-l1"><a class="reference internal" href="../module/CPackBundle.html">CPackBundle</a></li>
<li class="toctree-l1"><a class="reference internal" href="../module/CPackCygwin.html">CPackCygwin</a></li>
<li class="toctree-l1"><a class="reference internal" href="../module/CPackDeb.html">CPackDeb</a></li>
<li class="toctree-l1"><a class="reference internal" href="../module/CPackDMG.html">CPackDMG</a></li>
<li class="toctree-l1"><a class="reference internal" href="../module/CPackFreeBSD.html">CPackFreeBSD</a></li>
<li class="toctree-l1"><a class="reference internal" href="../module/CPackNSIS.html">CPackNSIS</a></li>
<li class="toctree-l1"><a class="reference internal" href="../module/CPackNuGet.html">CPackNuGet</a></li>
<li class="toctree-l1"><a class="reference internal" href="../module/CPackProductBuild.html">CPackProductBuild</a></li>
<li class="toctree-l1"><a class="reference internal" href="../module/CPackRPM.html">CPackRPM</a></li>
<li class="toctree-l1"><a class="reference internal" href="../module/CPackWIX.html">CPackWIX</a></li>
</ul>
</div>
</section>
</section>
</section>


            <div class="clearer"></div>
          </div>
        </div>
      </div>
      <div class="sphinxsidebar" role="navigation" aria-label="main navigation">
        <div class="sphinxsidebarwrapper">
  <div>
    <h3><a href="../index.html">Table of Contents</a></h3>
    <ul>
<li><a class="reference internal" href="#">cmake-modules(7)</a><ul>
<li><a class="reference internal" href="#utility-modules">Utility Modules</a></li>
<li><a class="reference internal" href="#find-modules">Find Modules</a></li>
<li><a class="reference internal" href="#deprecated-modules">Deprecated Modules</a><ul>
<li><a class="reference internal" href="#deprecated-utility-modules">Deprecated Utility Modules</a></li>
<li><a class="reference internal" href="#deprecated-find-modules">Deprecated Find Modules</a></li>
<li><a class="reference internal" href="#legacy-cpack-modules">Legacy CPack Modules</a></li>
</ul>
</li>
</ul>
</li>
</ul>

  </div>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="cmake-language.7.html"
                          title="previous chapter">cmake-language(7)</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="../module/AndroidTestUtilities.html"
                          title="next chapter">AndroidTestUtilities</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../_sources/manual/cmake-modules.7.rst.txt"
            rel="nofollow">Show Source</a></li>
    </ul>
   </div>
<div id="searchbox" style="display: none" role="search">
  <h3 id="searchlabel">Quick search</h3>
    <div class="searchformwrapper">
    <form class="search" action="../search.html" method="get">
      <input type="text" name="q" aria-labelledby="searchlabel" autocomplete="off" autocorrect="off" autocapitalize="off" spellcheck="false"/>
      <input type="submit" value="Go" />
    </form>
    </div>
</div>
<script>document.getElementById('searchbox').style.display = "block"</script>
        </div>
      </div>
      <div class="clearer"></div>
    </div>
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             >index</a></li>
        <li class="right" >
          <a href="../module/AndroidTestUtilities.html" title="AndroidTestUtilities"
             >next</a> |</li>
        <li class="right" >
          <a href="cmake-language.7.html" title="cmake-language(7)"
             >previous</a> |</li>
  <li>
    <img src="../_static/cmake-logo-16.png" alt=""
         style="vertical-align: middle; margin-top: -2px" />
  </li>
  <li>
    <a href="https://cmake.org/">CMake</a> &#187;
  </li>
  <li>
    <a href="../index.html">3.28.1 Documentation</a> &#187;
  </li>

        <li class="nav-item nav-item-this"><a href="">cmake-modules(7)</a></li> 
      </ul>
    </div>

    <div class="footer" role="contentinfo">
        &#169; Copyright 2000-2023 Kitware, Inc. and Contributors.
      Created using <a href="https://www.sphinx-doc.org/">Sphinx</a> 6.2.1.
    </div>
  </body>
</html>