
<!DOCTYPE html>

<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="generator" content="Docutils 0.19: https://docutils.sourceforge.io/" />

  <title>include_external_msproject &mdash; CMake 3.28.1 Documentation</title>

    <link rel="stylesheet" type="text/css" href="../_static/pygments.css" />
    <link rel="stylesheet" type="text/css" href="../_static/cmake.css" />
    
    <script data-url_root="../" id="documentation_options" src="../_static/documentation_options.js"></script>
    <script src="../_static/doctools.js"></script>
    <script src="../_static/sphinx_highlight.js"></script>
    
    <link rel="icon" href="../_static/cmake-favicon.ico"/>
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="next" title="include_regular_expression" href="include_regular_expression.html" />
    <link rel="prev" title="include_directories" href="include_directories.html" />
 

  </head><body>

    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             accesskey="I">index</a></li>
        <li class="right" >
          <a href="include_regular_expression.html" title="include_regular_expression"
             accesskey="N">next</a> |</li>
        <li class="right" >
          <a href="include_directories.html" title="include_directories"
             accesskey="P">previous</a> |</li>
  <li>
    <img src="../_static/cmake-logo-16.png" alt=""
         style="vertical-align: middle; margin-top: -2px" />
  </li>
  <li>
    <a href="https://cmake.org/">CMake</a> &#187;
  </li>
  <li>
    <a href="../index.html">3.28.1 Documentation</a> &#187;
  </li>

          <li class="nav-item nav-item-1"><a href="../manual/cmake-commands.7.html" accesskey="U">cmake-commands(7)</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href="">include_external_msproject</a></li> 
      </ul>
    </div>  

    <div class="document">
      <div class="documentwrapper">
        <div class="bodywrapper">
          <div class="body" role="main">
            
  <section id="include-external-msproject">
<span id="command:include_external_msproject"></span><h1>include_external_msproject<a class="headerlink" href="#include-external-msproject" title="Permalink to this heading">¶</a></h1>
<p>Include an external Microsoft project file in a workspace.</p>
<div class="highlight-cmake notranslate"><div class="highlight"><pre><span></span><span class="nf">include_external_msproject(</span><span class="nb">projectname</span><span class="w"> </span><span class="nb">location</span>
<span class="w">                           </span><span class="p">[</span><span class="no">TYPE</span><span class="w"> </span><span class="nb">projectTypeGUID</span><span class="p">]</span>
<span class="w">                           </span><span class="p">[</span><span class="no">GUID</span><span class="w"> </span><span class="nb">projectGUID</span><span class="p">]</span>
<span class="w">                           </span><span class="p">[</span><span class="no">PLATFORM</span><span class="w"> </span><span class="nb">platformName</span><span class="p">]</span>
<span class="w">                           </span><span class="nb">dep1</span><span class="w"> </span><span class="nb">dep2</span><span class="w"> </span><span class="p">...</span><span class="nf">)</span>
</pre></div>
</div>
<p>Includes an external Microsoft project in the generated workspace
file.  Currently does nothing on UNIX.  This will create a target
named <code class="docutils literal notranslate"><span class="pre">[projectname]</span></code>.  This can be used in the <span class="target" id="index-0-command:add_dependencies"></span><a class="reference internal" href="add_dependencies.html#command:add_dependencies" title="add_dependencies"><code class="xref cmake cmake-command docutils literal notranslate"><span class="pre">add_dependencies()</span></code></a>
command to make things depend on the external project.</p>
<p><code class="docutils literal notranslate"><span class="pre">TYPE</span></code>, <code class="docutils literal notranslate"><span class="pre">GUID</span></code> and <code class="docutils literal notranslate"><span class="pre">PLATFORM</span></code> are optional parameters that allow one to
specify the type of project, id (<code class="docutils literal notranslate"><span class="pre">GUID</span></code>) of the project and the name of
the target platform.  This is useful for projects requiring values
other than the default (e.g.  WIX projects).</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.9: </span>If the imported project has different configuration names than the
current project, set the <span class="target" id="index-0-prop_tgt:MAP_IMPORTED_CONFIG_&lt;CONFIG&gt;"></span><a class="reference internal" href="../prop_tgt/MAP_IMPORTED_CONFIG_CONFIG.html#prop_tgt:MAP_IMPORTED_CONFIG_&lt;CONFIG&gt;" title="MAP_IMPORTED_CONFIG_&lt;CONFIG&gt;"><code class="xref cmake cmake-prop_tgt docutils literal notranslate"><span class="pre">MAP_IMPORTED_CONFIG_&lt;CONFIG&gt;</span></code></a>
target property to specify the mapping.</p>
</div>
</section>


            <div class="clearer"></div>
          </div>
        </div>
      </div>
      <div class="sphinxsidebar" role="navigation" aria-label="main navigation">
        <div class="sphinxsidebarwrapper">
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="include_directories.html"
                          title="previous chapter">include_directories</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="include_regular_expression.html"
                          title="next chapter">include_regular_expression</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../_sources/command/include_external_msproject.rst.txt"
            rel="nofollow">Show Source</a></li>
    </ul>
   </div>
<div id="searchbox" style="display: none" role="search">
  <h3 id="searchlabel">Quick search</h3>
    <div class="searchformwrapper">
    <form class="search" action="../search.html" method="get">
      <input type="text" name="q" aria-labelledby="searchlabel" autocomplete="off" autocorrect="off" autocapitalize="off" spellcheck="false"/>
      <input type="submit" value="Go" />
    </form>
    </div>
</div>
<script>document.getElementById('searchbox').style.display = "block"</script>
        </div>
      </div>
      <div class="clearer"></div>
    </div>
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             >index</a></li>
        <li class="right" >
          <a href="include_regular_expression.html" title="include_regular_expression"
             >next</a> |</li>
        <li class="right" >
          <a href="include_directories.html" title="include_directories"
             >previous</a> |</li>
  <li>
    <img src="../_static/cmake-logo-16.png" alt=""
         style="vertical-align: middle; margin-top: -2px" />
  </li>
  <li>
    <a href="https://cmake.org/">CMake</a> &#187;
  </li>
  <li>
    <a href="../index.html">3.28.1 Documentation</a> &#187;
  </li>

          <li class="nav-item nav-item-1"><a href="../manual/cmake-commands.7.html" >cmake-commands(7)</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href="">include_external_msproject</a></li> 
      </ul>
    </div>

    <div class="footer" role="contentinfo">
        &#169; Copyright 2000-2023 Kitware, Inc. and Contributors.
      Created using <a href="https://www.sphinx-doc.org/">Sphinx</a> 6.2.1.
    </div>
  </body>
</html>