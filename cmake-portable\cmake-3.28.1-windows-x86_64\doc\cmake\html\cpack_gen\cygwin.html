
<!DOCTYPE html>

<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="generator" content="Docutils 0.19: https://docutils.sourceforge.io/" />

  <title>CPack Cygwin Generator &mdash; CMake 3.28.1 Documentation</title>

    <link rel="stylesheet" type="text/css" href="../_static/pygments.css" />
    <link rel="stylesheet" type="text/css" href="../_static/cmake.css" />
    
    <script data-url_root="../" id="documentation_options" src="../_static/documentation_options.js"></script>
    <script src="../_static/doctools.js"></script>
    <script src="../_static/sphinx_highlight.js"></script>
    
    <link rel="icon" href="../_static/cmake-favicon.ico"/>
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="next" title="CPack DEB Generator" href="deb.html" />
    <link rel="prev" title="CPack Bundle Generator" href="bundle.html" />
 

  </head><body>

    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             accesskey="I">index</a></li>
        <li class="right" >
          <a href="deb.html" title="CPack DEB Generator"
             accesskey="N">next</a> |</li>
        <li class="right" >
          <a href="bundle.html" title="CPack Bundle Generator"
             accesskey="P">previous</a> |</li>
  <li>
    <img src="../_static/cmake-logo-16.png" alt=""
         style="vertical-align: middle; margin-top: -2px" />
  </li>
  <li>
    <a href="https://cmake.org/">CMake</a> &#187;
  </li>
  <li>
    <a href="../index.html">3.28.1 Documentation</a> &#187;
  </li>

          <li class="nav-item nav-item-1"><a href="../manual/cpack-generators.7.html" accesskey="U">cpack-generators(7)</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href="">CPack Cygwin Generator</a></li> 
      </ul>
    </div>  

    <div class="document">
      <div class="documentwrapper">
        <div class="bodywrapper">
          <div class="body" role="main">
            
  <section id="cpack-cygwin-generator">
<span id="cpack_gen:CPack Cygwin Generator"></span><h1>CPack Cygwin Generator<a class="headerlink" href="#cpack-cygwin-generator" title="Permalink to this heading">¶</a></h1>
<p>Cygwin CPack generator (Cygwin).</p>
<section id="variables-affecting-the-cpack-cygwin-generator">
<h2>Variables affecting the CPack Cygwin generator<a class="headerlink" href="#variables-affecting-the-cpack-cygwin-generator" title="Permalink to this heading">¶</a></h2>
<ul>
<li><div class="versionadded">
<p><span class="versionmodified added">New in version 3.18: </span><span class="target" id="index-0-variable:CPACK_ARCHIVE_THREADS"></span><a class="reference internal" href="archive.html#variable:CPACK_ARCHIVE_THREADS" title="CPACK_ARCHIVE_THREADS"><code class="xref cmake cmake-variable docutils literal notranslate"><span class="pre">CPACK_ARCHIVE_THREADS</span></code></a></p>
</div>
</li>
</ul>
</section>
<section id="variables-specific-to-cpack-cygwin-generator">
<h2>Variables specific to CPack Cygwin generator<a class="headerlink" href="#variables-specific-to-cpack-cygwin-generator" title="Permalink to this heading">¶</a></h2>
<p>The
following variable is specific to installers build on and/or for
Cygwin:</p>
<dl class="cmake variable">
<dt class="sig sig-object cmake" id="variable:CPACK_CYGWIN_PATCH_NUMBER">
<span class="sig-name descname"><span class="pre">CPACK_CYGWIN_PATCH_NUMBER</span></span><a class="headerlink" href="#variable:CPACK_CYGWIN_PATCH_NUMBER" title="Permalink to this definition">¶</a></dt>
<dd><p>The Cygwin patch number.  FIXME: This documentation is incomplete.</p>
</dd></dl>

<dl class="cmake variable">
<dt class="sig sig-object cmake" id="variable:CPACK_CYGWIN_PATCH_FILE">
<span class="sig-name descname"><span class="pre">CPACK_CYGWIN_PATCH_FILE</span></span><a class="headerlink" href="#variable:CPACK_CYGWIN_PATCH_FILE" title="Permalink to this definition">¶</a></dt>
<dd><p>The Cygwin patch file.  FIXME: This documentation is incomplete.</p>
</dd></dl>

<dl class="cmake variable">
<dt class="sig sig-object cmake" id="variable:CPACK_CYGWIN_BUILD_SCRIPT">
<span class="sig-name descname"><span class="pre">CPACK_CYGWIN_BUILD_SCRIPT</span></span><a class="headerlink" href="#variable:CPACK_CYGWIN_BUILD_SCRIPT" title="Permalink to this definition">¶</a></dt>
<dd><p>The Cygwin build script.  FIXME: This documentation is incomplete.</p>
</dd></dl>

</section>
</section>


            <div class="clearer"></div>
          </div>
        </div>
      </div>
      <div class="sphinxsidebar" role="navigation" aria-label="main navigation">
        <div class="sphinxsidebarwrapper">
  <div>
    <h3><a href="../index.html">Table of Contents</a></h3>
    <ul>
<li><a class="reference internal" href="#">CPack Cygwin Generator</a><ul>
<li><a class="reference internal" href="#variables-affecting-the-cpack-cygwin-generator">Variables affecting the CPack Cygwin generator</a></li>
<li><a class="reference internal" href="#variables-specific-to-cpack-cygwin-generator">Variables specific to CPack Cygwin generator</a></li>
</ul>
</li>
</ul>

  </div>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="bundle.html"
                          title="previous chapter">CPack Bundle Generator</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="deb.html"
                          title="next chapter">CPack DEB Generator</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../_sources/cpack_gen/cygwin.rst.txt"
            rel="nofollow">Show Source</a></li>
    </ul>
   </div>
<div id="searchbox" style="display: none" role="search">
  <h3 id="searchlabel">Quick search</h3>
    <div class="searchformwrapper">
    <form class="search" action="../search.html" method="get">
      <input type="text" name="q" aria-labelledby="searchlabel" autocomplete="off" autocorrect="off" autocapitalize="off" spellcheck="false"/>
      <input type="submit" value="Go" />
    </form>
    </div>
</div>
<script>document.getElementById('searchbox').style.display = "block"</script>
        </div>
      </div>
      <div class="clearer"></div>
    </div>
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             >index</a></li>
        <li class="right" >
          <a href="deb.html" title="CPack DEB Generator"
             >next</a> |</li>
        <li class="right" >
          <a href="bundle.html" title="CPack Bundle Generator"
             >previous</a> |</li>
  <li>
    <img src="../_static/cmake-logo-16.png" alt=""
         style="vertical-align: middle; margin-top: -2px" />
  </li>
  <li>
    <a href="https://cmake.org/">CMake</a> &#187;
  </li>
  <li>
    <a href="../index.html">3.28.1 Documentation</a> &#187;
  </li>

          <li class="nav-item nav-item-1"><a href="../manual/cpack-generators.7.html" >cpack-generators(7)</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href="">CPack Cygwin Generator</a></li> 
      </ul>
    </div>

    <div class="footer" role="contentinfo">
        &#169; Copyright 2000-2023 Kitware, Inc. and Contributors.
      Created using <a href="https://www.sphinx-doc.org/">Sphinx</a> 6.2.1.
    </div>
  </body>
</html>