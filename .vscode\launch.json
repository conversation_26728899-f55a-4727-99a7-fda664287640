{"version": "0.2.0", "configurations": [{"name": "启动WHIP服务器", "type": "python", "request": "launch", "program": "${workspaceFolder}/whip_server_with_ai.py", "console": "integratedTerminal", "cwd": "${workspaceFolder}", "env": {"CUDA_PATH": "C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v12.6", "CUDA_PATH_V12_6": "C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v12.6", "PATH": "${env:PATH};C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v12.6\\bin;C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v12.6\\libnvvp"}, "args": []}, {"name": "启动WHIP服务器 (详细模式)", "type": "python", "request": "launch", "program": "${workspaceFolder}/whip_server_with_ai.py", "console": "integratedTerminal", "cwd": "${workspaceFolder}", "env": {"CUDA_PATH": "C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v12.6", "CUDA_PATH_V12_6": "C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v12.6", "PATH": "${env:PATH};C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v12.6\\bin;C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v12.6\\libnvvp"}, "args": ["--verbose"]}]}