
<!DOCTYPE html>

<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="generator" content="Docutils 0.19: https://docutils.sourceforge.io/" />

  <title>CPack productbuild Generator &mdash; CMake 3.28.1 Documentation</title>

    <link rel="stylesheet" type="text/css" href="../_static/pygments.css" />
    <link rel="stylesheet" type="text/css" href="../_static/cmake.css" />
    
    <script data-url_root="../" id="documentation_options" src="../_static/documentation_options.js"></script>
    <script src="../_static/doctools.js"></script>
    <script src="../_static/sphinx_highlight.js"></script>
    
    <link rel="icon" href="../_static/cmake-favicon.ico"/>
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="next" title="CPack RPM Generator" href="rpm.html" />
    <link rel="prev" title="CPack PackageMaker Generator" href="packagemaker.html" />
 

  </head><body>

    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             accesskey="I">index</a></li>
        <li class="right" >
          <a href="rpm.html" title="CPack RPM Generator"
             accesskey="N">next</a> |</li>
        <li class="right" >
          <a href="packagemaker.html" title="CPack PackageMaker Generator"
             accesskey="P">previous</a> |</li>
  <li>
    <img src="../_static/cmake-logo-16.png" alt=""
         style="vertical-align: middle; margin-top: -2px" />
  </li>
  <li>
    <a href="https://cmake.org/">CMake</a> &#187;
  </li>
  <li>
    <a href="../index.html">3.28.1 Documentation</a> &#187;
  </li>

          <li class="nav-item nav-item-1"><a href="../manual/cpack-generators.7.html" accesskey="U">cpack-generators(7)</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href="">CPack productbuild Generator</a></li> 
      </ul>
    </div>  

    <div class="document">
      <div class="documentwrapper">
        <div class="bodywrapper">
          <div class="body" role="main">
            
  <section id="cpack-productbuild-generator">
<span id="cpack_gen:CPack productbuild Generator"></span><h1>CPack productbuild Generator<a class="headerlink" href="#cpack-productbuild-generator" title="Permalink to this heading">¶</a></h1>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.7.</span></p>
</div>
<p>productbuild CPack generator (macOS).</p>
<section id="variables-specific-to-cpack-productbuild-generator">
<h2>Variables specific to CPack productbuild generator<a class="headerlink" href="#variables-specific-to-cpack-productbuild-generator" title="Permalink to this heading">¶</a></h2>
<p>The following variable is specific to installers built on Mac
macOS using ProductBuild:</p>
<dl class="cmake variable">
<dt class="sig sig-object cmake" id="variable:CPACK_COMMAND_PRODUCTBUILD">
<span class="sig-name descname"><span class="pre">CPACK_COMMAND_PRODUCTBUILD</span></span><a class="headerlink" href="#variable:CPACK_COMMAND_PRODUCTBUILD" title="Permalink to this definition">¶</a></dt>
<dd><p>Path to the <code class="docutils literal notranslate"><span class="pre">productbuild(1)</span></code> command used to generate a product archive for
the macOS Installer or Mac App Store.  This variable can be used to override
the automatically detected command (or specify its location if the
auto-detection fails to find it).</p>
</dd></dl>

<dl class="cmake variable">
<dt class="sig sig-object cmake" id="variable:CPACK_PRODUCTBUILD_IDENTIFIER">
<span class="sig-name descname"><span class="pre">CPACK_PRODUCTBUILD_IDENTIFIER</span></span><a class="headerlink" href="#variable:CPACK_PRODUCTBUILD_IDENTIFIER" title="Permalink to this definition">¶</a></dt>
<dd><div class="versionadded">
<p><span class="versionmodified added">New in version 3.23.</span></p>
</div>
<p>Set the unique (non-localized) product identifier to be associated with the
product (i.e., <code class="docutils literal notranslate"><span class="pre">com.kitware.cmake</span></code>). Any component product names will be
appended to this value.</p>
</dd></dl>

<dl class="cmake variable">
<dt class="sig sig-object cmake" id="variable:CPACK_PRODUCTBUILD_IDENTITY_NAME">
<span class="sig-name descname"><span class="pre">CPACK_PRODUCTBUILD_IDENTITY_NAME</span></span><a class="headerlink" href="#variable:CPACK_PRODUCTBUILD_IDENTITY_NAME" title="Permalink to this definition">¶</a></dt>
<dd><div class="versionadded">
<p><span class="versionmodified added">New in version 3.8.</span></p>
</div>
<p>Adds a digital signature to the resulting package.</p>
</dd></dl>

<dl class="cmake variable">
<dt class="sig sig-object cmake" id="variable:CPACK_PRODUCTBUILD_KEYCHAIN_PATH">
<span class="sig-name descname"><span class="pre">CPACK_PRODUCTBUILD_KEYCHAIN_PATH</span></span><a class="headerlink" href="#variable:CPACK_PRODUCTBUILD_KEYCHAIN_PATH" title="Permalink to this definition">¶</a></dt>
<dd><div class="versionadded">
<p><span class="versionmodified added">New in version 3.8.</span></p>
</div>
<p>Specify a specific keychain to search for the signing identity.</p>
</dd></dl>

<dl class="cmake variable">
<dt class="sig sig-object cmake" id="variable:CPACK_COMMAND_PKGBUILD">
<span class="sig-name descname"><span class="pre">CPACK_COMMAND_PKGBUILD</span></span><a class="headerlink" href="#variable:CPACK_COMMAND_PKGBUILD" title="Permalink to this definition">¶</a></dt>
<dd><p>Path to the <code class="docutils literal notranslate"><span class="pre">pkgbuild(1)</span></code> command used to generate an macOS component package
on macOS.  This variable can be used to override the automatically detected
command (or specify its location if the auto-detection fails to find it).</p>
</dd></dl>

<dl class="cmake variable">
<dt class="sig sig-object cmake" id="variable:CPACK_PKGBUILD_IDENTITY_NAME">
<span class="sig-name descname"><span class="pre">CPACK_PKGBUILD_IDENTITY_NAME</span></span><a class="headerlink" href="#variable:CPACK_PKGBUILD_IDENTITY_NAME" title="Permalink to this definition">¶</a></dt>
<dd><div class="versionadded">
<p><span class="versionmodified added">New in version 3.8.</span></p>
</div>
<p>Adds a digital signature to the resulting package.</p>
</dd></dl>

<dl class="cmake variable">
<dt class="sig sig-object cmake" id="variable:CPACK_PKGBUILD_KEYCHAIN_PATH">
<span class="sig-name descname"><span class="pre">CPACK_PKGBUILD_KEYCHAIN_PATH</span></span><a class="headerlink" href="#variable:CPACK_PKGBUILD_KEYCHAIN_PATH" title="Permalink to this definition">¶</a></dt>
<dd><div class="versionadded">
<p><span class="versionmodified added">New in version 3.8.</span></p>
</div>
<p>Specify a specific keychain to search for the signing identity.</p>
</dd></dl>

<dl class="cmake variable">
<dt class="sig sig-object cmake" id="variable:CPACK_PREFLIGHT_&lt;COMP&gt;_SCRIPT">
<span class="sig-name descname"><span class="pre">CPACK_PREFLIGHT_&lt;COMP&gt;_SCRIPT</span></span><a class="headerlink" href="#variable:CPACK_PREFLIGHT_<COMP>_SCRIPT" title="Permalink to this definition">¶</a></dt>
<dd><p>Full path to a file that will be used as the <code class="docutils literal notranslate"><span class="pre">preinstall</span></code> script for the
named <code class="docutils literal notranslate"><span class="pre">&lt;COMP&gt;</span></code> component's package, where <code class="docutils literal notranslate"><span class="pre">&lt;COMP&gt;</span></code> is the uppercased
component name.  No <code class="docutils literal notranslate"><span class="pre">preinstall</span></code> script is added if this variable is not
defined for a given component.</p>
</dd></dl>

<dl class="cmake variable">
<dt class="sig sig-object cmake" id="variable:CPACK_POSTFLIGHT_&lt;COMP&gt;_SCRIPT">
<span class="sig-name descname"><span class="pre">CPACK_POSTFLIGHT_&lt;COMP&gt;_SCRIPT</span></span><a class="headerlink" href="#variable:CPACK_POSTFLIGHT_<COMP>_SCRIPT" title="Permalink to this definition">¶</a></dt>
<dd><p>Full path to a file that will be used as the <code class="docutils literal notranslate"><span class="pre">postinstall</span></code> script for the
named <code class="docutils literal notranslate"><span class="pre">&lt;COMP&gt;</span></code> component's package, where <code class="docutils literal notranslate"><span class="pre">&lt;COMP&gt;</span></code> is the uppercased
component name.  No <code class="docutils literal notranslate"><span class="pre">postinstall</span></code> script is added if this variable is not
defined for a given component.</p>
</dd></dl>

<dl class="cmake variable">
<dt class="sig sig-object cmake" id="variable:CPACK_PRODUCTBUILD_RESOURCES_DIR">
<span class="sig-name descname"><span class="pre">CPACK_PRODUCTBUILD_RESOURCES_DIR</span></span><a class="headerlink" href="#variable:CPACK_PRODUCTBUILD_RESOURCES_DIR" title="Permalink to this definition">¶</a></dt>
<dd><div class="versionadded">
<p><span class="versionmodified added">New in version 3.9.</span></p>
</div>
<p>If specified the productbuild generator copies files from this directory
(including subdirectories) to the <code class="docutils literal notranslate"><span class="pre">Resources</span></code> directory. This is done
before the <span class="target" id="index-0-variable:CPACK_RESOURCE_FILE_WELCOME"></span><a class="reference internal" href="../module/CPack.html#variable:CPACK_RESOURCE_FILE_WELCOME" title="CPACK_RESOURCE_FILE_WELCOME"><code class="xref cmake cmake-variable docutils literal notranslate"><span class="pre">CPACK_RESOURCE_FILE_WELCOME</span></code></a>,
<span class="target" id="index-0-variable:CPACK_RESOURCE_FILE_README"></span><a class="reference internal" href="../module/CPack.html#variable:CPACK_RESOURCE_FILE_README" title="CPACK_RESOURCE_FILE_README"><code class="xref cmake cmake-variable docutils literal notranslate"><span class="pre">CPACK_RESOURCE_FILE_README</span></code></a>, and
<span class="target" id="index-0-variable:CPACK_RESOURCE_FILE_LICENSE"></span><a class="reference internal" href="../module/CPack.html#variable:CPACK_RESOURCE_FILE_LICENSE" title="CPACK_RESOURCE_FILE_LICENSE"><code class="xref cmake cmake-variable docutils literal notranslate"><span class="pre">CPACK_RESOURCE_FILE_LICENSE</span></code></a> files are copied.</p>
</dd></dl>

<dl class="cmake variable">
<dt class="sig sig-object cmake" id="variable:CPACK_PRODUCTBUILD_DOMAINS">
<span class="sig-name descname"><span class="pre">CPACK_PRODUCTBUILD_DOMAINS</span></span><a class="headerlink" href="#variable:CPACK_PRODUCTBUILD_DOMAINS" title="Permalink to this definition">¶</a></dt>
<dd><div class="versionadded">
<p><span class="versionmodified added">New in version 3.23.</span></p>
</div>
<p>This option enables more granular control over where the product may be
installed. When it is set to true, a <code class="docutils literal notranslate"><span class="pre">domains</span></code> element of the following
form will be added to the productbuild Distribution XML:</p>
<div class="highlight-xml notranslate"><div class="highlight"><pre><span></span><span class="nt">&lt;domains</span><span class="w"> </span><span class="na">enable_anywhere=</span><span class="s">&quot;true&quot;</span><span class="w"> </span><span class="na">enable_currentUserHome=</span><span class="s">&quot;false&quot;</span><span class="w"> </span><span class="na">enable_localSystem=</span><span class="s">&quot;true&quot;</span><span class="nt">/&gt;</span>
</pre></div>
</div>
<p>The default values are as shown above, but can be overridden with
<span class="target" id="index-0-variable:CPACK_PRODUCTBUILD_DOMAINS_ANYWHERE"></span><a class="reference internal" href="#variable:CPACK_PRODUCTBUILD_DOMAINS_ANYWHERE" title="CPACK_PRODUCTBUILD_DOMAINS_ANYWHERE"><code class="xref cmake cmake-variable docutils literal notranslate"><span class="pre">CPACK_PRODUCTBUILD_DOMAINS_ANYWHERE</span></code></a>,
<span class="target" id="index-0-variable:CPACK_PRODUCTBUILD_DOMAINS_USER"></span><a class="reference internal" href="#variable:CPACK_PRODUCTBUILD_DOMAINS_USER" title="CPACK_PRODUCTBUILD_DOMAINS_USER"><code class="xref cmake cmake-variable docutils literal notranslate"><span class="pre">CPACK_PRODUCTBUILD_DOMAINS_USER</span></code></a>, and
<span class="target" id="index-0-variable:CPACK_PRODUCTBUILD_DOMAINS_ROOT"></span><a class="reference internal" href="#variable:CPACK_PRODUCTBUILD_DOMAINS_ROOT" title="CPACK_PRODUCTBUILD_DOMAINS_ROOT"><code class="xref cmake cmake-variable docutils literal notranslate"><span class="pre">CPACK_PRODUCTBUILD_DOMAINS_ROOT</span></code></a>.</p>
</dd></dl>

<dl class="cmake variable">
<dt class="sig sig-object cmake" id="variable:CPACK_PRODUCTBUILD_DOMAINS_ANYWHERE">
<span class="sig-name descname"><span class="pre">CPACK_PRODUCTBUILD_DOMAINS_ANYWHERE</span></span><a class="headerlink" href="#variable:CPACK_PRODUCTBUILD_DOMAINS_ANYWHERE" title="Permalink to this definition">¶</a></dt>
<dd><div class="versionadded">
<p><span class="versionmodified added">New in version 3.23.</span></p>
</div>
<p>May be used to override the <code class="docutils literal notranslate"><span class="pre">enable_anywhere</span></code> attribute in the <code class="docutils literal notranslate"><span class="pre">domains</span></code>
element of the Distribution XML. When set to true, the product can be
installed at the root of any volume, including non-system volumes.</p>
<p><span class="target" id="index-0-variable:CPACK_PRODUCTBUILD_DOMAINS"></span><a class="reference internal" href="#variable:CPACK_PRODUCTBUILD_DOMAINS" title="CPACK_PRODUCTBUILD_DOMAINS"><code class="xref cmake cmake-variable docutils literal notranslate"><span class="pre">CPACK_PRODUCTBUILD_DOMAINS</span></code></a> must be set to true for this variable
to have any effect.</p>
</dd></dl>

<dl class="cmake variable">
<dt class="sig sig-object cmake" id="variable:CPACK_PRODUCTBUILD_DOMAINS_USER">
<span class="sig-name descname"><span class="pre">CPACK_PRODUCTBUILD_DOMAINS_USER</span></span><a class="headerlink" href="#variable:CPACK_PRODUCTBUILD_DOMAINS_USER" title="Permalink to this definition">¶</a></dt>
<dd><div class="versionadded">
<p><span class="versionmodified added">New in version 3.23.</span></p>
</div>
<p>May be used to override the <code class="docutils literal notranslate"><span class="pre">enable_currentUserHome</span></code> attribute in the
<code class="docutils literal notranslate"><span class="pre">domains</span></code> element of the Distribution XML. When set to true, the product
can be installed into the current user's home directory. Note that when
installing into the user's home directory, the following additional
requirements will apply:</p>
<ul class="simple">
<li><p>The installer may not write outside the user's home directory.</p></li>
<li><p>The install will be performed as the current user rather than as <code class="docutils literal notranslate"><span class="pre">root</span></code>.
This may have ramifications for <span class="target" id="index-0-variable:CPACK_PREFLIGHT_&lt;COMP&gt;_SCRIPT"></span><a class="reference internal" href="#variable:CPACK_PREFLIGHT_&lt;COMP&gt;_SCRIPT" title="CPACK_PREFLIGHT_&lt;COMP&gt;_SCRIPT"><code class="xref cmake cmake-variable docutils literal notranslate"><span class="pre">CPACK_PREFLIGHT_&lt;COMP&gt;_SCRIPT</span></code></a>
and <span class="target" id="index-0-variable:CPACK_POSTFLIGHT_&lt;COMP&gt;_SCRIPT"></span><a class="reference internal" href="#variable:CPACK_POSTFLIGHT_&lt;COMP&gt;_SCRIPT" title="CPACK_POSTFLIGHT_&lt;COMP&gt;_SCRIPT"><code class="xref cmake cmake-variable docutils literal notranslate"><span class="pre">CPACK_POSTFLIGHT_&lt;COMP&gt;_SCRIPT</span></code></a>.</p></li>
<li><p>Administrative privileges will not be needed to perform the install.</p></li>
</ul>
<p><span class="target" id="index-1-variable:CPACK_PRODUCTBUILD_DOMAINS"></span><a class="reference internal" href="#variable:CPACK_PRODUCTBUILD_DOMAINS" title="CPACK_PRODUCTBUILD_DOMAINS"><code class="xref cmake cmake-variable docutils literal notranslate"><span class="pre">CPACK_PRODUCTBUILD_DOMAINS</span></code></a> must be set to true for this variable
to have any effect.</p>
</dd></dl>

<dl class="cmake variable">
<dt class="sig sig-object cmake" id="variable:CPACK_PRODUCTBUILD_DOMAINS_ROOT">
<span class="sig-name descname"><span class="pre">CPACK_PRODUCTBUILD_DOMAINS_ROOT</span></span><a class="headerlink" href="#variable:CPACK_PRODUCTBUILD_DOMAINS_ROOT" title="Permalink to this definition">¶</a></dt>
<dd><div class="versionadded">
<p><span class="versionmodified added">New in version 3.23.</span></p>
</div>
<p>May be used to override the <code class="docutils literal notranslate"><span class="pre">enable_localSystem</span></code> attribute in the
<code class="docutils literal notranslate"><span class="pre">domains</span></code> element of the Distribution XML. When set to true, the product
can be installed in the root directory. This should normally be set to true
unless the product should only be installed to the user's home directory.</p>
<p><span class="target" id="index-2-variable:CPACK_PRODUCTBUILD_DOMAINS"></span><a class="reference internal" href="#variable:CPACK_PRODUCTBUILD_DOMAINS" title="CPACK_PRODUCTBUILD_DOMAINS"><code class="xref cmake cmake-variable docutils literal notranslate"><span class="pre">CPACK_PRODUCTBUILD_DOMAINS</span></code></a> must be set to true for this variable
to have any effect.</p>
</dd></dl>

<section id="background-image">
<h3>Background Image<a class="headerlink" href="#background-image" title="Permalink to this heading">¶</a></h3>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.17.</span></p>
</div>
<p>This group of variables controls the background image of the generated
installer.</p>
<dl class="cmake variable">
<dt class="sig sig-object cmake" id="variable:CPACK_PRODUCTBUILD_BACKGROUND">
<span class="sig-name descname"><span class="pre">CPACK_PRODUCTBUILD_BACKGROUND</span></span><a class="headerlink" href="#variable:CPACK_PRODUCTBUILD_BACKGROUND" title="Permalink to this definition">¶</a></dt>
<dd><p>Adds a background to Distribution XML if specified. The value contains the
path to image in <code class="docutils literal notranslate"><span class="pre">Resources</span></code> directory.</p>
</dd></dl>

<dl class="cmake variable">
<dt class="sig sig-object cmake" id="variable:CPACK_PRODUCTBUILD_BACKGROUND_ALIGNMENT">
<span class="sig-name descname"><span class="pre">CPACK_PRODUCTBUILD_BACKGROUND_ALIGNMENT</span></span><a class="headerlink" href="#variable:CPACK_PRODUCTBUILD_BACKGROUND_ALIGNMENT" title="Permalink to this definition">¶</a></dt>
<dd><p>Adds an <code class="docutils literal notranslate"><span class="pre">alignment</span></code> attribute to the background in Distribution XML.
Refer to Apple documentation for valid values.</p>
</dd></dl>

<dl class="cmake variable">
<dt class="sig sig-object cmake" id="variable:CPACK_PRODUCTBUILD_BACKGROUND_SCALING">
<span class="sig-name descname"><span class="pre">CPACK_PRODUCTBUILD_BACKGROUND_SCALING</span></span><a class="headerlink" href="#variable:CPACK_PRODUCTBUILD_BACKGROUND_SCALING" title="Permalink to this definition">¶</a></dt>
<dd><p>Adds a <code class="docutils literal notranslate"><span class="pre">scaling</span></code> attribute to the background in Distribution XML.
Refer to Apple documentation for valid values.</p>
</dd></dl>

<dl class="cmake variable">
<dt class="sig sig-object cmake" id="variable:CPACK_PRODUCTBUILD_BACKGROUND_MIME_TYPE">
<span class="sig-name descname"><span class="pre">CPACK_PRODUCTBUILD_BACKGROUND_MIME_TYPE</span></span><a class="headerlink" href="#variable:CPACK_PRODUCTBUILD_BACKGROUND_MIME_TYPE" title="Permalink to this definition">¶</a></dt>
<dd><p>Adds a <code class="docutils literal notranslate"><span class="pre">mime-type</span></code> attribute to the background in Distribution XML.
The option contains MIME type of an image.</p>
</dd></dl>

<dl class="cmake variable">
<dt class="sig sig-object cmake" id="variable:CPACK_PRODUCTBUILD_BACKGROUND_UTI">
<span class="sig-name descname"><span class="pre">CPACK_PRODUCTBUILD_BACKGROUND_UTI</span></span><a class="headerlink" href="#variable:CPACK_PRODUCTBUILD_BACKGROUND_UTI" title="Permalink to this definition">¶</a></dt>
<dd><p>Adds an <code class="docutils literal notranslate"><span class="pre">uti</span></code> attribute to the background in Distribution XML.
The option contains UTI type of an image.</p>
</dd></dl>

<dl class="cmake variable">
<dt class="sig sig-object cmake" id="variable:CPACK_PRODUCTBUILD_BACKGROUND_DARKAQUA">
<span class="sig-name descname"><span class="pre">CPACK_PRODUCTBUILD_BACKGROUND_DARKAQUA</span></span><a class="headerlink" href="#variable:CPACK_PRODUCTBUILD_BACKGROUND_DARKAQUA" title="Permalink to this definition">¶</a></dt>
<dd><p>Adds a background for the Dark Aqua theme to Distribution XML if
specified. The value contains the path to image in <code class="docutils literal notranslate"><span class="pre">Resources</span></code>
directory.</p>
</dd></dl>

<dl class="cmake variable">
<dt class="sig sig-object cmake" id="variable:CPACK_PRODUCTBUILD_BACKGROUND_DARKAQUA_ALIGNMENT">
<span class="sig-name descname"><span class="pre">CPACK_PRODUCTBUILD_BACKGROUND_DARKAQUA_ALIGNMENT</span></span><a class="headerlink" href="#variable:CPACK_PRODUCTBUILD_BACKGROUND_DARKAQUA_ALIGNMENT" title="Permalink to this definition">¶</a></dt>
<dd><p>Does the same as <span class="target" id="index-0-variable:CPACK_PRODUCTBUILD_BACKGROUND_ALIGNMENT"></span><a class="reference internal" href="#variable:CPACK_PRODUCTBUILD_BACKGROUND_ALIGNMENT" title="CPACK_PRODUCTBUILD_BACKGROUND_ALIGNMENT"><code class="xref cmake cmake-variable docutils literal notranslate"><span class="pre">CPACK_PRODUCTBUILD_BACKGROUND_ALIGNMENT</span></code></a> option,
but for the dark theme.</p>
</dd></dl>

<dl class="cmake variable">
<dt class="sig sig-object cmake" id="variable:CPACK_PRODUCTBUILD_BACKGROUND_DARKAQUA_SCALING">
<span class="sig-name descname"><span class="pre">CPACK_PRODUCTBUILD_BACKGROUND_DARKAQUA_SCALING</span></span><a class="headerlink" href="#variable:CPACK_PRODUCTBUILD_BACKGROUND_DARKAQUA_SCALING" title="Permalink to this definition">¶</a></dt>
<dd><p>Does the same as <span class="target" id="index-0-variable:CPACK_PRODUCTBUILD_BACKGROUND_SCALING"></span><a class="reference internal" href="#variable:CPACK_PRODUCTBUILD_BACKGROUND_SCALING" title="CPACK_PRODUCTBUILD_BACKGROUND_SCALING"><code class="xref cmake cmake-variable docutils literal notranslate"><span class="pre">CPACK_PRODUCTBUILD_BACKGROUND_SCALING</span></code></a> option,
but for the dark theme.</p>
</dd></dl>

<dl class="cmake variable">
<dt class="sig sig-object cmake" id="variable:CPACK_PRODUCTBUILD_BACKGROUND_DARKAQUA_MIME_TYPE">
<span class="sig-name descname"><span class="pre">CPACK_PRODUCTBUILD_BACKGROUND_DARKAQUA_MIME_TYPE</span></span><a class="headerlink" href="#variable:CPACK_PRODUCTBUILD_BACKGROUND_DARKAQUA_MIME_TYPE" title="Permalink to this definition">¶</a></dt>
<dd><p>Does the same as <span class="target" id="index-0-variable:CPACK_PRODUCTBUILD_BACKGROUND_MIME_TYPE"></span><a class="reference internal" href="#variable:CPACK_PRODUCTBUILD_BACKGROUND_MIME_TYPE" title="CPACK_PRODUCTBUILD_BACKGROUND_MIME_TYPE"><code class="xref cmake cmake-variable docutils literal notranslate"><span class="pre">CPACK_PRODUCTBUILD_BACKGROUND_MIME_TYPE</span></code></a> option,
but for the dark theme.</p>
</dd></dl>

<dl class="cmake variable">
<dt class="sig sig-object cmake" id="variable:CPACK_PRODUCTBUILD_BACKGROUND_DARKAQUA_UTI">
<span class="sig-name descname"><span class="pre">CPACK_PRODUCTBUILD_BACKGROUND_DARKAQUA_UTI</span></span><a class="headerlink" href="#variable:CPACK_PRODUCTBUILD_BACKGROUND_DARKAQUA_UTI" title="Permalink to this definition">¶</a></dt>
<dd><p>Does the same as <span class="target" id="index-0-variable:CPACK_PRODUCTBUILD_BACKGROUND_UTI"></span><a class="reference internal" href="#variable:CPACK_PRODUCTBUILD_BACKGROUND_UTI" title="CPACK_PRODUCTBUILD_BACKGROUND_UTI"><code class="xref cmake cmake-variable docutils literal notranslate"><span class="pre">CPACK_PRODUCTBUILD_BACKGROUND_UTI</span></code></a> option,
but for the dark theme.</p>
</dd></dl>

</section>
</section>
<section id="distribution-xml-template">
<h2>Distribution XML Template<a class="headerlink" href="#distribution-xml-template" title="Permalink to this heading">¶</a></h2>
<p>CPack uses a template file to generate the <code class="docutils literal notranslate"><span class="pre">distribution.dist</span></code> file used
internally by this package generator. Ordinarily, CMake provides the template
file, but projects may supply their own by placing a file called
<code class="docutils literal notranslate"><span class="pre">CPack.distribution.dist.in</span></code> in one of the directories listed in the
<span class="target" id="index-0-variable:CMAKE_MODULE_PATH"></span><a class="reference internal" href="../variable/CMAKE_MODULE_PATH.html#variable:CMAKE_MODULE_PATH" title="CMAKE_MODULE_PATH"><code class="xref cmake cmake-variable docutils literal notranslate"><span class="pre">CMAKE_MODULE_PATH</span></code></a> variable. CPack will then pick up the project's
template file instead of using its own.</p>
<p>The <code class="docutils literal notranslate"><span class="pre">distribution.dist</span></code> file is generated by performing substitutions
similar to the <span class="target" id="index-0-command:configure_file"></span><a class="reference internal" href="../command/configure_file.html#command:configure_file" title="configure_file"><code class="xref cmake cmake-command docutils literal notranslate"><span class="pre">configure_file()</span></code></a> command. Any variable set when
CPack runs will be available for substitution using the usual <code class="docutils literal notranslate"><span class="pre">&#64;...&#64;</span></code>
form. The following variables are also set internally and made available for
substitution:</p>
<dl>
<dt><code class="docutils literal notranslate"><span class="pre">CPACK_RESOURCE_FILE_LICENSE_NOPATH</span></code></dt><dd><p>Same as <span class="target" id="index-1-variable:CPACK_RESOURCE_FILE_LICENSE"></span><a class="reference internal" href="../module/CPack.html#variable:CPACK_RESOURCE_FILE_LICENSE" title="CPACK_RESOURCE_FILE_LICENSE"><code class="xref cmake cmake-variable docutils literal notranslate"><span class="pre">CPACK_RESOURCE_FILE_LICENSE</span></code></a> except without the path.
The named file will be available in the same directory as the generated
<code class="docutils literal notranslate"><span class="pre">distribution.dist</span></code> file.</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">CPACK_RESOURCE_FILE_README_NOPATH</span></code></dt><dd><p>Same as <span class="target" id="index-1-variable:CPACK_RESOURCE_FILE_README"></span><a class="reference internal" href="../module/CPack.html#variable:CPACK_RESOURCE_FILE_README" title="CPACK_RESOURCE_FILE_README"><code class="xref cmake cmake-variable docutils literal notranslate"><span class="pre">CPACK_RESOURCE_FILE_README</span></code></a> except without the path.
The named file will be available in the same directory as the generated
<code class="docutils literal notranslate"><span class="pre">distribution.dist</span></code> file.</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">CPACK_RESOURCE_FILE_WELCOME_NOPATH</span></code></dt><dd><p>Same as <span class="target" id="index-1-variable:CPACK_RESOURCE_FILE_WELCOME"></span><a class="reference internal" href="../module/CPack.html#variable:CPACK_RESOURCE_FILE_WELCOME" title="CPACK_RESOURCE_FILE_WELCOME"><code class="xref cmake cmake-variable docutils literal notranslate"><span class="pre">CPACK_RESOURCE_FILE_WELCOME</span></code></a> except without the path.
The named file will be available in the same directory as the generated
<code class="docutils literal notranslate"><span class="pre">distribution.dist</span></code> file.</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">CPACK_APPLE_PKG_INSTALLER_CONTENT</span></code></dt><dd><div class="versionadded">
<p><span class="versionmodified added">New in version 3.23.</span></p>
</div>
<p>This contains all the XML elements that specify installer-wide options
(including domain details), default backgrounds and the choices outline.</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">CPACK_PACKAGEMAKER_CHOICES</span></code></dt><dd><div class="deprecated">
<p><span class="versionmodified deprecated">Deprecated since version 3.23.</span></p>
</div>
<p>This contains only the XML elements that specify the default backgrounds
and the choices outline. It does not include the installer-wide options or
any domain details. Use <code class="docutils literal notranslate"><span class="pre">CPACK_APPLE_PKG_INSTALLER_CONTENT</span></code> instead.</p>
</dd>
</dl>
</section>
</section>


            <div class="clearer"></div>
          </div>
        </div>
      </div>
      <div class="sphinxsidebar" role="navigation" aria-label="main navigation">
        <div class="sphinxsidebarwrapper">
  <div>
    <h3><a href="../index.html">Table of Contents</a></h3>
    <ul>
<li><a class="reference internal" href="#">CPack productbuild Generator</a><ul>
<li><a class="reference internal" href="#variables-specific-to-cpack-productbuild-generator">Variables specific to CPack productbuild generator</a><ul>
<li><a class="reference internal" href="#background-image">Background Image</a></li>
</ul>
</li>
<li><a class="reference internal" href="#distribution-xml-template">Distribution XML Template</a></li>
</ul>
</li>
</ul>

  </div>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="packagemaker.html"
                          title="previous chapter">CPack PackageMaker Generator</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="rpm.html"
                          title="next chapter">CPack RPM Generator</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../_sources/cpack_gen/productbuild.rst.txt"
            rel="nofollow">Show Source</a></li>
    </ul>
   </div>
<div id="searchbox" style="display: none" role="search">
  <h3 id="searchlabel">Quick search</h3>
    <div class="searchformwrapper">
    <form class="search" action="../search.html" method="get">
      <input type="text" name="q" aria-labelledby="searchlabel" autocomplete="off" autocorrect="off" autocapitalize="off" spellcheck="false"/>
      <input type="submit" value="Go" />
    </form>
    </div>
</div>
<script>document.getElementById('searchbox').style.display = "block"</script>
        </div>
      </div>
      <div class="clearer"></div>
    </div>
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             >index</a></li>
        <li class="right" >
          <a href="rpm.html" title="CPack RPM Generator"
             >next</a> |</li>
        <li class="right" >
          <a href="packagemaker.html" title="CPack PackageMaker Generator"
             >previous</a> |</li>
  <li>
    <img src="../_static/cmake-logo-16.png" alt=""
         style="vertical-align: middle; margin-top: -2px" />
  </li>
  <li>
    <a href="https://cmake.org/">CMake</a> &#187;
  </li>
  <li>
    <a href="../index.html">3.28.1 Documentation</a> &#187;
  </li>

          <li class="nav-item nav-item-1"><a href="../manual/cpack-generators.7.html" >cpack-generators(7)</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href="">CPack productbuild Generator</a></li> 
      </ul>
    </div>

    <div class="footer" role="contentinfo">
        &#169; Copyright 2000-2023 Kitware, Inc. and Contributors.
      Created using <a href="https://www.sphinx-doc.org/">Sphinx</a> 6.2.1.
    </div>
  </body>
</html>