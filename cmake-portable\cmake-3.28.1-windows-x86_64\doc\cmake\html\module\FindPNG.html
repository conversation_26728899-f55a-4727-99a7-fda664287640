
<!DOCTYPE html>

<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="generator" content="Docutils 0.19: https://docutils.sourceforge.io/" />

  <title>FindPNG &mdash; CMake 3.28.1 Documentation</title>

    <link rel="stylesheet" type="text/css" href="../_static/pygments.css" />
    <link rel="stylesheet" type="text/css" href="../_static/cmake.css" />
    
    <script data-url_root="../" id="documentation_options" src="../_static/documentation_options.js"></script>
    <script src="../_static/doctools.js"></script>
    <script src="../_static/sphinx_highlight.js"></script>
    
    <link rel="icon" href="../_static/cmake-favicon.ico"/>
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="next" title="FindPostgreSQL" href="FindPostgreSQL.html" />
    <link rel="prev" title="FindPkgConfig" href="FindPkgConfig.html" />
 

  </head><body>

    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             accesskey="I">index</a></li>
        <li class="right" >
          <a href="FindPostgreSQL.html" title="FindPostgreSQL"
             accesskey="N">next</a> |</li>
        <li class="right" >
          <a href="FindPkgConfig.html" title="FindPkgConfig"
             accesskey="P">previous</a> |</li>
  <li>
    <img src="../_static/cmake-logo-16.png" alt=""
         style="vertical-align: middle; margin-top: -2px" />
  </li>
  <li>
    <a href="https://cmake.org/">CMake</a> &#187;
  </li>
  <li>
    <a href="../index.html">3.28.1 Documentation</a> &#187;
  </li>

          <li class="nav-item nav-item-1"><a href="../manual/cmake-modules.7.html" accesskey="U">cmake-modules(7)</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href="">FindPNG</a></li> 
      </ul>
    </div>  

    <div class="document">
      <div class="documentwrapper">
        <div class="bodywrapper">
          <div class="body" role="main">
            
  <section id="findpng">
<span id="module:FindPNG"></span><h1>FindPNG<a class="headerlink" href="#findpng" title="Permalink to this heading">¶</a></h1>
<p>Find libpng, the official reference library for the PNG image format.</p>
<section id="imported-targets">
<h2>Imported targets<a class="headerlink" href="#imported-targets" title="Permalink to this heading">¶</a></h2>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.5.</span></p>
</div>
<p>This module defines the following <span class="target" id="index-0-prop_tgt:IMPORTED"></span><a class="reference internal" href="../prop_tgt/IMPORTED.html#prop_tgt:IMPORTED" title="IMPORTED"><code class="xref cmake cmake-prop_tgt docutils literal notranslate"><span class="pre">IMPORTED</span></code></a> target:</p>
<dl class="simple">
<dt><code class="docutils literal notranslate"><span class="pre">PNG::PNG</span></code></dt><dd><p>The libpng library, if found.</p>
</dd>
</dl>
</section>
<section id="result-variables">
<h2>Result variables<a class="headerlink" href="#result-variables" title="Permalink to this heading">¶</a></h2>
<p>This module will set the following variables in your project:</p>
<dl class="simple">
<dt><code class="docutils literal notranslate"><span class="pre">PNG_INCLUDE_DIRS</span></code></dt><dd><p>where to find png.h, etc.</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">PNG_LIBRARIES</span></code></dt><dd><p>the libraries to link against to use PNG.</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">PNG_DEFINITIONS</span></code></dt><dd><p>You should add_definitions(${PNG_DEFINITIONS}) before compiling code
that includes png library files.</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">PNG_FOUND</span></code></dt><dd><p>If false, do not try to use PNG.</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">PNG_VERSION_STRING</span></code></dt><dd><p>the version of the PNG library found (since CMake 2.8.8)</p>
</dd>
</dl>
</section>
<section id="obsolete-variables">
<h2>Obsolete variables<a class="headerlink" href="#obsolete-variables" title="Permalink to this heading">¶</a></h2>
<p>The following variables may also be set, for backwards compatibility:</p>
<dl class="simple">
<dt><code class="docutils literal notranslate"><span class="pre">PNG_LIBRARY</span></code></dt><dd><p>where to find the PNG library.</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">PNG_INCLUDE_DIR</span></code></dt><dd><p>where to find the PNG headers (same as PNG_INCLUDE_DIRS)</p>
</dd>
</dl>
<p>Since PNG depends on the ZLib compression library, none of the above
will be defined unless ZLib can be found.</p>
</section>
</section>


            <div class="clearer"></div>
          </div>
        </div>
      </div>
      <div class="sphinxsidebar" role="navigation" aria-label="main navigation">
        <div class="sphinxsidebarwrapper">
  <div>
    <h3><a href="../index.html">Table of Contents</a></h3>
    <ul>
<li><a class="reference internal" href="#">FindPNG</a><ul>
<li><a class="reference internal" href="#imported-targets">Imported targets</a></li>
<li><a class="reference internal" href="#result-variables">Result variables</a></li>
<li><a class="reference internal" href="#obsolete-variables">Obsolete variables</a></li>
</ul>
</li>
</ul>

  </div>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="FindPkgConfig.html"
                          title="previous chapter">FindPkgConfig</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="FindPostgreSQL.html"
                          title="next chapter">FindPostgreSQL</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../_sources/module/FindPNG.rst.txt"
            rel="nofollow">Show Source</a></li>
    </ul>
   </div>
<div id="searchbox" style="display: none" role="search">
  <h3 id="searchlabel">Quick search</h3>
    <div class="searchformwrapper">
    <form class="search" action="../search.html" method="get">
      <input type="text" name="q" aria-labelledby="searchlabel" autocomplete="off" autocorrect="off" autocapitalize="off" spellcheck="false"/>
      <input type="submit" value="Go" />
    </form>
    </div>
</div>
<script>document.getElementById('searchbox').style.display = "block"</script>
        </div>
      </div>
      <div class="clearer"></div>
    </div>
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             >index</a></li>
        <li class="right" >
          <a href="FindPostgreSQL.html" title="FindPostgreSQL"
             >next</a> |</li>
        <li class="right" >
          <a href="FindPkgConfig.html" title="FindPkgConfig"
             >previous</a> |</li>
  <li>
    <img src="../_static/cmake-logo-16.png" alt=""
         style="vertical-align: middle; margin-top: -2px" />
  </li>
  <li>
    <a href="https://cmake.org/">CMake</a> &#187;
  </li>
  <li>
    <a href="../index.html">3.28.1 Documentation</a> &#187;
  </li>

          <li class="nav-item nav-item-1"><a href="../manual/cmake-modules.7.html" >cmake-modules(7)</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href="">FindPNG</a></li> 
      </ul>
    </div>

    <div class="footer" role="contentinfo">
        &#169; Copyright 2000-2023 Kitware, Inc. and Contributors.
      Created using <a href="https://www.sphinx-doc.org/">Sphinx</a> 6.2.1.
    </div>
  </body>
</html>