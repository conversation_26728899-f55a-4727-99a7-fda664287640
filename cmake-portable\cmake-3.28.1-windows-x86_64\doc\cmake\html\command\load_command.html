
<!DOCTYPE html>

<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="generator" content="Docutils 0.19: https://docutils.sourceforge.io/" />

  <title>load_command &mdash; CMake 3.28.1 Documentation</title>

    <link rel="stylesheet" type="text/css" href="../_static/pygments.css" />
    <link rel="stylesheet" type="text/css" href="../_static/cmake.css" />
    
    <script data-url_root="../" id="documentation_options" src="../_static/documentation_options.js"></script>
    <script src="../_static/doctools.js"></script>
    <script src="../_static/sphinx_highlight.js"></script>
    
    <link rel="icon" href="../_static/cmake-favicon.ico"/>
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="next" title="make_directory" href="make_directory.html" />
    <link rel="prev" title="install_targets" href="install_targets.html" />
 

  </head><body>

    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             accesskey="I">index</a></li>
        <li class="right" >
          <a href="make_directory.html" title="make_directory"
             accesskey="N">next</a> |</li>
        <li class="right" >
          <a href="install_targets.html" title="install_targets"
             accesskey="P">previous</a> |</li>
  <li>
    <img src="../_static/cmake-logo-16.png" alt=""
         style="vertical-align: middle; margin-top: -2px" />
  </li>
  <li>
    <a href="https://cmake.org/">CMake</a> &#187;
  </li>
  <li>
    <a href="../index.html">3.28.1 Documentation</a> &#187;
  </li>

          <li class="nav-item nav-item-1"><a href="../manual/cmake-commands.7.html" accesskey="U">cmake-commands(7)</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href="">load_command</a></li> 
      </ul>
    </div>  

    <div class="document">
      <div class="documentwrapper">
        <div class="bodywrapper">
          <div class="body" role="main">
            
  <section id="load-command">
<span id="command:load_command"></span><h1>load_command<a class="headerlink" href="#load-command" title="Permalink to this heading">¶</a></h1>
<p>Disallowed since version 3.0.  See CMake Policy <span class="target" id="index-0-policy:CMP0031"></span><a class="reference internal" href="../policy/CMP0031.html#policy:CMP0031" title="CMP0031"><code class="xref cmake cmake-policy docutils literal notranslate"><span class="pre">CMP0031</span></code></a>.</p>
<p>Load a command into a running CMake.</p>
<div class="highlight-cmake notranslate"><div class="highlight"><pre><span></span><span class="nf">load_command(</span><span class="no">COMMAND_NAME</span><span class="w"> </span><span class="nv">&lt;loc1&gt;</span><span class="w"> </span><span class="p">[</span><span class="nb">loc2</span><span class="w"> </span><span class="p">...]</span><span class="nf">)</span>
</pre></div>
</div>
<p>The given locations are searched for a library whose name is
cmCOMMAND_NAME.  If found, it is loaded as a module and the command is
added to the set of available CMake commands.  Usually,
<span class="target" id="index-0-command:try_compile"></span><a class="reference internal" href="try_compile.html#command:try_compile" title="try_compile"><code class="xref cmake cmake-command docutils literal notranslate"><span class="pre">try_compile()</span></code></a> is used before this command to compile the
module.  If the command is successfully loaded a variable named</p>
<div class="highlight-cmake notranslate"><div class="highlight"><pre><span></span><span class="no">CMAKE_LOADED_COMMAND_</span><span class="nv">&lt;COMMAND_NAME&gt;</span>
</pre></div>
</div>
<p>will be set to the full path of the module that was loaded.  Otherwise
the variable will not be set.</p>
</section>


            <div class="clearer"></div>
          </div>
        </div>
      </div>
      <div class="sphinxsidebar" role="navigation" aria-label="main navigation">
        <div class="sphinxsidebarwrapper">
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="install_targets.html"
                          title="previous chapter">install_targets</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="make_directory.html"
                          title="next chapter">make_directory</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../_sources/command/load_command.rst.txt"
            rel="nofollow">Show Source</a></li>
    </ul>
   </div>
<div id="searchbox" style="display: none" role="search">
  <h3 id="searchlabel">Quick search</h3>
    <div class="searchformwrapper">
    <form class="search" action="../search.html" method="get">
      <input type="text" name="q" aria-labelledby="searchlabel" autocomplete="off" autocorrect="off" autocapitalize="off" spellcheck="false"/>
      <input type="submit" value="Go" />
    </form>
    </div>
</div>
<script>document.getElementById('searchbox').style.display = "block"</script>
        </div>
      </div>
      <div class="clearer"></div>
    </div>
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             >index</a></li>
        <li class="right" >
          <a href="make_directory.html" title="make_directory"
             >next</a> |</li>
        <li class="right" >
          <a href="install_targets.html" title="install_targets"
             >previous</a> |</li>
  <li>
    <img src="../_static/cmake-logo-16.png" alt=""
         style="vertical-align: middle; margin-top: -2px" />
  </li>
  <li>
    <a href="https://cmake.org/">CMake</a> &#187;
  </li>
  <li>
    <a href="../index.html">3.28.1 Documentation</a> &#187;
  </li>

          <li class="nav-item nav-item-1"><a href="../manual/cmake-commands.7.html" >cmake-commands(7)</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href="">load_command</a></li> 
      </ul>
    </div>

    <div class="footer" role="contentinfo">
        &#169; Copyright 2000-2023 Kitware, Inc. and Contributors.
      Created using <a href="https://www.sphinx-doc.org/">Sphinx</a> 6.2.1.
    </div>
  </body>
</html>