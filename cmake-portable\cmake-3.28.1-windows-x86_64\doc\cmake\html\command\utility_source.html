
<!DOCTYPE html>

<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="generator" content="Docutils 0.19: https://docutils.sourceforge.io/" />

  <title>utility_source &mdash; CMake 3.28.1 Documentation</title>

    <link rel="stylesheet" type="text/css" href="../_static/pygments.css" />
    <link rel="stylesheet" type="text/css" href="../_static/cmake.css" />
    
    <script data-url_root="../" id="documentation_options" src="../_static/documentation_options.js"></script>
    <script src="../_static/doctools.js"></script>
    <script src="../_static/sphinx_highlight.js"></script>
    
    <link rel="icon" href="../_static/cmake-favicon.ico"/>
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="next" title="variable_requires" href="variable_requires.html" />
    <link rel="prev" title="use_mangled_mesa" href="use_mangled_mesa.html" />
 

  </head><body>

    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             accesskey="I">index</a></li>
        <li class="right" >
          <a href="variable_requires.html" title="variable_requires"
             accesskey="N">next</a> |</li>
        <li class="right" >
          <a href="use_mangled_mesa.html" title="use_mangled_mesa"
             accesskey="P">previous</a> |</li>
  <li>
    <img src="../_static/cmake-logo-16.png" alt=""
         style="vertical-align: middle; margin-top: -2px" />
  </li>
  <li>
    <a href="https://cmake.org/">CMake</a> &#187;
  </li>
  <li>
    <a href="../index.html">3.28.1 Documentation</a> &#187;
  </li>

          <li class="nav-item nav-item-1"><a href="../manual/cmake-commands.7.html" accesskey="U">cmake-commands(7)</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href="">utility_source</a></li> 
      </ul>
    </div>  

    <div class="document">
      <div class="documentwrapper">
        <div class="bodywrapper">
          <div class="body" role="main">
            
  <section id="utility-source">
<span id="command:utility_source"></span><h1>utility_source<a class="headerlink" href="#utility-source" title="Permalink to this heading">¶</a></h1>
<p>Disallowed since version 3.0.  See CMake Policy <span class="target" id="index-0-policy:CMP0034"></span><a class="reference internal" href="../policy/CMP0034.html#policy:CMP0034" title="CMP0034"><code class="xref cmake cmake-policy docutils literal notranslate"><span class="pre">CMP0034</span></code></a>.</p>
<p>Specify the source tree of a third-party utility.</p>
<div class="highlight-cmake notranslate"><div class="highlight"><pre><span></span><span class="nf">utility_source(</span><span class="nb">cache_entry</span><span class="w"> </span><span class="nb">executable_name</span>
<span class="w">               </span><span class="nb">path_to_source</span><span class="w"> </span><span class="p">[</span><span class="nb">file1</span><span class="w"> </span><span class="nb">file2</span><span class="w"> </span><span class="p">...]</span><span class="nf">)</span>
</pre></div>
</div>
<p>When a third-party utility's source is included in the distribution,
this command specifies its location and name.  The cache entry will
not be set unless the <code class="docutils literal notranslate"><span class="pre">path_to_source</span></code> and all listed files exist.  It
is assumed that the source tree of the utility will have been built
before it is needed.</p>
<p>When cross compiling CMake will print a warning if a <code class="docutils literal notranslate"><span class="pre">utility_source()</span></code>
command is executed, because in many cases it is used to build an
executable which is executed later on.  This doesn't work when cross
compiling, since the executable can run only on their target platform.
So in this case the cache entry has to be adjusted manually so it
points to an executable which is runnable on the build host.</p>
</section>


            <div class="clearer"></div>
          </div>
        </div>
      </div>
      <div class="sphinxsidebar" role="navigation" aria-label="main navigation">
        <div class="sphinxsidebarwrapper">
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="use_mangled_mesa.html"
                          title="previous chapter">use_mangled_mesa</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="variable_requires.html"
                          title="next chapter">variable_requires</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../_sources/command/utility_source.rst.txt"
            rel="nofollow">Show Source</a></li>
    </ul>
   </div>
<div id="searchbox" style="display: none" role="search">
  <h3 id="searchlabel">Quick search</h3>
    <div class="searchformwrapper">
    <form class="search" action="../search.html" method="get">
      <input type="text" name="q" aria-labelledby="searchlabel" autocomplete="off" autocorrect="off" autocapitalize="off" spellcheck="false"/>
      <input type="submit" value="Go" />
    </form>
    </div>
</div>
<script>document.getElementById('searchbox').style.display = "block"</script>
        </div>
      </div>
      <div class="clearer"></div>
    </div>
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             >index</a></li>
        <li class="right" >
          <a href="variable_requires.html" title="variable_requires"
             >next</a> |</li>
        <li class="right" >
          <a href="use_mangled_mesa.html" title="use_mangled_mesa"
             >previous</a> |</li>
  <li>
    <img src="../_static/cmake-logo-16.png" alt=""
         style="vertical-align: middle; margin-top: -2px" />
  </li>
  <li>
    <a href="https://cmake.org/">CMake</a> &#187;
  </li>
  <li>
    <a href="../index.html">3.28.1 Documentation</a> &#187;
  </li>

          <li class="nav-item nav-item-1"><a href="../manual/cmake-commands.7.html" >cmake-commands(7)</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href="">utility_source</a></li> 
      </ul>
    </div>

    <div class="footer" role="contentinfo">
        &#169; Copyright 2000-2023 Kitware, Inc. and Contributors.
      Created using <a href="https://www.sphinx-doc.org/">Sphinx</a> 6.2.1.
    </div>
  </body>
</html>