
<!DOCTYPE html>

<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="generator" content="Docutils 0.19: https://docutils.sourceforge.io/" />

  <title>cmake-properties(7) &mdash; CMake 3.28.1 Documentation</title>

    <link rel="stylesheet" type="text/css" href="../_static/pygments.css" />
    <link rel="stylesheet" type="text/css" href="../_static/cmake.css" />
    
    <script data-url_root="../" id="documentation_options" src="../_static/documentation_options.js"></script>
    <script src="../_static/doctools.js"></script>
    <script src="../_static/sphinx_highlight.js"></script>
    
    <link rel="icon" href="../_static/cmake-favicon.ico"/>
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="next" title="ALLOW_DUPLICATE_CUSTOM_TARGETS" href="../prop_gbl/ALLOW_DUPLICATE_CUSTOM_TARGETS.html" />
    <link rel="prev" title="cmake-presets(7)" href="cmake-presets.7.html" />
 

  </head><body>

    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             accesskey="I">index</a></li>
        <li class="right" >
          <a href="../prop_gbl/ALLOW_DUPLICATE_CUSTOM_TARGETS.html" title="ALLOW_DUPLICATE_CUSTOM_TARGETS"
             accesskey="N">next</a> |</li>
        <li class="right" >
          <a href="cmake-presets.7.html" title="cmake-presets(7)"
             accesskey="P">previous</a> |</li>
  <li>
    <img src="../_static/cmake-logo-16.png" alt=""
         style="vertical-align: middle; margin-top: -2px" />
  </li>
  <li>
    <a href="https://cmake.org/">CMake</a> &#187;
  </li>
  <li>
    <a href="../index.html">3.28.1 Documentation</a> &#187;
  </li>

        <li class="nav-item nav-item-this"><a href="">cmake-properties(7)</a></li> 
      </ul>
    </div>  

    <div class="document">
      <div class="documentwrapper">
        <div class="bodywrapper">
          <div class="body" role="main">
            
  <span class="target" id="manual:cmake-properties(7)"></span><section id="cmake-properties-7">
<h1><a class="toc-backref" href="#id1" role="doc-backlink">cmake-properties(7)</a><a class="headerlink" href="#cmake-properties-7" title="Permalink to this heading">¶</a></h1>
<nav class="contents" id="contents">
<p class="topic-title">Contents</p>
<ul class="simple">
<li><p><a class="reference internal" href="#cmake-properties-7" id="id1">cmake-properties(7)</a></p>
<ul>
<li><p><a class="reference internal" href="#properties-of-global-scope" id="id2">Properties of Global Scope</a></p></li>
<li><p><a class="reference internal" href="#properties-on-directories" id="id3">Properties on Directories</a></p></li>
<li><p><a class="reference internal" href="#properties-on-targets" id="id4">Properties on Targets</a></p></li>
<li><p><a class="reference internal" href="#properties-on-tests" id="id5">Properties on Tests</a></p></li>
<li><p><a class="reference internal" href="#properties-on-source-files" id="id6">Properties on Source Files</a></p></li>
<li><p><a class="reference internal" href="#properties-on-cache-entries" id="id7">Properties on Cache Entries</a></p></li>
<li><p><a class="reference internal" href="#properties-on-installed-files" id="id8">Properties on Installed Files</a></p></li>
<li><p><a class="reference internal" href="#deprecated-properties-on-directories" id="id9">Deprecated Properties on Directories</a></p></li>
<li><p><a class="reference internal" href="#deprecated-properties-on-targets" id="id10">Deprecated Properties on Targets</a></p></li>
<li><p><a class="reference internal" href="#deprecated-properties-on-source-files" id="id11">Deprecated Properties on Source Files</a></p></li>
</ul>
</li>
</ul>
</nav>
<section id="properties-of-global-scope">
<span id="global-properties"></span><h2><a class="toc-backref" href="#id2" role="doc-backlink">Properties of Global Scope</a><a class="headerlink" href="#properties-of-global-scope" title="Permalink to this heading">¶</a></h2>
<div class="toctree-wrapper compound">
<ul>
<li class="toctree-l1"><a class="reference internal" href="../prop_gbl/ALLOW_DUPLICATE_CUSTOM_TARGETS.html">ALLOW_DUPLICATE_CUSTOM_TARGETS</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_gbl/AUTOGEN_SOURCE_GROUP.html">AUTOGEN_SOURCE_GROUP</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_gbl/AUTOGEN_TARGETS_FOLDER.html">AUTOGEN_TARGETS_FOLDER</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_gbl/AUTOMOC_SOURCE_GROUP.html">AUTOMOC_SOURCE_GROUP</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_gbl/AUTOMOC_TARGETS_FOLDER.html">AUTOMOC_TARGETS_FOLDER</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_gbl/AUTORCC_SOURCE_GROUP.html">AUTORCC_SOURCE_GROUP</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_gbl/AUTOUIC_SOURCE_GROUP.html">AUTOUIC_SOURCE_GROUP</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_gbl/CMAKE_C_KNOWN_FEATURES.html">CMAKE_C_KNOWN_FEATURES</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_gbl/CMAKE_CUDA_KNOWN_FEATURES.html">CMAKE_CUDA_KNOWN_FEATURES</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_gbl/CMAKE_CXX_KNOWN_FEATURES.html">CMAKE_CXX_KNOWN_FEATURES</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_gbl/CMAKE_ROLE.html">CMAKE_ROLE</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_gbl/DEBUG_CONFIGURATIONS.html">DEBUG_CONFIGURATIONS</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_gbl/DISABLED_FEATURES.html">DISABLED_FEATURES</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_gbl/ECLIPSE_EXTRA_CPROJECT_CONTENTS.html">ECLIPSE_EXTRA_CPROJECT_CONTENTS</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_gbl/ECLIPSE_EXTRA_NATURES.html">ECLIPSE_EXTRA_NATURES</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_gbl/ENABLED_FEATURES.html">ENABLED_FEATURES</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_gbl/ENABLED_LANGUAGES.html">ENABLED_LANGUAGES</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_gbl/FIND_LIBRARY_USE_LIB32_PATHS.html">FIND_LIBRARY_USE_LIB32_PATHS</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_gbl/FIND_LIBRARY_USE_LIB64_PATHS.html">FIND_LIBRARY_USE_LIB64_PATHS</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_gbl/FIND_LIBRARY_USE_LIBX32_PATHS.html">FIND_LIBRARY_USE_LIBX32_PATHS</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_gbl/FIND_LIBRARY_USE_OPENBSD_VERSIONING.html">FIND_LIBRARY_USE_OPENBSD_VERSIONING</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_gbl/GENERATOR_IS_MULTI_CONFIG.html">GENERATOR_IS_MULTI_CONFIG</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_gbl/GLOBAL_DEPENDS_DEBUG_MODE.html">GLOBAL_DEPENDS_DEBUG_MODE</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_gbl/GLOBAL_DEPENDS_NO_CYCLES.html">GLOBAL_DEPENDS_NO_CYCLES</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_gbl/IN_TRY_COMPILE.html">IN_TRY_COMPILE</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_gbl/JOB_POOLS.html">JOB_POOLS</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_gbl/PACKAGES_FOUND.html">PACKAGES_FOUND</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_gbl/PACKAGES_NOT_FOUND.html">PACKAGES_NOT_FOUND</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_gbl/PREDEFINED_TARGETS_FOLDER.html">PREDEFINED_TARGETS_FOLDER</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_gbl/REPORT_UNDEFINED_PROPERTIES.html">REPORT_UNDEFINED_PROPERTIES</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_gbl/RULE_LAUNCH_COMPILE.html">RULE_LAUNCH_COMPILE</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_gbl/RULE_LAUNCH_CUSTOM.html">RULE_LAUNCH_CUSTOM</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_gbl/RULE_LAUNCH_LINK.html">RULE_LAUNCH_LINK</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_gbl/RULE_MESSAGES.html">RULE_MESSAGES</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_gbl/TARGET_ARCHIVES_MAY_BE_SHARED_LIBS.html">TARGET_ARCHIVES_MAY_BE_SHARED_LIBS</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_gbl/TARGET_MESSAGES.html">TARGET_MESSAGES</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_gbl/TARGET_SUPPORTS_SHARED_LIBS.html">TARGET_SUPPORTS_SHARED_LIBS</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_gbl/USE_FOLDERS.html">USE_FOLDERS</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_gbl/XCODE_EMIT_EFFECTIVE_PLATFORM_NAME.html">XCODE_EMIT_EFFECTIVE_PLATFORM_NAME</a></li>
</ul>
</div>
</section>
<section id="properties-on-directories">
<span id="directory-properties"></span><h2><a class="toc-backref" href="#id3" role="doc-backlink">Properties on Directories</a><a class="headerlink" href="#properties-on-directories" title="Permalink to this heading">¶</a></h2>
<div class="toctree-wrapper compound">
<ul>
<li class="toctree-l1"><a class="reference internal" href="../prop_dir/ADDITIONAL_CLEAN_FILES.html">ADDITIONAL_CLEAN_FILES</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_dir/BINARY_DIR.html">BINARY_DIR</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_dir/BUILDSYSTEM_TARGETS.html">BUILDSYSTEM_TARGETS</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_dir/CACHE_VARIABLES.html">CACHE_VARIABLES</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_dir/CLEAN_NO_CUSTOM.html">CLEAN_NO_CUSTOM</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_dir/CMAKE_CONFIGURE_DEPENDS.html">CMAKE_CONFIGURE_DEPENDS</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_dir/COMPILE_DEFINITIONS.html">COMPILE_DEFINITIONS</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_dir/COMPILE_OPTIONS.html">COMPILE_OPTIONS</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_dir/DEFINITIONS.html">DEFINITIONS</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_dir/EXCLUDE_FROM_ALL.html">EXCLUDE_FROM_ALL</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_dir/IMPLICIT_DEPENDS_INCLUDE_TRANSFORM.html">IMPLICIT_DEPENDS_INCLUDE_TRANSFORM</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_dir/IMPORTED_TARGETS.html">IMPORTED_TARGETS</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_dir/INCLUDE_DIRECTORIES.html">INCLUDE_DIRECTORIES</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_dir/INCLUDE_REGULAR_EXPRESSION.html">INCLUDE_REGULAR_EXPRESSION</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_dir/LABELS.html">LABELS</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_dir/LINK_DIRECTORIES.html">LINK_DIRECTORIES</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_dir/LINK_OPTIONS.html">LINK_OPTIONS</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_dir/LISTFILE_STACK.html">LISTFILE_STACK</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_dir/MACROS.html">MACROS</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_dir/PARENT_DIRECTORY.html">PARENT_DIRECTORY</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_dir/RULE_LAUNCH_COMPILE.html">RULE_LAUNCH_COMPILE</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_dir/RULE_LAUNCH_CUSTOM.html">RULE_LAUNCH_CUSTOM</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_dir/RULE_LAUNCH_LINK.html">RULE_LAUNCH_LINK</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_dir/SOURCE_DIR.html">SOURCE_DIR</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_dir/SUBDIRECTORIES.html">SUBDIRECTORIES</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_dir/SYSTEM.html">SYSTEM</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_dir/TESTS.html">TESTS</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_dir/TEST_INCLUDE_FILES.html">TEST_INCLUDE_FILES</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_dir/VARIABLES.html">VARIABLES</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_dir/VS_GLOBAL_SECTION_POST_section.html">VS_GLOBAL_SECTION_POST_&lt;section&gt;</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_dir/VS_GLOBAL_SECTION_PRE_section.html">VS_GLOBAL_SECTION_PRE_&lt;section&gt;</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_dir/VS_STARTUP_PROJECT.html">VS_STARTUP_PROJECT</a></li>
</ul>
</div>
</section>
<section id="properties-on-targets">
<span id="target-properties"></span><h2><a class="toc-backref" href="#id4" role="doc-backlink">Properties on Targets</a><a class="headerlink" href="#properties-on-targets" title="Permalink to this heading">¶</a></h2>
<div class="toctree-wrapper compound">
<ul>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/ADDITIONAL_CLEAN_FILES.html">ADDITIONAL_CLEAN_FILES</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/AIX_EXPORT_ALL_SYMBOLS.html">AIX_EXPORT_ALL_SYMBOLS</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/ALIAS_GLOBAL.html">ALIAS_GLOBAL</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/ALIASED_TARGET.html">ALIASED_TARGET</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/ANDROID_ANT_ADDITIONAL_OPTIONS.html">ANDROID_ANT_ADDITIONAL_OPTIONS</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/ANDROID_API.html">ANDROID_API</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/ANDROID_API_MIN.html">ANDROID_API_MIN</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/ANDROID_ARCH.html">ANDROID_ARCH</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/ANDROID_ASSETS_DIRECTORIES.html">ANDROID_ASSETS_DIRECTORIES</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/ANDROID_GUI.html">ANDROID_GUI</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/ANDROID_JAR_DEPENDENCIES.html">ANDROID_JAR_DEPENDENCIES</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/ANDROID_JAR_DIRECTORIES.html">ANDROID_JAR_DIRECTORIES</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/ANDROID_JAVA_SOURCE_DIR.html">ANDROID_JAVA_SOURCE_DIR</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/ANDROID_NATIVE_LIB_DEPENDENCIES.html">ANDROID_NATIVE_LIB_DEPENDENCIES</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/ANDROID_NATIVE_LIB_DIRECTORIES.html">ANDROID_NATIVE_LIB_DIRECTORIES</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/ANDROID_PROCESS_MAX.html">ANDROID_PROCESS_MAX</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/ANDROID_PROGUARD.html">ANDROID_PROGUARD</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/ANDROID_PROGUARD_CONFIG_PATH.html">ANDROID_PROGUARD_CONFIG_PATH</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/ANDROID_SECURE_PROPS_PATH.html">ANDROID_SECURE_PROPS_PATH</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/ANDROID_SKIP_ANT_STEP.html">ANDROID_SKIP_ANT_STEP</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/ANDROID_STL_TYPE.html">ANDROID_STL_TYPE</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/ARCHIVE_OUTPUT_DIRECTORY.html">ARCHIVE_OUTPUT_DIRECTORY</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/ARCHIVE_OUTPUT_DIRECTORY_CONFIG.html">ARCHIVE_OUTPUT_DIRECTORY_&lt;CONFIG&gt;</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/ARCHIVE_OUTPUT_NAME.html">ARCHIVE_OUTPUT_NAME</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/ARCHIVE_OUTPUT_NAME_CONFIG.html">ARCHIVE_OUTPUT_NAME_&lt;CONFIG&gt;</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/AUTOGEN_BUILD_DIR.html">AUTOGEN_BUILD_DIR</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/AUTOGEN_ORIGIN_DEPENDS.html">AUTOGEN_ORIGIN_DEPENDS</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/AUTOGEN_PARALLEL.html">AUTOGEN_PARALLEL</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/AUTOGEN_TARGET_DEPENDS.html">AUTOGEN_TARGET_DEPENDS</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/AUTOGEN_USE_SYSTEM_INCLUDE.html">AUTOGEN_USE_SYSTEM_INCLUDE</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/AUTOMOC.html">AUTOMOC</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/AUTOMOC_COMPILER_PREDEFINES.html">AUTOMOC_COMPILER_PREDEFINES</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/AUTOMOC_DEPEND_FILTERS.html">AUTOMOC_DEPEND_FILTERS</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/AUTOMOC_EXECUTABLE.html">AUTOMOC_EXECUTABLE</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/AUTOMOC_MACRO_NAMES.html">AUTOMOC_MACRO_NAMES</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/AUTOMOC_MOC_OPTIONS.html">AUTOMOC_MOC_OPTIONS</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/AUTOMOC_PATH_PREFIX.html">AUTOMOC_PATH_PREFIX</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/AUTORCC.html">AUTORCC</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/AUTORCC_EXECUTABLE.html">AUTORCC_EXECUTABLE</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/AUTORCC_OPTIONS.html">AUTORCC_OPTIONS</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/AUTOUIC.html">AUTOUIC</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/AUTOUIC_EXECUTABLE.html">AUTOUIC_EXECUTABLE</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/AUTOUIC_OPTIONS.html">AUTOUIC_OPTIONS</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/AUTOUIC_SEARCH_PATHS.html">AUTOUIC_SEARCH_PATHS</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/BINARY_DIR.html">BINARY_DIR</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/BUILD_RPATH.html">BUILD_RPATH</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/BUILD_RPATH_USE_ORIGIN.html">BUILD_RPATH_USE_ORIGIN</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/BUILD_WITH_INSTALL_NAME_DIR.html">BUILD_WITH_INSTALL_NAME_DIR</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/BUILD_WITH_INSTALL_RPATH.html">BUILD_WITH_INSTALL_RPATH</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/BUNDLE.html">BUNDLE</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/BUNDLE_EXTENSION.html">BUNDLE_EXTENSION</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/C_EXTENSIONS.html">C_EXTENSIONS</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/C_STANDARD.html">C_STANDARD</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/C_STANDARD_REQUIRED.html">C_STANDARD_REQUIRED</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/COMMON_LANGUAGE_RUNTIME.html">COMMON_LANGUAGE_RUNTIME</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/COMPATIBLE_INTERFACE_BOOL.html">COMPATIBLE_INTERFACE_BOOL</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/COMPATIBLE_INTERFACE_NUMBER_MAX.html">COMPATIBLE_INTERFACE_NUMBER_MAX</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/COMPATIBLE_INTERFACE_NUMBER_MIN.html">COMPATIBLE_INTERFACE_NUMBER_MIN</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/COMPATIBLE_INTERFACE_STRING.html">COMPATIBLE_INTERFACE_STRING</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/COMPILE_DEFINITIONS.html">COMPILE_DEFINITIONS</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/COMPILE_FEATURES.html">COMPILE_FEATURES</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/COMPILE_FLAGS.html">COMPILE_FLAGS</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/COMPILE_OPTIONS.html">COMPILE_OPTIONS</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/COMPILE_PDB_NAME.html">COMPILE_PDB_NAME</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/COMPILE_PDB_NAME_CONFIG.html">COMPILE_PDB_NAME_&lt;CONFIG&gt;</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/COMPILE_PDB_OUTPUT_DIRECTORY.html">COMPILE_PDB_OUTPUT_DIRECTORY</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/COMPILE_PDB_OUTPUT_DIRECTORY_CONFIG.html">COMPILE_PDB_OUTPUT_DIRECTORY_&lt;CONFIG&gt;</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/COMPILE_WARNING_AS_ERROR.html">COMPILE_WARNING_AS_ERROR</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/CONFIG_OUTPUT_NAME.html">&lt;CONFIG&gt;_OUTPUT_NAME</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/CONFIG_POSTFIX.html">&lt;CONFIG&gt;_POSTFIX</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/CROSSCOMPILING_EMULATOR.html">CROSSCOMPILING_EMULATOR</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/CUDA_ARCHITECTURES.html">CUDA_ARCHITECTURES</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/CUDA_CUBIN_COMPILATION.html">CUDA_CUBIN_COMPILATION</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/CUDA_EXTENSIONS.html">CUDA_EXTENSIONS</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/CUDA_FATBIN_COMPILATION.html">CUDA_FATBIN_COMPILATION</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/CUDA_OPTIX_COMPILATION.html">CUDA_OPTIX_COMPILATION</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/CUDA_PTX_COMPILATION.html">CUDA_PTX_COMPILATION</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/CUDA_RESOLVE_DEVICE_SYMBOLS.html">CUDA_RESOLVE_DEVICE_SYMBOLS</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/CUDA_RUNTIME_LIBRARY.html">CUDA_RUNTIME_LIBRARY</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/CUDA_SEPARABLE_COMPILATION.html">CUDA_SEPARABLE_COMPILATION</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/CUDA_STANDARD.html">CUDA_STANDARD</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/CUDA_STANDARD_REQUIRED.html">CUDA_STANDARD_REQUIRED</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/CXX_EXTENSIONS.html">CXX_EXTENSIONS</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/CXX_MODULE_DIRS.html">CXX_MODULE_DIRS</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/CXX_MODULE_DIRS_NAME.html">CXX_MODULE_DIRS_&lt;NAME&gt;</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/CXX_MODULE_SET.html">CXX_MODULE_SET</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/CXX_MODULE_SET_NAME.html">CXX_MODULE_SET_&lt;NAME&gt;</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/CXX_MODULE_SETS.html">CXX_MODULE_SETS</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/CXX_SCAN_FOR_MODULES.html">CXX_SCAN_FOR_MODULES</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/CXX_STANDARD.html">CXX_STANDARD</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/CXX_STANDARD_REQUIRED.html">CXX_STANDARD_REQUIRED</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/DEBUG_POSTFIX.html">DEBUG_POSTFIX</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/DEFINE_SYMBOL.html">DEFINE_SYMBOL</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/DEPLOYMENT_ADDITIONAL_FILES.html">DEPLOYMENT_ADDITIONAL_FILES</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/DEPLOYMENT_REMOTE_DIRECTORY.html">DEPLOYMENT_REMOTE_DIRECTORY</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/DEPRECATION.html">DEPRECATION</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/DISABLE_PRECOMPILE_HEADERS.html">DISABLE_PRECOMPILE_HEADERS</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/DLL_NAME_WITH_SOVERSION.html">DLL_NAME_WITH_SOVERSION</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/DOTNET_SDK.html">DOTNET_SDK</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/DOTNET_TARGET_FRAMEWORK.html">DOTNET_TARGET_FRAMEWORK</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/DOTNET_TARGET_FRAMEWORK_VERSION.html">DOTNET_TARGET_FRAMEWORK_VERSION</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/EchoString.html">EchoString</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/ENABLE_EXPORTS.html">ENABLE_EXPORTS</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/EXCLUDE_FROM_ALL.html">EXCLUDE_FROM_ALL</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/EXCLUDE_FROM_DEFAULT_BUILD.html">EXCLUDE_FROM_DEFAULT_BUILD</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/EXCLUDE_FROM_DEFAULT_BUILD_CONFIG.html">EXCLUDE_FROM_DEFAULT_BUILD_&lt;CONFIG&gt;</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/EXPORT_COMPILE_COMMANDS.html">EXPORT_COMPILE_COMMANDS</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/EXPORT_NAME.html">EXPORT_NAME</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/EXPORT_NO_SYSTEM.html">EXPORT_NO_SYSTEM</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/EXPORT_PROPERTIES.html">EXPORT_PROPERTIES</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/FOLDER.html">FOLDER</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/Fortran_BUILDING_INSTRINSIC_MODULES.html">Fortran_BUILDING_INSTRINSIC_MODULES</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/Fortran_FORMAT.html">Fortran_FORMAT</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/Fortran_MODULE_DIRECTORY.html">Fortran_MODULE_DIRECTORY</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/Fortran_PREPROCESS.html">Fortran_PREPROCESS</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/FRAMEWORK.html">FRAMEWORK</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/FRAMEWORK_MULTI_CONFIG_POSTFIX_CONFIG.html">FRAMEWORK_MULTI_CONFIG_POSTFIX_&lt;CONFIG&gt;</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/FRAMEWORK_VERSION.html">FRAMEWORK_VERSION</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/GENERATOR_FILE_NAME.html">GENERATOR_FILE_NAME</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/GHS_INTEGRITY_APP.html">GHS_INTEGRITY_APP</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/GHS_NO_SOURCE_GROUP_FILE.html">GHS_NO_SOURCE_GROUP_FILE</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/GNUtoMS.html">GNUtoMS</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/HAS_CXX.html">HAS_CXX</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/HEADER_DIRS.html">HEADER_DIRS</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/HEADER_DIRS_NAME.html">HEADER_DIRS_&lt;NAME&gt;</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/HEADER_SET.html">HEADER_SET</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/HEADER_SET_NAME.html">HEADER_SET_&lt;NAME&gt;</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/HEADER_SETS.html">HEADER_SETS</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/HIP_ARCHITECTURES.html">HIP_ARCHITECTURES</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/HIP_EXTENSIONS.html">HIP_EXTENSIONS</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/HIP_STANDARD.html">HIP_STANDARD</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/HIP_STANDARD_REQUIRED.html">HIP_STANDARD_REQUIRED</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/IMPLICIT_DEPENDS_INCLUDE_TRANSFORM.html">IMPLICIT_DEPENDS_INCLUDE_TRANSFORM</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/IMPORTED.html">IMPORTED</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/IMPORTED_COMMON_LANGUAGE_RUNTIME.html">IMPORTED_COMMON_LANGUAGE_RUNTIME</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/IMPORTED_CONFIGURATIONS.html">IMPORTED_CONFIGURATIONS</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/IMPORTED_CXX_MODULES_COMPILE_DEFINITIONS.html">IMPORTED_CXX_MODULES_COMPILE_DEFINITIONS</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/IMPORTED_CXX_MODULES_COMPILE_FEATURES.html">IMPORTED_CXX_MODULES_COMPILE_FEATURES</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/IMPORTED_CXX_MODULES_COMPILE_OPTIONS.html">IMPORTED_CXX_MODULES_COMPILE_OPTIONS</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/IMPORTED_CXX_MODULES_INCLUDE_DIRECTORIES.html">IMPORTED_CXX_MODULES_INCLUDE_DIRECTORIES</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/IMPORTED_CXX_MODULES_LINK_LIBRARIES.html">IMPORTED_CXX_MODULES_LINK_LIBRARIES</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/IMPORTED_GLOBAL.html">IMPORTED_GLOBAL</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/IMPORTED_IMPLIB.html">IMPORTED_IMPLIB</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/IMPORTED_IMPLIB_CONFIG.html">IMPORTED_IMPLIB_&lt;CONFIG&gt;</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/IMPORTED_LIBNAME.html">IMPORTED_LIBNAME</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/IMPORTED_LIBNAME_CONFIG.html">IMPORTED_LIBNAME_&lt;CONFIG&gt;</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/IMPORTED_LINK_DEPENDENT_LIBRARIES.html">IMPORTED_LINK_DEPENDENT_LIBRARIES</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/IMPORTED_LINK_DEPENDENT_LIBRARIES_CONFIG.html">IMPORTED_LINK_DEPENDENT_LIBRARIES_&lt;CONFIG&gt;</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/IMPORTED_LINK_INTERFACE_LANGUAGES.html">IMPORTED_LINK_INTERFACE_LANGUAGES</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/IMPORTED_LINK_INTERFACE_LANGUAGES_CONFIG.html">IMPORTED_LINK_INTERFACE_LANGUAGES_&lt;CONFIG&gt;</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/IMPORTED_LINK_INTERFACE_LIBRARIES.html">IMPORTED_LINK_INTERFACE_LIBRARIES</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/IMPORTED_LINK_INTERFACE_LIBRARIES_CONFIG.html">IMPORTED_LINK_INTERFACE_LIBRARIES_&lt;CONFIG&gt;</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/IMPORTED_LINK_INTERFACE_MULTIPLICITY.html">IMPORTED_LINK_INTERFACE_MULTIPLICITY</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/IMPORTED_LINK_INTERFACE_MULTIPLICITY_CONFIG.html">IMPORTED_LINK_INTERFACE_MULTIPLICITY_&lt;CONFIG&gt;</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/IMPORTED_LOCATION.html">IMPORTED_LOCATION</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/IMPORTED_LOCATION_CONFIG.html">IMPORTED_LOCATION_&lt;CONFIG&gt;</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/IMPORTED_NO_SONAME.html">IMPORTED_NO_SONAME</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/IMPORTED_NO_SONAME_CONFIG.html">IMPORTED_NO_SONAME_&lt;CONFIG&gt;</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/IMPORTED_OBJECTS.html">IMPORTED_OBJECTS</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/IMPORTED_OBJECTS_CONFIG.html">IMPORTED_OBJECTS_&lt;CONFIG&gt;</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/IMPORTED_SONAME.html">IMPORTED_SONAME</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/IMPORTED_SONAME_CONFIG.html">IMPORTED_SONAME_&lt;CONFIG&gt;</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/IMPORT_PREFIX.html">IMPORT_PREFIX</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/IMPORT_SUFFIX.html">IMPORT_SUFFIX</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/INCLUDE_DIRECTORIES.html">INCLUDE_DIRECTORIES</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/INSTALL_NAME_DIR.html">INSTALL_NAME_DIR</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/INSTALL_REMOVE_ENVIRONMENT_RPATH.html">INSTALL_REMOVE_ENVIRONMENT_RPATH</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/INSTALL_RPATH.html">INSTALL_RPATH</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/INSTALL_RPATH_USE_LINK_PATH.html">INSTALL_RPATH_USE_LINK_PATH</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/INTERFACE_AUTOMOC_MACRO_NAMES.html">INTERFACE_AUTOMOC_MACRO_NAMES</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/INTERFACE_AUTOUIC_OPTIONS.html">INTERFACE_AUTOUIC_OPTIONS</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/INTERFACE_COMPILE_DEFINITIONS.html">INTERFACE_COMPILE_DEFINITIONS</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/INTERFACE_COMPILE_FEATURES.html">INTERFACE_COMPILE_FEATURES</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/INTERFACE_COMPILE_OPTIONS.html">INTERFACE_COMPILE_OPTIONS</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/INTERFACE_CXX_MODULE_SETS.html">INTERFACE_CXX_MODULE_SETS</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/INTERFACE_HEADER_SETS.html">INTERFACE_HEADER_SETS</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/INTERFACE_HEADER_SETS_TO_VERIFY.html">INTERFACE_HEADER_SETS_TO_VERIFY</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/INTERFACE_INCLUDE_DIRECTORIES.html">INTERFACE_INCLUDE_DIRECTORIES</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/INTERFACE_LINK_DEPENDS.html">INTERFACE_LINK_DEPENDS</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/INTERFACE_LINK_DIRECTORIES.html">INTERFACE_LINK_DIRECTORIES</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/INTERFACE_LINK_LIBRARIES.html">INTERFACE_LINK_LIBRARIES</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/INTERFACE_LINK_LIBRARIES_DIRECT.html">INTERFACE_LINK_LIBRARIES_DIRECT</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/INTERFACE_LINK_LIBRARIES_DIRECT_EXCLUDE.html">INTERFACE_LINK_LIBRARIES_DIRECT_EXCLUDE</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/INTERFACE_LINK_OPTIONS.html">INTERFACE_LINK_OPTIONS</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/INTERFACE_POSITION_INDEPENDENT_CODE.html">INTERFACE_POSITION_INDEPENDENT_CODE</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/INTERFACE_PRECOMPILE_HEADERS.html">INTERFACE_PRECOMPILE_HEADERS</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/INTERFACE_SOURCES.html">INTERFACE_SOURCES</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/INTERFACE_SYSTEM_INCLUDE_DIRECTORIES.html">INTERFACE_SYSTEM_INCLUDE_DIRECTORIES</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/INTERPROCEDURAL_OPTIMIZATION.html">INTERPROCEDURAL_OPTIMIZATION</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/INTERPROCEDURAL_OPTIMIZATION_CONFIG.html">INTERPROCEDURAL_OPTIMIZATION_&lt;CONFIG&gt;</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/ISPC_HEADER_DIRECTORY.html">ISPC_HEADER_DIRECTORY</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/ISPC_HEADER_SUFFIX.html">ISPC_HEADER_SUFFIX</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/ISPC_INSTRUCTION_SETS.html">ISPC_INSTRUCTION_SETS</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/JOB_POOL_COMPILE.html">JOB_POOL_COMPILE</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/JOB_POOL_LINK.html">JOB_POOL_LINK</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/JOB_POOL_PRECOMPILE_HEADER.html">JOB_POOL_PRECOMPILE_HEADER</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/LABELS.html">LABELS</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/LANG_CLANG_TIDY.html">&lt;LANG&gt;_CLANG_TIDY</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/LANG_CLANG_TIDY_EXPORT_FIXES_DIR.html">&lt;LANG&gt;_CLANG_TIDY_EXPORT_FIXES_DIR</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/LANG_COMPILER_LAUNCHER.html">&lt;LANG&gt;_COMPILER_LAUNCHER</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/LANG_CPPCHECK.html">&lt;LANG&gt;_CPPCHECK</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/LANG_CPPLINT.html">&lt;LANG&gt;_CPPLINT</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/LANG_EXTENSIONS.html">&lt;LANG&gt;_EXTENSIONS</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/LANG_INCLUDE_WHAT_YOU_USE.html">&lt;LANG&gt;_INCLUDE_WHAT_YOU_USE</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/LANG_LINKER_LAUNCHER.html">&lt;LANG&gt;_LINKER_LAUNCHER</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/LANG_STANDARD.html">&lt;LANG&gt;_STANDARD</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/LANG_STANDARD_REQUIRED.html">&lt;LANG&gt;_STANDARD_REQUIRED</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/LANG_VISIBILITY_PRESET.html">&lt;LANG&gt;_VISIBILITY_PRESET</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/LIBRARY_OUTPUT_DIRECTORY.html">LIBRARY_OUTPUT_DIRECTORY</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/LIBRARY_OUTPUT_DIRECTORY_CONFIG.html">LIBRARY_OUTPUT_DIRECTORY_&lt;CONFIG&gt;</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/LIBRARY_OUTPUT_NAME.html">LIBRARY_OUTPUT_NAME</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/LIBRARY_OUTPUT_NAME_CONFIG.html">LIBRARY_OUTPUT_NAME_&lt;CONFIG&gt;</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/LINK_DEPENDS.html">LINK_DEPENDS</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/LINK_DEPENDS_NO_SHARED.html">LINK_DEPENDS_NO_SHARED</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/LINK_DIRECTORIES.html">LINK_DIRECTORIES</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/LINK_FLAGS.html">LINK_FLAGS</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/LINK_FLAGS_CONFIG.html">LINK_FLAGS_&lt;CONFIG&gt;</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/LINK_INTERFACE_LIBRARIES.html">LINK_INTERFACE_LIBRARIES</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/LINK_INTERFACE_LIBRARIES_CONFIG.html">LINK_INTERFACE_LIBRARIES_&lt;CONFIG&gt;</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/LINK_INTERFACE_MULTIPLICITY.html">LINK_INTERFACE_MULTIPLICITY</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/LINK_INTERFACE_MULTIPLICITY_CONFIG.html">LINK_INTERFACE_MULTIPLICITY_&lt;CONFIG&gt;</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/LINK_LIBRARIES.html">LINK_LIBRARIES</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/LINK_LIBRARIES_ONLY_TARGETS.html">LINK_LIBRARIES_ONLY_TARGETS</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/LINK_LIBRARY_OVERRIDE.html">LINK_LIBRARY_OVERRIDE</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/LINK_LIBRARY_OVERRIDE_LIBRARY.html">LINK_LIBRARY_OVERRIDE_&lt;LIBRARY&gt;</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/LINK_OPTIONS.html">LINK_OPTIONS</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/LINK_SEARCH_END_STATIC.html">LINK_SEARCH_END_STATIC</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/LINK_SEARCH_START_STATIC.html">LINK_SEARCH_START_STATIC</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/LINK_WHAT_YOU_USE.html">LINK_WHAT_YOU_USE</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/LINKER_LANGUAGE.html">LINKER_LANGUAGE</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/LOCATION.html">LOCATION</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/LOCATION_CONFIG.html">LOCATION_&lt;CONFIG&gt;</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/MACHO_COMPATIBILITY_VERSION.html">MACHO_COMPATIBILITY_VERSION</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/MACHO_CURRENT_VERSION.html">MACHO_CURRENT_VERSION</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/MACOSX_BUNDLE.html">MACOSX_BUNDLE</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/MACOSX_BUNDLE_INFO_PLIST.html">MACOSX_BUNDLE_INFO_PLIST</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/MACOSX_FRAMEWORK_INFO_PLIST.html">MACOSX_FRAMEWORK_INFO_PLIST</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/MACOSX_RPATH.html">MACOSX_RPATH</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/MANUALLY_ADDED_DEPENDENCIES.html">MANUALLY_ADDED_DEPENDENCIES</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/MAP_IMPORTED_CONFIG_CONFIG.html">MAP_IMPORTED_CONFIG_&lt;CONFIG&gt;</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/MSVC_DEBUG_INFORMATION_FORMAT.html">MSVC_DEBUG_INFORMATION_FORMAT</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/MSVC_RUNTIME_LIBRARY.html">MSVC_RUNTIME_LIBRARY</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/NAME.html">NAME</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/NO_SONAME.html">NO_SONAME</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/NO_SYSTEM_FROM_IMPORTED.html">NO_SYSTEM_FROM_IMPORTED</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/OBJC_EXTENSIONS.html">OBJC_EXTENSIONS</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/OBJC_STANDARD.html">OBJC_STANDARD</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/OBJC_STANDARD_REQUIRED.html">OBJC_STANDARD_REQUIRED</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/OBJCXX_EXTENSIONS.html">OBJCXX_EXTENSIONS</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/OBJCXX_STANDARD.html">OBJCXX_STANDARD</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/OBJCXX_STANDARD_REQUIRED.html">OBJCXX_STANDARD_REQUIRED</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/OPTIMIZE_DEPENDENCIES.html">OPTIMIZE_DEPENDENCIES</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/OSX_ARCHITECTURES.html">OSX_ARCHITECTURES</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/OSX_ARCHITECTURES_CONFIG.html">OSX_ARCHITECTURES_&lt;CONFIG&gt;</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/OUTPUT_NAME.html">OUTPUT_NAME</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/OUTPUT_NAME_CONFIG.html">OUTPUT_NAME_&lt;CONFIG&gt;</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/PCH_INSTANTIATE_TEMPLATES.html">PCH_INSTANTIATE_TEMPLATES</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/PCH_WARN_INVALID.html">PCH_WARN_INVALID</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/PDB_NAME.html">PDB_NAME</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/PDB_NAME_CONFIG.html">PDB_NAME_&lt;CONFIG&gt;</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/PDB_OUTPUT_DIRECTORY.html">PDB_OUTPUT_DIRECTORY</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/PDB_OUTPUT_DIRECTORY_CONFIG.html">PDB_OUTPUT_DIRECTORY_&lt;CONFIG&gt;</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/POSITION_INDEPENDENT_CODE.html">POSITION_INDEPENDENT_CODE</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/PRECOMPILE_HEADERS.html">PRECOMPILE_HEADERS</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/PRECOMPILE_HEADERS_REUSE_FROM.html">PRECOMPILE_HEADERS_REUSE_FROM</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/PREFIX.html">PREFIX</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/PRIVATE_HEADER.html">PRIVATE_HEADER</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/PROJECT_LABEL.html">PROJECT_LABEL</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/PUBLIC_HEADER.html">PUBLIC_HEADER</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/RESOURCE.html">RESOURCE</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/RULE_LAUNCH_COMPILE.html">RULE_LAUNCH_COMPILE</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/RULE_LAUNCH_CUSTOM.html">RULE_LAUNCH_CUSTOM</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/RULE_LAUNCH_LINK.html">RULE_LAUNCH_LINK</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/RUNTIME_OUTPUT_DIRECTORY.html">RUNTIME_OUTPUT_DIRECTORY</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/RUNTIME_OUTPUT_DIRECTORY_CONFIG.html">RUNTIME_OUTPUT_DIRECTORY_&lt;CONFIG&gt;</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/RUNTIME_OUTPUT_NAME.html">RUNTIME_OUTPUT_NAME</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/RUNTIME_OUTPUT_NAME_CONFIG.html">RUNTIME_OUTPUT_NAME_&lt;CONFIG&gt;</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/SKIP_BUILD_RPATH.html">SKIP_BUILD_RPATH</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/SOURCE_DIR.html">SOURCE_DIR</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/SOURCES.html">SOURCES</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/SOVERSION.html">SOVERSION</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/STATIC_LIBRARY_FLAGS.html">STATIC_LIBRARY_FLAGS</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/STATIC_LIBRARY_FLAGS_CONFIG.html">STATIC_LIBRARY_FLAGS_&lt;CONFIG&gt;</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/STATIC_LIBRARY_OPTIONS.html">STATIC_LIBRARY_OPTIONS</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/SUFFIX.html">SUFFIX</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/Swift_DEPENDENCIES_FILE.html">Swift_DEPENDENCIES_FILE</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/Swift_LANGUAGE_VERSION.html">Swift_LANGUAGE_VERSION</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/Swift_MODULE_DIRECTORY.html">Swift_MODULE_DIRECTORY</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/Swift_MODULE_NAME.html">Swift_MODULE_NAME</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/SYSTEM.html">SYSTEM</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/TYPE.html">TYPE</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/UNITY_BUILD.html">UNITY_BUILD</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/UNITY_BUILD_BATCH_SIZE.html">UNITY_BUILD_BATCH_SIZE</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/UNITY_BUILD_CODE_AFTER_INCLUDE.html">UNITY_BUILD_CODE_AFTER_INCLUDE</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/UNITY_BUILD_CODE_BEFORE_INCLUDE.html">UNITY_BUILD_CODE_BEFORE_INCLUDE</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/UNITY_BUILD_MODE.html">UNITY_BUILD_MODE</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/UNITY_BUILD_UNIQUE_ID.html">UNITY_BUILD_UNIQUE_ID</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/VERIFY_INTERFACE_HEADER_SETS.html">VERIFY_INTERFACE_HEADER_SETS</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/VERSION.html">VERSION</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/VISIBILITY_INLINES_HIDDEN.html">VISIBILITY_INLINES_HIDDEN</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/VS_CONFIGURATION_TYPE.html">VS_CONFIGURATION_TYPE</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/VS_DEBUGGER_COMMAND.html">VS_DEBUGGER_COMMAND</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/VS_DEBUGGER_COMMAND_ARGUMENTS.html">VS_DEBUGGER_COMMAND_ARGUMENTS</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/VS_DEBUGGER_ENVIRONMENT.html">VS_DEBUGGER_ENVIRONMENT</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/VS_DEBUGGER_WORKING_DIRECTORY.html">VS_DEBUGGER_WORKING_DIRECTORY</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/VS_DESKTOP_EXTENSIONS_VERSION.html">VS_DESKTOP_EXTENSIONS_VERSION</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/VS_DOTNET_DOCUMENTATION_FILE.html">VS_DOTNET_DOCUMENTATION_FILE</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/VS_DOTNET_REFERENCE_refname.html">VS_DOTNET_REFERENCE_&lt;refname&gt;</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/VS_DOTNET_REFERENCEPROP_refname_TAG_tagname.html">VS_DOTNET_REFERENCEPROP_&lt;refname&gt;_TAG_&lt;tagname&gt;</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/VS_DOTNET_REFERENCES.html">VS_DOTNET_REFERENCES</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/VS_DOTNET_REFERENCES_COPY_LOCAL.html">VS_DOTNET_REFERENCES_COPY_LOCAL</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/VS_DOTNET_STARTUP_OBJECT.html">VS_DOTNET_STARTUP_OBJECT</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/VS_DOTNET_TARGET_FRAMEWORK_VERSION.html">VS_DOTNET_TARGET_FRAMEWORK_VERSION</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/VS_DPI_AWARE.html">VS_DPI_AWARE</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/VS_GLOBAL_KEYWORD.html">VS_GLOBAL_KEYWORD</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/VS_GLOBAL_PROJECT_TYPES.html">VS_GLOBAL_PROJECT_TYPES</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/VS_GLOBAL_ROOTNAMESPACE.html">VS_GLOBAL_ROOTNAMESPACE</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/VS_GLOBAL_variable.html">VS_GLOBAL_&lt;variable&gt;</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/VS_IOT_EXTENSIONS_VERSION.html">VS_IOT_EXTENSIONS_VERSION</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/VS_IOT_STARTUP_TASK.html">VS_IOT_STARTUP_TASK</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/VS_JUST_MY_CODE_DEBUGGING.html">VS_JUST_MY_CODE_DEBUGGING</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/VS_KEYWORD.html">VS_KEYWORD</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/VS_MOBILE_EXTENSIONS_VERSION.html">VS_MOBILE_EXTENSIONS_VERSION</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/VS_NO_COMPILE_BATCHING.html">VS_NO_COMPILE_BATCHING</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/VS_NO_SOLUTION_DEPLOY.html">VS_NO_SOLUTION_DEPLOY</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/VS_PACKAGE_REFERENCES.html">VS_PACKAGE_REFERENCES</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/VS_PLATFORM_TOOLSET.html">VS_PLATFORM_TOOLSET</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/VS_PROJECT_IMPORT.html">VS_PROJECT_IMPORT</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/VS_SCC_AUXPATH.html">VS_SCC_AUXPATH</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/VS_SCC_LOCALPATH.html">VS_SCC_LOCALPATH</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/VS_SCC_PROJECTNAME.html">VS_SCC_PROJECTNAME</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/VS_SCC_PROVIDER.html">VS_SCC_PROVIDER</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/VS_SDK_REFERENCES.html">VS_SDK_REFERENCES</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/VS_SOLUTION_DEPLOY.html">VS_SOLUTION_DEPLOY</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/VS_SOURCE_SETTINGS_tool.html">VS_SOURCE_SETTINGS_&lt;tool&gt;</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/VS_USER_PROPS.html">VS_USER_PROPS</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/VS_WINDOWS_TARGET_PLATFORM_MIN_VERSION.html">VS_WINDOWS_TARGET_PLATFORM_MIN_VERSION</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/VS_WINRT_COMPONENT.html">VS_WINRT_COMPONENT</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/VS_WINRT_REFERENCES.html">VS_WINRT_REFERENCES</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/WATCOM_RUNTIME_LIBRARY.html">WATCOM_RUNTIME_LIBRARY</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/WIN32_EXECUTABLE.html">WIN32_EXECUTABLE</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/WINDOWS_EXPORT_ALL_SYMBOLS.html">WINDOWS_EXPORT_ALL_SYMBOLS</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/XCODE_ATTRIBUTE_an-attribute.html">XCODE_ATTRIBUTE_&lt;an-attribute&gt;</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/XCODE_EMBED_FRAMEWORKS_CODE_SIGN_ON_COPY.html">XCODE_EMBED_FRAMEWORKS_CODE_SIGN_ON_COPY</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/XCODE_EMBED_FRAMEWORKS_REMOVE_HEADERS_ON_COPY.html">XCODE_EMBED_FRAMEWORKS_REMOVE_HEADERS_ON_COPY</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/XCODE_EMBED_type.html">XCODE_EMBED_&lt;type&gt;</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/XCODE_EMBED_type_CODE_SIGN_ON_COPY.html">XCODE_EMBED_&lt;type&gt;_CODE_SIGN_ON_COPY</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/XCODE_EMBED_type_PATH.html">XCODE_EMBED_&lt;type&gt;_PATH</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/XCODE_EMBED_type_REMOVE_HEADERS_ON_COPY.html">XCODE_EMBED_&lt;type&gt;_REMOVE_HEADERS_ON_COPY</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/XCODE_EXPLICIT_FILE_TYPE.html">XCODE_EXPLICIT_FILE_TYPE</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/XCODE_GENERATE_SCHEME.html">XCODE_GENERATE_SCHEME</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/XCODE_LINK_BUILD_PHASE_MODE.html">XCODE_LINK_BUILD_PHASE_MODE</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/XCODE_PRODUCT_TYPE.html">XCODE_PRODUCT_TYPE</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/XCODE_SCHEME_ADDRESS_SANITIZER.html">XCODE_SCHEME_ADDRESS_SANITIZER</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/XCODE_SCHEME_ADDRESS_SANITIZER_USE_AFTER_RETURN.html">XCODE_SCHEME_ADDRESS_SANITIZER_USE_AFTER_RETURN</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/XCODE_SCHEME_ARGUMENTS.html">XCODE_SCHEME_ARGUMENTS</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/XCODE_SCHEME_DEBUG_AS_ROOT.html">XCODE_SCHEME_DEBUG_AS_ROOT</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/XCODE_SCHEME_DEBUG_DOCUMENT_VERSIONING.html">XCODE_SCHEME_DEBUG_DOCUMENT_VERSIONING</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/XCODE_SCHEME_DISABLE_MAIN_THREAD_CHECKER.html">XCODE_SCHEME_DISABLE_MAIN_THREAD_CHECKER</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/XCODE_SCHEME_DYNAMIC_LIBRARY_LOADS.html">XCODE_SCHEME_DYNAMIC_LIBRARY_LOADS</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/XCODE_SCHEME_DYNAMIC_LINKER_API_USAGE.html">XCODE_SCHEME_DYNAMIC_LINKER_API_USAGE</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/XCODE_SCHEME_ENABLE_GPU_API_VALIDATION.html">XCODE_SCHEME_ENABLE_GPU_API_VALIDATION</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/XCODE_SCHEME_ENABLE_GPU_FRAME_CAPTURE_MODE.html">XCODE_SCHEME_ENABLE_GPU_FRAME_CAPTURE_MODE</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/XCODE_SCHEME_ENABLE_GPU_SHADER_VALIDATION.html">XCODE_SCHEME_ENABLE_GPU_SHADER_VALIDATION</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/XCODE_SCHEME_ENVIRONMENT.html">XCODE_SCHEME_ENVIRONMENT</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/XCODE_SCHEME_EXECUTABLE.html">XCODE_SCHEME_EXECUTABLE</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/XCODE_SCHEME_GUARD_MALLOC.html">XCODE_SCHEME_GUARD_MALLOC</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/XCODE_SCHEME_LAUNCH_CONFIGURATION.html">XCODE_SCHEME_LAUNCH_CONFIGURATION</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/XCODE_SCHEME_LAUNCH_MODE.html">XCODE_SCHEME_LAUNCH_MODE</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/XCODE_SCHEME_MAIN_THREAD_CHECKER_STOP.html">XCODE_SCHEME_MAIN_THREAD_CHECKER_STOP</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/XCODE_SCHEME_MALLOC_GUARD_EDGES.html">XCODE_SCHEME_MALLOC_GUARD_EDGES</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/XCODE_SCHEME_MALLOC_SCRIBBLE.html">XCODE_SCHEME_MALLOC_SCRIBBLE</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/XCODE_SCHEME_MALLOC_STACK.html">XCODE_SCHEME_MALLOC_STACK</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/XCODE_SCHEME_THREAD_SANITIZER.html">XCODE_SCHEME_THREAD_SANITIZER</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/XCODE_SCHEME_THREAD_SANITIZER_STOP.html">XCODE_SCHEME_THREAD_SANITIZER_STOP</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/XCODE_SCHEME_UNDEFINED_BEHAVIOUR_SANITIZER.html">XCODE_SCHEME_UNDEFINED_BEHAVIOUR_SANITIZER</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/XCODE_SCHEME_UNDEFINED_BEHAVIOUR_SANITIZER_STOP.html">XCODE_SCHEME_UNDEFINED_BEHAVIOUR_SANITIZER_STOP</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/XCODE_SCHEME_WORKING_DIRECTORY.html">XCODE_SCHEME_WORKING_DIRECTORY</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/XCODE_SCHEME_ZOMBIE_OBJECTS.html">XCODE_SCHEME_ZOMBIE_OBJECTS</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/XCODE_XCCONFIG.html">XCODE_XCCONFIG</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/XCTEST.html">XCTEST</a></li>
</ul>
</div>
</section>
<section id="properties-on-tests">
<span id="test-properties"></span><h2><a class="toc-backref" href="#id5" role="doc-backlink">Properties on Tests</a><a class="headerlink" href="#properties-on-tests" title="Permalink to this heading">¶</a></h2>
<div class="toctree-wrapper compound">
<ul>
<li class="toctree-l1"><a class="reference internal" href="../prop_test/ATTACHED_FILES.html">ATTACHED_FILES</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_test/ATTACHED_FILES_ON_FAIL.html">ATTACHED_FILES_ON_FAIL</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_test/COST.html">COST</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_test/DEPENDS.html">DEPENDS</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_test/DISABLED.html">DISABLED</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_test/ENVIRONMENT.html">ENVIRONMENT</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_test/ENVIRONMENT_MODIFICATION.html">ENVIRONMENT_MODIFICATION</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_test/FAIL_REGULAR_EXPRESSION.html">FAIL_REGULAR_EXPRESSION</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_test/FIXTURES_CLEANUP.html">FIXTURES_CLEANUP</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_test/FIXTURES_REQUIRED.html">FIXTURES_REQUIRED</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_test/FIXTURES_SETUP.html">FIXTURES_SETUP</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_test/GENERATED_RESOURCE_SPEC_FILE.html">GENERATED_RESOURCE_SPEC_FILE</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_test/LABELS.html">LABELS</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_test/MEASUREMENT.html">MEASUREMENT</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_test/PASS_REGULAR_EXPRESSION.html">PASS_REGULAR_EXPRESSION</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_test/PROCESSOR_AFFINITY.html">PROCESSOR_AFFINITY</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_test/PROCESSORS.html">PROCESSORS</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_test/REQUIRED_FILES.html">REQUIRED_FILES</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_test/RESOURCE_GROUPS.html">RESOURCE_GROUPS</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_test/RESOURCE_LOCK.html">RESOURCE_LOCK</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_test/RUN_SERIAL.html">RUN_SERIAL</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_test/SKIP_REGULAR_EXPRESSION.html">SKIP_REGULAR_EXPRESSION</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_test/SKIP_RETURN_CODE.html">SKIP_RETURN_CODE</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_test/TIMEOUT.html">TIMEOUT</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_test/TIMEOUT_AFTER_MATCH.html">TIMEOUT_AFTER_MATCH</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_test/TIMEOUT_SIGNAL_GRACE_PERIOD.html">TIMEOUT_SIGNAL_GRACE_PERIOD</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_test/TIMEOUT_SIGNAL_NAME.html">TIMEOUT_SIGNAL_NAME</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_test/WILL_FAIL.html">WILL_FAIL</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_test/WORKING_DIRECTORY.html">WORKING_DIRECTORY</a></li>
</ul>
</div>
</section>
<section id="properties-on-source-files">
<span id="source-file-properties"></span><h2><a class="toc-backref" href="#id6" role="doc-backlink">Properties on Source Files</a><a class="headerlink" href="#properties-on-source-files" title="Permalink to this heading">¶</a></h2>
<div class="toctree-wrapper compound">
<ul>
<li class="toctree-l1"><a class="reference internal" href="../prop_sf/ABSTRACT.html">ABSTRACT</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_sf/AUTORCC_OPTIONS.html">AUTORCC_OPTIONS</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_sf/AUTOUIC_OPTIONS.html">AUTOUIC_OPTIONS</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_sf/COMPILE_DEFINITIONS.html">COMPILE_DEFINITIONS</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_sf/COMPILE_FLAGS.html">COMPILE_FLAGS</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_sf/COMPILE_OPTIONS.html">COMPILE_OPTIONS</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_sf/CXX_SCAN_FOR_MODULES.html">CXX_SCAN_FOR_MODULES</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_sf/EXTERNAL_OBJECT.html">EXTERNAL_OBJECT</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_sf/Fortran_FORMAT.html">Fortran_FORMAT</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_sf/Fortran_PREPROCESS.html">Fortran_PREPROCESS</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_sf/GENERATED.html">GENERATED</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_sf/HEADER_FILE_ONLY.html">HEADER_FILE_ONLY</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_sf/INCLUDE_DIRECTORIES.html">INCLUDE_DIRECTORIES</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_sf/KEEP_EXTENSION.html">KEEP_EXTENSION</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_sf/LABELS.html">LABELS</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_sf/LANGUAGE.html">LANGUAGE</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_sf/LOCATION.html">LOCATION</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_sf/MACOSX_PACKAGE_LOCATION.html">MACOSX_PACKAGE_LOCATION</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_sf/OBJECT_DEPENDS.html">OBJECT_DEPENDS</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_sf/OBJECT_OUTPUTS.html">OBJECT_OUTPUTS</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_sf/SKIP_AUTOGEN.html">SKIP_AUTOGEN</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_sf/SKIP_AUTOMOC.html">SKIP_AUTOMOC</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_sf/SKIP_AUTORCC.html">SKIP_AUTORCC</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_sf/SKIP_AUTOUIC.html">SKIP_AUTOUIC</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_sf/SKIP_LINTING.html">SKIP_LINTING</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_sf/SKIP_PRECOMPILE_HEADERS.html">SKIP_PRECOMPILE_HEADERS</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_sf/SKIP_UNITY_BUILD_INCLUSION.html">SKIP_UNITY_BUILD_INCLUSION</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_sf/Swift_DEPENDENCIES_FILE.html">Swift_DEPENDENCIES_FILE</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_sf/Swift_DIAGNOSTICS_FILE.html">Swift_DIAGNOSTICS_FILE</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_sf/SYMBOLIC.html">SYMBOLIC</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_sf/UNITY_GROUP.html">UNITY_GROUP</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_sf/VS_COPY_TO_OUT_DIR.html">VS_COPY_TO_OUT_DIR</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_sf/VS_CSHARP_tagname.html">VS_CSHARP_&lt;tagname&gt;</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_sf/VS_DEPLOYMENT_CONTENT.html">VS_DEPLOYMENT_CONTENT</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_sf/VS_DEPLOYMENT_LOCATION.html">VS_DEPLOYMENT_LOCATION</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_sf/VS_INCLUDE_IN_VSIX.html">VS_INCLUDE_IN_VSIX</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_sf/VS_RESOURCE_GENERATOR.html">VS_RESOURCE_GENERATOR</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_sf/VS_SETTINGS.html">VS_SETTINGS</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_sf/VS_SHADER_DISABLE_OPTIMIZATIONS.html">VS_SHADER_DISABLE_OPTIMIZATIONS</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_sf/VS_SHADER_ENABLE_DEBUG.html">VS_SHADER_ENABLE_DEBUG</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_sf/VS_SHADER_ENTRYPOINT.html">VS_SHADER_ENTRYPOINT</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_sf/VS_SHADER_FLAGS.html">VS_SHADER_FLAGS</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_sf/VS_SHADER_MODEL.html">VS_SHADER_MODEL</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_sf/VS_SHADER_OBJECT_FILE_NAME.html">VS_SHADER_OBJECT_FILE_NAME</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_sf/VS_SHADER_OUTPUT_HEADER_FILE.html">VS_SHADER_OUTPUT_HEADER_FILE</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_sf/VS_SHADER_TYPE.html">VS_SHADER_TYPE</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_sf/VS_SHADER_VARIABLE_NAME.html">VS_SHADER_VARIABLE_NAME</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_sf/VS_TOOL_OVERRIDE.html">VS_TOOL_OVERRIDE</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_sf/VS_XAML_TYPE.html">VS_XAML_TYPE</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_sf/WRAP_EXCLUDE.html">WRAP_EXCLUDE</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_sf/XCODE_EXPLICIT_FILE_TYPE.html">XCODE_EXPLICIT_FILE_TYPE</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_sf/XCODE_FILE_ATTRIBUTES.html">XCODE_FILE_ATTRIBUTES</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_sf/XCODE_LAST_KNOWN_FILE_TYPE.html">XCODE_LAST_KNOWN_FILE_TYPE</a></li>
</ul>
</div>
</section>
<section id="properties-on-cache-entries">
<span id="cache-entry-properties"></span><h2><a class="toc-backref" href="#id7" role="doc-backlink">Properties on Cache Entries</a><a class="headerlink" href="#properties-on-cache-entries" title="Permalink to this heading">¶</a></h2>
<div class="toctree-wrapper compound">
<ul>
<li class="toctree-l1"><a class="reference internal" href="../prop_cache/ADVANCED.html">ADVANCED</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_cache/HELPSTRING.html">HELPSTRING</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_cache/MODIFIED.html">MODIFIED</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_cache/STRINGS.html">STRINGS</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_cache/TYPE.html">TYPE</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_cache/VALUE.html">VALUE</a></li>
</ul>
</div>
</section>
<section id="properties-on-installed-files">
<span id="installed-file-properties"></span><h2><a class="toc-backref" href="#id8" role="doc-backlink">Properties on Installed Files</a><a class="headerlink" href="#properties-on-installed-files" title="Permalink to this heading">¶</a></h2>
<div class="toctree-wrapper compound">
<ul>
<li class="toctree-l1"><a class="reference internal" href="../prop_inst/CPACK_DESKTOP_SHORTCUTS.html">CPACK_DESKTOP_SHORTCUTS</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_inst/CPACK_NEVER_OVERWRITE.html">CPACK_NEVER_OVERWRITE</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_inst/CPACK_PERMANENT.html">CPACK_PERMANENT</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_inst/CPACK_START_MENU_SHORTCUTS.html">CPACK_START_MENU_SHORTCUTS</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_inst/CPACK_STARTUP_SHORTCUTS.html">CPACK_STARTUP_SHORTCUTS</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_inst/CPACK_WIX_ACL.html">CPACK_WIX_ACL</a></li>
</ul>
</div>
</section>
<section id="deprecated-properties-on-directories">
<h2><a class="toc-backref" href="#id9" role="doc-backlink">Deprecated Properties on Directories</a><a class="headerlink" href="#deprecated-properties-on-directories" title="Permalink to this heading">¶</a></h2>
<div class="toctree-wrapper compound">
<ul>
<li class="toctree-l1"><a class="reference internal" href="../prop_dir/ADDITIONAL_MAKE_CLEAN_FILES.html">ADDITIONAL_MAKE_CLEAN_FILES</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_dir/COMPILE_DEFINITIONS_CONFIG.html">COMPILE_DEFINITIONS_&lt;CONFIG&gt;</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_dir/INTERPROCEDURAL_OPTIMIZATION.html">INTERPROCEDURAL_OPTIMIZATION</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_dir/INTERPROCEDURAL_OPTIMIZATION_CONFIG.html">INTERPROCEDURAL_OPTIMIZATION_&lt;CONFIG&gt;</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_dir/TEST_INCLUDE_FILE.html">TEST_INCLUDE_FILE</a></li>
</ul>
</div>
</section>
<section id="deprecated-properties-on-targets">
<h2><a class="toc-backref" href="#id10" role="doc-backlink">Deprecated Properties on Targets</a><a class="headerlink" href="#deprecated-properties-on-targets" title="Permalink to this heading">¶</a></h2>
<div class="toctree-wrapper compound">
<ul>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/COMPILE_DEFINITIONS_CONFIG.html">COMPILE_DEFINITIONS_&lt;CONFIG&gt;</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/IMPORTED_NO_SYSTEM.html">IMPORTED_NO_SYSTEM</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/IOS_INSTALL_COMBINED.html">IOS_INSTALL_COMBINED</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/POST_INSTALL_SCRIPT.html">POST_INSTALL_SCRIPT</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/PRE_INSTALL_SCRIPT.html">PRE_INSTALL_SCRIPT</a></li>
<li class="toctree-l1"><a class="reference internal" href="../prop_tgt/VS_WINRT_EXTENSIONS.html">VS_WINRT_EXTENSIONS</a></li>
</ul>
</div>
</section>
<section id="deprecated-properties-on-source-files">
<h2><a class="toc-backref" href="#id11" role="doc-backlink">Deprecated Properties on Source Files</a><a class="headerlink" href="#deprecated-properties-on-source-files" title="Permalink to this heading">¶</a></h2>
<div class="toctree-wrapper compound">
<ul>
<li class="toctree-l1"><a class="reference internal" href="../prop_sf/COMPILE_DEFINITIONS_CONFIG.html">COMPILE_DEFINITIONS_&lt;CONFIG&gt;</a></li>
</ul>
</div>
</section>
</section>


            <div class="clearer"></div>
          </div>
        </div>
      </div>
      <div class="sphinxsidebar" role="navigation" aria-label="main navigation">
        <div class="sphinxsidebarwrapper">
  <div>
    <h3><a href="../index.html">Table of Contents</a></h3>
    <ul>
<li><a class="reference internal" href="#">cmake-properties(7)</a><ul>
<li><a class="reference internal" href="#properties-of-global-scope">Properties of Global Scope</a></li>
<li><a class="reference internal" href="#properties-on-directories">Properties on Directories</a></li>
<li><a class="reference internal" href="#properties-on-targets">Properties on Targets</a></li>
<li><a class="reference internal" href="#properties-on-tests">Properties on Tests</a></li>
<li><a class="reference internal" href="#properties-on-source-files">Properties on Source Files</a></li>
<li><a class="reference internal" href="#properties-on-cache-entries">Properties on Cache Entries</a></li>
<li><a class="reference internal" href="#properties-on-installed-files">Properties on Installed Files</a></li>
<li><a class="reference internal" href="#deprecated-properties-on-directories">Deprecated Properties on Directories</a></li>
<li><a class="reference internal" href="#deprecated-properties-on-targets">Deprecated Properties on Targets</a></li>
<li><a class="reference internal" href="#deprecated-properties-on-source-files">Deprecated Properties on Source Files</a></li>
</ul>
</li>
</ul>

  </div>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="cmake-presets.7.html"
                          title="previous chapter">cmake-presets(7)</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="../prop_gbl/ALLOW_DUPLICATE_CUSTOM_TARGETS.html"
                          title="next chapter">ALLOW_DUPLICATE_CUSTOM_TARGETS</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../_sources/manual/cmake-properties.7.rst.txt"
            rel="nofollow">Show Source</a></li>
    </ul>
   </div>
<div id="searchbox" style="display: none" role="search">
  <h3 id="searchlabel">Quick search</h3>
    <div class="searchformwrapper">
    <form class="search" action="../search.html" method="get">
      <input type="text" name="q" aria-labelledby="searchlabel" autocomplete="off" autocorrect="off" autocapitalize="off" spellcheck="false"/>
      <input type="submit" value="Go" />
    </form>
    </div>
</div>
<script>document.getElementById('searchbox').style.display = "block"</script>
        </div>
      </div>
      <div class="clearer"></div>
    </div>
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             >index</a></li>
        <li class="right" >
          <a href="../prop_gbl/ALLOW_DUPLICATE_CUSTOM_TARGETS.html" title="ALLOW_DUPLICATE_CUSTOM_TARGETS"
             >next</a> |</li>
        <li class="right" >
          <a href="cmake-presets.7.html" title="cmake-presets(7)"
             >previous</a> |</li>
  <li>
    <img src="../_static/cmake-logo-16.png" alt=""
         style="vertical-align: middle; margin-top: -2px" />
  </li>
  <li>
    <a href="https://cmake.org/">CMake</a> &#187;
  </li>
  <li>
    <a href="../index.html">3.28.1 Documentation</a> &#187;
  </li>

        <li class="nav-item nav-item-this"><a href="">cmake-properties(7)</a></li> 
      </ul>
    </div>

    <div class="footer" role="contentinfo">
        &#169; Copyright 2000-2023 Kitware, Inc. and Contributors.
      Created using <a href="https://www.sphinx-doc.org/">Sphinx</a> 6.2.1.
    </div>
  </body>
</html>