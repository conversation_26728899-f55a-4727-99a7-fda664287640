
<!DOCTYPE html>

<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="generator" content="Docutils 0.19: https://docutils.sourceforge.io/" />

  <title>Using Dependencies Guide &mdash; CMake 3.28.1 Documentation</title>

    <link rel="stylesheet" type="text/css" href="../../_static/pygments.css" />
    <link rel="stylesheet" type="text/css" href="../../_static/cmake.css" />
    
    <script data-url_root="../../" id="documentation_options" src="../../_static/documentation_options.js"></script>
    <script src="../../_static/doctools.js"></script>
    <script src="../../_static/sphinx_highlight.js"></script>
    
    <link rel="icon" href="../../_static/cmake-favicon.ico"/>
    <link rel="index" title="Index" href="../../genindex.html" />
    <link rel="search" title="Search" href="../../search.html" />
    <link rel="next" title="Importing and Exporting Guide" href="../importing-exporting/index.html" />
    <link rel="prev" title="User Interaction Guide" href="../user-interaction/index.html" />
 

  </head><body>

    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../../genindex.html" title="General Index"
             accesskey="I">index</a></li>
        <li class="right" >
          <a href="../importing-exporting/index.html" title="Importing and Exporting Guide"
             accesskey="N">next</a> |</li>
        <li class="right" >
          <a href="../user-interaction/index.html" title="User Interaction Guide"
             accesskey="P">previous</a> |</li>
  <li>
    <img src="../../_static/cmake-logo-16.png" alt=""
         style="vertical-align: middle; margin-top: -2px" />
  </li>
  <li>
    <a href="https://cmake.org/">CMake</a> &#187;
  </li>
  <li>
    <a href="../../index.html">3.28.1 Documentation</a> &#187;
  </li>

        <li class="nav-item nav-item-this"><a href="">Using Dependencies Guide</a></li> 
      </ul>
    </div>  

    <div class="document">
      <div class="documentwrapper">
        <div class="bodywrapper">
          <div class="body" role="main">
            
  <section id="using-dependencies-guide">
<span id="guide:Using Dependencies Guide"></span><h1><a class="toc-backref" href="#id3" role="doc-backlink">Using Dependencies Guide</a><a class="headerlink" href="#using-dependencies-guide" title="Permalink to this heading">¶</a></h1>
<nav class="contents" id="contents">
<p class="topic-title">Contents</p>
<ul class="simple">
<li><p><a class="reference internal" href="#using-dependencies-guide" id="id3">Using Dependencies Guide</a></p>
<ul>
<li><p><a class="reference internal" href="#introduction" id="id4">Introduction</a></p></li>
<li><p><a class="reference internal" href="#using-pre-built-packages-with-find-package" id="id5">Using Pre-built Packages With <code class="docutils literal notranslate"><span class="pre">find_package()</span></code></a></p>
<ul>
<li><p><a class="reference internal" href="#config-file-packages" id="id6">Config-file packages</a></p></li>
<li><p><a class="reference internal" href="#find-module-files" id="id7">Find Module Files</a></p></li>
<li><p><a class="reference internal" href="#imported-targets" id="id8">Imported Targets</a></p></li>
</ul>
</li>
<li><p><a class="reference internal" href="#downloading-and-building-from-source-with-fetchcontent" id="id9">Downloading And Building From Source With <code class="docutils literal notranslate"><span class="pre">FetchContent</span></code></a></p></li>
<li><p><a class="reference internal" href="#fetchcontent-and-find-package-integration" id="id10"><code class="docutils literal notranslate"><span class="pre">FetchContent</span></code> And <code class="docutils literal notranslate"><span class="pre">find_package()</span></code> Integration</a></p></li>
<li><p><a class="reference internal" href="#dependency-providers" id="id11">Dependency Providers</a></p></li>
</ul>
</li>
</ul>
</nav>
<section id="introduction">
<h2><a class="toc-backref" href="#id4" role="doc-backlink">Introduction</a><a class="headerlink" href="#introduction" title="Permalink to this heading">¶</a></h2>
<p>Projects will frequently depend on other projects, assets, and artifacts.
CMake provides a number of ways to incorporate such things into the build.
Projects and users have the flexibility to choose between methods that
best suit their needs.</p>
<p>The primary methods of bringing dependencies into the build are the
<span class="target" id="index-0-command:find_package"></span><a class="reference internal" href="../../command/find_package.html#command:find_package" title="find_package"><code class="xref cmake cmake-command docutils literal notranslate"><span class="pre">find_package()</span></code></a> command and the <span class="target" id="index-0-module:FetchContent"></span><a class="reference internal" href="../../module/FetchContent.html#module:FetchContent" title="FetchContent"><code class="xref cmake cmake-module docutils literal notranslate"><span class="pre">FetchContent</span></code></a> module.
The <span class="target" id="index-0-module:FindPkgConfig"></span><a class="reference internal" href="../../module/FindPkgConfig.html#module:FindPkgConfig" title="FindPkgConfig"><code class="xref cmake cmake-module docutils literal notranslate"><span class="pre">FindPkgConfig</span></code></a> module is also sometimes used, although it
lacks some of the integration of the other two and is not discussed any
further in this guide.</p>
<p>Dependencies can also be made available by a custom
<a class="reference internal" href="../../command/cmake_language.html#dependency-providers"><span class="std std-ref">dependency provider</span></a>.
This might be a third party package manager, or it might be custom code
implemented by the developer.  Dependency providers co-operate with the
primary methods mentioned above to extend their flexibility.</p>
</section>
<section id="using-pre-built-packages-with-find-package">
<span id="prebuilt-find-package"></span><h2><a class="toc-backref" href="#id5" role="doc-backlink">Using Pre-built Packages With <code class="docutils literal notranslate"><span class="pre">find_package()</span></code></a><a class="headerlink" href="#using-pre-built-packages-with-find-package" title="Permalink to this heading">¶</a></h2>
<p>A package needed by the project may already be built and available at some
location on the user's system.  That package might have also been built by
CMake, or it could have used a different build system entirely.  It might
even just be a collection of files that didn't need to be built at all.
CMake provides the <span class="target" id="index-1-command:find_package"></span><a class="reference internal" href="../../command/find_package.html#command:find_package" title="find_package"><code class="xref cmake cmake-command docutils literal notranslate"><span class="pre">find_package()</span></code></a> command for these scenarios.
It searches well-known locations, along with additional hints and paths
provided by the project or user.  It also supports package components and
packages being optional.  Result variables are provided to allow the project
to customize its own behavior according to whether the package or specific
components were found.</p>
<p>In most cases, projects should generally use the <a class="reference internal" href="../../command/find_package.html#basic-signature"><span class="std std-ref">Basic Signature</span></a>.
Most of the time, this will involve just the package name, maybe a version
constraint, and the <code class="docutils literal notranslate"><span class="pre">REQUIRED</span></code> keyword if the dependency is not optional.
A set of package components may also be specified.</p>
<div class="literal-block-wrapper docutils container" id="id1">
<div class="code-block-caption"><span class="caption-text">Examples of <code class="docutils literal notranslate"><span class="pre">find_package()</span></code> basic signature</span><a class="headerlink" href="#id1" title="Permalink to this code">¶</a></div>
<div class="highlight-cmake notranslate"><div class="highlight"><pre><span></span><span class="nf">find_package(</span><span class="nb">Catch2</span><span class="nf">)</span>
<span class="nf">find_package(</span><span class="nb">GTest</span><span class="w"> </span><span class="no">REQUIRED</span><span class="nf">)</span>
<span class="nf">find_package(</span><span class="nb">Boost</span><span class="w"> </span><span class="m">1.79</span><span class="w"> </span><span class="no">COMPONENTS</span><span class="w"> </span><span class="nb">date_time</span><span class="nf">)</span>
</pre></div>
</div>
</div>
<p>The <span class="target" id="index-2-command:find_package"></span><a class="reference internal" href="../../command/find_package.html#command:find_package" title="find_package"><code class="xref cmake cmake-command docutils literal notranslate"><span class="pre">find_package()</span></code></a> command supports two main methods for carrying
out the search:</p>
<dl class="simple">
<dt><strong>Config mode</strong></dt><dd><p>With this method, the command looks for files that are typically provided
by the package itself.  This is the more reliable method of the two, since
the package details should always be in sync with the package.</p>
</dd>
<dt><strong>Module mode</strong></dt><dd><p>Not all packages are CMake-aware. Many don't provide the files needed to
support config mode.  For such cases, a Find module file can be provided
separately, either by the project or by CMake.  A Find module is typically
a heuristic implementation which knows what the package normally provides
and how to present that package to the project.  Since Find modules are
usually distributed separately from the package, they are not as reliable.
They are typically maintained separately, and they are likely to follow
different release schedules, so they can easily become out-of-date.</p>
</dd>
</dl>
<p>Depending on the arguments used, <span class="target" id="index-3-command:find_package"></span><a class="reference internal" href="../../command/find_package.html#command:find_package" title="find_package"><code class="xref cmake cmake-command docutils literal notranslate"><span class="pre">find_package()</span></code></a> may use one or both
of the above methods.  By restricting the options to just the basic signature,
both config mode and module mode can be used to satisfy the dependency.
The presence of other options may restrict the call to using only one of the
two methods, potentially reducing the command's ability to find the dependency.
See the <span class="target" id="index-4-command:find_package"></span><a class="reference internal" href="../../command/find_package.html#command:find_package" title="find_package"><code class="xref cmake cmake-command docutils literal notranslate"><span class="pre">find_package()</span></code></a> documentation for full details about this
complex topic.</p>
<p>For both search methods, the user can also set cache variables on the
<span class="target" id="index-0-manual:cmake(1)"></span><a class="reference internal" href="../../manual/cmake.1.html#manual:cmake(1)" title="cmake(1)"><code class="xref cmake cmake-manual docutils literal notranslate"><span class="pre">cmake(1)</span></code></a> command line or in the <span class="target" id="index-0-manual:ccmake(1)"></span><a class="reference internal" href="../../manual/ccmake.1.html#manual:ccmake(1)" title="ccmake(1)"><code class="xref cmake cmake-manual docutils literal notranslate"><span class="pre">ccmake(1)</span></code></a> or
<span class="target" id="index-0-manual:cmake-gui(1)"></span><a class="reference internal" href="../../manual/cmake-gui.1.html#manual:cmake-gui(1)" title="cmake-gui(1)"><code class="xref cmake cmake-manual docutils literal notranslate"><span class="pre">cmake-gui(1)</span></code></a> UI tools to influence and override where to find
packages. See the <a class="reference internal" href="../user-interaction/index.html#setting-build-variables"><span class="std std-ref">User Interaction Guide</span></a>
for more on how to set cache variables.</p>
<section id="config-file-packages">
<span id="libraries-providing-config-file-packages"></span><h3><a class="toc-backref" href="#id6" role="doc-backlink">Config-file packages</a><a class="headerlink" href="#config-file-packages" title="Permalink to this heading">¶</a></h3>
<p>The preferred way for a third party to provide executables, libraries,
headers, and other files for use with CMake is to provide
<a class="reference internal" href="../../manual/cmake-packages.7.html#config-file-packages"><span class="std std-ref">config files</span></a>.  These are text files shipped
with the package, which define CMake targets, variables, commands, and so on.
The config file is an ordinary CMake script, which is read in by the
<span class="target" id="index-5-command:find_package"></span><a class="reference internal" href="../../command/find_package.html#command:find_package" title="find_package"><code class="xref cmake cmake-command docutils literal notranslate"><span class="pre">find_package()</span></code></a> command.</p>
<p>The config files can usually be found in a directory whose name matches the
pattern <code class="docutils literal notranslate"><span class="pre">lib/cmake/&lt;PackageName&gt;</span></code>, although they may be in other locations
instead (see <a class="reference internal" href="../../command/find_package.html#search-procedure"><span class="std std-ref">Config Mode Search Procedure</span></a>).  The <code class="docutils literal notranslate"><span class="pre">&lt;PackageName&gt;</span></code> is usually the
first argument to the <span class="target" id="index-6-command:find_package"></span><a class="reference internal" href="../../command/find_package.html#command:find_package" title="find_package"><code class="xref cmake cmake-command docutils literal notranslate"><span class="pre">find_package()</span></code></a> command, and it may even be the
only argument.  Alternative names can also be specified with the <code class="docutils literal notranslate"><span class="pre">NAMES</span></code>
option:</p>
<div class="literal-block-wrapper docutils container" id="id2">
<div class="code-block-caption"><span class="caption-text">Providing alternative names when finding a package</span><a class="headerlink" href="#id2" title="Permalink to this code">¶</a></div>
<div class="highlight-cmake notranslate"><div class="highlight"><pre><span></span><span class="nf">find_package(</span><span class="nb">SomeThing</span>
<span class="w">  </span><span class="no">NAMES</span>
<span class="w">    </span><span class="nb">SameThingOtherName</span><span class="w">   </span><span class="c"># Another name for the package</span>
<span class="w">    </span><span class="nb">SomeThing</span><span class="w">            </span><span class="c"># Also still look for its canonical name</span>
<span class="nf">)</span>
</pre></div>
</div>
</div>
<p>The config file must be named either <code class="docutils literal notranslate"><span class="pre">&lt;PackageName&gt;Config.cmake</span></code> or
<code class="docutils literal notranslate"><span class="pre">&lt;LowercasePackageName&gt;-config.cmake</span></code> (the former is used for the remainder
of this guide, but both are supported).  This file is the entry point
to the package for CMake.  A separate optional file named
<code class="docutils literal notranslate"><span class="pre">&lt;PackageName&gt;ConfigVersion.cmake</span></code> or
<code class="docutils literal notranslate"><span class="pre">&lt;LowercasePackageName&gt;-config-version.cmake</span></code> may also exist in the same
directory.  This file is used by CMake to determine whether the version of
the package satisfies any version constraint included in the call to
<span class="target" id="index-7-command:find_package"></span><a class="reference internal" href="../../command/find_package.html#command:find_package" title="find_package"><code class="xref cmake cmake-command docutils literal notranslate"><span class="pre">find_package()</span></code></a>.  It is optional to specify a version when calling
<span class="target" id="index-8-command:find_package"></span><a class="reference internal" href="../../command/find_package.html#command:find_package" title="find_package"><code class="xref cmake cmake-command docutils literal notranslate"><span class="pre">find_package()</span></code></a>, even if a <code class="docutils literal notranslate"><span class="pre">&lt;PackageName&gt;ConfigVersion.cmake</span></code>
file is present.</p>
<p>If the <code class="docutils literal notranslate"><span class="pre">&lt;PackageName&gt;Config.cmake</span></code> file is found and any version constraint
is satisfied, the <span class="target" id="index-9-command:find_package"></span><a class="reference internal" href="../../command/find_package.html#command:find_package" title="find_package"><code class="xref cmake cmake-command docutils literal notranslate"><span class="pre">find_package()</span></code></a> command considers the package to be
found, and the entire package is assumed to be complete as designed.</p>
<p>There may be additional files providing CMake commands or
<a class="reference internal" href="../../manual/cmake-buildsystem.7.html#imported-targets"><span class="std std-ref">Imported Targets</span></a> for you to use.  CMake does not enforce any naming
convention for these files.  They are related to the primary
<code class="docutils literal notranslate"><span class="pre">&lt;PackageName&gt;Config.cmake</span></code> file by use of the CMake <span class="target" id="index-0-command:include"></span><a class="reference internal" href="../../command/include.html#command:include" title="include"><code class="xref cmake cmake-command docutils literal notranslate"><span class="pre">include()</span></code></a>
command.  The <code class="docutils literal notranslate"><span class="pre">&lt;PackageName&gt;Config.cmake</span></code> file would typically include
these for you, so they won't usually require any additional step other than
the call to <span class="target" id="index-10-command:find_package"></span><a class="reference internal" href="../../command/find_package.html#command:find_package" title="find_package"><code class="xref cmake cmake-command docutils literal notranslate"><span class="pre">find_package()</span></code></a>.</p>
<p>If the location of the package is in a
<a class="reference internal" href="../../command/find_package.html#search-procedure"><span class="std std-ref">directory known to CMake</span></a>, the
<span class="target" id="index-11-command:find_package"></span><a class="reference internal" href="../../command/find_package.html#command:find_package" title="find_package"><code class="xref cmake cmake-command docutils literal notranslate"><span class="pre">find_package()</span></code></a> call should succeed.  The directories known to CMake
are platform-specific.  For example, packages installed on Linux with a
standard system package manager will be found in the <code class="docutils literal notranslate"><span class="pre">/usr</span></code> prefix
automatically.  Packages installed in <code class="docutils literal notranslate"><span class="pre">Program</span> <span class="pre">Files</span></code> on Windows will
similarly be found automatically.</p>
<p>Packages will not be found automatically without help if they are in
locations not known to CMake, such as <code class="docutils literal notranslate"><span class="pre">/opt/mylib</span></code> or <code class="docutils literal notranslate"><span class="pre">$HOME/dev/prefix</span></code>.
This is a normal situation, and CMake provides several ways for users to
specify where to find such libraries.</p>
<p>The <span class="target" id="index-0-variable:CMAKE_PREFIX_PATH"></span><a class="reference internal" href="../../variable/CMAKE_PREFIX_PATH.html#variable:CMAKE_PREFIX_PATH" title="CMAKE_PREFIX_PATH"><code class="xref cmake cmake-variable docutils literal notranslate"><span class="pre">CMAKE_PREFIX_PATH</span></code></a> variable may be
<a class="reference internal" href="../user-interaction/index.html#setting-build-variables"><span class="std std-ref">set when invoking CMake</span></a>.
It is treated as a list of base paths in which to search for
<a class="reference internal" href="../../manual/cmake-packages.7.html#config-file-packages"><span class="std std-ref">config files</span></a>.  A package installed in
<code class="docutils literal notranslate"><span class="pre">/opt/somepackage</span></code> will typically install config files such as
<code class="docutils literal notranslate"><span class="pre">/opt/somepackage/lib/cmake/somePackage/SomePackageConfig.cmake</span></code>.
In that case, <code class="docutils literal notranslate"><span class="pre">/opt/somepackage</span></code> should be added to
<span class="target" id="index-1-variable:CMAKE_PREFIX_PATH"></span><a class="reference internal" href="../../variable/CMAKE_PREFIX_PATH.html#variable:CMAKE_PREFIX_PATH" title="CMAKE_PREFIX_PATH"><code class="xref cmake cmake-variable docutils literal notranslate"><span class="pre">CMAKE_PREFIX_PATH</span></code></a>.</p>
<p>The environment variable <code class="docutils literal notranslate"><span class="pre">CMAKE_PREFIX_PATH</span></code> may also be populated with
prefixes to search for packages.  Like the <code class="docutils literal notranslate"><span class="pre">PATH</span></code> environment variable,
this is a list, but it needs to use the platform-specific environment variable
list item separator (<code class="docutils literal notranslate"><span class="pre">:</span></code> on Unix and <code class="docutils literal notranslate"><span class="pre">;</span></code> on Windows).</p>
<p>The <span class="target" id="index-2-variable:CMAKE_PREFIX_PATH"></span><a class="reference internal" href="../../variable/CMAKE_PREFIX_PATH.html#variable:CMAKE_PREFIX_PATH" title="CMAKE_PREFIX_PATH"><code class="xref cmake cmake-variable docutils literal notranslate"><span class="pre">CMAKE_PREFIX_PATH</span></code></a> variable provides convenience in cases
where multiple prefixes need to be specified, or when multiple packages
are available under the same prefix.  Paths to packages may also be
specified by setting variables matching <code class="docutils literal notranslate"><span class="pre">&lt;PackageName&gt;_DIR</span></code>, such as
<code class="docutils literal notranslate"><span class="pre">SomePackage_DIR</span></code>.  Note that this is not a prefix, but should be a full
path to a directory containing a config-style package file, such as
<code class="docutils literal notranslate"><span class="pre">/opt/somepackage/lib/cmake/SomePackage</span></code> in the above example.
See the <span class="target" id="index-12-command:find_package"></span><a class="reference internal" href="../../command/find_package.html#command:find_package" title="find_package"><code class="xref cmake cmake-command docutils literal notranslate"><span class="pre">find_package()</span></code></a> documentation for other CMake variables and
environment variables that can affect the search.</p>
</section>
<section id="find-module-files">
<span id="libraries-not-providing-config-file-packages"></span><h3><a class="toc-backref" href="#id7" role="doc-backlink">Find Module Files</a><a class="headerlink" href="#find-module-files" title="Permalink to this heading">¶</a></h3>
<p>Packages which do not provide config files can still be found with the
<span class="target" id="index-13-command:find_package"></span><a class="reference internal" href="../../command/find_package.html#command:find_package" title="find_package"><code class="xref cmake cmake-command docutils literal notranslate"><span class="pre">find_package()</span></code></a> command, if a <code class="docutils literal notranslate"><span class="pre">FindSomePackage.cmake</span></code> file is
available.  These Find module files are different to config files in that:</p>
<ol class="arabic simple">
<li><p>Find module files should not be provided by the package itself.</p></li>
<li><p>The availability of a <code class="docutils literal notranslate"><span class="pre">Find&lt;PackageName&gt;.cmake</span></code> file does not indicate
the availability of the package, or any particular part of the package.</p></li>
<li><p>CMake does not search the locations specified in the
<span class="target" id="index-3-variable:CMAKE_PREFIX_PATH"></span><a class="reference internal" href="../../variable/CMAKE_PREFIX_PATH.html#variable:CMAKE_PREFIX_PATH" title="CMAKE_PREFIX_PATH"><code class="xref cmake cmake-variable docutils literal notranslate"><span class="pre">CMAKE_PREFIX_PATH</span></code></a> variable for <code class="docutils literal notranslate"><span class="pre">Find&lt;PackageName&gt;.cmake</span></code>
files.  Instead, CMake searches for such files in the locations given
by the <span class="target" id="index-0-variable:CMAKE_MODULE_PATH"></span><a class="reference internal" href="../../variable/CMAKE_MODULE_PATH.html#variable:CMAKE_MODULE_PATH" title="CMAKE_MODULE_PATH"><code class="xref cmake cmake-variable docutils literal notranslate"><span class="pre">CMAKE_MODULE_PATH</span></code></a> variable.  It is common for users to
set the <span class="target" id="index-1-variable:CMAKE_MODULE_PATH"></span><a class="reference internal" href="../../variable/CMAKE_MODULE_PATH.html#variable:CMAKE_MODULE_PATH" title="CMAKE_MODULE_PATH"><code class="xref cmake cmake-variable docutils literal notranslate"><span class="pre">CMAKE_MODULE_PATH</span></code></a> when running CMake, and it is common
for CMake projects to append to <span class="target" id="index-2-variable:CMAKE_MODULE_PATH"></span><a class="reference internal" href="../../variable/CMAKE_MODULE_PATH.html#variable:CMAKE_MODULE_PATH" title="CMAKE_MODULE_PATH"><code class="xref cmake cmake-variable docutils literal notranslate"><span class="pre">CMAKE_MODULE_PATH</span></code></a> to allow use
of local Find module files.</p></li>
<li><p>CMake ships <code class="docutils literal notranslate"><span class="pre">Find&lt;PackageName&gt;.cmake</span></code> files for some
<span class="target" id="index-0-manual:cmake-modules(7)"></span><a class="reference internal" href="../../manual/cmake-modules.7.html#manual:cmake-modules(7)" title="cmake-modules(7)"><code class="xref cmake cmake-manual docutils literal notranslate"><span class="pre">third</span> <span class="pre">party</span> <span class="pre">packages</span></code></a>.  These files are a
maintenance burden for CMake, and it is not unusual for these to fall
behind the latest releases of the packages they are associated with.
In general, new Find modules are not added to CMake any more.  Projects
should encourage the upstream packages to provide a config file where
possible.  If that is unsuccessful, the project should provide its own
Find module for the package.</p></li>
</ol>
<p>See <a class="reference internal" href="../../manual/cmake-developer.7.html#find-modules"><span class="std std-ref">Find Modules</span></a> for a detailed discussion of how to write a
Find module file.</p>
</section>
<section id="imported-targets">
<span id="imported-targets-from-packages"></span><h3><a class="toc-backref" href="#id8" role="doc-backlink">Imported Targets</a><a class="headerlink" href="#imported-targets" title="Permalink to this heading">¶</a></h3>
<p>Both config files and Find module files can define <a class="reference internal" href="../../manual/cmake-buildsystem.7.html#imported-targets"><span class="std std-ref">Imported Targets</span></a>.
These will typically have names of the form <code class="docutils literal notranslate"><span class="pre">SomePrefix::ThingName</span></code>.
Where these are available, the project should prefer to use them instead of
any CMake variables that may also be provided.  Such targets typically carry
usage requirements and apply things like header search paths, compiler
definitions, etc. automatically to other targets that link to them (e.g. using
<span class="target" id="index-0-command:target_link_libraries"></span><a class="reference internal" href="../../command/target_link_libraries.html#command:target_link_libraries" title="target_link_libraries"><code class="xref cmake cmake-command docutils literal notranslate"><span class="pre">target_link_libraries()</span></code></a>).  This is both more robust and more
convenient than trying to apply the same things manually using variables.
Check the documentation for the package or Find module to see what imported
targets it defines, if any.</p>
<p>Imported targets should also encapsulate any configuration-specific paths.
This includes the location of binaries (libraries, executables), compiler
flags, and any other configuration-dependent quantities.  Find modules may
be less reliable in providing these details than config files.</p>
<p>A complete example which finds a third party package and uses a library
from it might look like the following:</p>
<div class="highlight-cmake notranslate"><div class="highlight"><pre><span></span><span class="nf">cmake_minimum_required(</span><span class="no">VERSION</span><span class="w"> </span><span class="m">3.10</span><span class="nf">)</span>
<span class="nf">project(</span><span class="nb">MyExeProject</span><span class="w"> </span><span class="no">VERSION</span><span class="w"> </span><span class="m">1.0.0</span><span class="nf">)</span>

<span class="c"># Make project-provided Find modules available</span>
<span class="nf">list(</span><span class="no">APPEND</span><span class="w"> </span><span class="no">CMAKE_MODULE_PATH</span><span class="w"> </span><span class="s">&quot;${CMAKE_CURRENT_SOURCE_DIR}/cmake&quot;</span><span class="nf">)</span>

<span class="nf">find_package(</span><span class="nb">SomePackage</span><span class="w"> </span><span class="no">REQUIRED</span><span class="nf">)</span>
<span class="nf">add_executable(</span><span class="nb">MyExe</span><span class="w"> </span><span class="nb">main.cpp</span><span class="nf">)</span>
<span class="nf">target_link_libraries(</span><span class="nb">MyExe</span><span class="w"> </span><span class="no">PRIVATE</span><span class="w"> </span><span class="nb">SomePrefix</span><span class="o">::</span><span class="nb">LibName</span><span class="nf">)</span>
</pre></div>
</div>
<p>Note that the above call to <span class="target" id="index-14-command:find_package"></span><a class="reference internal" href="../../command/find_package.html#command:find_package" title="find_package"><code class="xref cmake cmake-command docutils literal notranslate"><span class="pre">find_package()</span></code></a> could be resolved by
a config file or a Find module.  It uses only the basic arguments supported
by the <a class="reference internal" href="../../command/find_package.html#basic-signature"><span class="std std-ref">Basic Signature</span></a>.  A <code class="docutils literal notranslate"><span class="pre">FindSomePackage.cmake</span></code> file in the
<code class="docutils literal notranslate"><span class="pre">${CMAKE_CURRENT_SOURCE_DIR}/cmake</span></code> directory would allow the
<span class="target" id="index-15-command:find_package"></span><a class="reference internal" href="../../command/find_package.html#command:find_package" title="find_package"><code class="xref cmake cmake-command docutils literal notranslate"><span class="pre">find_package()</span></code></a> command to succeed using module mode, for example.
If no such module file is present, the system would be searched for a config
file.</p>
</section>
</section>
<section id="downloading-and-building-from-source-with-fetchcontent">
<h2><a class="toc-backref" href="#id9" role="doc-backlink">Downloading And Building From Source With <code class="docutils literal notranslate"><span class="pre">FetchContent</span></code></a><a class="headerlink" href="#downloading-and-building-from-source-with-fetchcontent" title="Permalink to this heading">¶</a></h2>
<p>Dependencies do not necessarily have to be pre-built in order to use them
with CMake.  They can be built from sources as part of the main project.
The <span class="target" id="index-1-module:FetchContent"></span><a class="reference internal" href="../../module/FetchContent.html#module:FetchContent" title="FetchContent"><code class="xref cmake cmake-module docutils literal notranslate"><span class="pre">FetchContent</span></code></a> module provides functionality to download
content (typically sources, but can be anything) and add it to the main
project if the dependency also uses CMake.  The dependency's sources will
be built along with the rest of the project, just as though the sources were
part of the project's own sources.</p>
<p>The general pattern is that the project should first declare all the
dependencies it wants to use, then ask for them to be made available.
The following demonstrates the principle (see <a class="reference internal" href="../../module/FetchContent.html#fetch-content-examples"><span class="std std-ref">Examples</span></a>
for more):</p>
<div class="highlight-cmake notranslate"><div class="highlight"><pre><span></span><span class="nf">include(</span><span class="nb">FetchContent</span><span class="nf">)</span>
<span class="nf">FetchContent_Declare(</span>
<span class="w">  </span><span class="nb">googletest</span>
<span class="w">  </span><span class="no">GIT_REPOSITORY</span><span class="w"> </span><span class="na">https://github.com/google/googletest.git</span>
<span class="w">  </span><span class="no">GIT_TAG</span><span class="w">        </span><span class="m">703</span><span class="nb">bd9caab50b139428cea1aaff9974ebee5742e</span><span class="w"> </span><span class="c"># release-1.10.0</span>
<span class="nf">)</span>
<span class="nf">FetchContent_Declare(</span>
<span class="w">  </span><span class="nb">Catch2</span>
<span class="w">  </span><span class="no">GIT_REPOSITORY</span><span class="w"> </span><span class="na">https://github.com/catchorg/Catch2.git</span>
<span class="w">  </span><span class="no">GIT_TAG</span><span class="w">        </span><span class="m">605</span><span class="nb">a34765aa5d5ecbf476b4598a862ada971b0cc</span><span class="w"> </span><span class="c"># v3.0.1</span>
<span class="nf">)</span>
<span class="nf">FetchContent_MakeAvailable(</span><span class="nb">googletest</span><span class="w"> </span><span class="nb">Catch2</span><span class="nf">)</span>
</pre></div>
</div>
<p>Various download methods are supported, including downloading and extracting
archives from a URL (a range of archive formats are supported), and a number
of repository formats including Git, Subversion, and Mercurial.
Custom download, update, and patch commands can also be used to support
arbitrary use cases.</p>
<p>When a dependency is added to the project with <span class="target" id="index-2-module:FetchContent"></span><a class="reference internal" href="../../module/FetchContent.html#module:FetchContent" title="FetchContent"><code class="xref cmake cmake-module docutils literal notranslate"><span class="pre">FetchContent</span></code></a>, the
project links to the dependency's targets just like any other target from the
project.  If the dependency provides namespaced targets of the form
<code class="docutils literal notranslate"><span class="pre">SomePrefix::ThingName</span></code>, the project should link to those rather than to
any non-namespaced targets.  See the next section for why this is recommended.</p>
<p>Not all dependencies can be brought into the project this way.  Some
dependencies define targets whose names clash with other targets from the
project or other dependencies.  Concrete executable and library targets
created by <span class="target" id="index-0-command:add_executable"></span><a class="reference internal" href="../../command/add_executable.html#command:add_executable" title="add_executable"><code class="xref cmake cmake-command docutils literal notranslate"><span class="pre">add_executable()</span></code></a> and <span class="target" id="index-0-command:add_library"></span><a class="reference internal" href="../../command/add_library.html#command:add_library" title="add_library"><code class="xref cmake cmake-command docutils literal notranslate"><span class="pre">add_library()</span></code></a> are global,
so each one must be unique across the whole build.  If a dependency would
add a clashing target name, it cannot be brought directly into the build
with this method.</p>
</section>
<section id="fetchcontent-and-find-package-integration">
<h2><a class="toc-backref" href="#id10" role="doc-backlink"><code class="docutils literal notranslate"><span class="pre">FetchContent</span></code> And <code class="docutils literal notranslate"><span class="pre">find_package()</span></code> Integration</a><a class="headerlink" href="#fetchcontent-and-find-package-integration" title="Permalink to this heading">¶</a></h2>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.24.</span></p>
</div>
<p>Some dependencies support being added by either <span class="target" id="index-16-command:find_package"></span><a class="reference internal" href="../../command/find_package.html#command:find_package" title="find_package"><code class="xref cmake cmake-command docutils literal notranslate"><span class="pre">find_package()</span></code></a> or
<span class="target" id="index-3-module:FetchContent"></span><a class="reference internal" href="../../module/FetchContent.html#module:FetchContent" title="FetchContent"><code class="xref cmake cmake-module docutils literal notranslate"><span class="pre">FetchContent</span></code></a>.  Such dependencies must ensure they define the same
namespaced targets in both installed and built-from-source scenarios.
A consuming project then links to those namespaced targets and can handle
both scenarios transparently, as long as the project does not use anything
else that isn't provided by both methods.</p>
<p>The project can indicate it is happy to accept a dependency by either method
using the <code class="docutils literal notranslate"><span class="pre">FIND_PACKAGE_ARGS</span></code> option to <span class="target" id="index-0-command:fetchcontent_declare"></span><a class="reference internal" href="../../module/FetchContent.html#command:fetchcontent_declare" title="fetchcontent_declare"><code class="xref cmake cmake-command docutils literal notranslate"><span class="pre">FetchContent_Declare()</span></code></a>.
This allows <span class="target" id="index-0-command:fetchcontent_makeavailable"></span><a class="reference internal" href="../../module/FetchContent.html#command:fetchcontent_makeavailable" title="fetchcontent_makeavailable"><code class="xref cmake cmake-command docutils literal notranslate"><span class="pre">FetchContent_MakeAvailable()</span></code></a> to try satisfying the
dependency with a call to <span class="target" id="index-17-command:find_package"></span><a class="reference internal" href="../../command/find_package.html#command:find_package" title="find_package"><code class="xref cmake cmake-command docutils literal notranslate"><span class="pre">find_package()</span></code></a> first, using the arguments
after the <code class="docutils literal notranslate"><span class="pre">FIND_PACKAGE_ARGS</span></code> keyword, if any.  If that doesn't find the
dependency, it is built from source as described previously instead.</p>
<div class="highlight-cmake notranslate"><div class="highlight"><pre><span></span><span class="nf">include(</span><span class="nb">FetchContent</span><span class="nf">)</span>
<span class="nf">FetchContent_Declare(</span>
<span class="w">  </span><span class="nb">googletest</span>
<span class="w">  </span><span class="no">GIT_REPOSITORY</span><span class="w"> </span><span class="na">https://github.com/google/googletest.git</span>
<span class="w">  </span><span class="no">GIT_TAG</span><span class="w">        </span><span class="m">703</span><span class="nb">bd9caab50b139428cea1aaff9974ebee5742e</span><span class="w"> </span><span class="c"># release-1.10.0</span>
<span class="w">  </span><span class="no">FIND_PACKAGE_ARGS</span><span class="w"> </span><span class="no">NAMES</span><span class="w"> </span><span class="nb">GTest</span>
<span class="nf">)</span>
<span class="nf">FetchContent_MakeAvailable(</span><span class="nb">googletest</span><span class="nf">)</span>

<span class="nf">add_executable(</span><span class="nb">ThingUnitTest</span><span class="w"> </span><span class="nb">thing_ut.cpp</span><span class="nf">)</span>
<span class="nf">target_link_libraries(</span><span class="nb">ThingUnitTest</span><span class="w"> </span><span class="nb">GTest</span><span class="o">::</span><span class="nb">gtest_main</span><span class="nf">)</span>
</pre></div>
</div>
<p>The above example calls
<span class="target" id="index-18-command:find_package"></span><a class="reference internal" href="../../command/find_package.html#command:find_package" title="find_package"><code class="xref cmake cmake-command docutils literal notranslate"><span class="pre">find_package(googletest</span> <span class="pre">NAMES</span> <span class="pre">GTest)</span></code></a> first.
CMake provides a <span class="target" id="index-0-module:FindGTest"></span><a class="reference internal" href="../../module/FindGTest.html#module:FindGTest" title="FindGTest"><code class="xref cmake cmake-module docutils literal notranslate"><span class="pre">FindGTest</span></code></a> module, so if that finds a GTest package
installed somewhere, it will make it available, and the dependency will not be
built from source.  If no GTest package is found, it <em>will</em> be built from
source.  In either case, the <code class="docutils literal notranslate"><span class="pre">GTest::gtest_main</span></code> target is expected to be
defined, so we link our unit test executable to that target.</p>
<p>High-level control is also available through the
<span class="target" id="index-0-variable:FETCHCONTENT_TRY_FIND_PACKAGE_MODE"></span><a class="reference internal" href="../../module/FetchContent.html#variable:FETCHCONTENT_TRY_FIND_PACKAGE_MODE" title="FETCHCONTENT_TRY_FIND_PACKAGE_MODE"><code class="xref cmake cmake-variable docutils literal notranslate"><span class="pre">FETCHCONTENT_TRY_FIND_PACKAGE_MODE</span></code></a> variable.  This can be set to
<code class="docutils literal notranslate"><span class="pre">NEVER</span></code> to disable all redirection to <span class="target" id="index-19-command:find_package"></span><a class="reference internal" href="../../command/find_package.html#command:find_package" title="find_package"><code class="xref cmake cmake-command docutils literal notranslate"><span class="pre">find_package()</span></code></a>.  It can be
set to <code class="docutils literal notranslate"><span class="pre">ALWAYS</span></code> to try <span class="target" id="index-20-command:find_package"></span><a class="reference internal" href="../../command/find_package.html#command:find_package" title="find_package"><code class="xref cmake cmake-command docutils literal notranslate"><span class="pre">find_package()</span></code></a> even if <code class="docutils literal notranslate"><span class="pre">FIND_PACKAGE_ARGS</span></code>
was not specified (this should be used with caution).</p>
<p>The project might also decide that a particular dependency must be built from
source.  This might be needed if a patched or unreleased version of the
dependency is required, or to satisfy some policy that requires all
dependencies to be built from source.  The project can enforce this by adding
the <code class="docutils literal notranslate"><span class="pre">OVERRIDE_FIND_PACKAGE</span></code> keyword to <span class="target" id="index-1-command:fetchcontent_declare"></span><a class="reference internal" href="../../module/FetchContent.html#command:fetchcontent_declare" title="fetchcontent_declare"><code class="xref cmake cmake-command docutils literal notranslate"><span class="pre">FetchContent_Declare()</span></code></a>.
A call to <span class="target" id="index-21-command:find_package"></span><a class="reference internal" href="../../command/find_package.html#command:find_package" title="find_package"><code class="xref cmake cmake-command docutils literal notranslate"><span class="pre">find_package()</span></code></a> for that dependency will then be redirected
to <span class="target" id="index-1-command:fetchcontent_makeavailable"></span><a class="reference internal" href="../../module/FetchContent.html#command:fetchcontent_makeavailable" title="fetchcontent_makeavailable"><code class="xref cmake cmake-command docutils literal notranslate"><span class="pre">FetchContent_MakeAvailable()</span></code></a> instead.</p>
<div class="highlight-cmake notranslate"><div class="highlight"><pre><span></span><span class="nf">include(</span><span class="nb">FetchContent</span><span class="nf">)</span>
<span class="nf">FetchContent_Declare(</span>
<span class="w">  </span><span class="nb">Catch2</span>
<span class="w">  </span><span class="no">URL</span><span class="w"> </span><span class="na">https://intranet.mycomp.com/vendored/Catch2_2.13.4_patched.tgz</span>
<span class="w">  </span><span class="no">URL_HASH</span><span class="w"> </span><span class="no">MD5</span><span class="p">=</span><span class="nb">abc123...</span>
<span class="w">  </span><span class="no">OVERRIDE_FIND_PACKAGE</span>
<span class="nf">)</span>

<span class="c"># The following is automatically redirected to FetchContent_MakeAvailable(Catch2)</span>
<span class="nf">find_package(</span><span class="nb">Catch2</span><span class="nf">)</span>
</pre></div>
</div>
<p>For more advanced use cases, see the
<span class="target" id="index-0-variable:CMAKE_FIND_PACKAGE_REDIRECTS_DIR"></span><a class="reference internal" href="../../variable/CMAKE_FIND_PACKAGE_REDIRECTS_DIR.html#variable:CMAKE_FIND_PACKAGE_REDIRECTS_DIR" title="CMAKE_FIND_PACKAGE_REDIRECTS_DIR"><code class="xref cmake cmake-variable docutils literal notranslate"><span class="pre">CMAKE_FIND_PACKAGE_REDIRECTS_DIR</span></code></a> variable.</p>
</section>
<section id="dependency-providers">
<span id="dependency-providers-overview"></span><h2><a class="toc-backref" href="#id11" role="doc-backlink">Dependency Providers</a><a class="headerlink" href="#dependency-providers" title="Permalink to this heading">¶</a></h2>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.24.</span></p>
</div>
<p>The preceding section discussed techniques that projects can use to specify
their dependencies.  Ideally, the project shouldn't really care where a
dependency comes from, as long as it provides the things it expects (often
just some imported targets).  The project says what it needs and may also
specify where to get it from, in the absence of any other details, so that it
can still be built out-of-the-box.</p>
<p>The developer, on the other hand, may be much more interested in controlling
<em>how</em> a dependency is provided to the project.  You might want to use a
particular version of a package that you built yourself.  You might want
to use a third party package manager.  You might want to redirect some
requests to a different URL on a system you control for security or
performance reasons.  CMake supports these sort of scenarios through
<a class="reference internal" href="../../command/cmake_language.html#dependency-providers"><span class="std std-ref">Dependency Providers</span></a>.</p>
<p>A dependency provider can be set to intercept <span class="target" id="index-22-command:find_package"></span><a class="reference internal" href="../../command/find_package.html#command:find_package" title="find_package"><code class="xref cmake cmake-command docutils literal notranslate"><span class="pre">find_package()</span></code></a> and
<span class="target" id="index-2-command:fetchcontent_makeavailable"></span><a class="reference internal" href="../../module/FetchContent.html#command:fetchcontent_makeavailable" title="fetchcontent_makeavailable"><code class="xref cmake cmake-command docutils literal notranslate"><span class="pre">FetchContent_MakeAvailable()</span></code></a> calls.  The provider is given an
opportunity to satisfy such requests before falling back to the built-in
implementation if the provider doesn't fulfill it.</p>
<p>Only one dependency provider can be set, and it can only be set at a very
specific point early in the CMake run.
The <span class="target" id="index-0-variable:CMAKE_PROJECT_TOP_LEVEL_INCLUDES"></span><a class="reference internal" href="../../variable/CMAKE_PROJECT_TOP_LEVEL_INCLUDES.html#variable:CMAKE_PROJECT_TOP_LEVEL_INCLUDES" title="CMAKE_PROJECT_TOP_LEVEL_INCLUDES"><code class="xref cmake cmake-variable docutils literal notranslate"><span class="pre">CMAKE_PROJECT_TOP_LEVEL_INCLUDES</span></code></a> variable lists CMake files
that will be read while processing the first <span class="target" id="index-0-command:project"></span><a class="reference internal" href="../../command/project.html#command:project" title="project"><code class="xref cmake cmake-command docutils literal notranslate"><span class="pre">project()</span></code></a> call (and
only that call).  This is the only time a dependency provider may be set.
At most, one single provider is expected to be used throughout the whole
project.</p>
<p>For some scenarios, the user wouldn't need to know the details of how the
dependency provider is set.  A third party may provide a file that can be
added to <span class="target" id="index-1-variable:CMAKE_PROJECT_TOP_LEVEL_INCLUDES"></span><a class="reference internal" href="../../variable/CMAKE_PROJECT_TOP_LEVEL_INCLUDES.html#variable:CMAKE_PROJECT_TOP_LEVEL_INCLUDES" title="CMAKE_PROJECT_TOP_LEVEL_INCLUDES"><code class="xref cmake cmake-variable docutils literal notranslate"><span class="pre">CMAKE_PROJECT_TOP_LEVEL_INCLUDES</span></code></a>, which will set up
the dependency provider on the user's behalf.  This is the recommended
approach for package managers.  The developer can use such a file like so:</p>
<div class="highlight-none notranslate"><div class="highlight"><pre><span></span>cmake -DCMAKE_PROJECT_TOP_LEVEL_INCLUDES=/path/to/package_manager/setup.cmake ...
</pre></div>
</div>
<p>For details on how to implement your own custom dependency provider, see the
<span class="target" id="index-0-command:cmake_language"></span><a class="reference internal" href="../../command/cmake_language.html#set-dependency-provider" title="cmake_language(set_dependency_provider)"><code class="xref cmake cmake-command docutils literal notranslate"><span class="pre">cmake_language(SET_DEPENDENCY_PROVIDER)</span></code></a> command.</p>
</section>
</section>


            <div class="clearer"></div>
          </div>
        </div>
      </div>
      <div class="sphinxsidebar" role="navigation" aria-label="main navigation">
        <div class="sphinxsidebarwrapper">
  <div>
    <h3><a href="../../index.html">Table of Contents</a></h3>
    <ul>
<li><a class="reference internal" href="#">Using Dependencies Guide</a><ul>
<li><a class="reference internal" href="#introduction">Introduction</a></li>
<li><a class="reference internal" href="#using-pre-built-packages-with-find-package">Using Pre-built Packages With <code class="docutils literal notranslate"><span class="pre">find_package()</span></code></a><ul>
<li><a class="reference internal" href="#config-file-packages">Config-file packages</a></li>
<li><a class="reference internal" href="#find-module-files">Find Module Files</a></li>
<li><a class="reference internal" href="#imported-targets">Imported Targets</a></li>
</ul>
</li>
<li><a class="reference internal" href="#downloading-and-building-from-source-with-fetchcontent">Downloading And Building From Source With <code class="docutils literal notranslate"><span class="pre">FetchContent</span></code></a></li>
<li><a class="reference internal" href="#fetchcontent-and-find-package-integration"><code class="docutils literal notranslate"><span class="pre">FetchContent</span></code> And <code class="docutils literal notranslate"><span class="pre">find_package()</span></code> Integration</a></li>
<li><a class="reference internal" href="#dependency-providers">Dependency Providers</a></li>
</ul>
</li>
</ul>

  </div>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="../user-interaction/index.html"
                          title="previous chapter">User Interaction Guide</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="../importing-exporting/index.html"
                          title="next chapter">Importing and Exporting Guide</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../../_sources/guide/using-dependencies/index.rst.txt"
            rel="nofollow">Show Source</a></li>
    </ul>
   </div>
<div id="searchbox" style="display: none" role="search">
  <h3 id="searchlabel">Quick search</h3>
    <div class="searchformwrapper">
    <form class="search" action="../../search.html" method="get">
      <input type="text" name="q" aria-labelledby="searchlabel" autocomplete="off" autocorrect="off" autocapitalize="off" spellcheck="false"/>
      <input type="submit" value="Go" />
    </form>
    </div>
</div>
<script>document.getElementById('searchbox').style.display = "block"</script>
        </div>
      </div>
      <div class="clearer"></div>
    </div>
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../../genindex.html" title="General Index"
             >index</a></li>
        <li class="right" >
          <a href="../importing-exporting/index.html" title="Importing and Exporting Guide"
             >next</a> |</li>
        <li class="right" >
          <a href="../user-interaction/index.html" title="User Interaction Guide"
             >previous</a> |</li>
  <li>
    <img src="../../_static/cmake-logo-16.png" alt=""
         style="vertical-align: middle; margin-top: -2px" />
  </li>
  <li>
    <a href="https://cmake.org/">CMake</a> &#187;
  </li>
  <li>
    <a href="../../index.html">3.28.1 Documentation</a> &#187;
  </li>

        <li class="nav-item nav-item-this"><a href="">Using Dependencies Guide</a></li> 
      </ul>
    </div>

    <div class="footer" role="contentinfo">
        &#169; Copyright 2000-2023 Kitware, Inc. and Contributors.
      Created using <a href="https://www.sphinx-doc.org/">Sphinx</a> 6.2.1.
    </div>
  </body>
</html>