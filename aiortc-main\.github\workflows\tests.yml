name: tests

on: [push, pull_request]

jobs:

  docs:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-python@v5
        with:
          python-version: 3.9
      - name: Build documentation
        run: |
          pip install . -r requirements/doc.txt
          make -C docs html SPHINXOPTS=-W

  lint:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-python@v5
        with:
          python-version: 3.9
      - name: Run linters
        run: |
          pip install . -r requirements/lint.txt
          ruff check .
          ruff format --check --diff .
          check-manifest
          mypy src tests

  test:
    runs-on: ${{ matrix.os }}
    strategy:
      fail-fast: false
      matrix:
        os: [ubuntu-latest]
        python:
          - '3.13'
          - '3.12'
          - '3.11'
          - '3.10'
          - '3.9'
    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-python@v5
        with:
          python-version: ${{ matrix.python }}
      - name: Disable firewall
        if: matrix.os == 'macos-latest'
        run: |
          sudo /usr/libexec/ApplicationFirewall/socketfilterfw --setglobalstate off
      - name: Run tests
        run: |
          python -m pip install -U pip setuptools wheel
          pip install .[dev]
          coverage run -m unittest discover -v
          coverage xml
        shell: bash
      - name: Upload coverage report
        uses: codecov/codecov-action@v4
        with:
          token: ${{ secrets.CODECOV_TOKEN }}

  package:
    runs-on: ubuntu-latest
    needs: [lint, test]
    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-python@v5
        with:
          python-version: 3.9
      - name: Install packages
        run: pip install build wheel
      - name: Build package
        run: python -m build
      - name: Publish to PyPI
        if: github.event_name == 'push' && startsWith(github.event.ref, 'refs/tags/')
        uses: pypa/gh-action-pypi-publish@release/v1
        with:
          user: __token__
          password: ${{ secrets.PYPI_TOKEN }}
