
<!DOCTYPE html>

<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="generator" content="Docutils 0.19: https://docutils.sourceforge.io/" />

  <title>UseJava &mdash; CMake 3.28.1 Documentation</title>

    <link rel="stylesheet" type="text/css" href="../_static/pygments.css" />
    <link rel="stylesheet" type="text/css" href="../_static/cmake.css" />
    
    <script data-url_root="../" id="documentation_options" src="../_static/documentation_options.js"></script>
    <script src="../_static/doctools.js"></script>
    <script src="../_static/sphinx_highlight.js"></script>
    
    <link rel="icon" href="../_static/cmake-favicon.ico"/>
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="next" title="UseSWIG" href="UseSWIG.html" />
    <link rel="prev" title="UseEcos" href="UseEcos.html" />
 

  </head><body>

    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             accesskey="I">index</a></li>
        <li class="right" >
          <a href="UseSWIG.html" title="UseSWIG"
             accesskey="N">next</a> |</li>
        <li class="right" >
          <a href="UseEcos.html" title="UseEcos"
             accesskey="P">previous</a> |</li>
  <li>
    <img src="../_static/cmake-logo-16.png" alt=""
         style="vertical-align: middle; margin-top: -2px" />
  </li>
  <li>
    <a href="https://cmake.org/">CMake</a> &#187;
  </li>
  <li>
    <a href="../index.html">3.28.1 Documentation</a> &#187;
  </li>

          <li class="nav-item nav-item-1"><a href="../manual/cmake-modules.7.html" accesskey="U">cmake-modules(7)</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href="">UseJava</a></li> 
      </ul>
    </div>  

    <div class="document">
      <div class="documentwrapper">
        <div class="bodywrapper">
          <div class="body" role="main">
            
  <section id="usejava">
<span id="module:UseJava"></span><h1>UseJava<a class="headerlink" href="#usejava" title="Permalink to this heading">¶</a></h1>
<p>This file provides support for <code class="docutils literal notranslate"><span class="pre">Java</span></code>.  It is assumed that
<span class="target" id="index-0-module:FindJava"></span><a class="reference internal" href="FindJava.html#module:FindJava" title="FindJava"><code class="xref cmake cmake-module docutils literal notranslate"><span class="pre">FindJava</span></code></a> has already been loaded.  See <span class="target" id="index-1-module:FindJava"></span><a class="reference internal" href="FindJava.html#module:FindJava" title="FindJava"><code class="xref cmake cmake-module docutils literal notranslate"><span class="pre">FindJava</span></code></a> for
information on how to load Java into your CMake project.</p>
<section id="synopsis">
<h2>Synopsis<a class="headerlink" href="#synopsis" title="Permalink to this heading">¶</a></h2>
<pre class="literal-block"><a class="reference internal" href="#creating-and-installing-jars">Creating and Installing JARS</a>
  <a class="reference internal" href="#add-jar">add_jar</a> (&lt;target_name&gt; [SOURCES] &lt;source1&gt; [&lt;source2&gt;...] ...)
  <a class="reference internal" href="#install-jar">install_jar</a> (&lt;target_name&gt; DESTINATION &lt;destination&gt; [COMPONENT &lt;component&gt;])
  <a class="reference internal" href="#install-jni-symlink">install_jni_symlink</a> (&lt;target_name&gt; DESTINATION &lt;destination&gt; [COMPONENT &lt;component&gt;])

<a class="reference internal" href="#header-generation">Header Generation</a>
  <a class="reference internal" href="#create-javah">create_javah</a> ((TARGET &lt;target&gt; | GENERATED_FILES &lt;VAR&gt;) CLASSES &lt;class&gt;... ...)

<a class="reference internal" href="#exporting-jar-targets">Exporting JAR Targets</a>
  <a class="reference internal" href="#install-jar-exports">install_jar_exports</a> (TARGETS &lt;jars&gt;... FILE &lt;filename&gt; DESTINATION &lt;destination&gt; ...)
  <a class="reference internal" href="#export-jars">export_jars</a> (TARGETS &lt;jars&gt;... [NAMESPACE &lt;namespace&gt;] FILE &lt;filename&gt;)

<a class="reference internal" href="#finding-jars">Finding JARs</a>
  <a class="reference internal" href="#find-jar">find_jar</a> (&lt;VAR&gt; NAMES &lt;name1&gt; [&lt;name2&gt;...] [PATHS &lt;path1&gt; [&lt;path2&gt;... ENV &lt;var&gt;]] ...)

<a class="reference internal" href="#creating-java-documentation">Creating Java Documentation</a>
  <a class="reference internal" href="#create-javadoc">create_javadoc</a> (&lt;VAR&gt; (PACKAGES &lt;pkg1&gt; [&lt;pkg2&gt;...] | FILES &lt;file1&gt; [&lt;file2&gt;...]) ...)</pre>
</section>
<section id="creating-and-installing-jars">
<h2>Creating And Installing JARs<a class="headerlink" href="#creating-and-installing-jars" title="Permalink to this heading">¶</a></h2>
<span class="target" id="add-jar"></span><dl class="cmake command">
<dt class="sig sig-object cmake" id="command:add_jar">
<span class="sig-name descname"><span class="pre">add_jar</span></span><a class="headerlink" href="#command:add_jar" title="Permalink to this definition">¶</a></dt>
<dd><p>Creates a jar file containing java objects and, optionally, resources:</p>
<div class="highlight-none notranslate"><div class="highlight"><pre><span></span>add_jar(&lt;target_name&gt;
        [SOURCES] &lt;source1&gt; [&lt;source2&gt;...] [&lt;resource1&gt;...]
        [RESOURCES NAMESPACE &lt;ns1&gt; &lt;resource1&gt;... [NAMESPACE &lt;nsX&gt; &lt;resourceX&gt;...]... ]
        [INCLUDE_JARS &lt;jar1&gt; [&lt;jar2&gt;...]]
        [ENTRY_POINT &lt;entry&gt;]
        [VERSION &lt;version&gt;]
        [MANIFEST &lt;manifest&gt;]
        [OUTPUT_NAME &lt;name&gt;]
        [OUTPUT_DIR &lt;dir&gt;]
        [GENERATE_NATIVE_HEADERS &lt;target&gt;
                                 [DESTINATION (&lt;dir&gt;|INSTALL &lt;dir&gt; [BUILD &lt;dir&gt;])]]
        )
</pre></div>
</div>
<p>This command creates a <code class="docutils literal notranslate"><span class="pre">&lt;target_name&gt;.jar</span></code>.  It compiles the given
<code class="docutils literal notranslate"><span class="pre">&lt;source&gt;</span></code> files and adds the given <code class="docutils literal notranslate"><span class="pre">&lt;resource&gt;</span></code> files to
the jar file.  Source files can be java files or listing files
(prefixed by <code class="docutils literal notranslate"><span class="pre">&#64;</span></code>).  If only resource files are given then just a jar file
is created.</p>
<dl>
<dt><code class="docutils literal notranslate"><span class="pre">SOURCES</span></code></dt><dd><p>Compiles the specified source files and adds the result in the jar file.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.4: </span>Support for response files, prefixed by <code class="docutils literal notranslate"><span class="pre">&#64;</span></code>.</p>
</div>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">RESOURCES</span></code></dt><dd><div class="versionadded">
<p><span class="versionmodified added">New in version 3.21.</span></p>
</div>
<p>Adds the named <code class="docutils literal notranslate"><span class="pre">&lt;resource&gt;</span></code> files to the jar by stripping the source file
path and placing the file beneath <code class="docutils literal notranslate"><span class="pre">&lt;ns&gt;</span></code> within the jar.</p>
<p>For example:</p>
<div class="highlight-none notranslate"><div class="highlight"><pre><span></span>RESOURCES NAMESPACE &quot;/com/my/namespace&quot; &quot;a/path/to/resource.txt&quot;
</pre></div>
</div>
<p>results in a resource accessible via <code class="docutils literal notranslate"><span class="pre">/com/my/namespace/resource.txt</span></code>
within the jar.</p>
<p>Resources may be added without adjusting the namespace by adding them to
the list of <code class="docutils literal notranslate"><span class="pre">SOURCES</span></code> (original behavior), in this case, resource
paths must be relative to <code class="docutils literal notranslate"><span class="pre">CMAKE_CURRENT_SOURCE_DIR</span></code>.  Adding resources
without using the <code class="docutils literal notranslate"><span class="pre">RESOURCES</span></code> parameter in out of source builds will
almost certainly result in confusion.</p>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>Adding resources via the <code class="docutils literal notranslate"><span class="pre">SOURCES</span></code> parameter relies upon a hard-coded
list of file extensions which are tested to determine whether they
compile (e.g. File.java). <code class="docutils literal notranslate"><span class="pre">SOURCES</span></code> files which match the extensions
are compiled. Files which do not match are treated as resources. To
include uncompiled resources matching those file extensions use
the <code class="docutils literal notranslate"><span class="pre">RESOURCES</span></code> parameter.</p>
</div>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">INCLUDE_JARS</span></code></dt><dd><p>The list of jars are added to the classpath when compiling the java sources
and also to the dependencies of the target. <code class="docutils literal notranslate"><span class="pre">INCLUDE_JARS</span></code> also accepts
other target names created by <code class="docutils literal notranslate"><span class="pre">add_jar()</span></code>. For backwards compatibility,
jar files listed as sources are ignored (as they have been since the first
version of this module).</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">ENTRY_POINT</span></code></dt><dd><p>Defines an entry point in the jar file.</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">VERSION</span></code></dt><dd><p>Adds a version to the target output name.</p>
<p>The following example will create a jar file with the name
<code class="docutils literal notranslate"><span class="pre">shibboleet-1.2.0.jar</span></code> and will create a symlink <code class="docutils literal notranslate"><span class="pre">shibboleet.jar</span></code>
pointing to the jar with the version information.</p>
<div class="highlight-cmake notranslate"><div class="highlight"><pre><span></span><span class="nf">add_jar(</span><span class="nb">shibboleet</span><span class="w"> </span><span class="nb">shibbotleet.java</span><span class="w"> </span><span class="no">VERSION</span><span class="w"> </span><span class="m">1.2.0</span><span class="nf">)</span>
</pre></div>
</div>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">MANIFEST</span></code></dt><dd><p>Defines a custom manifest for the jar.</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">OUTPUT_NAME</span></code></dt><dd><p>Specify a different output name for the target.</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">OUTPUT_DIR</span></code></dt><dd><p>Sets the directory where the jar file will be generated. If not specified,
<span class="target" id="index-0-variable:CMAKE_CURRENT_BINARY_DIR"></span><a class="reference internal" href="../variable/CMAKE_CURRENT_BINARY_DIR.html#variable:CMAKE_CURRENT_BINARY_DIR" title="CMAKE_CURRENT_BINARY_DIR"><code class="xref cmake cmake-variable docutils literal notranslate"><span class="pre">CMAKE_CURRENT_BINARY_DIR</span></code></a> is used as the output directory.</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">GENERATE_NATIVE_HEADERS</span></code></dt><dd><div class="versionadded">
<p><span class="versionmodified added">New in version 3.11.</span></p>
</div>
<p>Generates native header files for methods declared as native. These files
provide the connective glue that allow your Java and C code to interact.
An INTERFACE target will be created for an easy usage of generated files.
Sub-option <code class="docutils literal notranslate"><span class="pre">DESTINATION</span></code> can be used to specify the output directory for
generated header files.</p>
<p>This option requires, at least, version 1.8 of the JDK.</p>
<p>For an optimum usage of this option, it is recommended to include module
JNI before any call to <code class="docutils literal notranslate"><span class="pre">add_jar()</span></code>. The produced target for native
headers can then be used to compile C/C++ sources with the
<span class="target" id="index-0-command:target_link_libraries"></span><a class="reference internal" href="../command/target_link_libraries.html#command:target_link_libraries" title="target_link_libraries"><code class="xref cmake cmake-command docutils literal notranslate"><span class="pre">target_link_libraries()</span></code></a> command.</p>
<div class="highlight-cmake notranslate"><div class="highlight"><pre><span></span><span class="nf">find_package(</span><span class="no">JNI</span><span class="nf">)</span>
<span class="nf">add_jar(</span><span class="nb">foo</span><span class="w"> </span><span class="nb">foo.java</span><span class="w"> </span><span class="no">GENERATE_NATIVE_HEADERS</span><span class="w"> </span><span class="nb">foo-native</span><span class="nf">)</span>
<span class="nf">add_library(</span><span class="nb">bar</span><span class="w"> </span><span class="nb">bar.cpp</span><span class="nf">)</span>
<span class="nf">target_link_libraries(</span><span class="nb">bar</span><span class="w"> </span><span class="no">PRIVATE</span><span class="w"> </span><span class="nb">foo-native</span><span class="nf">)</span>
</pre></div>
</div>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.20: </span><code class="docutils literal notranslate"><span class="pre">DESTINATION</span></code> sub-option now supports the possibility to specify
different output directories for <code class="docutils literal notranslate"><span class="pre">BUILD</span></code> and <code class="docutils literal notranslate"><span class="pre">INSTALL</span></code> steps. If
<code class="docutils literal notranslate"><span class="pre">BUILD</span></code> directory is not specified, a default directory will be used.</p>
<p>To export the interface target generated by <code class="docutils literal notranslate"><span class="pre">GENERATE_NATIVE_HEADERS</span></code>
option, sub-option <code class="docutils literal notranslate"><span class="pre">INSTALL</span></code> of <code class="docutils literal notranslate"><span class="pre">DESTINATION</span></code> is required:</p>
<div class="highlight-cmake notranslate"><div class="highlight"><pre><span></span><span class="nf">add_jar(</span><span class="nb">foo</span><span class="w"> </span><span class="nb">foo.java</span><span class="w"> </span><span class="no">GENERATE_NATIVE_HEADERS</span><span class="w"> </span><span class="nb">foo-native</span>
<span class="w">                     </span><span class="no">DESTINATION</span><span class="w"> </span><span class="no">INSTALL</span><span class="w"> </span><span class="nb">include</span><span class="nf">)</span>
<span class="nf">install(</span><span class="no">TARGETS</span><span class="w"> </span><span class="nb">foo-native</span><span class="w"> </span><span class="no">EXPORT</span><span class="w"> </span><span class="nb">native</span><span class="nf">)</span>
<span class="nf">install(</span><span class="no">DIRECTORY</span><span class="w"> </span><span class="s">&quot;$&lt;TARGET_PROPERTY:foo-native,NATIVE_HEADERS_DIRECTORY&gt;/&quot;</span>
<span class="w">        </span><span class="no">DESTINATION</span><span class="w"> </span><span class="nb">include</span><span class="nf">)</span>
<span class="nf">install(</span><span class="no">EXPORT</span><span class="w"> </span><span class="nb">native</span><span class="w"> </span><span class="no">DESTINATION</span><span class="w"> </span><span class="na">/to/export</span><span class="w"> </span><span class="no">NAMESPACE</span><span class="w"> </span><span class="nb">foo</span><span class="nf">)</span>
</pre></div>
</div>
</div>
</dd>
</dl>
<p>Some variables can be set to customize the behavior of <code class="docutils literal notranslate"><span class="pre">add_jar()</span></code> as well
as the java compiler:</p>
<dl>
<dt><code class="docutils literal notranslate"><span class="pre">CMAKE_JAVA_COMPILE_FLAGS</span></code></dt><dd><p>Specify additional flags to java compiler.</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">CMAKE_JAVA_INCLUDE_PATH</span></code></dt><dd><p>Specify additional paths to the class path.</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">CMAKE_JNI_TARGET</span></code></dt><dd><p>If the target is a JNI library, sets this boolean variable to <code class="docutils literal notranslate"><span class="pre">TRUE</span></code> to
enable creation of a JNI symbolic link (see also
<a class="reference internal" href="#install-jni-symlink"><span class="std std-ref">install_jni_symlink()</span></a>).</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">CMAKE_JAR_CLASSES_PREFIX</span></code></dt><dd><p>If multiple jars should be produced from the same java source filetree,
to prevent the accumulation of duplicate class files in subsequent jars,
set/reset <code class="docutils literal notranslate"><span class="pre">CMAKE_JAR_CLASSES_PREFIX</span></code> prior to calling the <code class="docutils literal notranslate"><span class="pre">add_jar()</span></code>:</p>
<div class="highlight-cmake notranslate"><div class="highlight"><pre><span></span><span class="nf">set(</span><span class="no">CMAKE_JAR_CLASSES_PREFIX</span><span class="w"> </span><span class="na">com/redhat/foo</span><span class="nf">)</span>
<span class="nf">add_jar(</span><span class="nb">foo</span><span class="w"> </span><span class="nb">foo.java</span><span class="nf">)</span>

<span class="nf">set(</span><span class="no">CMAKE_JAR_CLASSES_PREFIX</span><span class="w"> </span><span class="na">com/redhat/bar</span><span class="nf">)</span>
<span class="nf">add_jar(</span><span class="nb">bar</span><span class="w"> </span><span class="nb">bar.java</span><span class="nf">)</span>
</pre></div>
</div>
</dd>
</dl>
<p>The <code class="docutils literal notranslate"><span class="pre">add_jar()</span></code> function sets the following target properties on
<code class="docutils literal notranslate"><span class="pre">&lt;target_name&gt;</span></code>:</p>
<dl>
<dt><code class="docutils literal notranslate"><span class="pre">INSTALL_FILES</span></code></dt><dd><p>The files which should be installed.  This is used by
<a class="reference internal" href="#install-jar"><span class="std std-ref">install_jar()</span></a>.</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">JNI_SYMLINK</span></code></dt><dd><p>The JNI symlink which should be installed.  This is used by
<a class="reference internal" href="#install-jni-symlink"><span class="std std-ref">install_jni_symlink()</span></a>.</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">JAR_FILE</span></code></dt><dd><p>The location of the jar file so that you can include it.</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">CLASSDIR</span></code></dt><dd><p>The directory where the class files can be found.  For example to use them
with <code class="docutils literal notranslate"><span class="pre">javah</span></code>.</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">NATIVE_HEADERS_DIRECTORY</span></code></dt><dd><div class="versionadded">
<p><span class="versionmodified added">New in version 3.20.</span></p>
</div>
<p>The directory where native headers are generated. Defined when option
<code class="docutils literal notranslate"><span class="pre">GENERATE_NATIVE_HEADERS</span></code> is specified.</p>
</dd>
</dl>
</dd></dl>

<span class="target" id="install-jar"></span><dl class="cmake command">
<dt class="sig sig-object cmake" id="command:install_jar">
<span class="sig-name descname"><span class="pre">install_jar</span></span><a class="headerlink" href="#command:install_jar" title="Permalink to this definition">¶</a></dt>
<dd><p>This command installs the jar file to the given destination:</p>
<div class="highlight-none notranslate"><div class="highlight"><pre><span></span>install_jar(&lt;target_name&gt; &lt;destination&gt;)
install_jar(&lt;target_name&gt; DESTINATION &lt;destination&gt; [COMPONENT &lt;component&gt;])
</pre></div>
</div>
<p>This command installs the <code class="docutils literal notranslate"><span class="pre">&lt;target_name&gt;</span></code> file to the given
<code class="docutils literal notranslate"><span class="pre">&lt;destination&gt;</span></code>.  It should be called in the same scope as
<a class="reference internal" href="#add-jar"><span class="std std-ref">add_jar()</span></a> or it will fail.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.4: </span>The second signature with <code class="docutils literal notranslate"><span class="pre">DESTINATION</span></code> and <code class="docutils literal notranslate"><span class="pre">COMPONENT</span></code> options.</p>
</div>
<dl class="simple">
<dt><code class="docutils literal notranslate"><span class="pre">DESTINATION</span></code></dt><dd><p>Specify the directory on disk to which a file will be installed.</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">COMPONENT</span></code></dt><dd><p>Specify an installation component name with which the install rule is
associated, such as &quot;runtime&quot; or &quot;development&quot;.</p>
</dd>
</dl>
<p>The <code class="docutils literal notranslate"><span class="pre">install_jar()</span></code> command sets the following target properties
on <code class="docutils literal notranslate"><span class="pre">&lt;target_name&gt;</span></code>:</p>
<dl class="simple">
<dt><code class="docutils literal notranslate"><span class="pre">INSTALL_DESTINATION</span></code></dt><dd><p>Holds the <code class="docutils literal notranslate"><span class="pre">&lt;destination&gt;</span></code> as described above, and is used by
<a class="reference internal" href="#install-jar-exports"><span class="std std-ref">install_jar_exports()</span></a>.</p>
</dd>
</dl>
</dd></dl>

<span class="target" id="install-jni-symlink"></span><dl class="cmake command">
<dt class="sig sig-object cmake" id="command:install_jni_symlink">
<span class="sig-name descname"><span class="pre">install_jni_symlink</span></span><a class="headerlink" href="#command:install_jni_symlink" title="Permalink to this definition">¶</a></dt>
<dd><p>Installs JNI symlinks for target generated by <a class="reference internal" href="#add-jar"><span class="std std-ref">add_jar()</span></a>:</p>
<div class="highlight-none notranslate"><div class="highlight"><pre><span></span>install_jni_symlink(&lt;target_name&gt; &lt;destination&gt;)
install_jni_symlink(&lt;target_name&gt; DESTINATION &lt;destination&gt; [COMPONENT &lt;component&gt;])
</pre></div>
</div>
<p>This command installs the <code class="docutils literal notranslate"><span class="pre">&lt;target_name&gt;</span></code> JNI symlinks to the given
<code class="docutils literal notranslate"><span class="pre">&lt;destination&gt;</span></code>.  It should be called in the same scope as
<a class="reference internal" href="#add-jar"><span class="std std-ref">add_jar()</span></a> or it will fail.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.4: </span>The second signature with <code class="docutils literal notranslate"><span class="pre">DESTINATION</span></code> and <code class="docutils literal notranslate"><span class="pre">COMPONENT</span></code> options.</p>
</div>
<dl class="simple">
<dt><code class="docutils literal notranslate"><span class="pre">DESTINATION</span></code></dt><dd><p>Specify the directory on disk to which a file will be installed.</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">COMPONENT</span></code></dt><dd><p>Specify an installation component name with which the install rule is
associated, such as &quot;runtime&quot; or &quot;development&quot;.</p>
</dd>
</dl>
<p>Utilize the following commands to create a JNI symbolic link:</p>
<div class="highlight-cmake notranslate"><div class="highlight"><pre><span></span><span class="nf">set(</span><span class="no">CMAKE_JNI_TARGET</span><span class="w"> </span><span class="no">TRUE</span><span class="nf">)</span>
<span class="nf">add_jar(</span><span class="nb">shibboleet</span><span class="w"> </span><span class="nb">shibbotleet.java</span><span class="w"> </span><span class="no">VERSION</span><span class="w"> </span><span class="m">1.2.0</span><span class="nf">)</span>
<span class="nf">install_jar(</span><span class="nb">shibboleet</span><span class="w"> </span><span class="o">${</span><span class="nt">LIB_INSTALL_DIR</span><span class="o">}</span><span class="na">/shibboleet</span><span class="nf">)</span>
<span class="nf">install_jni_symlink(</span><span class="nb">shibboleet</span><span class="w"> </span><span class="o">${</span><span class="nt">JAVA_LIB_INSTALL_DIR</span><span class="o">}</span><span class="nf">)</span>
</pre></div>
</div>
</dd></dl>

</section>
<section id="header-generation">
<h2>Header Generation<a class="headerlink" href="#header-generation" title="Permalink to this heading">¶</a></h2>
<span class="target" id="create-javah"></span><dl class="cmake command">
<dt class="sig sig-object cmake" id="command:create_javah">
<span class="sig-name descname"><span class="pre">create_javah</span></span><a class="headerlink" href="#command:create_javah" title="Permalink to this definition">¶</a></dt>
<dd><div class="versionadded">
<p><span class="versionmodified added">New in version 3.4.</span></p>
</div>
<p>Generates C header files for java classes:</p>
<div class="highlight-none notranslate"><div class="highlight"><pre><span></span>create_javah(TARGET &lt;target&gt; | GENERATED_FILES &lt;VAR&gt;
             CLASSES &lt;class&gt;...
             [CLASSPATH &lt;classpath&gt;...]
             [DEPENDS &lt;depend&gt;...]
             [OUTPUT_NAME &lt;path&gt;|OUTPUT_DIR &lt;path&gt;]
             )
</pre></div>
</div>
<div class="deprecated">
<p><span class="versionmodified deprecated">Deprecated since version 3.11: </span>This command will no longer be supported starting with version 10 of the JDK
due to the <a class="reference external" href="https://openjdk.org/jeps/313">suppression of javah tool</a>.
The <a class="reference internal" href="#add-jar"><span class="std std-ref">add_jar(GENERATE_NATIVE_HEADERS)</span></a> command should be
used instead.</p>
</div>
<p>Create C header files from java classes. These files provide the connective
glue that allow your Java and C code to interact.</p>
<p>There are two main signatures for <code class="docutils literal notranslate"><span class="pre">create_javah()</span></code>.  The first signature
returns generated files through variable specified by the <code class="docutils literal notranslate"><span class="pre">GENERATED_FILES</span></code>
option.  For example:</p>
<div class="highlight-cmake notranslate"><div class="highlight"><pre><span></span><span class="nf">create_javah(</span><span class="no">GENERATED_FILES</span><span class="w"> </span><span class="nb">files_headers</span>
<span class="w">  </span><span class="no">CLASSES</span><span class="w"> </span><span class="nb">org.cmake.HelloWorld</span>
<span class="w">  </span><span class="no">CLASSPATH</span><span class="w"> </span><span class="nb">hello.jar</span>
<span class="nf">)</span>
</pre></div>
</div>
<p>The second signature for <code class="docutils literal notranslate"><span class="pre">create_javah()</span></code> creates a target which
encapsulates header files generation. E.g.</p>
<div class="highlight-cmake notranslate"><div class="highlight"><pre><span></span><span class="nf">create_javah(</span><span class="no">TARGET</span><span class="w"> </span><span class="nb">target_headers</span>
<span class="w">  </span><span class="no">CLASSES</span><span class="w"> </span><span class="nb">org.cmake.HelloWorld</span>
<span class="w">  </span><span class="no">CLASSPATH</span><span class="w"> </span><span class="nb">hello.jar</span>
<span class="nf">)</span>
</pre></div>
</div>
<p>Both signatures share same options.</p>
<dl class="simple">
<dt><code class="docutils literal notranslate"><span class="pre">CLASSES</span></code></dt><dd><p>Specifies Java classes used to generate headers.</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">CLASSPATH</span></code></dt><dd><p>Specifies various paths to look up classes. Here <code class="docutils literal notranslate"><span class="pre">.class</span></code> files, jar
files or targets created by command add_jar can be used.</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">DEPENDS</span></code></dt><dd><p>Targets on which the javah target depends.</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">OUTPUT_NAME</span></code></dt><dd><p>Concatenates the resulting header files for all the classes listed by
option <code class="docutils literal notranslate"><span class="pre">CLASSES</span></code> into <code class="docutils literal notranslate"><span class="pre">&lt;path&gt;</span></code>.  Same behavior as option <code class="docutils literal notranslate"><span class="pre">-o</span></code> of
<code class="docutils literal notranslate"><span class="pre">javah</span></code> tool.</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">OUTPUT_DIR</span></code></dt><dd><p>Sets the directory where the header files will be generated.  Same behavior
as option <code class="docutils literal notranslate"><span class="pre">-d</span></code> of <code class="docutils literal notranslate"><span class="pre">javah</span></code> tool.  If not specified,
<span class="target" id="index-1-variable:CMAKE_CURRENT_BINARY_DIR"></span><a class="reference internal" href="../variable/CMAKE_CURRENT_BINARY_DIR.html#variable:CMAKE_CURRENT_BINARY_DIR" title="CMAKE_CURRENT_BINARY_DIR"><code class="xref cmake cmake-variable docutils literal notranslate"><span class="pre">CMAKE_CURRENT_BINARY_DIR</span></code></a> is used as the output directory.</p>
</dd>
</dl>
</dd></dl>

</section>
<section id="exporting-jar-targets">
<h2>Exporting JAR Targets<a class="headerlink" href="#exporting-jar-targets" title="Permalink to this heading">¶</a></h2>
<span class="target" id="install-jar-exports"></span><dl class="cmake command">
<dt class="sig sig-object cmake" id="command:install_jar_exports">
<span class="sig-name descname"><span class="pre">install_jar_exports</span></span><a class="headerlink" href="#command:install_jar_exports" title="Permalink to this definition">¶</a></dt>
<dd><div class="versionadded">
<p><span class="versionmodified added">New in version 3.7.</span></p>
</div>
<p>Installs a target export file:</p>
<div class="highlight-none notranslate"><div class="highlight"><pre><span></span>install_jar_exports(TARGETS &lt;jars&gt;...
                    [NAMESPACE &lt;namespace&gt;]
                    FILE &lt;filename&gt;
                    DESTINATION &lt;destination&gt; [COMPONENT &lt;component&gt;])
</pre></div>
</div>
<p>This command installs a target export file <code class="docutils literal notranslate"><span class="pre">&lt;filename&gt;</span></code> for the named jar
targets to the given <code class="docutils literal notranslate"><span class="pre">&lt;destination&gt;</span></code> directory.  Its function is similar to
that of <span class="target" id="index-0-command:install"></span><a class="reference internal" href="../command/install.html#export" title="install(export)"><code class="xref cmake cmake-command docutils literal notranslate"><span class="pre">install(EXPORT)</span></code></a>.</p>
<dl>
<dt><code class="docutils literal notranslate"><span class="pre">TARGETS</span></code></dt><dd><p>List of targets created by <a class="reference internal" href="#add-jar"><span class="std std-ref">add_jar()</span></a> command.</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">NAMESPACE</span></code></dt><dd><div class="versionadded">
<p><span class="versionmodified added">New in version 3.9.</span></p>
</div>
<p>The <code class="docutils literal notranslate"><span class="pre">&lt;namespace&gt;</span></code> value will be prepend to the target names as they are
written to the import file.</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">FILE</span></code></dt><dd><p>Specify name of the export file.</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">DESTINATION</span></code></dt><dd><p>Specify the directory on disk to which a file will be installed.</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">COMPONENT</span></code></dt><dd><p>Specify an installation component name with which the install rule is
associated, such as &quot;runtime&quot; or &quot;development&quot;.</p>
</dd>
</dl>
</dd></dl>

<span class="target" id="export-jars"></span><dl class="cmake command">
<dt class="sig sig-object cmake" id="command:export_jars">
<span class="sig-name descname"><span class="pre">export_jars</span></span><a class="headerlink" href="#command:export_jars" title="Permalink to this definition">¶</a></dt>
<dd><div class="versionadded">
<p><span class="versionmodified added">New in version 3.7.</span></p>
</div>
<p>Writes a target export file:</p>
<div class="highlight-none notranslate"><div class="highlight"><pre><span></span>export_jars(TARGETS &lt;jars&gt;...
            [NAMESPACE &lt;namespace&gt;]
            FILE &lt;filename&gt;)
</pre></div>
</div>
<p>This command writes a target export file <code class="docutils literal notranslate"><span class="pre">&lt;filename&gt;</span></code> for the named <code class="docutils literal notranslate"><span class="pre">&lt;jars&gt;</span></code>
targets.  Its function is similar to that of <span class="target" id="index-0-command:export"></span><a class="reference internal" href="../command/export.html#command:export" title="export"><code class="xref cmake cmake-command docutils literal notranslate"><span class="pre">export()</span></code></a>.</p>
<dl>
<dt><code class="docutils literal notranslate"><span class="pre">TARGETS</span></code></dt><dd><p>List of targets created by <a class="reference internal" href="#add-jar"><span class="std std-ref">add_jar()</span></a> command.</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">NAMESPACE</span></code></dt><dd><div class="versionadded">
<p><span class="versionmodified added">New in version 3.9.</span></p>
</div>
<p>The <code class="docutils literal notranslate"><span class="pre">&lt;namespace&gt;</span></code> value will be prepend to the target names as they are
written to the import file.</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">FILE</span></code></dt><dd><p>Specify name of the export file.</p>
</dd>
</dl>
</dd></dl>

</section>
<section id="finding-jars">
<h2>Finding JARs<a class="headerlink" href="#finding-jars" title="Permalink to this heading">¶</a></h2>
<span class="target" id="find-jar"></span><dl class="cmake command">
<dt class="sig sig-object cmake" id="command:find_jar">
<span class="sig-name descname"><span class="pre">find_jar</span></span><a class="headerlink" href="#command:find_jar" title="Permalink to this definition">¶</a></dt>
<dd><p>Finds the specified jar file:</p>
<div class="highlight-none notranslate"><div class="highlight"><pre><span></span>find_jar(&lt;VAR&gt;
         &lt;name&gt; | NAMES &lt;name1&gt; [&lt;name2&gt;...]
         [PATHS &lt;path1&gt; [&lt;path2&gt;... ENV &lt;var&gt;]]
         [VERSIONS &lt;version1&gt; [&lt;version2&gt;]]
         [DOC &quot;cache documentation string&quot;]
        )
</pre></div>
</div>
<p>This command is used to find a full path to the named jar.  A cache
entry named by <code class="docutils literal notranslate"><span class="pre">&lt;VAR&gt;</span></code> is created to store the result of this command.
If the full path to a jar is found the result is stored in the
variable and the search will not repeated unless the variable is
cleared.  If nothing is found, the result will be <code class="docutils literal notranslate"><span class="pre">&lt;VAR&gt;-NOTFOUND</span></code>, and
the search will be attempted again next time <code class="docutils literal notranslate"><span class="pre">find_jar()</span></code> is invoked with
the same variable.</p>
<dl class="simple">
<dt><code class="docutils literal notranslate"><span class="pre">NAMES</span></code></dt><dd><p>Specify one or more possible names for the jar file.</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">PATHS</span></code></dt><dd><p>Specify directories to search in addition to the default locations.
The <code class="docutils literal notranslate"><span class="pre">ENV</span></code> var sub-option reads paths from a system environment variable.</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">VERSIONS</span></code></dt><dd><p>Specify jar versions.</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">DOC</span></code></dt><dd><p>Specify the documentation string for the <code class="docutils literal notranslate"><span class="pre">&lt;VAR&gt;</span></code> cache entry.</p>
</dd>
</dl>
</dd></dl>

</section>
<section id="creating-java-documentation">
<h2>Creating Java Documentation<a class="headerlink" href="#creating-java-documentation" title="Permalink to this heading">¶</a></h2>
<span class="target" id="create-javadoc"></span><dl class="cmake command">
<dt class="sig sig-object cmake" id="command:create_javadoc">
<span class="sig-name descname"><span class="pre">create_javadoc</span></span><a class="headerlink" href="#command:create_javadoc" title="Permalink to this definition">¶</a></dt>
<dd><p>Creates java documentation based on files and packages:</p>
<div class="highlight-none notranslate"><div class="highlight"><pre><span></span>create_javadoc(&lt;VAR&gt;
               (PACKAGES &lt;pkg1&gt; [&lt;pkg2&gt;...] | FILES &lt;file1&gt; [&lt;file2&gt;...])
               [SOURCEPATH &lt;sourcepath&gt;]
               [CLASSPATH &lt;classpath&gt;]
               [INSTALLPATH &lt;install path&gt;]
               [DOCTITLE &lt;the documentation title&gt;]
               [WINDOWTITLE &lt;the title of the document&gt;]
               [AUTHOR (TRUE|FALSE)]
               [USE (TRUE|FALSE)]
               [VERSION (TRUE|FALSE)]
               )
</pre></div>
</div>
<p>The <code class="docutils literal notranslate"><span class="pre">create_javadoc()</span></code> command can be used to create java documentation.
There are two main signatures for <code class="docutils literal notranslate"><span class="pre">create_javadoc()</span></code>.</p>
<p>The first signature works with package names on a path with source files:</p>
<div class="highlight-cmake notranslate"><div class="highlight"><pre><span></span><span class="nf">create_javadoc(</span><span class="nb">my_example_doc</span>
<span class="w">               </span><span class="no">PACKAGES</span><span class="w"> </span><span class="nb">com.example.foo</span><span class="w"> </span><span class="nb">com.example.bar</span>
<span class="w">               </span><span class="no">SOURCEPATH</span><span class="w"> </span><span class="s">&quot;${CMAKE_CURRENT_SOURCE_DIR}&quot;</span>
<span class="w">               </span><span class="no">CLASSPATH</span><span class="w"> </span><span class="o">${</span><span class="nt">CMAKE_JAVA_INCLUDE_PATH</span><span class="o">}</span>
<span class="w">               </span><span class="no">WINDOWTITLE</span><span class="w"> </span><span class="s">&quot;My example&quot;</span>
<span class="w">               </span><span class="no">DOCTITLE</span><span class="w"> </span><span class="s">&quot;&lt;h1&gt;My example&lt;/h1&gt;&quot;</span>
<span class="w">               </span><span class="no">AUTHOR</span><span class="w"> </span><span class="no">TRUE</span>
<span class="w">               </span><span class="no">USE</span><span class="w"> </span><span class="no">TRUE</span>
<span class="w">               </span><span class="no">VERSION</span><span class="w"> </span><span class="no">TRUE</span>
<span class="w">              </span><span class="nf">)</span>
</pre></div>
</div>
<p>The second signature for <code class="docutils literal notranslate"><span class="pre">create_javadoc()</span></code> works on a given list of files:</p>
<div class="highlight-cmake notranslate"><div class="highlight"><pre><span></span><span class="nf">create_javadoc(</span><span class="nb">my_example_doc</span>
<span class="w">               </span><span class="no">FILES</span><span class="w"> </span><span class="na">java/A.java</span><span class="w"> </span><span class="na">java/B.java</span>
<span class="w">               </span><span class="no">CLASSPATH</span><span class="w"> </span><span class="o">${</span><span class="nt">CMAKE_JAVA_INCLUDE_PATH</span><span class="o">}</span>
<span class="w">               </span><span class="no">WINDOWTITLE</span><span class="w"> </span><span class="s">&quot;My example&quot;</span>
<span class="w">               </span><span class="no">DOCTITLE</span><span class="w"> </span><span class="s">&quot;&lt;h1&gt;My example&lt;/h1&gt;&quot;</span>
<span class="w">               </span><span class="no">AUTHOR</span><span class="w"> </span><span class="no">TRUE</span>
<span class="w">               </span><span class="no">USE</span><span class="w"> </span><span class="no">TRUE</span>
<span class="w">               </span><span class="no">VERSION</span><span class="w"> </span><span class="no">TRUE</span>
<span class="w">              </span><span class="nf">)</span>
</pre></div>
</div>
<p>Both signatures share most of the options. For more details please read the
javadoc manpage.</p>
<dl class="simple">
<dt><code class="docutils literal notranslate"><span class="pre">PACKAGES</span></code></dt><dd><p>Specify java packages.</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">FILES</span></code></dt><dd><p>Specify java source files. If relative paths are specified, they are
relative to <span class="target" id="index-0-variable:CMAKE_CURRENT_SOURCE_DIR"></span><a class="reference internal" href="../variable/CMAKE_CURRENT_SOURCE_DIR.html#variable:CMAKE_CURRENT_SOURCE_DIR" title="CMAKE_CURRENT_SOURCE_DIR"><code class="xref cmake cmake-variable docutils literal notranslate"><span class="pre">CMAKE_CURRENT_SOURCE_DIR</span></code></a>.</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">SOURCEPATH</span></code></dt><dd><p>Specify the directory where to look for packages. By default,
<span class="target" id="index-1-variable:CMAKE_CURRENT_SOURCE_DIR"></span><a class="reference internal" href="../variable/CMAKE_CURRENT_SOURCE_DIR.html#variable:CMAKE_CURRENT_SOURCE_DIR" title="CMAKE_CURRENT_SOURCE_DIR"><code class="xref cmake cmake-variable docutils literal notranslate"><span class="pre">CMAKE_CURRENT_SOURCE_DIR</span></code></a> directory is used.</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">CLASSPATH</span></code></dt><dd><p>Specify where to find user class files. Same behavior as option
<code class="docutils literal notranslate"><span class="pre">-classpath</span></code> of <code class="docutils literal notranslate"><span class="pre">javadoc</span></code> tool.</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">INSTALLPATH</span></code></dt><dd><p>Specify where to install the java documentation. If you specified, the
documentation will be installed to
<code class="docutils literal notranslate"><span class="pre">${CMAKE_INSTALL_PREFIX}/share/javadoc/&lt;VAR&gt;</span></code>.</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">DOCTITLE</span></code></dt><dd><p>Specify the title to place near the top of the overview summary file.
Same behavior as option <code class="docutils literal notranslate"><span class="pre">-doctitle</span></code> of <code class="docutils literal notranslate"><span class="pre">javadoc</span></code> tool.</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">WINDOWTITLE</span></code></dt><dd><p>Specify the title to be placed in the HTML <code class="docutils literal notranslate"><span class="pre">&lt;title&gt;</span></code> tag. Same behavior
as option <code class="docutils literal notranslate"><span class="pre">-windowtitle</span></code> of <code class="docutils literal notranslate"><span class="pre">javadoc</span></code> tool.</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">AUTHOR</span></code></dt><dd><p>When value <code class="docutils literal notranslate"><span class="pre">TRUE</span></code> is specified, includes the <code class="docutils literal notranslate"><span class="pre">&#64;author</span></code> text in the
generated docs. Same behavior as option  <code class="docutils literal notranslate"><span class="pre">-author</span></code> of <code class="docutils literal notranslate"><span class="pre">javadoc</span></code> tool.</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">USE</span></code></dt><dd><p>When value <code class="docutils literal notranslate"><span class="pre">TRUE</span></code> is specified, creates class and package usage pages.
Includes one Use page for each documented class and package. Same behavior
as option <code class="docutils literal notranslate"><span class="pre">-use</span></code> of <code class="docutils literal notranslate"><span class="pre">javadoc</span></code> tool.</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">VERSION</span></code></dt><dd><p>When value <code class="docutils literal notranslate"><span class="pre">TRUE</span></code> is specified, includes the version text in the
generated docs. Same behavior as option <code class="docutils literal notranslate"><span class="pre">-version</span></code> of <code class="docutils literal notranslate"><span class="pre">javadoc</span></code> tool.</p>
</dd>
</dl>
</dd></dl>

</section>
</section>


            <div class="clearer"></div>
          </div>
        </div>
      </div>
      <div class="sphinxsidebar" role="navigation" aria-label="main navigation">
        <div class="sphinxsidebarwrapper">
  <div>
    <h3><a href="../index.html">Table of Contents</a></h3>
    <ul>
<li><a class="reference internal" href="#">UseJava</a><ul>
<li><a class="reference internal" href="#synopsis">Synopsis</a></li>
<li><a class="reference internal" href="#creating-and-installing-jars">Creating And Installing JARs</a></li>
<li><a class="reference internal" href="#header-generation">Header Generation</a></li>
<li><a class="reference internal" href="#exporting-jar-targets">Exporting JAR Targets</a></li>
<li><a class="reference internal" href="#finding-jars">Finding JARs</a></li>
<li><a class="reference internal" href="#creating-java-documentation">Creating Java Documentation</a></li>
</ul>
</li>
</ul>

  </div>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="UseEcos.html"
                          title="previous chapter">UseEcos</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="UseSWIG.html"
                          title="next chapter">UseSWIG</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../_sources/module/UseJava.rst.txt"
            rel="nofollow">Show Source</a></li>
    </ul>
   </div>
<div id="searchbox" style="display: none" role="search">
  <h3 id="searchlabel">Quick search</h3>
    <div class="searchformwrapper">
    <form class="search" action="../search.html" method="get">
      <input type="text" name="q" aria-labelledby="searchlabel" autocomplete="off" autocorrect="off" autocapitalize="off" spellcheck="false"/>
      <input type="submit" value="Go" />
    </form>
    </div>
</div>
<script>document.getElementById('searchbox').style.display = "block"</script>
        </div>
      </div>
      <div class="clearer"></div>
    </div>
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             >index</a></li>
        <li class="right" >
          <a href="UseSWIG.html" title="UseSWIG"
             >next</a> |</li>
        <li class="right" >
          <a href="UseEcos.html" title="UseEcos"
             >previous</a> |</li>
  <li>
    <img src="../_static/cmake-logo-16.png" alt=""
         style="vertical-align: middle; margin-top: -2px" />
  </li>
  <li>
    <a href="https://cmake.org/">CMake</a> &#187;
  </li>
  <li>
    <a href="../index.html">3.28.1 Documentation</a> &#187;
  </li>

          <li class="nav-item nav-item-1"><a href="../manual/cmake-modules.7.html" >cmake-modules(7)</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href="">UseJava</a></li> 
      </ul>
    </div>

    <div class="footer" role="contentinfo">
        &#169; Copyright 2000-2023 Kitware, Inc. and Contributors.
      Created using <a href="https://www.sphinx-doc.org/">Sphinx</a> 6.2.1.
    </div>
  </body>
</html>