
<!DOCTYPE html>

<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="generator" content="Docutils 0.19: https://docutils.sourceforge.io/" />

  <title>add_compile_definitions &mdash; CMake 3.28.1 Documentation</title>

    <link rel="stylesheet" type="text/css" href="../_static/pygments.css" />
    <link rel="stylesheet" type="text/css" href="../_static/cmake.css" />
    
    <script data-url_root="../" id="documentation_options" src="../_static/documentation_options.js"></script>
    <script src="../_static/doctools.js"></script>
    <script src="../_static/sphinx_highlight.js"></script>
    
    <link rel="icon" href="../_static/cmake-favicon.ico"/>
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="next" title="add_compile_options" href="add_compile_options.html" />
    <link rel="prev" title="while" href="while.html" />
 

  </head><body>

    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             accesskey="I">index</a></li>
        <li class="right" >
          <a href="add_compile_options.html" title="add_compile_options"
             accesskey="N">next</a> |</li>
        <li class="right" >
          <a href="while.html" title="while"
             accesskey="P">previous</a> |</li>
  <li>
    <img src="../_static/cmake-logo-16.png" alt=""
         style="vertical-align: middle; margin-top: -2px" />
  </li>
  <li>
    <a href="https://cmake.org/">CMake</a> &#187;
  </li>
  <li>
    <a href="../index.html">3.28.1 Documentation</a> &#187;
  </li>

          <li class="nav-item nav-item-1"><a href="../manual/cmake-commands.7.html" accesskey="U">cmake-commands(7)</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href="">add_compile_definitions</a></li> 
      </ul>
    </div>  

    <div class="document">
      <div class="documentwrapper">
        <div class="bodywrapper">
          <div class="body" role="main">
            
  <section id="add-compile-definitions">
<span id="command:add_compile_definitions"></span><h1>add_compile_definitions<a class="headerlink" href="#add-compile-definitions" title="Permalink to this heading">¶</a></h1>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.12.</span></p>
</div>
<p>Add preprocessor definitions to the compilation of source files.</p>
<div class="highlight-cmake notranslate"><div class="highlight"><pre><span></span><span class="nf">add_compile_definitions(</span><span class="nv">&lt;definition&gt;</span><span class="w"> </span><span class="p">...</span><span class="nf">)</span>
</pre></div>
</div>
<p>Adds preprocessor definitions to the compiler command line.</p>
<p>The preprocessor definitions are added to the <span class="target" id="index-0-prop_dir:COMPILE_DEFINITIONS"></span><a class="reference internal" href="../prop_dir/COMPILE_DEFINITIONS.html#prop_dir:COMPILE_DEFINITIONS" title="COMPILE_DEFINITIONS"><code class="xref cmake cmake-prop_dir docutils literal notranslate"><span class="pre">COMPILE_DEFINITIONS</span></code></a>
directory property for the current <code class="docutils literal notranslate"><span class="pre">CMakeLists</span></code> file. They are also added to
the <span class="target" id="index-0-prop_tgt:COMPILE_DEFINITIONS"></span><a class="reference internal" href="../prop_tgt/COMPILE_DEFINITIONS.html#prop_tgt:COMPILE_DEFINITIONS" title="COMPILE_DEFINITIONS"><code class="xref cmake cmake-prop_tgt docutils literal notranslate"><span class="pre">COMPILE_DEFINITIONS</span></code></a> target property for each target in the
current <code class="docutils literal notranslate"><span class="pre">CMakeLists</span></code> file.</p>
<p>Definitions are specified using the syntax <code class="docutils literal notranslate"><span class="pre">VAR</span></code> or <code class="docutils literal notranslate"><span class="pre">VAR=value</span></code>.
Function-style definitions are not supported. CMake will automatically
escape the value correctly for the native build system (note that CMake
language syntax may require escapes to specify some values).</p>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.26: </span>Any leading <code class="docutils literal notranslate"><span class="pre">-D</span></code> on an item will be removed.</p>
</div>
<p>Arguments to <code class="docutils literal notranslate"><span class="pre">add_compile_definitions</span></code> may use generator expressions
with the syntax <code class="docutils literal notranslate"><span class="pre">$&lt;...&gt;</span></code>. See the <span class="target" id="index-0-manual:cmake-generator-expressions(7)"></span><a class="reference internal" href="../manual/cmake-generator-expressions.7.html#manual:cmake-generator-expressions(7)" title="cmake-generator-expressions(7)"><code class="xref cmake cmake-manual docutils literal notranslate"><span class="pre">cmake-generator-expressions(7)</span></code></a>
manual for available expressions.  See the <span class="target" id="index-1-manual:cmake-buildsystem(7)"></span><a class="reference internal" href="../manual/cmake-buildsystem.7.html#manual:cmake-buildsystem(7)" title="cmake-buildsystem(7)"><code class="xref cmake cmake-manual docutils literal notranslate"><span class="pre">cmake-buildsystem(7)</span></code></a> manual
for more on defining buildsystem properties.</p>
<section id="see-also">
<h2>See Also<a class="headerlink" href="#see-also" title="Permalink to this heading">¶</a></h2>
<ul class="simple">
<li><p>The command <span class="target" id="index-0-command:target_compile_definitions"></span><a class="reference internal" href="target_compile_definitions.html#command:target_compile_definitions" title="target_compile_definitions"><code class="xref cmake cmake-command docutils literal notranslate"><span class="pre">target_compile_definitions()</span></code></a> adds target-specific definitions.</p></li>
</ul>
</section>
</section>


            <div class="clearer"></div>
          </div>
        </div>
      </div>
      <div class="sphinxsidebar" role="navigation" aria-label="main navigation">
        <div class="sphinxsidebarwrapper">
  <div>
    <h3><a href="../index.html">Table of Contents</a></h3>
    <ul>
<li><a class="reference internal" href="#">add_compile_definitions</a><ul>
<li><a class="reference internal" href="#see-also">See Also</a></li>
</ul>
</li>
</ul>

  </div>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="while.html"
                          title="previous chapter">while</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="add_compile_options.html"
                          title="next chapter">add_compile_options</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../_sources/command/add_compile_definitions.rst.txt"
            rel="nofollow">Show Source</a></li>
    </ul>
   </div>
<div id="searchbox" style="display: none" role="search">
  <h3 id="searchlabel">Quick search</h3>
    <div class="searchformwrapper">
    <form class="search" action="../search.html" method="get">
      <input type="text" name="q" aria-labelledby="searchlabel" autocomplete="off" autocorrect="off" autocapitalize="off" spellcheck="false"/>
      <input type="submit" value="Go" />
    </form>
    </div>
</div>
<script>document.getElementById('searchbox').style.display = "block"</script>
        </div>
      </div>
      <div class="clearer"></div>
    </div>
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             >index</a></li>
        <li class="right" >
          <a href="add_compile_options.html" title="add_compile_options"
             >next</a> |</li>
        <li class="right" >
          <a href="while.html" title="while"
             >previous</a> |</li>
  <li>
    <img src="../_static/cmake-logo-16.png" alt=""
         style="vertical-align: middle; margin-top: -2px" />
  </li>
  <li>
    <a href="https://cmake.org/">CMake</a> &#187;
  </li>
  <li>
    <a href="../index.html">3.28.1 Documentation</a> &#187;
  </li>

          <li class="nav-item nav-item-1"><a href="../manual/cmake-commands.7.html" >cmake-commands(7)</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href="">add_compile_definitions</a></li> 
      </ul>
    </div>

    <div class="footer" role="contentinfo">
        &#169; Copyright 2000-2023 Kitware, Inc. and Contributors.
      Created using <a href="https://www.sphinx-doc.org/">Sphinx</a> 6.2.1.
    </div>
  </body>
</html>