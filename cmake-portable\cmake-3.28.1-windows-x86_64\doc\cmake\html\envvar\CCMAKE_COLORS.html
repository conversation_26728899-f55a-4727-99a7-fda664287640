
<!DOCTYPE html>

<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="generator" content="Docutils 0.19: https://docutils.sourceforge.io/" />

  <title>CCMAKE_COLORS &mdash; CMake 3.28.1 Documentation</title>

    <link rel="stylesheet" type="text/css" href="../_static/pygments.css" />
    <link rel="stylesheet" type="text/css" href="../_static/cmake.css" />
    
    <script data-url_root="../" id="documentation_options" src="../_static/documentation_options.js"></script>
    <script src="../_static/doctools.js"></script>
    <script src="../_static/sphinx_highlight.js"></script>
    
    <link rel="icon" href="../_static/cmake-favicon.ico"/>
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="next" title="cmake-file-api(7)" href="../manual/cmake-file-api.7.html" />
    <link rel="prev" title="DASHBOARD_TEST_FROM_CTEST" href="DASHBOARD_TEST_FROM_CTEST.html" />
 

  </head><body>

    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             accesskey="I">index</a></li>
        <li class="right" >
          <a href="../manual/cmake-file-api.7.html" title="cmake-file-api(7)"
             accesskey="N">next</a> |</li>
        <li class="right" >
          <a href="DASHBOARD_TEST_FROM_CTEST.html" title="DASHBOARD_TEST_FROM_CTEST"
             accesskey="P">previous</a> |</li>
  <li>
    <img src="../_static/cmake-logo-16.png" alt=""
         style="vertical-align: middle; margin-top: -2px" />
  </li>
  <li>
    <a href="https://cmake.org/">CMake</a> &#187;
  </li>
  <li>
    <a href="../index.html">3.28.1 Documentation</a> &#187;
  </li>

          <li class="nav-item nav-item-1"><a href="../manual/cmake-env-variables.7.html" accesskey="U">cmake-env-variables(7)</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href="">CCMAKE_COLORS</a></li> 
      </ul>
    </div>  

    <div class="document">
      <div class="documentwrapper">
        <div class="bodywrapper">
          <div class="body" role="main">
            
  <section id="ccmake-colors">
<span id="envvar:CCMAKE_COLORS"></span><h1>CCMAKE_COLORS<a class="headerlink" href="#ccmake-colors" title="Permalink to this heading">¶</a></h1>
<div class="versionadded">
<p><span class="versionmodified added">New in version 3.18.</span></p>
</div>
<p>Determines what colors are used by the CMake curses interface,
when run on a terminal that supports colors.
The syntax follows the same conventions as <code class="docutils literal notranslate"><span class="pre">LS_COLORS</span></code>;
that is, a list of key/value pairs separated by <code class="docutils literal notranslate"><span class="pre">:</span></code>.</p>
<p>Keys are a single letter corresponding to a CMake cache variable type:</p>
<ul class="simple">
<li><p><code class="docutils literal notranslate"><span class="pre">s</span></code>: A <code class="docutils literal notranslate"><span class="pre">STRING</span></code>.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">p</span></code>: A <code class="docutils literal notranslate"><span class="pre">FILEPATH</span></code>.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">c</span></code>: A value which has an associated list of choices.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">y</span></code>: A <code class="docutils literal notranslate"><span class="pre">BOOL</span></code> which has a true-like value (e.g. <code class="docutils literal notranslate"><span class="pre">ON</span></code>, <code class="docutils literal notranslate"><span class="pre">YES</span></code>).</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">n</span></code>: A <code class="docutils literal notranslate"><span class="pre">BOOL</span></code> which has a false-like value (e.g. <code class="docutils literal notranslate"><span class="pre">OFF</span></code>, <code class="docutils literal notranslate"><span class="pre">NO</span></code>).</p></li>
</ul>
<p>Values are an integer number that specifies what color to use.
<code class="docutils literal notranslate"><span class="pre">0</span></code> is black (you probably don't want to use that).
Others are determined by your terminal's color support.
Most (color) terminals will support at least 8 or 16 colors.
Some will support up to 256 colors. The colors will likely match
<a class="reference external" href="https://upload.wikimedia.org/wikipedia/commons/1/15/Xterm_256color_chart.svg">this chart</a>,
although the first 16 colors may match the original
<a class="reference external" href="https://en.wikipedia.org/wiki/Color_Graphics_Adapter#Color_palette">CGA color palette</a>.
(Many modern terminal emulators also allow their color palette,
at least for the first 16 colors, to be configured by the user.)</p>
<p>Note that fairly minimal checking is done for bad colors
(although a value higher than what curses believes your terminal supports
will be silently ignored) or bad syntax.</p>
<p>For example:</p>
<div class="highlight-none notranslate"><div class="highlight"><pre><span></span>CCMAKE_COLORS=&#39;s=39:p=220:c=207:n=196:y=46&#39;
</pre></div>
</div>
</section>


            <div class="clearer"></div>
          </div>
        </div>
      </div>
      <div class="sphinxsidebar" role="navigation" aria-label="main navigation">
        <div class="sphinxsidebarwrapper">
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="DASHBOARD_TEST_FROM_CTEST.html"
                          title="previous chapter">DASHBOARD_TEST_FROM_CTEST</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="../manual/cmake-file-api.7.html"
                          title="next chapter">cmake-file-api(7)</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../_sources/envvar/CCMAKE_COLORS.rst.txt"
            rel="nofollow">Show Source</a></li>
    </ul>
   </div>
<div id="searchbox" style="display: none" role="search">
  <h3 id="searchlabel">Quick search</h3>
    <div class="searchformwrapper">
    <form class="search" action="../search.html" method="get">
      <input type="text" name="q" aria-labelledby="searchlabel" autocomplete="off" autocorrect="off" autocapitalize="off" spellcheck="false"/>
      <input type="submit" value="Go" />
    </form>
    </div>
</div>
<script>document.getElementById('searchbox').style.display = "block"</script>
        </div>
      </div>
      <div class="clearer"></div>
    </div>
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             >index</a></li>
        <li class="right" >
          <a href="../manual/cmake-file-api.7.html" title="cmake-file-api(7)"
             >next</a> |</li>
        <li class="right" >
          <a href="DASHBOARD_TEST_FROM_CTEST.html" title="DASHBOARD_TEST_FROM_CTEST"
             >previous</a> |</li>
  <li>
    <img src="../_static/cmake-logo-16.png" alt=""
         style="vertical-align: middle; margin-top: -2px" />
  </li>
  <li>
    <a href="https://cmake.org/">CMake</a> &#187;
  </li>
  <li>
    <a href="../index.html">3.28.1 Documentation</a> &#187;
  </li>

          <li class="nav-item nav-item-1"><a href="../manual/cmake-env-variables.7.html" >cmake-env-variables(7)</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href="">CCMAKE_COLORS</a></li> 
      </ul>
    </div>

    <div class="footer" role="contentinfo">
        &#169; Copyright 2000-2023 Kitware, Inc. and Contributors.
      Created using <a href="https://www.sphinx-doc.org/">Sphinx</a> 6.2.1.
    </div>
  </body>
</html>